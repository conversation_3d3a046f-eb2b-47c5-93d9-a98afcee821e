function getCookie(cname:any){
    let name = cname + "=";
    let ca = document.cookie.split(';');
    for(let i=0; i<ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
    }
    return "";
}
export const environment = {
    production: true,
    test:false,
    API_URL:'/signority/api/',
    NEW_API_URL:'/neo/api/',
    logging:false,
    stoken: getCookie('sToken'),
    // stripeKey: 'pk_test_PsWEli8VyZPmVGGmwHXPVS89', // test key
    stripeKey: 'pk_live_38VxRdTi2HxJEi6QL6a7BUVI', // live key
};
