// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.



// local dev
function getCookie(cname:any){
    let name = cname + "=";
    let ca = document.cookie.split(';');
    for(let i=0; i<ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
    }
    return "";
}
export const environment = {
    production: false,
    test:true,
    API_URL: "/signority/api/",
    NEW_API_URL: "/neo/api/",
    logging: true,
    stoken: getCookie('sToken'),
    // stripeKey: 'pk_test_PsWEli8VyZPmVGGmwHXPVS89', // test key
    stripeKey: 'pk_live_38VxRdTi2HxJEi6QL6a7BUVI', // live key
    // API_URL: "https://test.signority.com/api/",
    // NEW_API_URL: "https://test.signority.com/neo/api/",
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
