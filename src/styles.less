
// Custom Theming for NG-ZORRO
// For more information: https://ng.ant.design/docs/customize-theme/en
@import "../node_modules/ng-zorro-antd/ng-zorro-antd.less";
@import "~@angular/material/prebuilt-themes/deeppurple-amber";


// Override less variables to here
// View all variables: https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/components/style/themes/default.less


// @primary-color: #017BC6;
/* You can add global styles to this file, and  also import other style files */



@tailwind base;
@tailwind components;
@tailwind utilities;


body{
  font-family: 'Lato', sans-serif;
}

a {
  color: #125b7e;
}

@media (max-width:640px) {
  .mobile-title {
      font-size: 50px !important;
      line-height: 50px !important;
      padding-bottom: 20px !important;
  }
}


.title {
  font-size: 70px;
  line-height: 70px;
  font-weight: 275;
  color: #2D323D;
}

.modal-table {
  width: 100%;
  border-collapse:separate; 
  border-spacing: 0 1em;
}

.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-label {
  display: flex;
  align-items: flex-end;
  flex-direction: row-reverse;
}
.material-select-height {
  height: 30px;
}

.mat-form-field-infix{
  border-top: 0px;
}
// material select
.mat-checked .mat-slide-toggle-bar {
  background-color: #6ac6fe !important;
}

.mat-checked .mat-slide-toggle-thumb {
  background-color: #017BC6 !important;
}
//material radio
.mat-radio-checked .mat-radio-inner-circle {
  background-color: #017BC6 !important;
}
.mat-radio-outer-circle {
  border-color: #017BC6 !important;
}
 .mat-radio-ripple .mat-ripple-element {
  background-color: rgba(1, 123, 198, 0.2) !important;
}
.mat-radio-button .mat-radio-outer-circle {
  transform: scale(0.8)
}

.mat-radio-button .mat-radio-ripple {
  top: -2px !important;
  left: -2px !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 100% !important;
}




/* Adjusted blue brand colours for sufficient 1.4.3 contrast */
.ant-table-row-expand-icon{
  color: black;
}

.ant-table-row-expand-icon:focus,
.ant-table-row-expand-icon:hover {
  color: black;
}

.ant-table-filter-dropdown button[nzType="link"] {
  visibility: hidden;
}

nz-table-inner-default {
  overflow: auto;
}

/* More specific for 400% zoom equivalent */
@media (max-width: 500px) {
  body:has(.team-settings-modal) .cdk-overlay-container .cdk-overlay-pane {
    left: 25px !important;
    transform: none !important;
  }
}

/* ensure within modal overrides */
.narrow-modal button.ant-btn:not(.ant-btn-primary) {
  border: 1px solid #797E8A;
}
// Darken unchecked Ant Design switch outline for ≥ 3 : 1 contrast
.ant-switch {
  border: 1px solid #797E8A;
}

/* WCAG 1.4.11 – Non-text Contrast: Provide visible keyboard focus indicator
   for mat-radio-button elements. Add focus highlighting for radio button groups
   to show which option is currently focused during keyboard navigation. */
.mat-radio-button:focus .mat-radio-label,
.mat-radio-button:focus-visible .mat-radio-label {
  background-color: #ffeb3b !important; // Bright yellow background for maximum visibility
  border: 3px solid #ff5722 !important; // Bright orange border
  border-radius: 6px !important;
  padding: 6px !important;
  margin: -6px !important;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.8) !important; // Glowing effect
}

.mat-radio-button:focus,
.mat-radio-button:focus-visible {
  outline: none !important; // Remove default outline to use custom one
}

/* Ensure the focus indicator is visible when using arrow keys to navigate */
.mat-radio-group:focus-within .mat-radio-button:focus .mat-radio-label,
.mat-radio-group:focus-within .mat-radio-button:focus-visible .mat-radio-label {
  background-color: #ffeb3b !important;
  border: 3px solid #ff5722 !important;
  border-radius: 6px !important;
  padding: 6px !important;
  margin: -6px !important;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.8) !important;
}

/* Alternative approach: Target the input directly */
.mat-radio-button input[type="radio"]:focus + .mat-radio-label,
.mat-radio-button input[type="radio"]:focus-visible + .mat-radio-label {
  background-color: #ffeb3b !important;
  border: 3px solid #ff5722 !important;
  border-radius: 6px !important;
  padding: 6px !important;
  margin: -6px !important;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.8) !important;
}

/* Debug: Add a permanent style to see if the selectors are working */
.mat-radio-button .mat-radio-label {
  transition: all 0.2s ease !important;
}
//material select
.mat-focused .mat-select-arrow {
  color: #017BC6 !important;
}
.mat-form-field-ripple {
  background-color: #017BC6 !important;
}
.mat-selected .mat-option-text {
  color: #017BC6;
}
//datepicker
.mat-calendar-body-selected {
  background-color: #017BC6 !important;
}
.mat-datepicker-toggle-active .mat-icon-button {
  color: #017BC6 !important;
}
.mat-calendar-body-cell-content:hover:not(.mat-calendar-body-disabled) {
  background-color: #e0f3ff !important;
}
.mat-calendar-body-today:not(.mat-calendar-body-selected) {
  background-color: #e0f3ff !important;
}
@media (hover: hover) {
  .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
      background-color:transparent
  }
}
.mat-focused .mat-form-field-required-marker {
  color: #f44336;
}
.mat-form-field.mat-focused .mat-form-field-label {
  color: #017BC6;
}
.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
  border-color: #017BC6;
  border-width: 2px;
}
.mat-calendar-body-today:not(.mat-calendar-body-selected){background-color: transparent!important} 
//checkbox
.mat-pseudo-checkbox-checked {
  background-color: #017BC6 !important;
}
.mat-menu-item.cdk-focused {
  background-color: white !important
}
.mat-menu-item.cdk-focused:hover {
  background-color: #f4f4f4 !important;
}
.mat-menu-item.cdk-keyboard-focused {
  background-color: #f4f4f4 !important;
}


.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
  color: #017BC6;
  border-color: #017BC6;
}
//ant design button colors 

    .ant-btn.ant-btn-primary {
    color: #fff;
    border-color: #055485;
    background: #055485;
    text-shadow: 0 -1px 0 rgba(0,0,0,.12);
    box-shadow: 0 2px #0000000b
  }

  .ant-btn.ant-btn-primary:hover,.ant-btn.ant-btn-primary:focus {
    color: #fff;
    border-color: #017BC6;
    background: #017BC6;
  }
  
  .ant-btn-dangerous.ant-btn-primary {
    color: #fff;
    border-color: #D40D00;
    background: #D40D00;
    text-shadow: 0 -1px 0 rgba(0,0,0,.12);
    box-shadow: 0 2px #0000000b
  }
  
  .ant-btn-dangerous.ant-btn-primary:hover,.ant-btn-dangerous.ant-btn-primary:focus {
    color: #fff;
    border-color: #E84545;
    background: #E84545;
  }
.ant-btn {
    line-height: 1.5715;
    position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: 0 2px #00000004;
    cursor: pointer;
    transition: all .3s cubic-bezier(.645,.045,.355,1);
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 2px;
    color: #2D323D;
    border-color: #C9CBCF;
    background: #fff;
}
 
.ant-btn:hover,.ant-btn:focus {
    color: #017BC6;
    border-color: #017BC6;
    background: #fff
}

.ant-pagination-item-active,
.ant-pagination-item-active a {
  color: #017BC6 !important;
  border-color: #017BC6 !important;
}

.ant-select-focused:not(.ant-select-disabled) .ant-select-selector, .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #017BC6 !important;
  box-shadow: 0 0 0 2px fade(#017BC6, 20%) !important; // optional, for focus ring
}

.ant-pagination-item:hover,
.ant-pagination-item-link:hover,
.ant-pagination .ng-star-inserted:hover,
.ant-pagination-item:hover,
.ant-pagination .ng-star-inserted:hover
 {
  border-color: #017BC6 !important;
  color: #017BC6 !important;
  transition: all 0.3s;
}

.ant-pagination-item:hover a {
  color: #017BC6 !important;
  transition: all 0.3s;
}
/* Inbox badge text colour override */
.badge {
  color: #fff;
}
/* Empty-state caption colour */
.ant-empty-description {
  color: #6b6b6b;
}

.mat-menu-item:focus-visible {
  border: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 against light backgrounds
  outline: none !important;
}

button.ant-btn:focus-visible,
.mat-select:focus-visible,
.ant-checkbox-wrapper:focus-visible,
.ant-form-item-control-input-content:focus-visible,
.ant-checkbox:focus-visible,
.ant-dropdown-trigger:focus-visible,
.ant-modal-close:focus-visible,
.mat-form-field-flex:focus-visible,
.mat-radio-container:focus-visible,
.ant-checkbox-input:focus-visible + .ant-checkbox-inner,
.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
  outline: #2D323D 3px solid;
  outline-offset: 2px;
  z-index: 2000 !important;
}

.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  outline: #2D323D 3px solid;
  outline-offset: -2px;
  z-index: 2000 !important;
}

.teamRow .ant-table-row-expand-icon {
  color: white;
  background: transparent;
}

.teamRow .ant-table-row-expand-icon:focus-visible,
.rootRow .ant-checkbox-input:focus-visible + .ant-checkbox-inner,
.teamRow .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
  color: white;
  font-weight: bold;
  outline: white 3px solid;
  outline-offset: 2px;
}

@media (min-width: 768px) {
  .ant-modal-content:not(.wide-modal .ant-modal-content, .wide-modal .ant-modal-wrap) {
    max-width: 520px !important;
    margin-left: auto;
    margin-right: auto;
  }

  .wide-modal {
    max-width: 960px !important;
  }
}

.ant-dropdown-menu-item-selected, .ant-dropdown-menu-submenu-title-selected {
  background-color: white !important;
}

.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {
  color: #2D323D !important;
}

.ant-table-thead .anticon {
  color: #017BC6 !important;
}

/* Table dropdown caret arrows styling */
.ant-table-thead th .anticon-caret-down,
.ant-table-thead th .anticon-caret-up {
  color: #2D323D !important;
}

.ant-table-thead th:hover .anticon-caret-down,
.ant-table-thead th:hover .anticon-caret-up {
  color: #017BC6 !important;
}

/* Remove focus styles on page load to prevent persistent focus after refresh */
*:focus:not(:focus-visible) {
  outline: none !important;
}

/* Table sorting arrows styling (caret-up and caret-down) */
.ant-table-column-sorter-up,
.ant-table-column-sorter-down {
  color: #2D323D !important;
}

.ant-table-thead th:hover .ant-table-column-sorter-up,
.ant-table-thead th:hover .ant-table-column-sorter-down {
  color: #017BC6 !important;
}

/* Active sorting arrow */
.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
  color: #017BC6 !important;
}

/* More specific targeting for active arrows */
.ant-table-column-sorter .ant-table-column-sorter-up.active,
.ant-table-column-sorter .ant-table-column-sorter-down.active {
  color: #017BC6 !important;
}

/* Even more specific for the inner sorter */
.ant-table-column-sorter-inner .ant-table-column-sorter-up.active,
.ant-table-column-sorter-inner .ant-table-column-sorter-down.active {
  color: #017BC6 !important;
}

.ant-table-filter-trigger.active {
  color: #017BC6 !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #017BC6;
  border-color: #017BC6;
}

.ant-pagination-item-ellipsis {
  color: #015e98 !important;
}

@media (max-width: 340px) {
  .retention-days {
    display: none;
  }
}

/* Global wide-modal styles - common properties for all wide modals */
.wide-modal {
  width: 90vw;
}

/* Restore full box outline around Angular Material selects */
.wide-modal .mat-form-field-appearance-legacy .mat-form-field-flex {
  border: 1px solid #797E8A;
  border-radius: 4px;
  padding: 0 0.5rem;
}

.wide-modal .mat-form-field-appearance-legacy .mat-form-field-underline {
  display: none;
}

/* Hide visual '*' required marker in wide-modal selects (label is sr-only) */
.wide-modal .mat-form-field-required-marker {
  display: none;
}

/* Inputs inside wide-modal */
.wide-modal input[nz-input],
.wide-modal textarea[nz-input] {
  border: 1px solid #797E8A;
}

/* Button border colour */
.wide-modal button.ant-btn:not(.ant-btn-primary) {
  border: 1px solid #797E8A;
}

/* Override Tailwind mt-10 for all modal content - higher specificity */
.wide-modal .mt-10,
.document-actions-modal .mt-10,
.branding-modal .mt-10,
.team-settings-modal .mt-10,
.ant-modal-content .mt-10,
.ant-modal-body .mt-10 {
  margin-top: 0 !important;
}

/* Force override for Tailwind mt-10 in modals - highest specificity */
.ant-modal .mt-10,
.ant-modal-content .mt-10,
.ant-modal-body .mt-10,
.wide-modal .mt-10,
.document-actions-modal .mt-10,
.branding-modal .mt-10,
.team-settings-modal .mt-10 {
  margin-top: 0 !important;
}

