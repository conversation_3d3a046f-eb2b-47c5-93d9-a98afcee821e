<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="7956387715738860999" datatype="html">
        <source>You are not logged in</source>
      </trans-unit>
      <trans-unit id="82744417109404626" datatype="html">
        <source>Login here</source>
      </trans-unit>
      <trans-unit id="4808589666930368915" datatype="html">
        <source>Draft</source>
      </trans-unit>
      <trans-unit id="1780950485960518731" datatype="html">
        <source>In Progress</source>
      </trans-unit>
      <trans-unit id="1161299114153631702" datatype="html">
        <source>Completed</source>
      </trans-unit>
      <trans-unit id="4188654437346976139" datatype="html">
        <source>Rejected</source>
      </trans-unit>
      <trans-unit id="464749780222721589" datatype="html">
        <source>Expired</source>
      </trans-unit>
      <trans-unit id="4043049091728011235" datatype="html">
        <source>Cancelled</source>
      </trans-unit>
      <trans-unit id="6233321270568573138" datatype="html">
        <source>Not Sent</source>
      </trans-unit>
      <trans-unit id="4471224693539884334" datatype="html">
        <source>Wait to Open</source>
      </trans-unit>
      <trans-unit id="7930265763736336562" datatype="html">
        <source>Reached Deadline</source>
      </trans-unit>
      <trans-unit id="8263947446165037076" datatype="html">
        <source>Working In Progress</source>
      </trans-unit>
      <trans-unit id="5239159362317338509" datatype="html">
        <source>Viewed</source>
      </trans-unit>
      <trans-unit id="3461261419427241196" datatype="html">
        <source>Finalized</source>
      </trans-unit>
      <trans-unit id="3613556404042286312" datatype="html">
        <source>You must move or delete all documents before deleting this folder</source>
      </trans-unit>
      <trans-unit id="7936928445097704718" datatype="html">
        <source>Regular User</source>
      </trans-unit>
      <trans-unit id="8419654635679877956" datatype="html">
        <source>Team Billing Admin</source>
      </trans-unit>
      <trans-unit id="2061766931861398488" datatype="html">
        <source>Team Admin</source>
      </trans-unit>
      <trans-unit id="1786686364877146278" datatype="html">
        <source>Super Admin</source>
      </trans-unit>
      <trans-unit id="1330199692750584158" datatype="html">
        <source>Doc Admin</source>
      </trans-unit>
      <trans-unit id="9223332816161929968" datatype="html">
        <source>An error has occured</source>
      </trans-unit>
      <trans-unit id="2550335661007823226" datatype="html">
        <source>Your validation token could not be sent to the specified email address</source>
      </trans-unit>
      <trans-unit id="year_shorthand" datatype="html">
        <source>y</source>
      </trans-unit>
      <trans-unit id="month_shorthand" datatype="html">
        <source>m</source>
      </trans-unit>
      <trans-unit id="week_shorthand" datatype="html">
        <source>w</source>
      </trans-unit>
      <trans-unit id="day_shorthand" datatype="html">
        <source>d</source>
      </trans-unit>
      <trans-unit id="hour_shorthand" datatype="html">
        <source>h</source>
      </trans-unit>
      <trans-unit id="minute_shorthand" datatype="html">
        <source>min</source>
      </trans-unit>
      <trans-unit id="6159557546518087785" datatype="html">
        <source> Export History </source>
      </trans-unit>
      <trans-unit id="5895211106512422075" datatype="html">
        <source> Bulk Export </source>
      </trans-unit>
      <trans-unit id="4318020328357731415" datatype="html">
        <source> Export Details </source>
      </trans-unit>
      <trans-unit id="6246540040798901621" datatype="html">
        <source>Create Export</source>
      </trans-unit>
      <trans-unit id="6012318692088405229" datatype="html">
        <source>View Exports</source>
      </trans-unit>
      <trans-unit id="4373148682011738839" datatype="html">
        <source>Exported by: </source>
      </trans-unit>
      <trans-unit id="4853764628207443728" datatype="html">
        <source>Export date: </source>
      </trans-unit>
      <trans-unit id="5028077468630516613" datatype="html">
        <source>Export period: </source>
      </trans-unit>
      <trans-unit id="3827822146858235480" datatype="html">
        <source>Document status: </source>
      </trans-unit>
      <trans-unit id="4154657255316028879" datatype="html">
        <source>Documents were exported to: </source>
      </trans-unit>
      <trans-unit id="3099741642167775297" datatype="html">
        <source>Download</source>
      </trans-unit>
      <trans-unit id="7495896529184484202" datatype="html">
        <source> Exports may take a few minutes to process. You will receive an email when the transfer is complete. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> NOTE: If the files selected for the export are greater than 1GB, the documents will be automatically seperated into seperate files. </source>
      </trans-unit>
      <trans-unit id="811612051377726703" datatype="html">
        <source>Document Period</source>
      </trans-unit>
      <trans-unit id="4730065082875720831" datatype="html">
        <source>Document Status</source>
      </trans-unit>
      <trans-unit id="8840469969068998430" datatype="html">
        <source>Export Date</source>
      </trans-unit>
      <trans-unit id="4213763744279134268" datatype="html">
        <source>Expire Date</source>
      </trans-unit>
      <trans-unit id="955411934612069853" datatype="html">
        <source>Export Status</source>
      </trans-unit>
      <trans-unit id="8043651428785671800" datatype="html">
        <source>Exported To</source>
      </trans-unit>
      <trans-unit id="5290201753887978505" datatype="html">
        <source>Document Name</source>
      </trans-unit>
      <trans-unit id="3715596725146409911" datatype="html">
        <source>Owner</source>
      </trans-unit>
      <trans-unit id="8820965746779312111" datatype="html">
        <source>Last Modified</source>
      </trans-unit>
      <trans-unit id="5611592591303869712" datatype="html">
        <source>Status</source>
      </trans-unit>
      <trans-unit id="1956960597221260757" datatype="html">
        <source>Document Size</source>
      </trans-unit>
      <trans-unit id="3396656329772283218" datatype="html">
        <source>Document status is required</source>
      </trans-unit>
      <trans-unit id="7554292872235426166" datatype="html">
        <source>Export documents from</source>
      </trans-unit>
      <trans-unit id="962483563535270150" datatype="html">
        <source>Start Date:</source>
      </trans-unit>
      <trans-unit id="2274111377733953132" datatype="html">
        <source>End Date:</source>
      </trans-unit>
      <trans-unit id="4851576400081652156" datatype="html">
        <source>The start date must be before the end date.</source>
      </trans-unit>
      <trans-unit id="3870953906744430573" datatype="html">
        <source>Dates are required</source>
      </trans-unit>
      <trans-unit id="175598048240893192" datatype="html">
        <source>Export documents to</source>
      </trans-unit>
      <trans-unit id="5159192167987054220" datatype="html">
        <source>Direct Download</source>
      </trans-unit>
      <trans-unit id="7165752655420971992" datatype="html">
        <source>Integration</source>
      </trans-unit>
      <trans-unit id="6608351232216795548" datatype="html">
        <source> A zipped file of your documents will be generated and an email will be sent with download link. </source>
      </trans-unit>
      <trans-unit id="152072063473538621" datatype="html">
        <source> Your bulk download will be pushed to your storage integration. You currently do not have one set up. <x id="START_LINK" ctype="x-a" equiv-text="&lt;a href=&quot;/UI/userSettings.html&quot;&gt;"/>Configure.<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="6925118866938435296" datatype="html">
        <source> Your bulk download will be pushed to your <x id="INTERPOLATION" equiv-text="{{getIntegrationText(userInfo?.accountdetails?.branding?.SyncType)}}"/> when complete. </source>
      </trans-unit>
      <trans-unit id="4582157133994782454" datatype="html">
        <source>File Type</source>
      </trans-unit>
      <trans-unit id="7462301153729425254" datatype="html">
        <source>Export</source>
      </trans-unit>
      <trans-unit id="2159130950882492111" datatype="html">
        <source>Cancel</source>
      </trans-unit>
      <trans-unit id="9178182467454450952" datatype="html">
        <source>Confirm</source>
      </trans-unit>
      <trans-unit id="4463175345930484836" datatype="html">
        <source>Are you sure you want to cancel this export task?</source>
      </trans-unit>
      <trans-unit id="1616102757855967475" datatype="html">
        <source>All</source>
      </trans-unit>
      <trans-unit id="383181061702648553" datatype="html">
        <source>Queued</source>
      </trans-unit>
      <trans-unit id="6423210280939340786" datatype="html">
        <source>Ready</source>
      </trans-unit>
      <trans-unit id="4640918287345181447" datatype="html">
        <source> Contacts </source>
      </trans-unit>
      <trans-unit id="2168510081275502293" datatype="html">
        <source>Add New</source>
      </trans-unit>
      <trans-unit id="365858379724947144" datatype="html">
        <source>Import from CSV</source>
      </trans-unit>
      <trans-unit id="7585826646011739428" datatype="html">
        <source>Edit</source>
      </trans-unit>
      <trans-unit id="7022070615528435141" datatype="html">
        <source>Delete</source>
      </trans-unit>
      <trans-unit id="6083640901515030001" datatype="html">
        <source>Name or Email</source>
      </trans-unit>
      <trans-unit id="6028371114637047813" datatype="html">
        <source>First Name</source>
      </trans-unit>
      <trans-unit id="4407559560004943843" datatype="html">
        <source>Last Name</source>
      </trans-unit>
      <trans-unit id="4768749765465246664" datatype="html">
        <source>Email</source>
      </trans-unit>
      <trans-unit id="8853673743662309508" datatype="html">
        <source>Country Code</source>
      </trans-unit>
      <trans-unit id="8194554728555410336" datatype="html">
        <source>Phone Number</source>
      </trans-unit>
      <trans-unit id="8396173896991769395" datatype="html">
        <source>Contacts Shared By Team Members</source>
      </trans-unit>
      <trans-unit id="1302597016907790111" datatype="html">
        <source>Create New Contact</source>
      </trans-unit>
      <trans-unit id="6407628057642629970" datatype="html">
        <source>First name is required</source>
      </trans-unit>
      <trans-unit id="8552587679160911578" datatype="html">
        <source>Email is required</source>
      </trans-unit>
      <trans-unit id="8009244340893545691" datatype="html">
        <source>Invalid Email Format</source>
      </trans-unit>
      <trans-unit id="2434052644140619814" datatype="html">
        <source>Phone number should have more than 2 and less than 17 digits</source>
      </trans-unit>
      <trans-unit id="8895198132333337043" datatype="html">
        <source>Edit Contact</source>
      </trans-unit>
      <trans-unit id="4645563458528840598" datatype="html">
        <source>Delete the selected contact(s) from the system?</source>
      </trans-unit>
      <trans-unit id="6763430149182628950" datatype="html">
        <source>Invalid CSV format</source>
      </trans-unit>
      <trans-unit id="1140599166278569579" datatype="html">
        <source>Invalid email</source>
      </trans-unit>
      <trans-unit id="7515883357904500238" datatype="html">
        <source>Are you sure?</source>
      </trans-unit>
      <trans-unit id="3122990514543251979" datatype="html">
        <source>You are about to import your contact list, existing emails will be ignored.</source>
      </trans-unit>
      <trans-unit id="5703920404403283011" datatype="html">
        <source>Estimated upcoming bill</source>
      </trans-unit>
      <trans-unit id="5063342301253907359" datatype="html">
        <source>$ <x id="INTERPOLATION" equiv-text="{{amount | currency:&apos;&apos;:&apos;&apos;}}"/></source>
      </trans-unit>
      <trans-unit id="5815412745800320040" datatype="html">
        <source>Payment Method</source>
      </trans-unit>
      <trans-unit id="2363158409383485440" datatype="html">
        <source>Invoices</source>
      </trans-unit>
      <trans-unit id="7908948960253560360" datatype="html">
        <source>Email notification logo</source>
      </trans-unit>
      <trans-unit id="4381944803872489731" datatype="html">
        <source>Canceled</source>
      </trans-unit>
      <trans-unit id="2318903923249324396" datatype="html">
        <source>Average time to sign in last 60 days</source>
      </trans-unit>
      <trans-unit id="6216566298410042527" datatype="html">
        <source>Knowledge Base</source>
      </trans-unit>
      <trans-unit id="6188321460279257820" datatype="html">
        <source>Stamp</source>
      </trans-unit>
      <trans-unit id="1830751631154138684" datatype="html">
        <source>Log a Support Ticket</source>
      </trans-unit>
      <trans-unit id="1336542143746897548" datatype="html">
        <source>Email Support</source>
      </trans-unit>
      <trans-unit id="8106197229004459454" datatype="html">
        <source>Your Role: </source>
      </trans-unit>
      <trans-unit id="1909437068682425542" datatype="html">
        <source>Estimated upcoming bill:</source>
      </trans-unit>
      <trans-unit id="7398839504124742099" datatype="html">
        <source>Request Additional Packages</source>
      </trans-unit>
      <trans-unit id="6724285536131649348" datatype="html">
        <source>Manage Plan</source>
      </trans-unit>
      <trans-unit id="2849750161085431718" datatype="html">
        <source>Upgrade</source>
      </trans-unit>
      <trans-unit id="181242789613963411" datatype="html">
        <source>Days left</source>
      </trans-unit>
      <trans-unit id="5870111697948961221" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was sent to <x id="PH_1" equiv-text="activity.recipientName"/></source>
      </trans-unit>
      <trans-unit id="5651519298668050608" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was sent to <x id="PH_1" equiv-text="activity.recipientName"/> and <x id="PH_2" equiv-text="activity.numberOfRecipientsHaveSameInvitationSequence-1"/> others</source>
      </trans-unit>
      <trans-unit id="4829097156267552632" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was automatically sent to <x id="PH_1" equiv-text="activity.recipientName"/></source>
      </trans-unit>
      <trans-unit id="3355928948932058269" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was sent</source>
      </trans-unit>
      <trans-unit id="6762062767708525467" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was opened by <x id="PH_1" equiv-text="activity.recipientName"/></source>
      </trans-unit>
      <trans-unit id="8825605150417493376" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was rejected by <x id="PH_1" equiv-text="activity.recipientName"/></source>
      </trans-unit>
      <trans-unit id="6871155519291397775" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was rejected. <x id="PH_1" equiv-text="activity.recipientName"/> included a reason: <x id="PH_2" equiv-text="activity.rejectionReason"/></source>
      </trans-unit>
      <trans-unit id="2153369716444790576" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was signed by <x id="PH_1" equiv-text="activity.recipientName"/> and is now complete</source>
      </trans-unit>
      <trans-unit id="8942275136096918017" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was signed by <x id="PH_1" equiv-text="activity.recipientName"/></source>
      </trans-unit>
      <trans-unit id="9186369073664096558" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was received for signing</source>
      </trans-unit>
      <trans-unit id="1717525525184528478" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was received for reviewing</source>
      </trans-unit>
      <trans-unit id="3751248364755339013" datatype="html">
        <source><x id="PH" equiv-text="activity.docTitle"/> was received for editing</source>
      </trans-unit>
      <trans-unit id="5146260369386738791" datatype="html">
        <source>View All Templates</source>
      </trans-unit>
      <trans-unit id="9008968878373693685" datatype="html">
        <source>Teams</source>
      </trans-unit>
      <trans-unit id="3897348120591552265" datatype="html">
        <source>Manage</source>
      </trans-unit>
      <trans-unit id="8204176479746810612" datatype="html">
        <source>Active</source>
      </trans-unit>
      <trans-unit id="4416413576346763682" datatype="html">
        <source>Pending</source>
      </trans-unit>
      <trans-unit id="8924033172984293587" datatype="html">
        <source>Service Period</source>
      </trans-unit>
      <trans-unit id="4555457172864212828" datatype="html">
        <source>Users</source>
      </trans-unit>
      <trans-unit id="5875130684997376720" datatype="html">
        <source>Billing</source>
      </trans-unit>
      <trans-unit id="6283742617642842956" datatype="html">
        <source>Branding</source>
      </trans-unit>
      <trans-unit id="5433675495457939071" datatype="html">
        <source>Templates</source>
      </trans-unit>
      <trans-unit id="2513871208438660469" datatype="html">
        <source>Support Channels</source>
      </trans-unit>
      <trans-unit id="8411209816955952128" datatype="html">
        <source>Document Report</source>
      </trans-unit>
      <trans-unit id="4210825064812829329" datatype="html">
        <source>Add shortcut</source>
      </trans-unit>
      <trans-unit id="7247964236720108342" datatype="html">
        <source>Recent Activities</source>
      </trans-unit>
      <trans-unit id="6931016926544540722" datatype="html">
        <source>Plan &amp; Usage</source>
      </trans-unit>
      <trans-unit id="984472239910175162" datatype="html">
        <source>All Documents</source>
      </trans-unit>
      <trans-unit id="4887486256247257929" datatype="html">
        <source>Shared Documents</source>
      </trans-unit>
      <trans-unit id="3418677553313974490" datatype="html">
        <source>Trash</source>
      </trans-unit>
      <trans-unit id="7685933846431786388" datatype="html">
        <source>Purge</source>
      </trans-unit>
      <trans-unit id="3123284102412669965" datatype="html">
        <source>Templates</source>
      </trans-unit>
      <trans-unit id="4193797647899727870" datatype="html">
        <source>Template Link</source>
      </trans-unit>
      <trans-unit id="633462462348222802" datatype="html">
        <source>Bulk Sign</source>
      </trans-unit>
      <trans-unit id="8342148727250324816" datatype="html">
        <source>Shared Templates</source>
      </trans-unit>
      <trans-unit id="6380216535341864874" datatype="html">
        <source>+ New Folder</source>
      </trans-unit>
      <trans-unit id="5008239917614360688" datatype="html">
        <source>Home Folder</source>
      </trans-unit>
      <trans-unit id="7268001259390508730" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;plus&quot; nzTheme=&quot;outline&quot; class=&quot;mr-1&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/>New Document </source>
      </trans-unit>
      <trans-unit id="3760091090255269022" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;plus&quot; nzTheme=&quot;outline&quot; class=&quot;mr-1&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/>Regular Template </source>
      </trans-unit>
      <trans-unit id="8615911173604543607" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;edit&quot; nzTheme=&quot;outline&quot; class=&quot;mr-1&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/>Rename Folder </source>
      </trans-unit>
      <trans-unit id="7967522028234224827" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;delete&quot; nzTheme=&quot;outline&quot; class=&quot;mr-1&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/>Delete Folder </source>
      </trans-unit>
      <trans-unit id="6272889378364553386" datatype="html">
        <source>New Folder</source>
      </trans-unit>
      <trans-unit id="7591154471114443990" datatype="html">
        <source>Enter a new folder name</source>
      </trans-unit>
      <trans-unit id="1945756652209874063" datatype="html">
        <source>The folder name is required</source>
      </trans-unit>
      <trans-unit id="8364953642290206900" datatype="html">
        <source>New Folder Name</source>
      </trans-unit>
      <trans-unit id="8975067309076582922" datatype="html">
        <source>Do you want to delete the selected folder? </source>
      </trans-unit>
      <trans-unit id="4563033843403652778" datatype="html">
        <source>Cancel Signing of this Document</source>
      </trans-unit>
      <trans-unit id="5968102867626399607" datatype="html">
        <source>Enter the reason for cancelling the document below. This will appear in the document history and audit trail.</source>
      </trans-unit>
      <trans-unit id="917077902940143908" datatype="html">
        <source>Reason for Cancelling</source>
      </trans-unit>
      <trans-unit id="4250833314736195520" datatype="html">
        <source>Cancellation Reason can not be left blank </source>
      </trans-unit>
      <trans-unit id="1876807815580986807" datatype="html">
        <source>Cancellation Type</source>
      </trans-unit>
      <trans-unit id="6119174964216962037" datatype="html">
        <source>Cancel the document and send an email notifying the signers</source>
      </trans-unit>
      <trans-unit id="7677117388562453185" datatype="html">
        <source>Cancel the document and do not send an email to the signers</source>
      </trans-unit>
      <trans-unit id="8804773407113557656" datatype="html">
        <source>Cancel the document and create a copy to resend with added content</source>
      </trans-unit>
      <trans-unit id="1563503089756318808" datatype="html">
        <source>Cancel the document and create a copy to resend without added content</source>
      </trans-unit>
      <trans-unit id="6768105685660462788" datatype="html">
        <source>Edit Title</source>
      </trans-unit>
      <trans-unit id="7433788871219754984" datatype="html">
        <source>Current Title</source>
      </trans-unit>
      <trans-unit id="3984554833741117821" datatype="html">
        <source>Enter a new title</source>
      </trans-unit>
      <trans-unit id="6056947233450594241" datatype="html">
        <source>The title cannot be left blank</source>
      </trans-unit>
      <trans-unit id="1197969065680365382" datatype="html">
        <source>You have chosen to download</source>
      </trans-unit>
      <trans-unit id="2271017954905322561" datatype="html">
        <source>Download (Single combined PDF)</source>
      </trans-unit>
      <trans-unit id="5011014127685026182" datatype="html">
        <source>Document (Seperate PDFs)</source>
      </trans-unit>
      <trans-unit id="461168043949975461" datatype="html">
        <source>Audit Trail</source>
      </trans-unit>
      <trans-unit id="4338564880669542233" datatype="html">
        <source>With audit Trail </source>
      </trans-unit>
      <trans-unit id="1475048252851266096" datatype="html">
        <source>Do you want to delete the selected document(s)? </source>
      </trans-unit>
      <trans-unit id="3232449681898458636" datatype="html">
        <source>Need to add more signatures? Signority can create a copy of this completed document, with all signatures embedded, for you to get signed again. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> Create a copy of this completed document for signing? </source>
      </trans-unit>
      <trans-unit id="734123165984004149" datatype="html">
        <source>Move Document</source>
      </trans-unit>
      <trans-unit id="6350715579652671881" datatype="html">
        <source>Please select a destination folder</source>
      </trans-unit>
      <trans-unit id="7772245508074687330" datatype="html">
        <source>Copy Document</source>
      </trans-unit>
      <trans-unit id="5797112218342870847" datatype="html">
        <source>How would you like to copy</source>
      </trans-unit>
      <trans-unit id="8582985114801032503" datatype="html">
        <source>Copy only the document files</source>
      </trans-unit>
      <trans-unit id="7034911067579592212" datatype="html">
        <source>Copy document files, tags, and workflow</source>
      </trans-unit>
      <trans-unit id="2499378831990273593" datatype="html">
        <source>Copy document files, tags, workflow and recipients</source>
      </trans-unit>
      <trans-unit id="3758105618006555115" datatype="html">
        <source> Do you want to share the selected Document with your team? </source>
      </trans-unit>
      <trans-unit id="2927011859785697200" datatype="html">
        <source> Do you want to unshare the selected Document with your team? </source>
      </trans-unit>
      <trans-unit id="5694132600373578888" datatype="html">
        <source> Do you want to share/unshare the selected Documents or Templates with your team? </source>
      </trans-unit>
      <trans-unit id="7419704019640008953" datatype="html">
        <source>Share</source>
      </trans-unit>
      <trans-unit id="4324682959769591036" datatype="html">
        <source>Unshare</source>
      </trans-unit>
      <trans-unit id="4578649722169591513" datatype="html">
        <source>Push to Another User</source>
      </trans-unit>
      <trans-unit id="5243368328896890619" datatype="html">
        <source>Target User&apos;s Email:</source>
      </trans-unit>
      <trans-unit id="4020488648719270022" datatype="html">
        <source>Please enter a valid email address </source>
      </trans-unit>
      <trans-unit id="9071901213339878522" datatype="html">
        <source> These documents will not exist in your folder any more. Are you sure you want to push the selected document(s) to the target user? </source>
      </trans-unit>
      <trans-unit id="6769593502464171178" datatype="html">
        <source>Are you sure you want to permanently delete the document(s)? Your documents cannot be recovered. </source>
      </trans-unit>
      <trans-unit id="2539191919061544581" datatype="html">
        <source>Reason: </source>
      </trans-unit>
      <trans-unit id="7697416054690538160" datatype="html">
        <source>Initial Link Signer</source>
      </trans-unit>
      <trans-unit id="718664953917016933" datatype="html">
        <source>
          <x id="INTERPOLATION" equiv-text="{{pageName}}"/>
        </source>
      </trans-unit>
      <trans-unit id="1240074268233806540" datatype="html">
        <source> Note: All documents in the Purge Queue will be automatically deleted permanently after 14 days. </source>
      </trans-unit>
      <trans-unit id="1354304998362056201" datatype="html">
        <source>
          <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i [class]=&quot;item.iconClass&quot;&gt;"/>
          <x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/>
          <x id="INTERPOLATION" equiv-text="{{item.text}}"/>
        </source>
      </trans-unit>
      <trans-unit id="1394437135097740729" datatype="html">
        <source>Title, Recipient, Email </source>
      </trans-unit>
      <trans-unit id="6362801977058095227" datatype="html">
        <source>Document title</source>
      </trans-unit>
      <trans-unit id="2892151212152447819" datatype="html">
        <source> Days left </source>
      </trans-unit>
      <trans-unit id="9193001935256028830" datatype="html">
        <source> Owner </source>
      </trans-unit>
      <trans-unit id="7817501228881741585" datatype="html">
        <source>Recipients</source>
      </trans-unit>
      <trans-unit id="187187500641108332" datatype="html">
        <source>
          <x id="INTERPOLATION" equiv-text="{{displayDocStatusHeader}}"/>
        </source>
      </trans-unit>
      <trans-unit id="3956848379154949348" datatype="html">
        <source>Secure document signed with Digital Signatures</source>
      </trans-unit>
      <trans-unit id="7708270344948043036" datatype="html">
        <source>
          <x id="INTERPOLATION" equiv-text="{{getPurgeDaysLeft(data)}}"/>
        </source>
      </trans-unit>
      <trans-unit id="7561346792324980690" datatype="html">
        <source><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span *ngIf=&quot;userProduct.IsThisATeamProduct&quot;&gt;"/><x id="INTERPOLATION" equiv-text="{{data.user.groupname}}"/> <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;italic&quot; &gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/>(<x id="INTERPOLATION_1" equiv-text="{{data.user.name}}"/>)<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_2" ctype="x-span_2" equiv-text="&lt;span *ngIf=&quot;!userProduct.IsThisATeamProduct&quot;&gt;"/><x id="INTERPOLATION_1" equiv-text="{{data.user.name}}"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="6528180404277206905" datatype="html">
        <source>No recipients</source>
      </trans-unit>
      <trans-unit id="3558321426841805811" datatype="html">
        <source>+<x id="INTERPOLATION" equiv-text="{{data.signerCount-1}}"/> recipient(s)</source>
      </trans-unit>
      <trans-unit id="3639141541660101998" datatype="html">
        <source>PUBLISHED</source>
      </trans-unit>
      <trans-unit id="7615937167799407588" datatype="html">
        <source>UNPUBLISHED</source>
      </trans-unit>
      <trans-unit id="918893257868417224" datatype="html">
        <source>USE TEMPLATE</source>
      </trans-unit>
      <trans-unit id="8218989429367543544" datatype="html">
        <source> Sign Document </source>
      </trans-unit>
      <trans-unit id="5349122987947420025" datatype="html">
        <source><x id="START_LINK" ctype="x-a" equiv-text="&lt;a tabindex=&quot;0&quot; (click)=&quot;setCurrentAction(&apos;viewDocument&apos;); currentDocuments = [data]&quot;&gt;"/>View Document<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="2357940352368659103" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{((pageIndex-1) * pageSize)+1}}"/> - <x id="INTERPOLATION_1" equiv-text="{{math.min((pageIndex-1) * pageSize + pageSize, total )}}"/> of <x id="INTERPOLATION_2" equiv-text="{{ total }}"/></source>
      </trans-unit>
      <trans-unit id="2103599052963519314" datatype="html">
        <source>Template Link Url</source>
      </trans-unit>
      <trans-unit id="2884868433691856240" datatype="html">
        <source>Template Name</source>
      </trans-unit>
      <trans-unit id="7947989024662584768" datatype="html">
        <source>Publishing Link</source>
      </trans-unit>
      <trans-unit id="6612139025379656701" datatype="html">
        <source>Publish/Unpublish Status</source>
      </trans-unit>
      <trans-unit id="6414713280785837164" datatype="html">
        <source>Current Status</source>
      </trans-unit>
      <trans-unit id="4058575476871566236" datatype="html">
        <source>Published</source>
      </trans-unit>
      <trans-unit id="3442748633775807547" datatype="html">
        <source>Create New Document</source>
      </trans-unit>
      <trans-unit id="8145280333733813708" datatype="html">
        <source>Use multiple templates</source>
      </trans-unit>
      <trans-unit id="8886284581841644863" datatype="html">
        <source>Templates to use:</source>
      </trans-unit>
      <trans-unit id="8627608682053516979" datatype="html">
        <source>Add a template to the list by clicking the <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;use-template-small&quot;&gt;"/>Use Template<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/> button</source>
      </trans-unit>
      <trans-unit id="8720977247725652816" datatype="html">
        <source>Ok</source>
      </trans-unit>
      <trans-unit id="179147468957418775" datatype="html">
        <source>Last Modified Time</source>
      </trans-unit>
      <trans-unit id="8631167936724837648" datatype="html">
        <source>Doc Status</source>
      </trans-unit>
      <trans-unit id="724295568060822188" datatype="html">
        <source>Recipient Status</source>
      </trans-unit>
      <trans-unit id="9097745026507306045" datatype="html">
        <source>Created Time</source>
      </trans-unit>
      <trans-unit id="6093210751404423786" datatype="html">
        <source>Ascending</source>
      </trans-unit>
      <trans-unit id="2382330284069784226" datatype="html">
        <source>Descending</source>
      </trans-unit>
      <trans-unit id="4551512393206290454" datatype="html">
        <source>Expiring Within a Week</source>
      </trans-unit>
      <trans-unit id="5475575483815438276" datatype="html">
        <source>Shared by: </source>
      </trans-unit>
      <trans-unit id="3222927691209073693" datatype="html">
        <source>Retention This document will be purged on: <x id="PH" equiv-text="this.formatDate(expireDate)"/> in <x id="PH_1" equiv-text="daysLeft"/> day(s)</source>
      </trans-unit>
      <trans-unit id="8137352858789890419" datatype="html">
        <source>Retention This document will be offloaded and purged on: <x id="PH" equiv-text="this.formatDate(expireDate)"/> in <x id="PH_1" equiv-text="daysLeft"/> day(s)</source>
      </trans-unit>
      <trans-unit id="7593555694782789615" datatype="html">
        <source>Open</source>
      </trans-unit>
      <trans-unit id="11754468154758206" datatype="html">
        <source>Rename Title</source>
      </trans-unit>
      <trans-unit id="5087973563104727451" datatype="html">
        <source>Legacy Download</source>
      </trans-unit>
      <trans-unit id="6608741999529281144" datatype="html">
        <source>Status and History</source>
      </trans-unit>
      <trans-unit id="8761490097458215682" datatype="html">
        <source>Move to Folder</source>
      </trans-unit>
      <trans-unit id="49259971247542645" datatype="html">
        <source>Copy as a new Document</source>
      </trans-unit>
      <trans-unit id="7003049976245974161" datatype="html">
        <source>Copy as a new Template Link</source>
      </trans-unit>
      <trans-unit id="6261822500835220700" datatype="html">
        <source>Copy as a new Regular Template</source>
      </trans-unit>
      <trans-unit id="8994157299126272196" datatype="html">
        <source>Unshare with my Team</source>
      </trans-unit>
      <trans-unit id="6306356241858643578" datatype="html">
        <source>Share with my Team</source>
      </trans-unit>
      <trans-unit id="3091026295011090440" datatype="html">
        <source>Push to an account</source>
      </trans-unit>
      <trans-unit id="8314493651838641190" datatype="html">
        <source>Sign Document</source>
      </trans-unit>
      <trans-unit id="5858191852151203699" datatype="html">
        <source>View Document</source>
      </trans-unit>
      <trans-unit id="2342177690795099047" datatype="html">
        <source>Share/Unshare with my Team</source>
      </trans-unit>
      <trans-unit id="7801943366990338407" datatype="html">
        <source>Copy as a new Bulksign</source>
      </trans-unit>
      <trans-unit id="6537928055334486435" datatype="html">
        <source>Copy as new Document</source>
      </trans-unit>
      <trans-unit id="6770769801335635194" datatype="html">
        <source>Restore</source>
      </trans-unit>
      <trans-unit id="*****************" datatype="html">
        <source>Delete Permanently</source>
      </trans-unit>
      <trans-unit id="522100040144967465" datatype="html">
        <source>Request Additional Signature</source>
      </trans-unit>
      <trans-unit id="8191138653242657133" datatype="html">
        <source>Cancel Signing</source>
      </trans-unit>
      <trans-unit id="5914105891692432627" datatype="html">
        <source>Report/Export</source>
      </trans-unit>
      <trans-unit id="5133987493894645096" datatype="html">
        <source>Copy as new Regular Template</source>
      </trans-unit>
      <trans-unit id="2239690503829511651" datatype="html">
        <source>Copy as new Template Link</source>
      </trans-unit>
      <trans-unit id="373104720888351737" datatype="html">
        <source>Copy as new Bulksign</source>
      </trans-unit>
      <trans-unit id="8879451357997164519" datatype="html">
        <source>Copy as new Template</source>
      </trans-unit>
      <trans-unit id="4247238958848519613" datatype="html">
        <source> Inbox </source>
      </trans-unit>
      <trans-unit id="3219770302156755205" datatype="html">
        <source>User Dashboard</source>
      </trans-unit>
      <trans-unit id="2618592958201094588" datatype="html">
        <source>Admin Console</source>
      </trans-unit>
      <trans-unit id="8742547199983394466" datatype="html">
        <source>Users &amp; Teams</source>
      </trans-unit>
      <trans-unit id="3230225499881624378" datatype="html">
        <source>Bulk Export</source>
      </trans-unit>
      <trans-unit id="7289559766031125120" datatype="html">
        <source> + New </source>
      </trans-unit>
      <trans-unit id="975449276385153932" datatype="html">
        <source>New Document</source>
      </trans-unit>
      <trans-unit id="4314061841141617340" datatype="html">
        <source>New Template</source>
      </trans-unit>
      <trans-unit id="1973561012745363503" datatype="html">
        <source>Profile Settings</source>
      </trans-unit>
      <trans-unit id="3797778920049399855" datatype="html">
        <source>Logout</source>
      </trans-unit>
      <trans-unit id="2387193387555669899" datatype="html">
        <source>Dashboard </source>
      </trans-unit>
      <trans-unit id="4733307402565258070" datatype="html">
        <source>Documents</source>
      </trans-unit>
      <trans-unit id="5945986349492910662" datatype="html">
        <source>Contacts</source>
      </trans-unit>
      <trans-unit id="799485662157076328" datatype="html">
        <source>Inbox</source>
      </trans-unit>
      <trans-unit id="1868606282505332204" datatype="html">
        <source>Reports</source>
      </trans-unit>
      <trans-unit id="3500952741173391574" datatype="html">
        <source>Security Report</source>
      </trans-unit>
      <trans-unit id="7657649505674481715" datatype="html">
        <source>Usage Report</source>
      </trans-unit>
      <trans-unit id="4243438157952688188" datatype="html">
        <source>Cancellation Report</source>
      </trans-unit>
      <trans-unit id="5605123772636494177" datatype="html">
        <source>Plan</source>
      </trans-unit>
      <trans-unit id="5722353260643724736" datatype="html">
        <source>Payment method</source>
      </trans-unit>
      <trans-unit id="5731972491580424912" datatype="html">
        <source>Invoice</source>
      </trans-unit>
      <trans-unit id="4944372417149760037" datatype="html">
        <source>Integrations</source>
      </trans-unit>
      <trans-unit id="4930506384627295710" datatype="html">
        <source>Settings</source>
      </trans-unit>
      <trans-unit id="4468741256615440810" datatype="html">
        <source>Global Settings</source>
      </trans-unit>
      <trans-unit id="1851945681442511250" datatype="html">
        <source>Stamps</source>
      </trans-unit>
      <trans-unit id="6658000829978978023" datatype="html">
        <source>Applications</source>
      </trans-unit>
      <trans-unit id="5041354590769758251" datatype="html">
        <source>Admin</source>
      </trans-unit>
      <trans-unit id="8175342407510654197" datatype="html">
        <source>Admin Dashboard</source>
      </trans-unit>
      <trans-unit id="4915431133669985304" datatype="html">
        <source>Profile</source>
      </trans-unit>
      <trans-unit id="7911416166208830577" datatype="html">
        <source>Help</source>
      </trans-unit>
      <trans-unit id="2762454768270664510" datatype="html">
        <source>Release Info</source>
      </trans-unit>
      <trans-unit id="2759332364257099569" datatype="html">
        <source> Your email has not yet been confirmed, please check your email. <x id="START_LINK" ctype="x-a" equiv-text="&lt;a (click)=&quot;resendVerificationEmail()&quot;&gt;"/>Resend verification email<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="8354634182257412206" datatype="html">
        <source>New Release Notification!</source>
      </trans-unit>
      <trans-unit id="4999762519364687027" datatype="html">
        <source>Regular Template</source>
      </trans-unit>
      <trans-unit id="2647623044562731300" datatype="html">
        <source>Create a template to re-use it on new documents. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="Best for"/>Best for saving time on frequently-used documents.<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="5309419484327763910" datatype="html">
        <source><x id="START_LINK" ctype="x-a" equiv-text="/changePlan2.html?lang=fr&quot;&gt;"/>Upgrade<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="715456272530415469" datatype="html">
        <source>Speak to your billing admin about this premium feature.</source>
      </trans-unit>
      <trans-unit id="2604898253238667225" datatype="html">
        <source>Publish a document as a Template Link to make it available to anyone. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="Best for"/>Best for accepting applications online.<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="6633254828611955839" datatype="html">
        <source>Easily send a document to hundreds of people at once. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="Best for"/>Best for mass signings of individual documents.<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="7090794962567588414" datatype="html">
        <source>Your current plan supports up to <x id="PH" equiv-text="this.accountDetials!.storagelimit"/> documents. Please delete older documents you no longer need and try again.</source>
      </trans-unit>
      <trans-unit id="3871471133257074704" datatype="html">
        <source>Your current plan supports up to <x id="PH" equiv-text="this.accountDetials!.storagelimit"/> documents. Please delete older documents you no longer need and try again.</source>
      </trans-unit>
      <trans-unit id="3738015294448505364" datatype="html">
        <source>Please upgrade your plan to use this feature.</source>
      </trans-unit>
      <trans-unit id="1595978956655659983" datatype="html">
        <source> Cancellation Report </source>
      </trans-unit>
      <trans-unit id="7083489924857739182" datatype="html">
        <source> Report Settings </source>
      </trans-unit>
      <trans-unit id="5193539160604294602" datatype="html">
        <source>Generate</source>
      </trans-unit>
      <trans-unit id="4847981306961317598" datatype="html">
        <source> To create a report, set your filters above and click &quot;Generate&quot;.<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> Your report will be processed and made available below when ready. </source>
      </trans-unit>
      <trans-unit id="3943454209941761842" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span style=&quot;font-size: 16px&quot; class=&quot;font-bold&quot;&gt;"/>Saved Reports<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span *ngIf=&quot;unreadReportsCount &gt;0&quot; class=&quot;notify-div&quot;&gt;"/><x id="INTERPOLATION" equiv-text="{{unreadReportsCount}}"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="4452427314943113135" datatype="html">
        <source>Previous</source>
      </trans-unit>
      <trans-unit id="5848375712260968555" datatype="html">
        <source>Report Title:</source>
      </trans-unit>
      <trans-unit id="5487630018331375668" datatype="html">
        <source>From:</source>
      </trans-unit>
      <trans-unit id="5752829569732845992" datatype="html">
        <source>To:</source>
      </trans-unit>
      <trans-unit id="910656806257300801" datatype="html">
        <source>Sender</source>
      </trans-unit>
      <trans-unit id="3528333923659727599" datatype="html">
        <source>Last Modification Time</source>
      </trans-unit>
      <trans-unit id="4775550080689015987" datatype="html">
        <source>Reason</source>
      </trans-unit>
      <trans-unit id="1279037136679204178" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span style=&quot;vertical-align: middle; font-size: 18px;&quot; nz-icon nzType=&quot;edit&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span &gt;"/> Rename </source>
      </trans-unit>
      <trans-unit id="3803269292194853627" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span style=&quot;vertical-align: middle; font-size: 18px; ;&quot; nz-icon nzType=&quot;delete&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/> Delete </source>
      </trans-unit>
      <trans-unit id="1773092516931524563" datatype="html">
        <source>Report Title</source>
      </trans-unit>
      <trans-unit id="6426325864394685706" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>Report Type:<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> Cancellation Report</source>
      </trans-unit>
      <trans-unit id="6691551505117942733" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b &gt;"/>Date Range:<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> <x id="INTERPOLATION" equiv-text="{{dateFrom | date}}"/> to <x id="INTERPOLATION_1" equiv-text="{{dateTo | date}}"/></source>
      </trans-unit>
      <trans-unit id="8274346261976460414" datatype="html">
        <source>Please Note:</source>
      </trans-unit>
      <trans-unit id="714224327557931255" datatype="html">
        <source>Reports are run each night and will be ready by 8am EST timethe next morning. Otherwise your report may take up to an extra 24 hours.</source>
      </trans-unit>
      <trans-unit id="1738543688525351678" datatype="html">
        <source>Delete the selected report(s) from the system?</source>
      </trans-unit>
      <trans-unit id="6041115344061899387" datatype="html">
        <source>Rename</source>
      </trans-unit>
      <trans-unit id="7096514150394506042" datatype="html">
        <source>Filter Results</source>
      </trans-unit>
      <trans-unit id="4498682414491138092" datatype="html">
        <source>Yesterday</source>
      </trans-unit>
      <trans-unit id="5575954442017884054" datatype="html">
        <source>Last 7 Days</source>
      </trans-unit>
      <trans-unit id="1662170432588987648" datatype="html">
        <source>Last 30 Days</source>
      </trans-unit>
      <trans-unit id="8563120047945584269" datatype="html">
        <source>Quarter to Date</source>
      </trans-unit>
      <trans-unit id="8065968369037299258" datatype="html">
        <source>Last Quarter</source>
      </trans-unit>
      <trans-unit id="3160852305209455703" datatype="html">
        <source>Year to Date</source>
      </trans-unit>
      <trans-unit id="2652749250103868" datatype="html">
        <source>Subscription Period to Date</source>
      </trans-unit>
      <trans-unit id="3932526052071644855" datatype="html">
        <source>Uh oh, we can&apos;t run those dates</source>
      </trans-unit>
      <trans-unit id="753519348908833540" datatype="html">
        <source>Please make sure that your &quot;Date to&quot; is greater than your &quot;date from&quot;. You may want to check that you have chosen the right month or year.</source>
      </trans-unit>
      <trans-unit id="8384225360105280028" datatype="html">
        <source>Complete</source>
      </trans-unit>
      <trans-unit id="8520842233253013827" datatype="html">
        <source>Error Occured</source>
      </trans-unit>
      <trans-unit id="7287078438578844087" datatype="html">
        <source>Opened</source>
      </trans-unit>
      <trans-unit id="1587322198596975930" datatype="html">
        <source> Security Report </source>
      </trans-unit>
      <trans-unit id="7035314289264169914" datatype="html">
        <source>User:</source>
      </trans-unit>
      <trans-unit id="3622973494063039093" datatype="html">
        <source>Event:</source>
      </trans-unit>
      <trans-unit id="2392488717875840729" datatype="html">
        <source>User</source>
      </trans-unit>
      <trans-unit id="3304972143698843178" datatype="html">
        <source>Event</source>
      </trans-unit>
      <trans-unit id="8497528947328199741" datatype="html">
        <source>Time</source>
      </trans-unit>
      <trans-unit id="1827245616056417685" datatype="html">
        <source>IP Address</source>
      </trans-unit>
      <trans-unit id="7300095233097075542" datatype="html">
        <source>Visiting OS</source>
      </trans-unit>
      <trans-unit id="2197887163001584162" datatype="html">
        <source>Visiting Browser</source>
      </trans-unit>
      <trans-unit id="4388879716045736175" datatype="html">
        <source>Note</source>
      </trans-unit>
      <trans-unit id="131953131023499274" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>Report Type:<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> Security Report</source>
      </trans-unit>
      <trans-unit id="2819237508582663254" datatype="html">
        <source>User login failed</source>
      </trans-unit>
      <trans-unit id="8783496256027235624" datatype="html">
        <source>Admin changed team member password</source>
      </trans-unit>
      <trans-unit id="8289470636238214433" datatype="html">
        <source>User changed password</source>
      </trans-unit>
      <trans-unit id="1842315009518891791" datatype="html">
        <source>User updated 2FA settings</source>
      </trans-unit>
      <trans-unit id="3866366540640417549" datatype="html">
        <source>User reset password</source>
      </trans-unit>
      <trans-unit id="315176110174824547" datatype="html">
        <source> Usage Report </source>
      </trans-unit>
      <trans-unit id="8953033926734869941" datatype="html">
        <source>Name</source>
      </trans-unit>
      <trans-unit id="1752745806200253296" datatype="html">
        <source>Document Storage</source>
      </trans-unit>
      <trans-unit id="5613761968757608093" datatype="html">
        <source>Documents Sent</source>
      </trans-unit>
      <trans-unit id="5718970443953517060" datatype="html">
        <source>Average Time To Sign In</source>
      </trans-unit>
      <trans-unit id="6842969353084656480" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>Report Type:<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> Usage Report</source>
      </trans-unit>
      <trans-unit id="8166291202529695710" datatype="html">
        <source> Settings </source>
      </trans-unit>
      <trans-unit id="6390254643318110224" datatype="html">
        <source>Add Stamp</source>
      </trans-unit>
      <trans-unit id="8040881171107393560" datatype="html">
        <source>ID</source>
      </trans-unit>
      <trans-unit id="1859131936150262113" datatype="html">
        <source>Inactive</source>
      </trans-unit>
      <trans-unit id="6844286051306999234" datatype="html">
        <source>Delete the selected seal from the system?</source>
      </trans-unit>
      <trans-unit id="7447650496002366252" datatype="html">
        <source> Certificate Provider <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i *ngIf=&quot;!showFormProvider&quot;class=&quot;fa-regular fa-chevron-down ml-1 cursor-pointer&quot; (click)=&quot;showFormProvider = !showFormProvider&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/><x id="START_ITALIC_TEXT_1" equiv-text="&lt;i *ngIf=&quot;showFormProvider&quot; class=&quot;fa-regular fa-chevron-up ml-1 cursor-pointer&quot; (click)=&quot;showFormProvider = !showFormProvider&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/></source>
      </trans-unit>
      <trans-unit id="8232773653071363045" datatype="html">
        <source>Provider</source>
      </trans-unit>
      <trans-unit id="890779814635310897" datatype="html">
        <source>Provider is required</source>
      </trans-unit>
      <trans-unit id="7643999639877544644" datatype="html">
        <source>OAuth Client ID</source>
      </trans-unit>
      <trans-unit id="8239441308519390792" datatype="html">
        <source>OAuth Client ID is required</source>
      </trans-unit>
      <trans-unit id="4104801525903222149" datatype="html">
        <source>OAuth Client Secret</source>
      </trans-unit>
      <trans-unit id="2518571665289110224" datatype="html">
        <source>OAuth Client Secret is required</source>
      </trans-unit>
      <trans-unit id="6141513898443741196" datatype="html">
        <source>Credential ID</source>
      </trans-unit>
      <trans-unit id="7700781228914538709" datatype="html">
        <source>Credential ID is required</source>
      </trans-unit>
      <trans-unit id="120066562462881839" datatype="html">
        <source>Credential PIN</source>
      </trans-unit>
      <trans-unit id="961995236065034445" datatype="html">
        <source>Credential PIN is required</source>
      </trans-unit>
      <trans-unit id="4150775314839757538" datatype="html">
        <source> Settings <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i *ngIf=&quot;!showFormSettings&quot;class=&quot;fa-regular fa-chevron-down ml-1&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/><x id="START_ITALIC_TEXT_1" equiv-text="&lt;i *ngIf=&quot;showFormSettings&quot; class=&quot;fa-regular fa-chevron-up ml-1&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/></source>
      </trans-unit>
      <trans-unit id="3833692663709091545" datatype="html">
        <source>Name is required</source>
      </trans-unit>
      <trans-unit id="1431416938026210429" datatype="html">
        <source>Password</source>
      </trans-unit>
      <trans-unit id="4947200238571193022" datatype="html">
        <source> Password is required</source>
      </trans-unit>
      <trans-unit id="3179854216020052086" datatype="html">
        <source>Password must be at least 8 ~ 20 characters and have at least 1 uppercase, 1 lowercase, 1 number, and one of !@#$%^&amp;*()_+=[]<x id="INTERPOLATION" equiv-text="{{ &apos;{&apos; }}"/><x id="INTERPOLATION_1" equiv-text="{{ &apos;}&apos; }}"/>|- Cannot contain username.<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/></source>
      </trans-unit>
      <trans-unit id="6540643040059790455" datatype="html">
        <source>Graphic</source>
      </trans-unit>
      <trans-unit id="8354482071586555512" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;upload&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/> Select File </source>
      </trans-unit>
      <trans-unit id="453936576565370589" datatype="html">
        <source>Graphic is required</source>
      </trans-unit>
      <trans-unit id="8250815517290067568" datatype="html">
        <source>Display Subject</source>
      </trans-unit>
      <trans-unit id="2045219041580363848" datatype="html">
        <source>Display Date</source>
      </trans-unit>
      <trans-unit id="4226988467648478239" datatype="html">
        <source>Display Time</source>
      </trans-unit>
      <trans-unit id="2388387442246530179" datatype="html">
        <source>Display Reason</source>
      </trans-unit>
      <trans-unit id="2651553718911041659" datatype="html">
        <source>Enter reason for seal</source>
      </trans-unit>
      <trans-unit id="5211438786907834926" datatype="html">
        <source> Users/Teams <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i *ngIf=&quot;!showFormUsersTeams&quot;class=&quot;fa-regular fa-chevron-down ml-1&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/><x id="START_ITALIC_TEXT_1" equiv-text="&lt;i *ngIf=&quot;showFormUsersTeams&quot; class=&quot;fa-regular fa-chevron-up ml-1&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/></source>
      </trans-unit>
      <trans-unit id="8002962872891453592" datatype="html">
        <source>Keep old password</source>
      </trans-unit>
      <trans-unit id="2722829814024650459" datatype="html">
        <source>The file must be a PNG</source>
      </trans-unit>
      <trans-unit id="3768927257183755959" datatype="html">
        <source>Save</source>
      </trans-unit>
      <trans-unit id="6765071518635771784" datatype="html">
        <source>Company Name</source>
      </trans-unit>
      <trans-unit id="8069624459740371334" datatype="html">
        <source>Use Company Branding</source>
      </trans-unit>
      <trans-unit id="9152567765549416984" datatype="html">
        <source>Company Logo in the Email Header:<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br /&gt;"/>(suggested size: 200px*83px)</source>
      </trans-unit>
      <trans-unit id="952039476419334270" datatype="html">
        <source>Company Logo on Signing Page:<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br /&gt;"/>(suggested size: 140px*48px)</source>
      </trans-unit>
      <trans-unit id="7677732325203871174" datatype="html">
        <source>Email Subject</source>
      </trans-unit>
      <trans-unit id="2209597128743589683" datatype="html">
        <source>Email Footer</source>
      </trans-unit>
      <trans-unit id="1487017261609649703" datatype="html">
        <source>Locked by <x id="INTERPOLATION" equiv-text="{{ formData.locks[12].lockedUserName }}"/><x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i class=&quot;fa-sharp fa-solid fa-lock&quot; style=&quot;margin:0px 8px;&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/></source>
      </trans-unit>
      <trans-unit id="308511058187194142" datatype="html">
        <source>Email Signature</source>
      </trans-unit>
      <trans-unit id="7932367136615285835" datatype="html">
        <source>Email Tagline Below Logo</source>
      </trans-unit>
      <trans-unit id="7547431360535544968" datatype="html">
        <source>Email Preview</source>
      </trans-unit>
      <trans-unit id="2412552215421739741" datatype="html">
        <source>Dear <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>XXX<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span style=&quot;display: none;&quot; name=&quot;{[GUID_5abf2aec-ffbf-4573-a586-2a85de0b552e_GUID]}&gt;&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/>,</source>
      </trans-unit>
      <trans-unit id="5368138175714874144" datatype="html">
        <source><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/>You have been sent the following document for your electronic signature: <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>Document name<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/> The document was originally sent to you on: <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/><x id="INTERPOLATION" equiv-text="{{ originallyDate }}"/><x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/> This document was sent to you by: <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>XXX<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/>Please access the document using the link below.<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_DIV" ctype="x-div" equiv-text="&lt;div style=&quot;width: 100%; height:20px; line-height:20px;background-color:#0082d2; display:inline-block; color:#ffffff; font-size:10.5pt; font-weight:bold;text-align:center;&quot;&gt;"/><x id="START_LINK" ctype="x-a" equiv-text="&lt;a target=&quot;_blank&quot; style=&quot; color: #ffffff;&quot;&gt;"/><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span style=&quot;color: #fffffe;&quot;&gt;"/>Review Document<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/><x id="CLOSE_TAG_DIV" ctype="x-div" equiv-text="&lt;/div&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN_2" ctype="x-span_2" equiv-text="&lt;span style=&quot; font-family: Arial,sans-serif; font-size: 10.5pt; line-height: 1.2; color: #575747&quot;&gt;"/> If the above link does not work, you can paste the following address into your browser address bar: https://test.signority.net/UI/documentDesigner2.html?iid=&amp;lang=en <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> If you are not the signing authority please Reject the Document or Change the Signer. To do this open the document by clicking “Review Document” and then selecting “More” at the upper right of the page. In the menu select either the “Change Signer” or “Reject” option depending on what you want to do. If you don&apos;t see these options, contact the document sender for assistance.To stop future email reminders, click <x id="START_LINK_1" equiv-text="&lt;a target=&quot;_blank&quot; href=&quot;https://test.signority.net/UI/invitationStopReminders.html?guid=&quot;&gt;"/>here<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/>. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_SPAN_3" ctype="x-span_3" equiv-text="&lt;span *ngIf=&quot;!formData.emailSignature || !formData.useCompanyBranding&quot;&gt;"/>Regards,<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/>Signority Team<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_4" ctype="x-span_4" equiv-text="&lt;span *ngIf=&quot;formData.emailSignature &amp;&amp; formData.useCompanyBranding&quot; [innerHTML]=&quot;emailSignature | safeHtml&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/></source>
      </trans-unit>
      <trans-unit id="434042643539083968" datatype="html">
        <source> This message was intended for you and sent by Signority, a secure electronic signature service. To launch the site successfully, please use Firefox, Chrome, Safari, IE10 or above.<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/>To read more about us, follow <x id="START_LINK" ctype="x-a" equiv-text="&lt;a target=&quot;_blank&quot; href=&quot;http://signority.com/how-it-works/&quot; style=&quot;text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#0083d2&quot;&gt;"/>How it works<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/>. Stay informed by following us on <x id="START_LINK_1" equiv-text="&lt;a target=&quot;_blank&quot; href=&quot;http://www.linkedin.com/company/signority&quot; style=&quot;text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#0083d2&quot;&gt;"/>LinkedIn<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/> and <x id="START_LINK_2" equiv-text="&lt;a target=&quot;_blank&quot; href=&quot;https://twitter.com/signority/&quot; style=&quot;text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#0083d2&quot;&gt;"/>Twitter<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/>.<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> This message was sent to you from <x id="START_LINK_3" equiv-text="&lt;a style=&quot;color: #0083D2&quot;&gt;"/>Signority Team<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/> who is using Signority Electronic Signature Service. If you do not want to receive email from this sender, please contact the sender with your request. </source>
      </trans-unit>
      <trans-unit id="176179561**********" datatype="html">
        <source>Legal Notices</source>
      </trans-unit>
      <trans-unit id="1822385112718104276" datatype="html">
        <source>Custom Terms of Use: <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> (Notice of eSignature Use)</source>
      </trans-unit>
      <trans-unit id="6811172989903853135" datatype="html">
        <source>Leave empty to use system default</source>
      </trans-unit>
      <trans-unit id="5651682280839114662" datatype="html">
        <source>Provider Type</source>
      </trans-unit>
      <trans-unit id="6641024648411549335" datatype="html">
        <source>Host</source>
      </trans-unit>
      <trans-unit id="160640128319879431" datatype="html">
        <source>User Name</source>
      </trans-unit>
      <trans-unit id="8358388827744192621" datatype="html">
        <source>Encryption Type</source>
      </trans-unit>
      <trans-unit id="6013038726122253355" datatype="html">
        <source>Test SMTP Setting</source>
      </trans-unit>
      <trans-unit id="960823750575923515" datatype="html">
        <source>Target Email Address</source>
      </trans-unit>
      <trans-unit id="3994094760277141837" datatype="html">
        <source><x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i class=&quot;fa-solid fa-ban&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/> Cancel</source>
      </trans-unit>
      <trans-unit id="2394098169207197436" datatype="html">
        <source>Lock All Settings <x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i class=&quot;fa-solid fa-lock-open&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/></source>
      </trans-unit>
      <trans-unit id="6439365426343089851" datatype="html">
        <source>General</source>
      </trans-unit>
      <trans-unit id="8878700331247603166" datatype="html">
        <source>Security</source>
      </trans-unit>
      <trans-unit id="894965666917873756" datatype="html">
        <source>Document</source>
      </trans-unit>
      <trans-unit id="8044304973848447418" datatype="html">
        <source>Notification</source>
      </trans-unit>
      <trans-unit id="8422875188929347819" datatype="html">
        <source>Sharing</source>
      </trans-unit>
      <trans-unit id="5007671436855782977" datatype="html">
        <source>Signer Options</source>
      </trans-unit>
      <trans-unit id="451580319091363284" datatype="html">
        <source>Retention &amp; Backup</source>
      </trans-unit>
      <trans-unit id="8002701882170771367" datatype="html">
        <source>General<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="3864644965972535813" datatype="html">
        <source>Enable Help Instructions</source>
      </trans-unit>
      <trans-unit id="3894039560161506093" datatype="html">
        <source>Timezone</source>
      </trans-unit>
      <trans-unit id="1595236217024550114" datatype="html">
        <source>Pacific Time Zone</source>
      </trans-unit>
      <trans-unit id="2558609960713924660" datatype="html">
        <source>Mountain Time Zone</source>
      </trans-unit>
      <trans-unit id="8084675551450366854" datatype="html">
        <source>Central Time Zone</source>
      </trans-unit>
      <trans-unit id="1353464598160680125" datatype="html">
        <source>Eastern Time Zone</source>
      </trans-unit>
      <trans-unit id="2624130858066130355" datatype="html">
        <source>Atlantic Time Zone</source>
      </trans-unit>
      <trans-unit id="222310231424920916" datatype="html">
        <source>Newfoundland Time Zone</source>
      </trans-unit>
      <trans-unit id="4569255375251157489" datatype="html">
        <source>Default Language</source>
      </trans-unit>
      <trans-unit id="5866254605255506989" datatype="html">
        <source>English</source>
      </trans-unit>
      <trans-unit id="4134147244760971282" datatype="html">
        <source>Français</source>
      </trans-unit>
      <trans-unit id="3764423498356545977" datatype="html">
        <source>Document remaining usage warning threshold</source>
      </trans-unit>
      <trans-unit id="430913693530729750" datatype="html">
        <source>Security<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="1773161889942507313" datatype="html">
        <source>Set session logout timeout period</source>
      </trans-unit>
      <trans-unit id="5775432979678639979" datatype="html">
        <source>minute(s)</source>
      </trans-unit>
      <trans-unit id="163809137962387235" datatype="html">
        <source>hour(s)</source>
      </trans-unit>
      <trans-unit id="5788760032459048870" datatype="html">
        <source>day(s)</source>
      </trans-unit>
      <trans-unit id="7837191193465529210" datatype="html">
        <source>min 5 minute, max 365 day!</source>
      </trans-unit>
      <trans-unit id="1928985605110629839" datatype="html">
        <source>Enforce 2FA/MFA </source>
      </trans-unit>
      <trans-unit id="5853235498267274976" datatype="html">
        <source>Enable new device login notification</source>
      </trans-unit>
      <trans-unit id="4156493166196145855" datatype="html">
        <source>Document<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="3746991835021959442" datatype="html">
        <source>PDF type output for finalized documents</source>
      </trans-unit>
      <trans-unit id="3025463945112287238" datatype="html">
        <source>Standard PDF</source>
      </trans-unit>
      <trans-unit id="1545189854086874142" datatype="html">
        <source>PDF Encryption</source>
      </trans-unit>
      <trans-unit id="7326442301179360073" datatype="html">
        <source>PDF/A-2b</source>
      </trans-unit>
      <trans-unit id="5804813387686027751" datatype="html">
        <source><x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong&gt;"/>Standard PDF:<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/> No additional security measures. </source>
      </trans-unit>
      <trans-unit id="4250891834467122258" datatype="html">
        <source><x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong&gt;"/>Encrypted PDF:<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/> Protect the document&apos;s contents from unauthorized access. </source>
      </trans-unit>
      <trans-unit id="7550406879666384024" datatype="html">
        <source><x id="START_TAG_STRONG" ctype="x-strong" equiv-text="&lt;strong&gt;"/>PDF/A-2b:<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/> PDF/A-2b is a format for archiving documents. It requires fonts and graphics to be embedded and cannot be encrypted. </source>
      </trans-unit>
      <trans-unit id="4862833968633579462" datatype="html">
        <source>Enable eSignature for all users</source>
      </trans-unit>
      <trans-unit id="1370514728827777631" datatype="html">
        <source>Enable Digital Signatures for all users</source>
      </trans-unit>
      <trans-unit id="3347726427594623518" datatype="html">
        <source>Please enable at least one signature method for your team!</source>
      </trans-unit>
      <trans-unit id="7429411187364878418" datatype="html">
        <source>Merge finalized document and audit trail into one PDF</source>
      </trans-unit>
      <trans-unit id="6100999366332154964" datatype="html">
        <source>Attach document audit trail to final email attachment</source>
      </trans-unit>
      <trans-unit id="996489261511801038" datatype="html">
        <source>Combine final email attachment into one PDF file</source>
      </trans-unit>
      <trans-unit id="81731550702826057" datatype="html">
        <source>Display “Sign Powered by Signority” on signature tags </source>
      </trans-unit>
      <trans-unit id="1368602334683981978" datatype="html">
        <source>Document expiration after being sent</source>
      </trans-unit>
      <trans-unit id="5420599825707471340" datatype="html">
        <source><x id="TAG_INPUT" ctype="x-input" equiv-text="&lt;input nz-input placeholder=&quot;&quot; nzSize=&quot;default&quot; [(ngModel)]=&quot;formData.documentSetting.defaultExpireIntervalAfterSent&quot; style=&quot;width: 96px;&quot; [disabled]=&quot;formData.locks[16].locked === 2 ? true : false&quot; i18n-aria-label aria-label=&quot;Document expiration after being sent&quot;/&gt;"/> Days </source>
      </trans-unit>
      <trans-unit id="4239242332568742526" datatype="html">
        <source>Please set a default number of days a document should expire after it is sent. (This value can be between 1 to 365)</source>
      </trans-unit>
      <trans-unit id="278268821046310321" datatype="html">
        <source>Default Date Format</source>
      </trans-unit>
      <trans-unit id="2938036376962610154" datatype="html">
        <source>Default Text Tag Size</source>
      </trans-unit>
      <trans-unit id="444030876782674445" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;xs:block mr-2&quot;&gt;"/>Height <x id="TAG_INPUT" ctype="x-input" equiv-text="&lt;input nz-input placeholder=&quot;&quot; nzSize=&quot;default&quot; [(ngModel)]=&quot;formData.documentSetting.defaultTextHeight&quot; style=&quot;width: 96px;&quot; [disabled]=&quot;formData.locks[18].locked === 2 ? true : false&quot; i18n-aria-label aria-label=&quot;Default text height&quot;/&gt;"/> px <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span&gt;"/>Width <x id="TAG_INPUT_1" ctype="x-input_1" equiv-text="&lt;input nz-input placeholder=&quot;&quot; nzSize=&quot;default&quot; [(ngModel)]=&quot;formData.documentSetting.defaultTextWidth&quot; style=&quot;width: 96px;&quot; [disabled]=&quot;formData.locks[18].locked === 2 ? true : false&quot; i18n-aria-label aria-label=&quot;Default text width&quot;/&gt;"/> px <x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="1038594468980998722" datatype="html">
        <source>The default Text tag height should be between 13 and 792 pixel high.</source>
      </trans-unit>
      <trans-unit id="1904077165402204005" datatype="html">
        <source>The default Text tag width should be between 13 and 612 pixel wide.</source>
      </trans-unit>
      <trans-unit id="3254696822481804545" datatype="html">
        <source>Default Date Tag Size</source>
      </trans-unit>
      <trans-unit id="2495280281321911436" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span class=&quot;xs:block mr-2&quot;&gt;"/>Height <x id="TAG_INPUT" ctype="x-input" equiv-text="&lt;input nz-input placeholder=&quot;&quot; nzSize=&quot;default&quot; [(ngModel)]=&quot;formData.documentSetting.defaultDateHeight&quot; style=&quot;width: 96px;&quot; [disabled]=&quot;formData.locks[19].locked === 2 ? true : false&quot; i18n-aria-label aria-label=&quot;Default date tag height&quot;/&gt;"/> px<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span&gt;"/>Width <x id="TAG_INPUT_1" ctype="x-input_1" equiv-text="&lt;input nz-input placeholder=&quot;&quot; nzSize=&quot;default&quot; [(ngModel)]=&quot;formData.documentSetting.defaultDateWidth&quot; style=&quot;width: 96px;&quot; [disabled]=&quot;formData.locks[19].locked === 2 ? true : false&quot; i18n-aria-label aria-label=&quot;Default date tag width&quot;/&gt;"/> px<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="6059860945103444171" datatype="html">
        <source>Default Font Size for Tags</source>
      </trans-unit>
      <trans-unit id="7690008702749976411" datatype="html">
        <source>The default Date tag height should be between 13 and 792 pixel high.</source>
      </trans-unit>
      <trans-unit id="8685946463089888473" datatype="html">
        <source>The default Date tag width should be between 53 and 612 pixel wide.</source>
      </trans-unit>
      <trans-unit id="1885746634398371934" datatype="html">
        <source>The default Date tag width should be between 107 to 612 pixels wide to accommodate for the date format &apos;MMMM DD, YYYY&apos;.</source>
      </trans-unit>
      <trans-unit id="4385032369858686737" datatype="html">
        <source>Notification<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="3362796849472484319" datatype="html">
        <source>Send Reminder Emails</source>
      </trans-unit>
      <trans-unit id="227035899054882424" datatype="html">
        <source>Send email reminders every</source>
      </trans-unit>
      <trans-unit id="7903756899235160745" datatype="html">
        <source>Please enter a value between 1 and <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span *ngIf=&quot;formData.notificationsSetting.defaultSendReminderPeriodUnit === 1&quot;&gt;"/>365<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_1" ctype="x-span_1" equiv-text="&lt;span *ngIf=&quot;formData.notificationsSetting.defaultSendReminderPeriodUnit === 2&quot;&gt;"/>168<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="START_TAG_SPAN_2" ctype="x-span_2" equiv-text="&lt;span *ngIf=&quot;formData.notificationsSetting.defaultSendReminderPeriodUnit === 3&quot;&gt;"/>10080<x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/>.</source>
      </trans-unit>
      <trans-unit id="6653367669335137531" datatype="html">
        <source>Maximum number of emails sent</source>
      </trans-unit>
      <trans-unit id="779021336422822766" datatype="html">
        <source>Please enter a value between 1 and 365.</source>
      </trans-unit>
      <trans-unit id="8229375735530695928" datatype="html">
        <source>Send each document recipient an invitation email when documents are sent out for signing</source>
      </trans-unit>
      <trans-unit id="4050982270123870422" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>BE CAREFUL.<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> Turning this setting off will disable emails from being sent for documents for all of your account&apos;s/team’s users.</source>
      </trans-unit>
      <trans-unit id="6626795201453166536" datatype="html">
        <source>Send “Viewed” notification to document sender</source>
      </trans-unit>
      <trans-unit id="8945422387075714788" datatype="html">
        <source>Send “Signed” notification to document sender</source>
      </trans-unit>
      <trans-unit id="5304453905331773423" datatype="html">
        <source>Notification email to document sender when document is finalized</source>
      </trans-unit>
      <trans-unit id="1736945800521300239" datatype="html">
        <source>Attach a PDF of the final document</source>
      </trans-unit>
      <trans-unit id="4814741104068409727" datatype="html">
        <source>Provide a link to final document</source>
      </trans-unit>
      <trans-unit id="6659582128888651174" datatype="html">
        <source>Send an email only</source>
      </trans-unit>
      <trans-unit id="4348361394599756376" datatype="html">
        <source>Do not send any email</source>
      </trans-unit>
      <trans-unit id="6102144466086434122" datatype="html">
        <source>Notification email to document recipients when document is finalized</source>
      </trans-unit>
      <trans-unit id="5095434500909924541" datatype="html">
        <source>Send an expiration email to the document sender right after a document expires</source>
      </trans-unit>
      <trans-unit id="8744156212657427864" datatype="html">
        <source>Warning of document expiration before it expires</source>
      </trans-unit>
      <trans-unit id="4105242649061558493" datatype="html">
        <source>Send a copy of all email notifications to the following email address:</source>
      </trans-unit>
      <trans-unit id="1417693714872528491" datatype="html">
        <source>This field is required.</source>
      </trans-unit>
      <trans-unit id="4455969378179813442" datatype="html">
        <source>Send invoices and overage notifications to multiple email addresses. (Maximum 20, seperated by “,”)</source>
      </trans-unit>
      <trans-unit id="4671321416268887255" datatype="html">
        <source>Sharing<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="1171823543331635436" datatype="html">
        <source>Automatically share contacts with team</source>
      </trans-unit>
      <trans-unit id="6027594235276232193" datatype="html">
        <source>Automatically share documents with team</source>
      </trans-unit>
      <trans-unit id="1506375574895277562" datatype="html">
        <source>Automatically share templates with team</source>
      </trans-unit>
      <trans-unit id="3299184298401480004" datatype="html">
        <source>Signer Options<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="1803108078528461499" datatype="html">
        <source>Hide Reject Button</source>
      </trans-unit>
      <trans-unit id="2089704522321416726" datatype="html">
        <source>Hide Change Signer Button</source>
      </trans-unit>
      <trans-unit id="4252621524216642465" datatype="html">
        <source>Hide Save Button</source>
      </trans-unit>
      <trans-unit id="1786607007966529280" datatype="html">
        <source>Hide Download Button</source>
      </trans-unit>
      <trans-unit id="8865095684078908275" datatype="html">
        <source>Hide View Document Button</source>
      </trans-unit>
      <trans-unit id="8631961984302246603" datatype="html">
        <source>Hide Thumbnail </source>
      </trans-unit>
      <trans-unit id="568635960297133649" datatype="html">
        <source>Hide Draw Signature Option</source>
      </trans-unit>
      <trans-unit id="6634486533028505841" datatype="html">
        <source>Hide Type Signature Option</source>
      </trans-unit>
      <trans-unit id="6814087122067023110" datatype="html">
        <source>Hide Upload Signature Option</source>
      </trans-unit>
      <trans-unit id="5350539635073250874" datatype="html">
        <source>Allow users to delegate signing authority</source>
      </trans-unit>
      <trans-unit id="4415831411084989007" datatype="html">
        <source>Signing flow behavior when a recipient rejects</source>
      </trans-unit>
      <trans-unit id="2493405826717865222" datatype="html">
        <source>Continue with signing flow</source>
      </trans-unit>
      <trans-unit id="6693618654663817307" datatype="html">
        <source>Retention and Backup<x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span nz-icon nzType=&quot;down&quot; nzTheme=&quot;outline&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/></source>
      </trans-unit>
      <trans-unit id="1524477036347023684" datatype="html">
        <source>Enable Document Retention</source>
      </trans-unit>
      <trans-unit id="5244155105092059429" datatype="html">
        <source>Documents will be permanently deleted after</source>
      </trans-unit>
      <trans-unit id="5473725742121064035" datatype="html">
        <source>Please enter a value between 1 and 1000.</source>
      </trans-unit>
      <trans-unit id="142578725645636387" datatype="html">
        <source>Your plan allows a maximum retention period of <x id="INTERPOLATION" equiv-text="{{maxRetentionDays}}"/> days </source>
      </trans-unit>
      <trans-unit id="7547180950849264786" datatype="html">
        <source> &quot;Storage integration&quot; must be set up. Go to <x id="START_LINK" ctype="x-a" equiv-text="&lt;a href=&quot;/UI/userSettings.html&quot;&gt;"/>Settings<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="1664537959149506844" datatype="html">
        <source>Prevent documents from being deleted by users until deletion date</source>
      </trans-unit>
      <trans-unit id="6162770843720784846" datatype="html">
        <source> The settings you are about to save will be applied to all of your organization&apos;s users. Please make sure all settings are correct and communicate any major changes to your organization. <x id="START_TAG_DIV" ctype="x-div" equiv-text="&lt;div *ngIf=&quot;!formData.notificationsSetting.sendDocRecipient&quot;&gt;"/> You will be disabling email notifications for documents for all your users. To change this, go to <x id="START_LINK" ctype="x-a" equiv-text="&lt;a (click)=&quot;modalService.close();goToScroll(1739)&quot;&gt;"/>Notifications settings.<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/><x id="CLOSE_TAG_DIV" ctype="x-div" equiv-text="&lt;/div&gt;"/></source>
      </trans-unit>
      <trans-unit id="4964887824317237322" datatype="html">
        <source> The settings you are about to save will be applied to all of the team&apos;s users. Please make sure all settings are correct and communicate any major changes to the team. <x id="START_TAG_DIV" ctype="x-div" equiv-text="&lt;div *ngIf=&quot;!formData.notificationsSetting.sendDocRecipient&quot;&gt;"/> You will be disabling email notifications for documents for all your users. To change this, go to <x id="START_LINK" ctype="x-a" equiv-text="&lt;a (click)=&quot;modalService.close();goToScroll(1739)&quot;&gt;"/>Notifications settings.<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/><x id="CLOSE_TAG_DIV" ctype="x-div" equiv-text="&lt;/div&gt;"/></source>
      </trans-unit>
      <trans-unit id="4725929723826292877" datatype="html">
        <source>The Global Settings are inherited by all of your teams. Locking any will prevent your teams and sub-teams from changing them at their team level.</source>
      </trans-unit>
      <trans-unit id="1837263078836687021" datatype="html">
        <source>Team Settings</source>
      </trans-unit>
      <trans-unit id="1596177017846826329" datatype="html">
        <source>The Team Settings are inherited by your sub-teams. Locking any will prevent your sub-teams from changing them at their team level.</source>
      </trans-unit>
      <trans-unit id="6962699013778688473" datatype="html">
        <source>Continue</source>
      </trans-unit>
      <trans-unit id="3542042671420335679" datatype="html">
        <source>No</source>
      </trans-unit>
      <trans-unit id="5150308198693598366" datatype="html">
        <source>Turning this setting off will no longer push finalized documents to your storage integration when your documents are purged. You will be at risk of losing your files if you have not backed it through other means.</source>
      </trans-unit>
      <trans-unit id="3501986073704643784" datatype="html">
        <source>You are about to reduce the retention period to <x id="PH" equiv-text="this.formData.retentionBackup.retentionInterval"/> day(s). Documents finalized in the last <x id="PH_1" equiv-text="this.formData.retentionBackup.retentionInterval"/> day(s) will be impacted.</source>
      </trans-unit>
      <trans-unit id="9033235108052289962" datatype="html">
        <source> Users &amp; Teams </source>
      </trans-unit>
      <trans-unit id="3207604937156175649" datatype="html">
        <source>Add Team</source>
      </trans-unit>
      <trans-unit id="6084131277734018569" datatype="html">
        <source>Add Team Members</source>
      </trans-unit>
      <trans-unit id="3333997530670576074" datatype="html">
        <source>... More</source>
      </trans-unit>
      <trans-unit id="6700140000680419776" datatype="html">
        <source>+ Add Search Filter</source>
      </trans-unit>
      <trans-unit id="1914456093804362738" datatype="html">
        <source>Team Name</source>
      </trans-unit>
      <trans-unit id="6282342645998724484" datatype="html">
        <source>Show all Users</source>
      </trans-unit>
      <trans-unit id="2089008137126412851" datatype="html">
        <source>Show all Teams</source>
      </trans-unit>
      <trans-unit id="2354817630223808522" datatype="html">
        <source>Move</source>
      </trans-unit>
      <trans-unit id="7966925292449370136" datatype="html">
        <source>Resend Activation Email</source>
      </trans-unit>
      <trans-unit id="4496517922704272725" datatype="html">
        <source>Change Role</source>
      </trans-unit>
      <trans-unit id="6972105223770675605" datatype="html">
        <source>Reset Password</source>
      </trans-unit>
      <trans-unit id="8796296257159514110" datatype="html">
        <source>Transfer Documents</source>
      </trans-unit>
      <trans-unit id="5322473252816589112" datatype="html">
        <source>Deactivate</source>
      </trans-unit>
      <trans-unit id="7386690791159230506" datatype="html">
        <source> Starts with </source>
      </trans-unit>
      <trans-unit id="4748435688085541416" datatype="html">
        <source> Includes </source>
      </trans-unit>
      <trans-unit id="3948953681537223949" datatype="html">
        <source> apply</source>
      </trans-unit>
      <trans-unit id="4145496584631696119" datatype="html">
        <source>Role</source>
      </trans-unit>
      <trans-unit id="5043619589204949812" datatype="html">
        <source>Team name is required</source>
      </trans-unit>
      <trans-unit id="6064781388038010858" datatype="html">
        <source>Parent Team</source>
      </trans-unit>
      <trans-unit id="3724068243287682962" datatype="html">
        <source>Team</source>
      </trans-unit>
      <trans-unit id="5920399866799044940" datatype="html">
        <source>Team is required</source>
      </trans-unit>
      <trans-unit id="5964587283262433354" datatype="html">
        <source>Add Team Member</source>
      </trans-unit>
      <trans-unit id="5005290836214172506" datatype="html">
        <source>Last name is required</source>
      </trans-unit>
      <trans-unit id="5714136532829569569" datatype="html">
        <source>Accessible Teams</source>
      </trans-unit>
      <trans-unit id="2374364212956253275" datatype="html">
        <source>Acessible Docs</source>
      </trans-unit>
      <trans-unit id="1881210066244826100" datatype="html">
        <source>Only Shared Documents</source>
      </trans-unit>
      <trans-unit id="2931636178864605327" datatype="html">
        <source>Accessible docs is required</source>
      </trans-unit>
      <trans-unit id="1797951737846053792" datatype="html">
        <source>Move to Team</source>
      </trans-unit>
      <trans-unit id="8633968067722072928" datatype="html">
        <source>Move to</source>
      </trans-unit>
      <trans-unit id="2469820555623291480" datatype="html">
        <source> Moving teams(s)/user(s) to a different team may change the settings or branding available to them. </source>
      </trans-unit>
      <trans-unit id="3521237330535988059" datatype="html">
        <source>Current Name</source>
      </trans-unit>
      <trans-unit id="1659421062577668435" datatype="html">
        <source>Enter New Name</source>
      </trans-unit>
      <trans-unit id="1221596867309460035" datatype="html">
        <source>New Password</source>
      </trans-unit>
      <trans-unit id="205044832662301680" datatype="html">
        <source>Password is required</source>
      </trans-unit>
      <trans-unit id="3241357959735682038" datatype="html">
        <source>Confirm Password</source>
      </trans-unit>
      <trans-unit id="1469550443766423211" datatype="html">
        <source>Password confirmation is required<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/></source>
      </trans-unit>
      <trans-unit id="3369365440749922726" datatype="html">
        <source>Passwords must match<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/></source>
      </trans-unit>
      <trans-unit id="3660641992705857006" datatype="html">
        <source>Your password needs to meet the following requirements:<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -Between 8-20 characters long<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -1 uppercase letter<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -1 lowercase letter<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -1 number<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -1 special character of !@#$%^&amp;*()_+=[]<x id="INTERPOLATION" equiv-text="{{ &apos;{&apos; }}"/><x id="INTERPOLATION_1" equiv-text="{{ &apos;}&apos; }}"/>|-<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -Cannot contain username.<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> -Cannot contain a dictionary word. <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/></source>
      </trans-unit>
      <trans-unit id="4501551964556407727" datatype="html">
        <source>Document Transfer</source>
      </trans-unit>
      <trans-unit id="8443568478896860043" datatype="html">
        <source>Recipient</source>
      </trans-unit>
      <trans-unit id="1543171393020443049" datatype="html">
        <source>Recipient is required</source>
      </trans-unit>
      <trans-unit id="2118811034752241773" datatype="html">
        <source>Delete the selected team(s) from the system?</source>
      </trans-unit>
      <trans-unit id="4973288723313625104" datatype="html">
        <source>Deactivate the selected user(s) from the system?<x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/> Documents will remain with this user until you choose to move them to another user.</source>
      </trans-unit>
      <trans-unit id="272001291043953248" datatype="html">
        <source>Send activation email to <x id="INTERPOLATION" equiv-text="{{currentlySelectedItem.email}}"/>?</source>
      </trans-unit>
      <trans-unit id="1848079710318412423" datatype="html">
        <source>Edit Team Member</source>
      </trans-unit>
      <trans-unit id="1712604982246687512" datatype="html">
        <source> Moving user(s) to a different team may change the settings or branding available to them. </source>
      </trans-unit>
      <trans-unit id="3334976252583994548" datatype="html">
        <source>Edit Team</source>
      </trans-unit>
      <trans-unit id="4808791512433987109" datatype="html">
        <source>Active Users</source>
      </trans-unit>
      <trans-unit id="8234861422792481010" datatype="html">
        <source>Pending Users</source>
      </trans-unit>
      <trans-unit id="4621663703183620377" datatype="html">
        <source>Deactivated Users</source>
      </trans-unit>
      <trans-unit id="8004893392098645060" datatype="html">
        <source>Deactivated</source>
      </trans-unit>
      <trans-unit id="1629859042031827357" datatype="html">
        <source>Re-activate</source>
      </trans-unit>
      <trans-unit id="3201931959635523975" datatype="html">
        <source>Enable “Anchor Text” import by default when adding new files</source>
      </trans-unit>
      <trans-unit id="1660896360395226727" datatype="html">
        <source> The password required to use this encrypted stamp </source>
      </trans-unit>
      <trans-unit id="701497192810049034" datatype="html">
        <source> The email address of the person authorized to use this encrypted stamp </source>
      </trans-unit>
      <trans-unit id="888616856503604730" datatype="html">
        <source> Access codes sent via SMS or Email will expire after this amount of time. Default is 20 minutes. </source>
      </trans-unit>
      <trans-unit id="1752954424034713713" datatype="html">
        <source>Authentication code expiration period</source>
      </trans-unit>
      <trans-unit id="6643543474122183135" datatype="html">
        <source>Include recipients: </source>
      </trans-unit>
      <trans-unit id="7085368766517882232" datatype="html">
        <source>New document title:</source>
      </trans-unit>
      <trans-unit id="7173401654155517636" datatype="html">
        <source>Automatically Delete Exported Documents</source>
      </trans-unit>
      <trans-unit id="8051311646315881468" datatype="html">
        <source>Please enter a title </source>
      </trans-unit>
      <trans-unit id="528071483915993920" datatype="html">
        <source>Once your bulk export is complete, your documents will be removed from Signority. This action is permanent and cannot be undone.</source>
      </trans-unit>
      <trans-unit id="166663296502139071" datatype="html">
        <source>Exported documents deleted</source>
      </trans-unit>
      <trans-unit id="2807800733729323332" datatype="html">
        <source>Yes</source>
      </trans-unit>
      <trans-unit id="6696367964057164464" datatype="html">
        <source>Exported documents deleted: </source>
      </trans-unit>
      <trans-unit id="2287397382701336796" datatype="html">
        <source>Sorry, please enter the text exactly as displayed to confirm.</source>
      </trans-unit>
      <trans-unit id="841311519047609428" datatype="html">
        <source>DELETE</source>
      </trans-unit>
      <trans-unit id="8653792114761054508" datatype="html">
        <source>Type &apos;DELETE&apos; to confirm:</source>
      </trans-unit>
      <trans-unit id="1710907106900745605" datatype="html">
        <source>Plan Fee:</source>
      </trans-unit>
      <trans-unit id="2354338458778112796" datatype="html">
        <source>You will be cancelling your subscription which will end on <x id="INTERPOLATION" equiv-text="{{product.nextduedate}}"/>. After this date, your account will be suspended. If you have team members their accounts will be closed as well. If you want to come back, you will need to purchase a new subscription to activate your account. </source>
      </trans-unit>
      <trans-unit id="2824355527183488460" datatype="html">
        <source>Your Bill:</source>
      </trans-unit>
      <trans-unit id="3891595439009474793" datatype="html">
        <source>Next Billing Amount:</source>
      </trans-unit>
      <trans-unit id="3951158250545152491" datatype="html">
        <source>Final Bill Amount:</source>
      </trans-unit>
      <trans-unit id="3766578271554289695" datatype="html">
        <source>Cancel Plan</source>
      </trans-unit>
      <trans-unit id="4132134758428866522" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/>Your current plan<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/></source>
      </trans-unit>
      <trans-unit id="4238655147397322914" datatype="html">
        <source>Coupon Used:</source>
      </trans-unit>
      <trans-unit id="4484537605997653127" datatype="html">
        <source>Discount:</source>
      </trans-unit>
      <trans-unit id="4510618209881327008" datatype="html">
        <source>Your next invoice</source>
      </trans-unit>
      <trans-unit id="4625186655159745924" datatype="html">
        <source>Original Billing Amount:</source>
      </trans-unit>
      <trans-unit id="5210471907630011957" datatype="html">
        <source>Billing Cycle:</source>
      </trans-unit>
      <trans-unit id="5378037603801402814" datatype="html">
        <source>* Overage charges will apply if occurred during this final subscription period.</source>
      </trans-unit>
      <trans-unit id="6604251346807381297" datatype="html">
        <source> Service suspended (To reactivate your service, the most recent invoice must be paid.) </source>
      </trans-unit>
      <trans-unit id="7397788404523258542" datatype="html">
        <source>Deduction Amount:</source>
      </trans-unit>
      <trans-unit id="7426536434988301674" datatype="html">
        <source> Service suspended (To reactivate your service, all the outstanding invoices must be paid. Click <x id="START_LINK" ctype="x-a" equiv-text="&lt;a routerLink=&quot;/invoice/list&quot;&gt;"/>here<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/> to pay your invoices.) </source>
      </trans-unit>
      <trans-unit id="776674093429290523" datatype="html">
        <source>Next Invoice Date:</source>
      </trans-unit>
      <trans-unit id="7921647067521876856" datatype="html">
        <source>Subscription end date:</source>
      </trans-unit>
      <trans-unit id="8182542795579121917" datatype="html">
        <source>Plan:</source>
      </trans-unit>
      <trans-unit id="8245192177733730949" datatype="html">
        <source> Service Plan </source>
      </trans-unit>
      <trans-unit id="8766543515727166681" datatype="html">
        <source>Status:</source>
      </trans-unit>
      <trans-unit id="3249513483374643425" datatype="html">
        <source>Add</source>
      </trans-unit>
      <trans-unit id="3883498360560764926" datatype="html">
        <source>Add New Payment Method</source>
      </trans-unit>
      <trans-unit id="5300747019553511041" datatype="html">
        <source>Add Payment Source</source>
      </trans-unit>
      <trans-unit id="6554772258342786200" datatype="html">
        <source>Are you sure you would like to delete this payment method?</source>
      </trans-unit>
      <trans-unit id="8517497614332600322" datatype="html">
        <source> Payment Method </source>
      </trans-unit>
      <trans-unit id="8839524692578383318" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{exportInfo.exportedDocumentsDeleted}}"/> failed</source>
      </trans-unit>
      <trans-unit id="1813470836277579876" datatype="html">
        <source>Team Documents</source>
      </trans-unit>
      <trans-unit id="788405814003311577" datatype="html">
        <source> Manage All Documents </source>
      </trans-unit>
      <trans-unit id="4326675223384472720" datatype="html">
        <source>My Documents</source>
      </trans-unit>
      <trans-unit id="5137176804623261445" datatype="html">
        <source>Force regulatory information on signature tags</source>
      </trans-unit>
      <trans-unit id="8881910314162677261" datatype="html">
        <source>Display the time on sign date tags</source>
      </trans-unit>
      <trans-unit id="5544641237446844074" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="Billing C"/>Billing Cycle Period<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/></source>
      </trans-unit>
      <trans-unit id="4835648694234396043" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&quot;status_circle circle_trial&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span"/> Trial </source>
      </trans-unit>
      <trans-unit id="6342105141588866343" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&quot;status_circle circle_active&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span"/> Active </source>
      </trans-unit>
      <trans-unit id="7400619673082094973" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&quot;status_circle circle_cancelled&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span"/> Cancelled </source>
      </trans-unit>
      <trans-unit id="7911040896056537209" datatype="html">
        <source><x id="START_BOLD_TEXT" ctype="x-b" equiv-text="Team&apos;s Do"/>Team&apos;s Document Usage<x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/></source>
      </trans-unit>
      <trans-unit id="8141854439876074616" datatype="html">
        <source>Billing Cycle Renewal Date: </source>
      </trans-unit>
      <trans-unit id="8745055911235995774" datatype="html">
        <source>Trial Period Ends: </source>
      </trans-unit>
      <trans-unit id="8976275279466073600" datatype="html">
        <source>Your Plan: </source>
      </trans-unit>
      <trans-unit id="5422438226243573315" datatype="html">
        <source><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&quot;status_circle circle_unpaid&quot;&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span"/> Suspended </source>
      </trans-unit>
      <trans-unit id="2114466421795269064" datatype="html">
        <source><x id="START_TAG_STRONG" ctype="x-strong" equiv-text="Docu"/>Document Usage<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/></source>
      </trans-unit>
      <trans-unit id="5339837312058243750" datatype="html">
        <source><x id="START_TAG_STRONG" ctype="x-strong" equiv-text="Orga"/>Organization’s Document Usage<x id="CLOSE_TAG_STRONG" ctype="x-strong" equiv-text="&lt;/strong&gt;"/></source>
      </trans-unit>
      <trans-unit id="8690764517530763737" datatype="html">
        <source> Number of days a user can bypass 2FA/MFA after succesful login. Set value to 0 (Zero) to disable. </source>
      </trans-unit>
      <trans-unit id="6898271403359100637" datatype="html">
        <source>Set 2FA/MFA &quot;Remember Device&quot; period to </source>
      </trans-unit>
      <trans-unit id="7687599736304725682" datatype="html">
        <source> days</source>
      </trans-unit>
      <trans-unit id="2347436735267022484" datatype="html">
        <source><x id="START_PARAGRAPH" ctype="x-p" equiv-text="&lt;p&gt;"/> Encrypted stamps add an additional level of authority. They apply an encrypted stamp to a document on behalf of an organization. Encrypted stamps use various authentication methods to ensure they are applied by the person who is authorized to use it. <x id="CLOSE_PARAGRAPH" ctype="x-p" equiv-text="&lt;/p&gt;"/><x id="START_PARAGRAPH" ctype="x-p" equiv-text="&lt;p&gt;"/> Teams/users who are assigned to an encrypted stamp have the ability to request that stamp to be placed but they can not apply it unless authorized. <x id="CLOSE_PARAGRAPH" ctype="x-p" equiv-text="&lt;/p&gt;"/></source>
      </trans-unit>
      <trans-unit id="3629268642102702051" datatype="html">
        <source>Edit Encrypted Stamp</source>
      </trans-unit>
      <trans-unit id="4380577639186001982" datatype="html">
        <source><x id="START_PARAGRAPH" ctype="x-p" equiv-text="&lt;p&gt;"/> Stamps are a form of authentication used to convey official authorization or approval. They can be uploaded by their recipient or can be predefined to streamline the recipients workflow. <x id="CLOSE_PARAGRAPH" ctype="x-p" equiv-text="&lt;/p&gt;"/><x id="START_PARAGRAPH" ctype="x-p" equiv-text="&lt;p&gt;"/> Teams/users assigned to a Predefined Stamp will have access to that stamp when signing a document. <x id="CLOSE_PARAGRAPH" ctype="x-p" equiv-text="&lt;/p&gt;"/></source>
      </trans-unit>
      <trans-unit id="4441075081748469662" datatype="html">
        <source>Encrypted Stamp</source>
      </trans-unit>
      <trans-unit id="4691466660489055893" datatype="html">
        <source>Add Encrypted Stamp</source>
      </trans-unit>
      <trans-unit id="6859153072584916284" datatype="html">
        <source>Edit Stamp</source>
      </trans-unit>
      <trans-unit id="6907343497298540886" datatype="html">
        <source> Stamps </source>
      </trans-unit>
      <trans-unit id="7736312865585584294" datatype="html">
        <source>New Stamp</source>
      </trans-unit>
      <trans-unit id="8650499415827640724" datatype="html">
        <source>Type</source>
      </trans-unit>
      <trans-unit id="1316480640088961738" datatype="html">
        <source>Select which Users/Teams have access to this stamp when signing</source>
      </trans-unit>
      <trans-unit id="7130132445146123732" datatype="html">
        <source>Select which Users/Teams have access to this encrypted stamp when signing</source>
      </trans-unit>
      <trans-unit id="3604841730300275934" datatype="html">
        <source> Recipient uploaded stamps are only used for their signing session and are not saved into your account </source>
      </trans-unit>
      <trans-unit id="8769242499233959466" datatype="html">
        <source>Allow recipient to upload their own stamp</source>
      </trans-unit>
      <trans-unit id="8955599271764558241" datatype="html">
        <source>Allow recipient to add stamp tags to the document</source>
      </trans-unit>
      <trans-unit id="4453481880950412561" datatype="html">
        <source>Signority logo</source>
      </trans-unit>
      <trans-unit id="4550958265168690830" datatype="html">
        <source>Logo</source>
      </trans-unit>
      <trans-unit id="6570363013146073520" datatype="html">
        <source>Dashboard</source>
      </trans-unit>
      <trans-unit id="9084885127601930456" datatype="html">
        <source>logo</source>
      </trans-unit>
      <trans-unit id="160054547859684672" datatype="html">
        <source>New document title</source>
      </trans-unit>
      <trans-unit id="1744724360334271882" datatype="html">
        <source>The password required to use this encrypted stamp</source>
      </trans-unit>
      <trans-unit id="231907993165259235" datatype="html">
        <source>16pt</source>
      </trans-unit>
      <trans-unit id="2595846124353402173" datatype="html">
        <source>Include recipients</source>
      </trans-unit>
      <trans-unit id="2691017568527674584" datatype="html">
        <source>Previous page:</source>
      </trans-unit>
      <trans-unit id="3278564196622415008" datatype="html">
        <source> User: <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_MAT_FORM_FIELD" ctype="x-mat_form_field" equiv-text="&lt;mat-form-field class=&quot;material-select-height select&quot;&gt;"/><x id="START_TAG_MAT_SELECT" ctype="x-mat_select" equiv-text="&lt;mat-select name=&quot;User&quot; [(ngModel)]=&quot;selectedUserId&quot; i18n-aria-label aria-label=&quot;User&quot;&gt;"/><x id="START_TAG_MAT_OPTION" ctype="x-mat_option" equiv-text="&lt;mat-option *ngFor=&quot;let user of users&quot; [value]=&quot;user.value&quot;&gt;"/><x id="INTERPOLATION" equiv-text="{{user.label}}"/><x id="CLOSE_TAG_MAT_OPTION" ctype="x-mat_option" equiv-text="&lt;/mat-option&gt;"/><x id="CLOSE_TAG_MAT_SELECT" ctype="x-mat_select" equiv-text="&lt;/mat-select&gt;"/><x id="CLOSE_TAG_MAT_FORM_FIELD" ctype="x-mat_form_field" equiv-text="&lt;/mat-form-field&gt;"/></source>
      </trans-unit>
      <trans-unit id="3384163582855174125" datatype="html">
        <source>Default date tag width</source>
      </trans-unit>
      <trans-unit id="3456119142453531984" datatype="html">
        <source>20pt</source>
      </trans-unit>
      <trans-unit id="3586674587150281199" datatype="html">
        <source>Last name</source>
      </trans-unit>
      <trans-unit id="3702987354461063557" datatype="html">
        <source>Company Logo on Signing Page</source>
      </trans-unit>
      <trans-unit id="3719080555538542367" datatype="html">
        <source>SSL</source>
      </trans-unit>
      <trans-unit id="3855047621042682749" datatype="html">
        <source>Team Customized SMTP</source>
      </trans-unit>
      <trans-unit id="4300832166322733116" datatype="html">
        <source>Default text height</source>
      </trans-unit>
      <trans-unit id="444979833081311681" datatype="html">
        <source>YYYY-MM-DD</source>
      </trans-unit>
      <trans-unit id="4580988005648117665" datatype="html">
        <source>Search</source>
      </trans-unit>
      <trans-unit id="4638014477642290686" datatype="html">
        <source>MM-DD-YYYY</source>
      </trans-unit>
      <trans-unit id="4662193896245455406" datatype="html">
        <source>14pt</source>
      </trans-unit>
      <trans-unit id="4701112714126027417" datatype="html">
        <source>Send reminder emails every</source>
      </trans-unit>
      <trans-unit id="4743002849260529978" datatype="html">
        <source>Items per page:</source>
      </trans-unit>
      <trans-unit id="5159089529763797673" datatype="html">
        <source>Custom Terms of use</source>
      </trans-unit>
      <trans-unit id="5248717555542428023" datatype="html">
        <source>Username</source>
      </trans-unit>
      <trans-unit id="5342432350421167093" datatype="html">
        <source>First name</source>
      </trans-unit>
      <trans-unit id="5527122741707746933" datatype="html">
        <source>Signority Standard SMTP</source>
      </trans-unit>
      <trans-unit id="5643815781951276241" datatype="html">
        <source>DD-MM-YYYY</source>
      </trans-unit>
      <trans-unit id="5647064816204545704" datatype="html">
        <source>10pt</source>
      </trans-unit>
      <trans-unit id="5717703012251718942" datatype="html">
        <source>12pt</source>
      </trans-unit>
      <trans-unit id="5938145112234717204" datatype="html">
        <source>To import your contact list, use this <x id="START_LINK" ctype="x-a" equiv-text="&lt;a href=&quot;.\assets\files\importSample.csv&quot;&gt;"/><x id="START_UNDERLINED_TEXT" ctype="x-u" equiv-text="&lt;u&gt;"/>sample .csv file<x id="CLOSE_UNDERLINED_TEXT" ctype="x-u" equiv-text="&lt;/u&gt;"/><x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="6117946241126833991" datatype="html">
        <source>Port</source>
      </trans-unit>
      <trans-unit id="6121752651494720908" datatype="html">
        <source>18pt</source>
      </trans-unit>
      <trans-unit id="6217441912807899779" datatype="html">
        <source>2FA/MFA period</source>
      </trans-unit>
      <trans-unit id="6221116945238325541" datatype="html">
        <source>Page <x id="PH" equiv-text="page + 1"/> of <x id="PH_1" equiv-text="amountPages"/></source>
      </trans-unit>
      <trans-unit id="6252070156626006029" datatype="html">
        <source>None</source>
      </trans-unit>
      <trans-unit id="6316285129376026914" datatype="html">
        <source>Target User&apos;s Email</source>
      </trans-unit>
      <trans-unit id="6771140278501856515" datatype="html">
        <source>Use template</source>
      </trans-unit>
      <trans-unit id="6784758141439994527" datatype="html">
        <source>Last page</source>
      </trans-unit>
      <trans-unit id="7194445680476159638" datatype="html">
        <source> Event Type: <x id="LINE_BREAK" ctype="lb" equiv-text="&lt;br&gt;"/><x id="START_TAG_MAT_FORM_FIELD" ctype="x-mat_form_field" equiv-text="&lt;mat-form-field class=&quot;material-select-height select&quot;&gt;"/><x id="START_TAG_MAT_SELECT" ctype="x-mat_select" equiv-text="&lt;mat-select name=&quot;Event type&quot; [(ngModel)]=&quot;selectedEventType&quot; i18n-aria-label aria-label=&quot;Event Type&quot;&gt;"/><x id="START_TAG_MAT_OPTION" ctype="x-mat_option" equiv-text="&lt;mat-option *ngFor=&quot;let event of eventTypes&quot; [value]=&quot;event.value&quot;&gt;"/><x id="INTERPOLATION" equiv-text="{{event.label}}"/><x id="CLOSE_TAG_MAT_OPTION" ctype="x-mat_option" equiv-text="&lt;/mat-option&gt;"/><x id="CLOSE_TAG_MAT_SELECT" ctype="x-mat_select" equiv-text="&lt;/mat-select&gt;"/><x id="CLOSE_TAG_MAT_FORM_FIELD" ctype="x-mat_form_field" equiv-text="&lt;/mat-form-field&gt;"/></source>
      </trans-unit>
      <trans-unit id="7220507099625131968" datatype="html">
        <source>Company Logo in the Email Header</source>
      </trans-unit>
      <trans-unit id="750911178749235694" datatype="html">
        <source>DD/MM/YYYY</source>
      </trans-unit>
      <trans-unit id="759735821742691861" datatype="html">
        <source>Default date tag height</source>
      </trans-unit>
      <trans-unit id="7609856000684270651" datatype="html">
        <source>MMMM DD, YYYY</source>
      </trans-unit>
      <trans-unit id="7977017825222631725" datatype="html">
        <source>8pt</source>
      </trans-unit>
      <trans-unit id="807798206755225087" datatype="html">
        <source>With audit Trail</source>
      </trans-unit>
      <trans-unit id="8121141462865337916" datatype="html">
        <source>Phone number</source>
      </trans-unit>
      <trans-unit id="8205610343756523594" datatype="html">
        <source>Default text width</source>
      </trans-unit>
      <trans-unit id="8264734555769135431" datatype="html">
        <source>TLS</source>
      </trans-unit>
      <trans-unit id="8336178102124533757" datatype="html">
        <source>YYYY/MM/DD</source>
      </trans-unit>
      <trans-unit id="8642445555713453888" datatype="html">
        <source>First page</source>
      </trans-unit>
      <trans-unit id="8845219325208590180" datatype="html">
        <source>MM/DD/YYYY</source>
      </trans-unit>
      <trans-unit id="8933315370566056819" datatype="html">
        <source>Next page:</source>
      </trans-unit>
      <trans-unit id="8951537649084760474" datatype="html">
        <source>Type &apos;DELETE&apos; to confirm</source>
      </trans-unit>
      <trans-unit id="8970260868207598363" datatype="html">
        <source>Page 0 of <x id="PH" equiv-text="length"/></source>
      </trans-unit>
      <trans-unit id="9005002221339633665" datatype="html">
        <source>The email address of the person authorized to use this encrypted stamp</source>
      </trans-unit>
      <trans-unit id="9056870850481021142" datatype="html">
        <source>%</source>
      </trans-unit>
      <trans-unit id="122114774689391224" datatype="html">
        <source>role</source>
      </trans-unit>
      <trans-unit id="126074860850048964" datatype="html">
        <source>End date</source>
      </trans-unit>
      <trans-unit id="1387042064025131280" datatype="html">
        <source>Send invoices and overage notifications to multiple email addresses. (Maximum 20, seperated by comma)</source>
      </trans-unit>
      <trans-unit id="1585425644523299468" datatype="html">
        <source>Email *</source>
      </trans-unit>
      <trans-unit id="1603823091445377126" datatype="html">
        <source>Move selected templates up</source>
      </trans-unit>
      <trans-unit id="1776714439003265001" datatype="html">
        <source>Move selected templates to bottom</source>
      </trans-unit>
      <trans-unit id="1982290260733187291" datatype="html">
        <source>Event Type</source>
      </trans-unit>
      <trans-unit id="2026436537740665436" datatype="html">
        <source>Parent team</source>
      </trans-unit>
      <trans-unit id="216804552618436570" datatype="html">
        <source>Default date format</source>
      </trans-unit>
      <trans-unit id="2277429309523723525" datatype="html">
        <source>Document actions menu</source>
      </trans-unit>
      <trans-unit id="1972355467287977547" datatype="html">
        <source>Send an expiration email to the document sender right after document expires</source>
      </trans-unit>
      <trans-unit id="23820636813409229" datatype="html">
        <source>Authentication code expiration period unit</source>
      </trans-unit>
      <trans-unit id="2522509298628328978" datatype="html">
        <source>Send a copy of all email notifications to the following emial address:</source>
      </trans-unit>
      <trans-unit id="2671643902824593326" datatype="html">
        <source>Select date</source>
      </trans-unit>
      <trans-unit id="2831786343626252607" datatype="html">
        <source>Email reminder interval unit</source>
      </trans-unit>
      <trans-unit id="316513668522668171" datatype="html">
        <source>Warning of document expiration before it expires unit</source>
      </trans-unit>
      <trans-unit id="3489572889808515599" datatype="html">
        <source>Hide Thumbnail</source>
      </trans-unit>
      <trans-unit id="3588415639242079458" datatype="html">
        <source>New password</source>
      </trans-unit>
      <trans-unit id="3727817917426748475" datatype="html">
        <source>Session timeout unit</source>
      </trans-unit>
      <trans-unit id="3782563238994348625" datatype="html">
        <source>Confirm password</source>
      </trans-unit>
      <trans-unit id="3834967828985392284" datatype="html">
        <source>Move selected templates to top</source>
      </trans-unit>
      <trans-unit id="399803645309079564" datatype="html">
        <source>Team Name *</source>
      </trans-unit>
      <trans-unit id="4126304011787241908" datatype="html">
        <source>Last Name *</source>
      </trans-unit>
      <trans-unit id="4530840797839608171" datatype="html">
        <source>Enforce 2FA/MFA</source>
      </trans-unit>
      <trans-unit id="4549103421162996161" datatype="html">
        <source>Send viewed notification to document sender</source>
      </trans-unit>
      <trans-unit id="4591582389005124613" datatype="html">
        <source>Send reminder emails</source>
      </trans-unit>
      <trans-unit id="4731115507247017518" datatype="html">
        <source>current status</source>
      </trans-unit>
      <trans-unit id="5457938796893098392" datatype="html">
        <source>Release notes</source>
      </trans-unit>
      <trans-unit id="5470706554452951501" datatype="html">
        <source>Country code</source>
      </trans-unit>
      <trans-unit id="550195626538405809" datatype="html">
        <source>Team name</source>
      </trans-unit>
      <trans-unit id="5541762673199523759" datatype="html">
        <source>Document remaining usage warning threshold unit</source>
      </trans-unit>
      <trans-unit id="5546754027962780451" datatype="html">
        <source>Team *</source>
      </trans-unit>
      <trans-unit id="572405399885469224" datatype="html">
        <source>Move selected templates down</source>
      </trans-unit>
      <trans-unit id="5758447249914940431" datatype="html">
        <source>Default font size</source>
      </trans-unit>
      <trans-unit id="6130019760742749814" datatype="html">
        <source>Display Sign powered by Signority on signature tags</source>
      </trans-unit>
      <trans-unit id="613503448907877929" datatype="html">
        <source>Template link info</source>
      </trans-unit>
      <trans-unit id="6344901042764909416" datatype="html">
        <source>Download Type</source>
      </trans-unit>
      <trans-unit id="6439825853390442149" datatype="html">
        <source>Enable Anchor text import by default when adding new files</source>
      </trans-unit>
      <trans-unit id="6554622678429356915" datatype="html">
        <source>Copy type</source>
      </trans-unit>
      <trans-unit id="7040029317473362807" datatype="html">
        <source>Send Signed notification to document sender</source>
      </trans-unit>
      <trans-unit id="7120031799430567363" datatype="html">
        <source>Parent Team *</source>
      </trans-unit>
      <trans-unit id="7589628893254149926" datatype="html">
        <source>Send each document recipient an invitation email when the documents are sent out for signing</source>
      </trans-unit>
      <trans-unit id="7816199225288578692" datatype="html">
        <source>Report title</source>
      </trans-unit>
      <trans-unit id="7949255673527335232" datatype="html">
        <source>Accessible docs</source>
      </trans-unit>
      <trans-unit id="795890916060309463" datatype="html">
        <source>Roles</source>
      </trans-unit>
      <trans-unit id="8430432279551054277" datatype="html">
        <source>Enter new name</source>
      </trans-unit>
      <trans-unit id="8706888911786789589" datatype="html">
        <source>Accessible teams Select all teams</source>
      </trans-unit>
      <trans-unit id="8838478044018052369" datatype="html">
        <source>First Name *</source>
      </trans-unit>
      <trans-unit id="888341446683346521" datatype="html">
        <source>Start date</source>
      </trans-unit>
      <trans-unit id="9129338239108584829" datatype="html">
        <source>Use company branding</source>
      </trans-unit>
      <trans-unit id="912998870875118211" datatype="html">
        <source>Accessible Docs</source>
      </trans-unit>
      <trans-unit id="9163415903519647380" datatype="html">
        <source>Sandbox View <x id="START_TAG_MAT_SLIDE_TOGGLE" ctype="x-mat_slide_toggle" equiv-text="&lt;mat-slide-toggle appToggleEnterKey color=&quot;primary&quot; class=&quot;ml-2&quot; [(ngModel)]=&quot;sandBoxView&quot;&gt;"/><x id="CLOSE_TAG_MAT_SLIDE_TOGGLE" ctype="x-mat_slide_toggle" equiv-text="&lt;/mat-slide-toggle&gt;"/></source>
      </trans-unit>
      <trans-unit id="9221558938557907675" datatype="html">
        <source>Document status</source>
      </trans-unit>
      <trans-unit id="9107715181571497550" datatype="html">
        <source> If you have any questions please <x id="START_LINK" ctype="x-a" equiv-text="&lt;a href=&quot;https://www.signority.com/support-centre/&quot; target=&quot;_blank&quot;&gt;"/>contact us<x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/> and we hope to welcome you back soon! </source>
      </trans-unit>
      <trans-unit id="1085309836704157602" datatype="html">
        <source>Card Number</source>
      </trans-unit>
      <trans-unit id="1098210772183967258" datatype="html">
        <source>MM / YY</source>
      </trans-unit>
      <trans-unit id="1123313596810750547" datatype="html">
        <source> My Invoices </source>
      </trans-unit>
      <trans-unit id="1188451331823834032" datatype="html">
        <source>Recurring invoice for the plan</source>
      </trans-unit>
      <trans-unit id="1395915619425382563" datatype="html">
        <source>A valid expiry date is required</source>
      </trans-unit>
      <trans-unit id="1470711101626592232" datatype="html">
        <source>Street Address</source>
      </trans-unit>
      <trans-unit id="1671553784955820075" datatype="html">
        <source>Select an address</source>
      </trans-unit>
      <trans-unit id="1801879966570931927" datatype="html">
        <source>Expiry Date</source>
      </trans-unit>
      <trans-unit id="1917979079769004212" datatype="html">
        <source>Due Date</source>
      </trans-unit>
      <trans-unit id="1973853252230517273" datatype="html">
        <source>Invoice #</source>
      </trans-unit>
      <trans-unit id="2119321613357659984" datatype="html">
        <source> Unpaid </source>
      </trans-unit>
      <trans-unit id="2313823320089614542" datatype="html">
        <source>Select a credit card</source>
      </trans-unit>
      <trans-unit id="2314075913167237221" datatype="html">
        <source>City</source>
      </trans-unit>
      <trans-unit id="2549106983286751100" datatype="html">
        <source><x id="START_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;i [ngClass]=&quot;getCreditCardIcon(paymentSource)&quot; aria-hidden=&quot;true&quot;&gt;"/><x id="CLOSE_ITALIC_TEXT" ctype="x-i" equiv-text="&lt;/i&gt;"/> <x id="INTERPOLATION" equiv-text="{{paymentSource.brand}}"/> ending in <x id="INTERPOLATION_1" equiv-text="{{paymentSource.last4}}"/> </source>
      </trans-unit>
      <trans-unit id="2611475427610660053" datatype="html">
        <source>Transactions</source>
      </trans-unit>
      <trans-unit id="286659003915008564" datatype="html">
        <source>Service between</source>
      </trans-unit>
      <trans-unit id="3207050836358092212" datatype="html">
        <source>to</source>
      </trans-unit>
      <trans-unit id="3350216026636846610" datatype="html">
        <source>Annually</source>
      </trans-unit>
      <trans-unit id="3448462145758383019" datatype="html">
        <source>Total</source>
      </trans-unit>
      <trans-unit id="3746875819668943163" datatype="html">
        <source>Pay Now</source>
      </trans-unit>
      <trans-unit id="3799311833004953905" datatype="html">
        <source>Your Billing Addresses for your <x id="INTERPOLATION" equiv-text="{{selectedPaymentSource.brand}}"/> ending in <x id="INTERPOLATION_1" equiv-text="{{selectedPaymentSource.last4}}"/> </source>
      </trans-unit>
      <trans-unit id="3929281485914893910" datatype="html">
        <source>and</source>
      </trans-unit>
      <trans-unit id="516376854106818926" datatype="html">
        <source>Invoice # <x id="INTERPOLATION" equiv-text="{{currentInvoice.invoicenum}}"/></source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>A valid CVC is required</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>State/Province</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>Currency</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>Card Info</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>123</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source> Pay To </source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>Description</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>Feb</source>
      </trans-unit>
      <trans-unit id="*******************" datatype="html">
        <source>Transaction Date</source>
      </trans-unit>
      <trans-unit id="******************" datatype="html">
        <source>between</source>
      </trans-unit>
      <trans-unit id="516176798986294299" datatype="html">
        <source>Country</source>
      </trans-unit>
      <trans-unit id="5194248831925769790" datatype="html">
        <source>Add a new address for your <x id="INTERPOLATION" equiv-text="{{selectedPaymentSource.brand}}"/> ending in <x id="INTERPOLATION_1" equiv-text="{{selectedPaymentSource.last4}}"/> </source>
      </trans-unit>
      <trans-unit id="5216134244286952136" datatype="html">
        <source>Delete credit card</source>
      </trans-unit>
      <trans-unit id="5332281038433432736" datatype="html">
        <source>A valid card number is required</source>
      </trans-unit>
      <trans-unit id="5507326650332881991" datatype="html">
        <source>Apr</source>
      </trans-unit>
      <trans-unit id="5648574669404659458" datatype="html">
        <source>Aug</source>
      </trans-unit>
      <trans-unit id="5782523941518718252" datatype="html">
        <source>Sub Total: </source>
      </trans-unit>
      <trans-unit id="5788442138872799554" datatype="html">
        <source>Add Payment Method</source>
      </trans-unit>
      <trans-unit id="5851424994801012357" datatype="html">
        <source>Suspended</source>
      </trans-unit>
      <trans-unit id="5897160717458202605" datatype="html">
        <source> You must agree to the Terms of Service to proceed </source>
      </trans-unit>
      <trans-unit id="608526464352986839" datatype="html">
        <source>A card holder name is required</source>
      </trans-unit>
      <trans-unit id="6119650568055262997" datatype="html">
        <source>1234123412341234</source>
      </trans-unit>
      <trans-unit id="6207754626941051341" datatype="html">
        <source>Oct</source>
      </trans-unit>
      <trans-unit id="6248057760302687725" datatype="html">
        <source>CVC</source>
      </trans-unit>
      <trans-unit id="637997626417540782" datatype="html">
        <source>Add Coupon Code</source>
      </trans-unit>
      <trans-unit id="6438827956918137617" datatype="html">
        <source>Mar</source>
      </trans-unit>
      <trans-unit id="6533918067030990396" datatype="html">
        <source>Amount</source>
      </trans-unit>
      <trans-unit id="6651526424533175099" datatype="html">
        <source> Due Date: <x id="INTERPOLATION" equiv-text="{{displayDueDate}}"/> </source>
      </trans-unit>
      <trans-unit id="6659495924407376135" datatype="html">
        <source>Changing plan from</source>
      </trans-unit>
      <trans-unit id="6726678157852127039" datatype="html">
        <source>View Invoice</source>
      </trans-unit>
      <trans-unit id="6762743264882388498" datatype="html">
        <source>Monthly</source>
      </trans-unit>
      <trans-unit id="706445337301804886" datatype="html">
        <source>Postal Code/Zip</source>
      </trans-unit>
      <trans-unit id="7453590870141246500" datatype="html">
        <source>Add a new payment method </source>
      </trans-unit>
      <trans-unit id="7524659883245674915" datatype="html">
        <source>Invoice Date</source>
      </trans-unit>
      <trans-unit id="7593874341646872302" datatype="html">
        <source>Update Address</source>
      </trans-unit>
      <trans-unit id="7595747576974676670" datatype="html">
        <source>Jan</source>
      </trans-unit>
      <trans-unit id="7604569935429898139" datatype="html">
        <source>Coupon: </source>
      </trans-unit>
      <trans-unit id="7646624924970625487" datatype="html">
        <source>Total: </source>
      </trans-unit>
      <trans-unit id="1603503088924305243" datatype="html">
        <source>I agree to Signority&apos;s</source>
      </trans-unit>
      <trans-unit id="7777579586760423636" datatype="html">
        <source>Dec</source>
      </trans-unit>
      <trans-unit id="7827897375427522961" datatype="html">
        <source>Card Holder</source>
      </trans-unit>
      <trans-unit id="8008220216521768133" datatype="html">
        <source>Sept</source>
      </trans-unit>
      <trans-unit id="8199095338003039214" datatype="html">
        <source> Paid </source>
      </trans-unit>
      <trans-unit id="8269261039058575292" datatype="html">
        <source>Nov</source>
      </trans-unit>
      <trans-unit id="8282940047848889809" datatype="html">
        <source>Paid</source>
      </trans-unit>
      <trans-unit id="8386762418325828932" datatype="html">
        <source>Apply Coupon</source>
      </trans-unit>
      <trans-unit id="8469692700277617405" datatype="html">
        <source>May</source>
      </trans-unit>
      <trans-unit id="8643191370791561425" datatype="html">
        <source>Unpaid</source>
      </trans-unit>
      <trans-unit id="8737411379880557340" datatype="html">
        <source>John Smith</source>
      </trans-unit>
      <trans-unit id="8755295493430866446" datatype="html">
        <source>No transactions found</source>
      </trans-unit>
      <trans-unit id="877548117929476553" datatype="html">
        <source>You Pay: </source>
      </trans-unit>
      <trans-unit id="8800795830055030020" datatype="html">
        <source>Your Credit Card </source>
      </trans-unit>
      <trans-unit id="8874091041333810511" datatype="html">
        <source> Invoice Date: <x id="INTERPOLATION" equiv-text="{{displayInvoiceDate}}"/> </source>
      </trans-unit>
      <trans-unit id="9055297580745330415" datatype="html">
        <source>June</source>
      </trans-unit>
      <trans-unit id="9087113544612471348" datatype="html">
        <source>July</source>
      </trans-unit>
      <trans-unit id="7571375880734710634" datatype="html">
        <source> Terms of Service </source>
      </trans-unit>
      <trans-unit id="7805304351263086828" datatype="html">
        <source> Invoiced To </source>
      </trans-unit>
      <trans-unit id="1490765868160012201" datatype="html">
        <source>Tax (<x id="INTERPOLATION" equiv-text="{{currentInvoice.taxrate}}"/>%): </source>
      </trans-unit>
      <trans-unit id="3140471386747109856" datatype="html">
        <source>Provincial(<x id="INTERPOLATION" equiv-text="{{currentInvoice.taxrate2}}"/>%): </source>
      </trans-unit>
      <trans-unit id="4633690054512898253" datatype="html">
        <source>Pay as your business grows</source>
      </trans-unit>
      <trans-unit id="6389950345450445667" datatype="html">
        <source>Your subscription can be scaled up or down anytime</source>
      </trans-unit>
      <trans-unit id="1505957606856342133" datatype="html">
        <source>Individual Plans</source>
      </trans-unit>
      <trans-unit id="1644844063073463247" datatype="html">
        <source> Fair &amp; transparent pricing. No surprise fees. </source>
      </trans-unit>
      <trans-unit id="1855545208716189742" datatype="html">
        <source> /year </source>
      </trans-unit>
      <trans-unit id="1916086844933947564" datatype="html">
        <source>Change Plan</source>
      </trans-unit>
      <trans-unit id="2088826775686749470" datatype="html">
        <source> Have to terminate your plan? Click here </source>
      </trans-unit>
      <trans-unit id="2124095158498322187" datatype="html">
        <source>Coupon applied successfully</source>
      </trans-unit>
      <trans-unit id="251745249091878638" datatype="html">
        <source>Customize your plan to suit your business needs</source>
      </trans-unit>
      <trans-unit id="3243505317143754724" datatype="html">
        <source> Custom </source>
      </trans-unit>
      <trans-unit id="4069265362120683526" datatype="html">
        <source>Your service plan has been changed successfully; please review the invoice in the next page.</source>
      </trans-unit>
      <trans-unit id="4905798562247431262" datatype="html">
        <source>per month</source>
      </trans-unit>
      <trans-unit id="4972214555912252209" datatype="html">
        <source>Your plan was successfully cancelled</source>
      </trans-unit>
      <trans-unit id="5090217059296232646" datatype="html">
        <source> Total: </source>
      </trans-unit>
      <trans-unit id="5503478264281599851" datatype="html">
        <source> month billed annually </source>
      </trans-unit>
      <trans-unit id="5504637338623945185" datatype="html">
        <source>Pricing available on request</source>
      </trans-unit>
      <trans-unit id="5841114606088679243" datatype="html">
        <source>billed anually</source>
      </trans-unit>
      <trans-unit id="5879507732160465853" datatype="html">
        <source>Monthly Billing</source>
      </trans-unit>
      <trans-unit id="5936363006576320351" datatype="html">
        <source> Document Signing Made Easy </source>
      </trans-unit>
      <trans-unit id="6151404096345343985" datatype="html">
        <source> All paid plans come with secure digital signatures, regular templates, email support and our core feature set. </source>
      </trans-unit>
      <trans-unit id="6631471981739731583" datatype="html">
        <source> &lt; See all features &gt; </source>
      </trans-unit>
      <trans-unit id="6644743697292657108" datatype="html">
        <source>Annual Billing</source>
      </trans-unit>
      <trans-unit id="7023287310256428606" datatype="html">
        <source> month </source>
      </trans-unit>
      <trans-unit id="710455155768123371" datatype="html">
        <source>Your service plan has been changed successfully.</source>
      </trans-unit>
      <trans-unit id="7127329929373239791" datatype="html">
        <source>Choose Plan</source>
      </trans-unit>
      <trans-unit id="7556781000020084124" datatype="html">
        <source> Generate API Key </source>
      </trans-unit>
      <trans-unit id="7673470926238818267" datatype="html">
        <source>Team Plans</source>
      </trans-unit>
      <trans-unit id="8036977202721714375" datatype="html">
        <source>Yearly</source>
      </trans-unit>
      <trans-unit id="8594040199644154492" datatype="html">
        <source><x id="START_LINK" ctype="x-a" equiv-text="&lt;a href=&quot;https://www.signority.com/custom-pricing&quot; target=&quot;_blank&quot;&gt;"/> Contact Us <x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
      </trans-unit>
      <trans-unit id="8925502718842225529" datatype="html">
        <source> /month </source>
      </trans-unit>
      <trans-unit id="915177205982305070" datatype="html">
        <source> All paid Individual plans come with regular templates, linksign templates, bulksign templates, email support and our core feature set. </source>
      </trans-unit>
      <trans-unit id="4241199063649154183" datatype="html">
        <source>An erorr has occured</source>
      </trans-unit>
      <trans-unit id="4893605953930324502" datatype="html">
        <source>The coupon code is not available! Please try with other ones</source>
      </trans-unit>
    </body>
  </file>
</xliff>