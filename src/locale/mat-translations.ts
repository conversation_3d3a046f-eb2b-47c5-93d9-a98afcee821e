import { Injectable } from '@angular/core';
import {MatPaginatorIntl} from '@angular/material/paginator';

@Injectable()
export class PaginatorTranslation extends MatPaginatorIntl {
 paginatorIntl = new MatPaginatorIntl();

 constructor() {
   super(); 
   
 }

 override itemsPerPageLabel = $localize`Items per page:`;
 override nextPageLabel     = $localize`Next page:`;
 override previousPageLabel = $localize`Previous page:`;
 override firstPageLabel = $localize`First page`;
 override lastPageLabel = $localize`Last page`;
 override getRangeLabel = (page: number, pageSize: number, length: number) => {
    if (length === 0 || pageSize === 0) {
      return $localize`Page 0 of ${length}`;
    }
    const amountPages = Math.ceil(length / pageSize);
    return $localize`Page ${(page + 1)} of ${amountPages}`;  
 }
}