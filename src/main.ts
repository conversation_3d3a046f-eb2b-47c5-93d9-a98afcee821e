import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from '@environment/environment';

if (environment.production) {
    enableProdMode();
}

if (!environment.logging) {
      window.console.log = () => { }
}

platformBrowserDynamic().bootstrapModule(AppModule)
    .catch(err => console.error(err));

// Global accessibility: Make <a tabindex="0"> clickable with Enter or Space everywhere
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    document.body.addEventListener('keydown', function (event) {
      const target = event.target as HTMLElement;
      if (
        target.tagName === 'A' &&
        target.getAttribute('tabindex') === '0' &&
        (event.key === ' ' || event.key === 'Enter')
      ) {
        event.preventDefault();
        (target as HTMLElement).click();
      }
    }, true);
  });
}
