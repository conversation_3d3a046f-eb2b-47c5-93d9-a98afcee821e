import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IconDefinition } from '@ant-design/icons-angular';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { AppstoreOutline, CaretDownOutline, CloudOutline, CheckCircleOutline, CloseCircleOutline, CloseOutline, CopyOutline, DoubleLeftOutline, DoubleRightOutline, DownOutline, DownloadOutline, DeleteOutline, EditOutline, FileOutline, FileSearchOutline, FileTextOutline, FolderOutline, FormOutline, FolderOpenOutline, HomeOutline, InfoCircleTwoTone, LinkOutline, MenuOutline, MoreOutline, PlusOutline, QuestionCircleTwoTone, SettingOutline, ShareAltOutline, SolutionOutline, StarOutline, StopOutline, UpOutline, UsergroupAddOutline, UserAddOutline, UndoOutline, WalletOutline } from '@ant-design/icons-angular/icons';
import { FormsModule } from '@angular/forms';


import {CoreModule} from "@core/core.module";
import {PagesModule} from "./pages/pages.module";

import { NZ_I18N, en_US } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { UsersAndTeamsModule } from './pages/home-page/users-and-teams/users-and-teams.module';
import { HomePageModule } from './pages/home-page/home-page.module';
registerLocaleData(en);
const icons: IconDefinition[] = [ AppstoreOutline, CaretDownOutline, CloudOutline, CheckCircleOutline, CloseCircleOutline, CloseOutline, CopyOutline, DeleteOutline, DoubleLeftOutline, DoubleRightOutline, DownloadOutline, DownOutline, EditOutline, FileOutline, FileSearchOutline, FileTextOutline, FolderOutline, FolderOpenOutline, FormOutline, HomeOutline, InfoCircleTwoTone, LinkOutline, MenuOutline, MoreOutline, PlusOutline, QuestionCircleTwoTone, SettingOutline, ShareAltOutline, SolutionOutline, StarOutline, StopOutline, UpOutline, UsergroupAddOutline, UserAddOutline, UndoOutline, WalletOutline ];

@NgModule({
    declarations: [AppComponent],
    imports: [
        BrowserModule,
        AppRoutingModule,
        BrowserAnimationsModule,
        CoreModule,
        FormsModule,
        PagesModule,
        UsersAndTeamsModule,
        HomePageModule,
        NzIconModule.forRoot(icons),
    ],
    providers: [{ provide: NZ_I18N, useValue: en_US }],
    bootstrap: [AppComponent]
})
export class AppModule { }
