import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import {LoginPageComponent} from "./pages/login-page/login-page.component";
import {AuthGuard} from "@core/guards/auth.guard";

const routes: Routes = [
    {
        path: '',
        loadChildren: () => import("./pages/home-page/home-page.module").then(m => m.HomePageModule),
        canActivate:[AuthGuard]
    },
    { path: 'login', component: LoginPageComponent },
];

@NgModule({
    imports: [RouterModule.forRoot(routes, {useHash:true})],
    exports: [RouterModule]
})
export class AppRoutingModule { }
