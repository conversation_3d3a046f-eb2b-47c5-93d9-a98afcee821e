export const Code = {
		DOCUMENT_TYPE_DOCUMENT: 5,
		DOCUMENT_TYPE_TEMPLATE_LINK: 6,
		DOCUMENT_TYPE_REGULAR_TEMPLATE: 7,
		DOCUMENT_TYPE_BULK_SIGN_TEMPLATE: 8,
		DOCUMENT_TYPE_PACKAGE_TEMPLATE: 9,
		DOCUMENT_TYPE_REGULAR_TEMPLATE_FOR_PACKAGE: 10,

		INVITATION_TYPE_SIGNER: 15,
		INVITATION_TYPE_VIEWER: 16,
		INVITATION_TYPE_REVIEWER: 17,
		INVITATION_TYPE_SIGNING_HOST: 18,
		INVITATION_TYPE_ACKNOWLEDGE_RECEIPT: 19,
		INVITATION_TYPE_RECIPIENTS_MANAGER: 20,
		INVITATION_TYPE_OPEN_SIGNER: 21,
		INVITATION_TYPE_CONDITIONAL_SIGNER: 22,
		INVITATION_TYPE_EDITOR: 23,

		SIGNING_METHOD_SELF: 45,
		SIG<PERSON>NG_METHOD_OPEN: 47,
		SIGNING_METHOD_MULTI: 46,

		SIGNING_STATUS_DRAFT: 51,
		SIGNING_STATUS_IN_PROGRESS: 52,
		SIGNING_STATUS_COMPLETED: 53,
		SIGNING_STATUS_REJECTED: 54,
		SIGNING_STATUS_REACH_DEADLINE: 55,
		SIGNING_STATUS_SIGNING_CANCELED: 56,

		RECIPIENT_STATUS_NOT_SENT: 60,
		RECIPIENT_STATUS_WAIT_TO_OPEN: 61,
		RECIPIENT_STATUS_REACH_DEADLINE: 62,
		RECIPIENT_STATUS_WORK_IN_PROGRESS: 63,
		RECIPIENT_STATUS_VIEWED: 64,
		RECIPIENT_STATUS_RECIPIENT_REJECTED: 65,
		RECIPIENT_STATUS_FINALIZED: 67,
		RECIPIENT_STATUS_CANCELED: 68,

		RETENTION_TYPE_RETAIN_ONLY: 75,
		RETENTION_TYPE_RETAIN_AND_PURGE: 76,
		RETENTION_TYPE_PURGE_ONLY: 77,

		RETENTION_METHOD_PURGE_DOCUMENT_AFTER_DAYS: 14,

		INVITATION_AUTHENTICATION_TYPE_EMAIL: 131,
		INVITATION_AUTHENTICATION_TYPE_SMS: 132,

		FILE_TYPE_PDF: 151,
		FILE_TYPE_DOC: 152,
		FILE_TYPE_DOCX: 153,
		FILE_TYPE_JPG: 154,
		FILE_TYPE_PNG: 155,
		FILE_TYPE_XLS: 156,
		FILE_TYPE_TXT: 157,

		TAG_TYPE_SIGNATURE: 250,
		TAG_TYPE_INITIAL: 251,
		TAG_TYPE_TEXT: 252,
		TAG_TYPE_CHECKBOX: 253,
		TAG_TYPE_RADIO: 254,
		TAG_TYPE_DATE: 255,
		TAG_TYPE_ATTACHMENT: 256,
		TAG_TYPE_DROPDOWN: 257,
		TAG_TYPE_NOTE: 258,
		TAG_TYPE_ADDRESS: 259,
		TAG_TYPE_ADDRESS_SUB_DETAIL: 260,
		TAG_TYPE_ADDRESS_SUB_SUITE: 261,
		TAG_TYPE_ADDRESS_SUB_CITY: 262,
		TAG_TYPE_ADDRESS_SUB_PROVINCE: 263,
		TAG_TYPE_ADDRESS_SUB_COUNTRY: 264,
		TAG_TYPE_ADDRESS_SUB_POSTCODE: 265,
		TAG_TYPE_MASKED_TEXT: 266,
		TAG_TYPE_PREFILLABLE_TEXT: 267,
		TAG_TYPE_PREFILLABLE_NUMBER: 268,
		TAG_TYPE_NUMBER: 269,
		TAG_TYPE_SIGN_DATE: 270,

		//Fake code, does not exist on DB
		TAG_TYPE_PAGEVIEWEVIDENCE: -100,

		BRANDING_SMTP_PROVIDER_TYPE_SIGNORITY_STANDARD_SMTP: 400,
		BRANDING_SMTP_PROVIDER_TYPE_MAILJET_SMTP: 401,
		BRANDING_SMTP_PROVIDER_TYPE_REGULAR_SMTP: 402,

		CERTIFICATE_IN_PROGRESS: 500,
		CERTIFICATE_COMPLETED: 501,
		CERTIFICATE_REVOKED: 502,

		DOCUMENT_PKI_SIGNING_STATUS_IN_PROGRESS: 521,
		DOCUMENT_PKI_SIGNING_STATUS_COMPLETED: 522,

		INVITATION_PKI_SIGNING_STATUS_IN_PROGRESS: 531,
		INVITATION_PKI_SIGNING_STATUS_COMPLETED: 532,

		SECURITY_QUESTION_MOTHER_MAIDEN_NAME: 1000,
		SECURITY_QUESTION_FIRST_PET_NAME: 1001,
		SECURITY_QUESTION_FAVORITE_COLOR: 1002,
		SECURITY_QUESTION_STREET_NAME: 1003,
		SECURITY_QUESTION_CITY_MET_SPOUSE: 1004,
		SECURITY_QUESTION_CHILDHOOD_FRIEND: 1005,
		SECURITY_QUESTION_CHILDHOOD_NICKNAME: 1006,
		SECURITY_QUESTION_OLDEST_SIBLING: 1007,
		SECURITY_QUESTION_FIRST_JOB_CITY: 1008,
		SECURITY_QUESTION_FAV_CHILDHOOD_TEACHER: 1009,
		SECURITY_QUESTION_NAME_FIRST_STUFFED_ANIMAL: 1011,
		SECURITY_QUESTION_CITY_MOTHER_FROM: 1012,
		SECURITY_QUESTION_FIRST_KISS_CITY: 1013,
		SECURITY_QUESTION_WEDDING_PLACE: 1014,
		SECURITY_QUESTION_COLLEGE_APPLIED_NOT_ATTEND: 1015,
		SECURITY_QUESTION_HONEYMOON_CITY: 1016,
		SECURITY_QUESTION_DREAM_VACATION: 1017,
		SECURITY_QUESTION_GRADUATION_YEAR: 1018,

		STATUS_PENDING: 1,
		STATUS_ACTIVE: 2,
		STATUS_SUSPEND: 3,
		STATUS_DELETE: 4,

		questions: {
			1000: 'signority.questions.101000',
			1001: 'signority.questions.101001',
			1002: 'signority.questions.101002',
			1003: 'signority.questions.101003',
			1004: 'signority.questions.101004',
			1005: 'signority.questions.101005',
			1006: 'signority.questions.101006',
			1007: 'signority.questions.101007',
			1008: 'signority.questions.101008',
			1009: 'signority.questions.101009',
			1011: 'signority.questions.101011',
			1012: 'signority.questions.101012',
			1013: 'signority.questions.101013',
			1014: 'signority.questions.101014',
			1015: 'signority.questions.101015',
			1016: 'signority.questions.101016',
			1017: 'signority.questions.101017',
			1018: 'signority.questions.101018'
		},
		roles: {
			REGULAR_USER: 1,
			TEAM_BILLING_ADMIN: 2,
			TEAM_ADMIN: 3,
			TEAM_SUPERADMIN: 4,
			TEAM_DOCADMIN: 5
		},
		product_ids: {
			Lite: 10101,		//	personal
			Business_Plan: 310,
			Starter: 10201,		//	biz
			Pro: 10301,
			Plus: 10601,
			Premium: 11101,
			Vip: 11601
		},
		integration: {
			google: 1,
			dropbox: 2,
			s3: 3,
			one: 4,
			sftp: 5
		},
		report_type: {
			user: 0,
			team: 1,
			org: 2
		},

		fssCodes: [53,54,55,56],
		fssRetainTypes: [75,76]
	}
