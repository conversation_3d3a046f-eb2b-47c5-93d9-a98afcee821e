export interface UserRoot {
  user: User
  usageInfo: UsageInfo
  newFeatures: NewFeatures
  documentInfo: DocumentInfo
  accountdetails: Accountdetails
  success: boolean
  notificationMessages: NotificationMessage[]
  messagesCount: number
}
export interface NotificationMessage {
  docTitle: string
  documentId: number
  recipientName: string
  recipientEmail: string
  recipientInvitationSequence: number
  numberOfRecipientsHaveSameInvitationSequence: number
  recipientActionType: number
  actionCode: number
  rejectionReason: string
  actionTime: number
  documentSigningStatus: number
}


export interface UsageInfo {
  usageOfUser: number
  usageOfTeam: number
  usageOfOrg: number
}

export interface NewFeatures {
  nums: number
  show: boolean
  url: string
}

export interface DocumentInfo {
  inProgressCount: number
  awaitSignatureCount: number
  draftCount: number
  avergeCycleTimeFromSentToFinalizedLast60Days: number
  completedDocumentsCount: number
  docForViewCount: number
}

export interface Accountdetails {
  teamTransactionUsage: number
  teamAnnualTransactionUsage: number
  branding: Branding
  firstname: string
  email: string
  lastname: string
  productid: number
  productname: string
  userproductid: number
  status: string
  transactionlimit: number
  storagelimit: number
  TemplateLinkTransactionLimit: number
  BulkSignTransactionLimit: number
  transactionusage: number
  annualTransactionUsage: number
  cycle_begin: string
  cycle_end: string
  UsageThisCycle: number
  templateLinkUsage: number
  bulkSignUsage: number
  storageusage: number
  FeaturePKIEnabled: boolean
  FeatureApiKeyEnabled: boolean
  BulksignEnabled: boolean
  hsmDigitalSignatureEnabled: boolean
  hsmDigitalSignatureEnabledOnFiles: boolean
  conditionalTagEnabled: boolean
  IsThisATeamProduct: boolean
  userGroupRoleId: number
  smsSignerAuthenticationEnabled: boolean
  templateEnabled: boolean
  templateLinkEnabled: boolean
  signerAttachmentEnabled: boolean
  witnessSigningEnabled: boolean
  pageViewEvidenceEnabled: boolean
  templateOverlayEnabled: boolean
  smtpCustomizationEnabled: boolean
  dualTermOfServiceEnabled: boolean
  shareAddressBookEnabled: boolean
  emailAuthenticationEnabled: boolean
  teamTemplateDocumentSharingEnabled: boolean
  recipientNoteEnabled: boolean
  addTagsFromPdfFormFieldsEnabled: boolean
  addTagsFromPdfAnchorTextEnabled: boolean
  blindCarbonCopyEnabled: boolean
  isRetentionEnabled: boolean
  retentionMaxPeriodInDays: number
  retentionPeriodInDays: number
  billingCycle: string
  nextduedate: string
  stripeStatus: string
  stripeTrialStart: string
  stripeTrialEnd: string
  isTeampPlan: boolean
  hasgroup: boolean
  groupid: number
  groupname: string
}

export interface Branding {
  id: number
  UseSystemDefaultBrandingForTeam: boolean
  CompanyName: string
  CompanyLogo: string
  CompanyLogoWhite: string
  SMTPProviderType: number
  SMTPHost: string
  SMTPPort: string
  SMTPEmail: string
  SMTPUserName: string
  SMTPPassword: string
  SMTPEncryptionType: string
  EmailFooter: string
  EmailSignature: string
  PdfEncryptionEnabled: boolean
  TeamTermOfServiceEnabled: boolean
  WholeSiteDocumentOwnerTermOfService: string
  WholeSiteSignerTermOfService: string
  ContactsSharingEnabled: boolean
  DocSharingEnabled: boolean
  TemplateSharingEnabled: boolean
  ESignEnabled: boolean
  DSignEnabled: boolean
  ESignPoweredByEnabled: boolean
  ESignDocWithAuditTrailEnabled: boolean
  NotificationReminderEmailEnabled: boolean
  DefaultReminderEmailIntervalDays: number
  DefaultMaxReminderEmailCount: number
  DefaultExpireDaysAfterSent: number
  NotificationExpiredDocEmailEnabled: boolean
  DefaultLastReminderDaysBeforeExpire: number
  SyncEnabled: boolean
  oauth_passed: number
  SyncType: number
  SyncFileFormat: number
  TeamTwoFAType: boolean
  isRetentionEnabled: boolean
  RetentionPeriodInDays: number
  RetentionType: number
  isRetentionUserSettingEnabled: boolean
  defaultEmailSubject: string
  isIPWhiteLabelingEnabled: boolean
  isIPWhiteLabelingDisableForAdmins: boolean
  RetentionSaveBeforePurge: boolean
  DefaultTextHeight: number
  DefaultTextWidth: number
  DefaultDateHeight: number
  DefaultDateWidth: number
  TimeZones: string
  defaultSendReminderPeriodUnit: number
  defaultWarnSenderPeriodUnit: number
  WarnUsageEightyPercent: boolean
  WarnUsageNumDocs: number
}

export interface User {
  status: string
  email: string
  firstname: string
  lastname: string
  address1: string
  address2: string
  city: string
  state: string
  postcode: string
  country: string
  bms: number
  phonenumber: string
  companyname: string
  id: number
  datecreated: Datecreated
  credit: number
  userSetting: UserSetting
  userSettingsSecurity: UserSettingsSecurity
  currency: Currency
  userProduct: UserProduct
  team2fa: number
  teamname: string
  oneTime: boolean
  roles: Roles
  defaultEmailSubject: string
  FeaturePKIEnabled: boolean
  FeatureApiKeyEnabled: boolean
  BulksignEnabled: boolean
  DefaultDateFormat: string
  TimeZones: string
  rootGroupId: number
}

export interface Datecreated {
  date: number
  day: number
  hours: number
  minutes: number
  month: number
  nanos: number
  seconds: number
  time: number
  timezoneOffset: number
  year: number
}

export interface UserSetting {
  id: number
  GUID: string
  EmailAddressConfirmed: boolean
  PkiEnabled: boolean
  TimeZones: string
  DefaultExpireDaysAfterSent: number
  DefaultReminderEmailIntervalDays: number
  DefaultMaxReminderEmailCount: number
  DefaultLastReminderDaysBeforeExpire: number
  HelpInstructionEnabled: boolean
  NotificationReminderEmailEnabled: boolean
  NotificationRecipientViewDocEmailEnabled: boolean
  NotificationRecipientSignDocEmailEnabled: boolean
  NotificationFinalEmailWithAttachmentEmailEnabled: boolean
  CombineFinalEmailAttachmentsIntoOnePDFFile: boolean
  AttachDocCertificateToTheFinalEmailAttachment: boolean
  NotificationExpiredDocEmailEnabled: boolean
  NotificationFinalEmailWithAttachmentToAllRecipientsEnabled: boolean
  NotificationFinalEmailWithPDFDownloadLinkToAllRecipientsEnabled: boolean
  NotificationFinalEmailWithPDFDownloadLinkToSenderEnabled: boolean
  NotificationNoFinalEmailToRecpientsEnabled: boolean
  NotificationNoFinalEmailToSenderEnabled: boolean
  NotificationEmailSignature: string
  APIKey: string
  authKey: string
  LanguageLocalization: number
  ShareAddressBookToTeam: boolean
  ShareAutomaticallyDocToTeam: boolean
  ShareAutomaticallyTemplateToTeam: boolean
  DefaultDateFormat: string
  hideButtonChangeSigner: boolean
  hideButtonDownload: boolean
  hideButtonHelp: boolean
  hideButtonReject: boolean
  hideButtonSave: boolean
  hideButtonStatus: boolean
  hideButtonThumbnail: boolean
  hideSigningButtonDrawingSignature: boolean
  hideSigningButtonTypeSignature: boolean
  hideSigningButtonUploadSignature: boolean
  hideViewDocumentButton: boolean
  ContactsSharingEnabled: boolean
  DocSharingEnabled: boolean
  ESignEnabled: boolean
  DSignEnabled: boolean
  ESignPoweredByEnabled: boolean
  ESignDocWithAuditTrailEnabled: boolean
  NotificationCC: string
  isRetentionEnabled: boolean
  RetentionPeriodInDays: number
  RetentionType: number
  RetentionSaveBeforePurge: boolean
  defaultSendReminderPeriodUnit: number
  defaultWarnSenderPeriodUnit: number
  SendInvitationEmail: boolean
  blindCarbonCopy: BlindCarbonCopy
}

export interface BlindCarbonCopy {
  bccEmail: string
}

export interface UserSettingsSecurity {
  id: number
  TwoFAPhoneCountryCode: string
  TwoFAType: number
  TwoFADontAskAgain: number
  IsEnableNewDeviceLogin: number
  LoginFailureCounter: number
}

export interface Currency {
  code: string
  id: number
  prefix: string
  suffix: string
}

export interface UserProduct {
  id: number
  product: number
  name: string
  billingcycle: string
  nextduedate: string
  begindate: string
  status: string
  amount: number
  deductionamount: number
  apiKeyEnabled: boolean
  currency: Currency2
  stripeStatus: string
  stripeTrialStart: number
  stripeTrialEnd: number
  hsmEnabledOnFiles: boolean
  conditionalTagEnabled: boolean
  docSize: number
}

export interface Currency2 {
  code: string
  id: number
  prefix: string
  suffix: string
}

export interface Roles {
  id: number
  keyName: string
  displayName: string
}
