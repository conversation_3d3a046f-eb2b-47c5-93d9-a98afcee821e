export * from './enums'
import {User} from './user'
export * from './user'
export * from './document'
export {User}
export * from './code'


export type Role = "REGULAR_USER" | "BILLING_ADMIN" | "TEAM_ADMIN" | "DOC_ADMIN" | "SUPER_ADMIN";

export const Roles : Record<Role, number> ={
    REGULAR_USER:1,
    BILLING_ADMIN:2,
    TEAM_ADMIN:3,
    SUPER_ADMIN:4,
    DOC_ADMIN:5
}
export const AllRoles = Object.keys(Roles) as Array<Role>




export const Plans = ["team" , "enterprise" , "individual"] as const;
export type Plan = typeof Plans[number];

// export const Plans : Record<Plan, number> ={
//     team: 1,
//     enterprise : 2,
//     individual: 3
// }


export const ConsoleTypes = ["admin-console" , "user"] as const;
export type ConsoleType = typeof ConsoleTypes[number];


