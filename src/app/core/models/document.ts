import {User} from "@core/models/user";

export interface DocumentRoot {
  documents: Document[]
  documentCount: number
  success: boolean
}

export interface Document {
  id: number
  GUID: string
  user: User
  title: string
  signingStatus: number
  eSigningStatus: number
  version: number
  createdTime: number
  lastModifiedTime: number
  documentSentTime: number
  documentType: number
  deleted: boolean
  isPublished: boolean
  isRetentionEnabled: boolean
  RetentionStartTimestamp: number
  RetentionPeriodInDays: number
  RetentionType: number
  templateLink: boolean
  pkiEnabled: boolean
  hsmDigitalSignEnabled: boolean
  sharedToTeam: boolean
  hasBouncedEmails: boolean
  folderId: number
  signerCount: number
  firstRecipientName: string
  DownloadSeparateFilesEnabled: boolean
  ESignDocWithAuditTrailEnabled: boolean
  DocumentFileNumbers: number
}
// table top level views
export type tableType = "document" | "template" | "inbox" | "manage";
