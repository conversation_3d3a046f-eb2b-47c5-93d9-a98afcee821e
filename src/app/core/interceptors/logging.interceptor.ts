
import { Injectable } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';


import {
    HttpRequest,
    HttpHandler,
    HttpEvent,
    HttpInterceptor,
    HttpResponse, HttpErrorResponse
} from '@angular/common/http';
import { Observable, tap, finalize } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements HttpInterceptor {

    constructor(
        private modal: NzModalService
    ) {}

    intercept(req: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
        const started = Date.now();
        let ok: string;
        let response:HttpResponse<any> | HttpErrorResponse;
        // extend server response observable with logging
        return next.handle(req)
            .pipe(
                tap({
                    // Succeeds when there is a response; ignore other events
                    next: (event) => {
                        ok = 'succeeded'
                        if(event instanceof HttpResponse){
                            response = event;
                        }
                    },
                    // Operation failed; event is an HttpErrorResponse
                    error: (event) => {
                        ok = 'failed'
                        response = event
                    }
                }),
                // Log when response observable either completes or errors
                finalize(() => {
                    const elapsed = Date.now() - started;
                    const msg = `%c${req.method} ${req.urlWithParams} ${ok} in ${elapsed} ms.`;
                    switch (ok){
                        case "succeeded":{
                            console.log(msg, 'color:green');
                            break;
                        }
                        case "failed":{
                            console.log(msg, 'color:red');
                            break;
                        }
                    }
                    console.log('%cResponse ', 'color:#017BC6', response)
                    if(response.status == 401) {
                        this.modal.error({
                            nzTitle: $localize `You are not logged in`,
                            nzOkText: $localize `Login here`,
                            nzOnOk: ()=> {
                                 const win: Window = window;
                                 win.location = "/UI";
                            }
                        });
                       
                    }

                })
            );
    }
}
