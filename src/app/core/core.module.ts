import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';



import {HTTP_INTERCEPTORS, HttpClientModule} from "@angular/common/http";
import {RouterModule} from "@angular/router";

import {ApiService} from "@core/services/api.service";
import {MediaService} from "@core/services/media.service";
import {UserService} from "@core/services/user.service";
import {UrlService} from "@core/services/url.service";


import { CookieService } from 'ngx-cookie-service';

import {LoggingInterceptor} from "@core/interceptors/logging.interceptor";
import {AuthGuard} from "@core/guards/auth.guard";
import {AdminGuard} from "@core/guards/admin.guard";
import {NgHttpCachingModule} from "ng-http-caching";
import {BrandingService, CardService, DocumentService} from "@core/services";
import { ReportService } from './services/report.service';

/** Http interceptor providers in outside-in order */
export const httpInterceptorProviders = [
    // { provide: HTTP_INTERCEPTORS, useClass: CachingInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: LoggingInterceptor, multi: true },
];
@NgModule({
    imports: [
        CommonModule,
        HttpClientModule,
        RouterModule,
        NgHttpCachingModule
    ],
    providers:[
        httpInterceptorProviders,
        MediaService,
        ApiService,
        UserService,
        DocumentService,
        BrandingService,
        UrlService,
        CardService,
        CookieService,
        UrlService,
        AuthGuard,
        AdminGuard
    ],
})
export class CoreModule { }
