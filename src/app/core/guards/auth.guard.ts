import { Injectable } from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {Observable, tap} from 'rxjs';
import {UserService} from "@core/services";

import {UrlService} from "@core/services/url.service";

@Injectable({
    providedIn: 'root'
})
export class AuthGuard implements CanActivate {
    constructor(
       private userService:UserService,
       private urlService:UrlService,
       private router:Router
    ) {}

    canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {

        return this.userService.isLogin().pipe(
            tap(
                isLogin => {
                    if (!isLogin){
                        console.log("Haven't Login yet")
                        // this.urlService.setPreviousUrl(state.url)


                        // doesn't work since it tries to look up UI in angular routes
                        // this.router.navigateByUrl('/UI', {skipLocationChange: true}).then()
                        window.location.href="/UI"
                    }
                }
            )
        )

    }

}
