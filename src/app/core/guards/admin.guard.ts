import { Injectable } from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {Observable, tap} from 'rxjs';
import {UserService} from "@core/services";

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
    constructor(
        private router:Router,
        private userService:UserService
    ) {
    }


  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
     return this.userService.isAdmin().pipe(
            tap(
                isLogin => {
                    if (!isLogin){
                        console.log("Admin guard: No access ")
                        this.router.navigate(['/login']).then()
                    }
                }
            )
        )
  }

}
