import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class FocusService {
  private getRestoreElement: (() => HTMLElement | null) | null = null;

  saveFocus(getter: () => HTMLElement | null) {
    this.getRestoreElement = getter;
    console.log('[FocusService] Saved focus getter');
  }

  restoreFocus() {
    if (this.getRestoreElement) {
      const el = this.getRestoreElement();
      if (el) {
        console.log('[FocusService] Restoring focus to:', el);
        el.focus();
      } else {
        console.log('[FocusService] Could not find element to restore focus to.');
      }
      this.getRestoreElement = null;
    }
  }

  restoreFocusWithRetry(selector: string, maxAttempts = 10, delay = 50) {
    let attempts = 0;
    const tryFocus = () => {
      const el = document.querySelector(selector) as HTMLElement;
      console.log(`[FocusService] Attempt ${attempts + 1}:`, el);
      if (el) {
        el.focus();
        console.log('[FocusService] Focused after retry:', el);
      } else if (attempts < maxAttempts) {
        attempts++;
        setTimeout(tryFocus, delay);
      } else {
        console.log('[FocusService] Could not restore focus after retries.');
      }
    };
    tryFocus();
  }
} 