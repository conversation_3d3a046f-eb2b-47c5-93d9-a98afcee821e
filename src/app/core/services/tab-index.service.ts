import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface TabIndexState {
  currentIndex: number;
  totalElements: number;
  lastFocusedElement: string | null;
  tabOrder: string[]; // Array of element IDs in tab order
  currentTabIndex: number; // Position in the tab order
}

@Injectable({
  providedIn: 'root'
})
export class TabIndexService {
  private tabIndexState = new BehaviorSubject<TabIndexState>({
    currentIndex: 0,
    totalElements: 0,
    lastFocusedElement: null,
    tabOrder: ['homepage-logo'], // Start with logo as first element
    currentTabIndex: 0
  });

  public tabIndexState$ = this.tabIndexState.asObservable();

  constructor() {
    // Initialize with logo as the first focusable element
    this.resetToLogo();
  }

  /**
   * Reset tab index to start from logo (homepage link)
   */
  resetToLogo(): void {
    this.tabIndexState.next({
      currentIndex: 0,
      totalElements: 0,
      lastFocusedElement: 'logo',
      tabOrder: ['homepage-logo'],
      currentTabIndex: 0
    });
  }

  /**
   * Update tab index state when navigating
   * @param currentIndex Current tab index
   * @param totalElements Total focusable elements
   * @param lastFocusedElement ID or identifier of last focused element
   */
  updateTabIndex(currentIndex: number, totalElements: number, lastFocusedElement?: string): void {
    const currentState = this.tabIndexState.value;
    this.tabIndexState.next({
      currentIndex,
      totalElements,
      lastFocusedElement: lastFocusedElement || null,
      tabOrder: currentState.tabOrder,
      currentTabIndex: currentState.currentTabIndex
    });
  }

  /**
   * Get current tab index state
   */
  getCurrentState(): TabIndexState {
    return this.tabIndexState.value;
  }

  /**
   * Update the tab order and current position
   * @param elementId ID of the focused element
   * @param tabOrder Array of element IDs in tab order
   * @param currentIndex Current position in tab order
   */
  updateTabOrder(elementId: string, tabOrder: string[], currentIndex: number): void {
    const currentState = this.tabIndexState.value;
    this.tabIndexState.next({
      ...currentState,
      lastFocusedElement: elementId,
      tabOrder,
      currentTabIndex: currentIndex
    });
  }

  /**
   * Save current tab position to localStorage
   */
  saveTabPosition(): void {
    const state = this.getCurrentState();
    localStorage.setItem('tabIndexState', JSON.stringify(state));
  }

  /**
   * Load tab position from localStorage
   */
  loadTabPosition(): TabIndexState | null {
    const saved = localStorage.getItem('tabIndexState');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch {
        return null;
      }
    }
    return null;
  }

  /**
   * Set focus to the last focused element or logo if none
   */
  restoreFocus(): void {
    const state = this.getCurrentState();
    
    // Try to restore from saved position first
    const savedState = this.loadTabPosition();
    if (savedState && savedState.lastFocusedElement) {
      const element = document.getElementById(savedState.lastFocusedElement);
      if (element) {
        element.focus();
        return;
      }
    }
    
    // If no saved position or element not found, try current state
    if (state.lastFocusedElement) {
      const element = document.getElementById(state.lastFocusedElement);
      if (element) {
        element.focus();
        return;
      }
    }
    
    // Priority order: logo first, then any navigation element
    const logo = document.getElementById('homepage-logo');
    if (logo) {
      logo.focus();
      return;
    }
    
    // Final fallback: try to focus any navigation element
    const navElements = [
      'nav-dashboard',
      'nav-documents', 
      'nav-reports',
      'nav-templates',
      'nav-contacts',
      'nav-inbox',
      'quick-dashboard',
      'quick-documents',
      'quick-templates',
      'quick-contacts',
      'quick-inbox',
      'quick-reports',
      'quick-security-report',
      'quick-usage-report',
      'quick-cancel-report',
      'quick-admin-dashboard',
      'quick-admin-teams',
      'quick-admin-bulk',
      'quick-admin-plan',
      'quick-admin-branding',
      'quick-admin-plan-manage',
      'quick-admin-plan-payment',
      'quick-admin-plan-invoice',
      'quick-admin-settings',
      'quick-admin-settings-global',
      'quick-admin-settings-stamps',
      'quick-user-dashboard'
    ];
    
    for (const id of navElements) {
      const element = document.getElementById(id);
      if (element) {
        element.focus();
        return;
      }
    }
  }
} 