import { Injectable } from '@angular/core';
import {ApiService} from "@core/services/api.service";
import {map, Observable, of} from "rxjs";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { environment } from "@environment/environment";
import { NzModalService } from 'ng-zorro-antd/modal';

@Injectable({
    providedIn: 'root'
})
export class TeamSettingsService {


    constructor(
        private api:ApiService,
        private http: HttpClient,
        private modal: NzModalService
    ) {}

    get_group_setting(groupId:any):Observable<any>{
        return this.http.get(environment.NEW_API_URL + 'v1/group-setting/show?groupId=' + groupId + '&timestamp=' + new Date().getTime(),
            { headers: { sToken: this.api.getSToken() } }).pipe(
            map((res:any) => {
                if(!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }

    put_group_setting(formData:object):Observable<any>{
        return this.http.put(environment.NEW_API_URL + 'v1/group-setting/change',
            formData,
            { headers: { sToken: this.api.getSToken() } }).pipe(
            map((res:any) => {
                if(!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }

}
