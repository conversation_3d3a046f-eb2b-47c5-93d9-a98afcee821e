import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { map, Observable, of } from "rxjs";
import { NzModalService } from 'ng-zorro-antd/modal';
import { ApiService } from "@core/services/api.service";
import { environment } from '@environment/environment';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {

  constructor(
    private http: HttpClient,
    private modal: NzModalService,
    private apiService: ApiService,
  ) { }

  setPayment(token: any): Observable<any> {
    return this.http.post(environment.API_URL + 'v1/stripe/paymentSources',
      `token=${token.id}`,
      { headers: { 
        sToken: this.apiService.getSToken(),
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
       } }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }
  getPaymentSources(): Observable<any> {
    return this.http.get(environment.API_URL + 'v1/stripe/paymentSources?cache=' + Math.random(),
      { headers: {} }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }
  addPaymentSource(token:string): Observable<any> {
    return this.http.post(environment.API_URL + 'v1/stripe/paymentSources',
      `token=${token}`,
    { headers: { sToken: this.apiService.getSToken(), 'Content-Type': 'application/x-www-form-urlencoded' } }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }
  updatePaymentSource(paymentSource:any): Observable<any> {
    return this.http.post(environment.API_URL + 'v1/stripe/updatePaymentSource',
      paymentSource,
    { headers: { sToken: this.apiService.getSToken()} }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }

  payInvoice(id:number,paymentSourceId:string): Observable<any> {
    return this.http.put(environment.API_URL + 'v1/user/invoice/'+id,
      {paymentSource: paymentSourceId},
    { headers: { sToken: this.apiService.getSToken()} }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }

  deletePayment(cardId:number): Observable<any> {
    return this.http.delete(environment.API_URL + 'v1/stripe/paymentSources/'+cardId,
      { headers: { 
        sToken: this.apiService.getSToken(),
       } }).pipe(
        map((res: any) => {
          return res;
        })
      )
  }
  
  getInvoices(): Observable<any> {
    return this.http.get(environment.API_URL + 'v1/user/invoices?cache=' + Math.random(),
      { headers: { 
        sToken: this.apiService.getSToken(),
       } }).pipe(
        map((res: any) => {
          return res;
        })
      )
  }
  getInvoice(id:number): Observable<any> {
    return this.http.get(environment.API_URL + 'v1/user/invoice/'+id+'?cache=' + Math.random(),
      { headers: { 
        sToken: this.apiService.getSToken(),
       } }).pipe(
        map((res: any) => {
          return res;
        })
      )
  }
  getCoupon(id:string): Observable<any> {
    return this.http.get(environment.API_URL + 'v1/user/coupons/'+id+'?cache=' + Math.random(),
      { headers: { 
        sToken: this.apiService.getSToken(),
       } }).pipe(
        map((res: any) => {
          return res;
        })
      )
  }

  getPlans(): Observable<any> {
    return this.http.get(environment.API_URL + 'v1/products?cache=' + Math.random(),
      { headers: {} }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }
  changePlan(id:number, billingCycle:string, couponId:number, paymentSourceId:string): Observable<any> {
    let payload:any = {
      selectedPaymentSource: paymentSourceId,
      defaultSource: paymentSourceId,
    }
    if(couponId > 0) {
      payload.coupon= couponId;
    }
    return this.http.put(environment.API_URL + 'v1/user/products/'+id+'/billing/'+billingCycle,
      payload,
    { headers: { sToken: this.apiService.getSToken()} }).pipe(
        map((res: any) => {
          if (!res.success) {
            this.modal.error({
                nzTitle: res.userMsg,
            });
          }
          return res;
        })
      )
  }
}

