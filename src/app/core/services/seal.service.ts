import { Injectable } from '@angular/core';
import { map, Observable, of } from "rxjs";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { environment } from "@environment/environment";
import { NzModalService } from 'ng-zorro-antd/modal';
import {ApiService} from "@core/services/api.service";
import { NzUploadFile } from 'ng-zorro-antd/upload';


@Injectable({
    providedIn: 'root'
})
export class SealService {


    constructor(
        private http: HttpClient,
        private modal: NzModalService,
        private apiService:ApiService,

    ) { }

    addSeal( provider: string,
        oAuthClient: string,
        oAuthClientSecret: string,
        credentialId: string,
        credentialPin: string,
        name: string,
        email: string,
        password: string,
        status: number,
        displaySubject: boolean,
        displayDate: boolean,
        displayTime: boolean,
        displayReason: boolean,
        reason: string,
        img:NzUploadFile,
        imgData:any,
        encrypted:boolean,
        users: number[],
        teams: number[]): Observable<any> {

        return this.http.post(environment.API_URL + 'v1/seal',
            {
                sealName: name,
                provider: provider ? provider : "",
                oAuthClientId: oAuthClient ? oAuthClient : "",
                oAuthClientSecret: oAuthClientSecret ? oAuthClientSecret : "",
                credentialId: credentialId ? credentialId : "",
                credentialPIN: credentialPin ? credentialPin : "",
                sealStatus:status,
                displayEmail:email,
                password,
                displaySubject,
                displayDate,
                displayTime,
                displayReason,
                enterReasonForSeal:reason ? reason : "",
                userIds:users, 
                teamIds:teams, 
                img:JSON.stringify(img),
                imgDate: imgData,
                encrypted
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.result) {
                        this.modal.error({
                            nzTitle: res.msg,
                        });
                    }
                    return res;
                })
            )
    }

    listSeals(): Observable<any> {
        return this.http.get(environment.API_URL + 'v1/seal/list?timestamp=' + new Date().getTime(),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.result) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    editSeal( 
        id: number,
        provider: string,
        oAuthClient: string,
        oAuthClientSecret: string,
        credentialId: string,
        credentialPin: string,
        name: string,
        email: string,
        password: string,
        status: number,
        displaySubject: boolean,
        displayDate: boolean,
        displayTime: boolean,
        displayReason: boolean,
        reason: string,
        img:NzUploadFile,
        imgData:any,
        users: number[],
        teams: number[]): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/seal',
            {
                id,
                sealName: name,
                provider: provider ? provider : "",
                oAuthClientId: oAuthClient ? oAuthClient : "",
                oAuthClientSecret: oAuthClientSecret ? oAuthClientSecret : "",
                credentialId: credentialId ? credentialId : "",
                credentialPIN: credentialPin ? credentialPin : "",
                sealStatus:status,
                displayEmail:email,
                password,
                displaySubject,
                displayDate,
                displayTime,
                displayReason,
                enterReasonForSeal:reason ? reason : "",
                userIds:users,
                teamIds:teams,
                img:JSON.stringify(img),
                imgDate: imgData
            },
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
                map((res: any) => {
                    if (!res.result) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    deleteSeals(ids: number[]): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/seal',
                {
                    body: ids,
                    headers: { sToken: this.apiService.getSToken() }
                }).pipe(
                map((res: any) => {
                    if (!res.result) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }


}
