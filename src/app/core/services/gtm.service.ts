import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({providedIn: 'root'})
export class GTMService {
    constructor(private _router: Router) {}

    pageView(pathOverride?: string) {
        (<any>window).dataLayer.push({
            event: 'virtualPageview',
            virtualPageURL: pathOverride || this._router.url,
            //TODO: virtualPageTitle is needed for Google Analytics but app does not have it
            //      at this stage. Page titles need to be considered in the future.
            virtualPageTitle: pathOverride || this._router.url,
        });
    }
}