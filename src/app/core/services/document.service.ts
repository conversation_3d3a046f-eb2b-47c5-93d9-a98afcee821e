import { Injectable } from '@angular/core';
import {ApiService} from "@core/services/api.service";
import {map, Observable, startWith, Subject} from "rxjs";
import {Code, Document, DocumentRoot, User} from "@core/models";
import {UserService} from "@core/services/user.service";
import {minutesToReadible} from "@core/services/utilities.service";
import { NzModalService } from 'ng-zorro-antd/modal';
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { environment } from "@environment/environment";


export interface Template{
    title:string
    link:string
}

export interface DocumentInfo{
    draft:number
    in_progress:number
    finalized:number
    canceled:number
    expired:number
    avg_time_to_sign:string
}




@Injectable({
  providedIn: 'root'
})
export class DocumentService {

    user?:User
    //send messages between document compoent siblings
    private currentFolderId = new Subject<number>();
    currentFolderId$ = this.currentFolderId.asObservable();

    constructor(
        private apiService:ApiService,
        private userService:UserService,
        private modal: NzModalService,
        private http: HttpClient,


    ) {
        userService.getUser().subscribe(u => this.user = u)
    }

    changeFolder(folderId: number) {
        this.currentFolderId.next(folderId);
      }


    getTemplates():Observable<Template[]>{
      return this.apiService.get('v2/documents/search2?type=regular_template&pageIndex=1&pageSize=5&orderBy=ModifiedDate&orderSeq=DESC').pipe(
            map(res => {
                res =  <DocumentRoot>res

                return res.documents.map( (d:Document) => {
                     return {title:d.title, link: `/UI/documentDesigner2.html?id=${d.id}`}
                })
            })
        )
    }

    getDocumentInfo(scope:number=0):Observable<DocumentInfo>{
        // When type==0, only the statistics of the current user will be queried. 
        // when type==1, the statistics of the team will be queried according to the current user.
        // When type==2, the user will be queried Statistics for your company.
        return this.apiService.get('v1/document/report/count?type='+scope).pipe(
            map(res => {
                let info = {} as DocumentInfo
                info.draft = res.draftCount
                info.in_progress = res.inProgressCount
                info.finalized = res.completedDocumentsCount
                info.canceled = res.cancelCount
                info.expired = res.expiredCount
                
                let readibleTime = minutesToReadible(res.avergeCycleTimeFromSentToFinalizedLast60Days);
                if (readibleTime)  {
                    info.avg_time_to_sign = readibleTime;
                }
                
                return info
            })
        )
    }

    /** copied from Header2.js where it has
     * $('.button_badge').html(mydocument.awaitSignatureCount + mydocument.docForViewCount);
     */

    get_inbox_badge_count():Observable<number>{
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => {
                return res.documentInfo.awaitSignatureCount + res.documentInfo.docForViewCount;
            })
        )
    }


    create_new_doc(document:any):void{
        if (this.user!.hasOwnProperty("defaultEmailSubject") && this.user!.defaultEmailSubject != "") {
			document.emailSubject = this.user!.defaultEmailSubject;
		} else {
            let part1 = "has sent you the document"
            let part2 = "for Signing"
			document.emailSubject = this.user!.firstname + ' ' + this.user!.lastname + ' ' + part1 + ' ' + document.title + ' ' + part2;
		}
        this.apiService.post('v1/documents', {document: document}).subscribe(
            data => {
                console.log("document service, ", data)
                console.log( document.documentType,Code.DOCUMENT_TYPE_PACKAGE_TEMPLATE)
                if (!data.success) {
                    this.modal.error({
                        nzTitle: data.message,
                    });
                }
                if (document.documentType && document.documentType !== Code.DOCUMENT_TYPE_PACKAGE_TEMPLATE) {
                    window.location.href = ("/UI/documentDesigner2.html?id=" + data.document.id);
				}
				else {
					window.location.href = ("/UI/newDocFileUpload.html?id=" + data.document.id);
				}
            }
        )
    }

    getDocuments(type:String, recipientStatus:string, pageIndex:number, pageSize:number,orderBy:string, orderSeq:string, status:string, deleted:boolean, searchValue:string, folderId:number): Observable<any> {
        let endpoint = environment.API_URL + 'v2/documents/search2?type='+type+'&recipientStatus='+recipientStatus+
        '&pageIndex='+pageIndex+'&pageSize='+pageSize+'&orderBy='+orderBy+'&orderSeq='+orderSeq+'&status='+status+'&deleted='+deleted;
        if(folderId >=0) endpoint += '&folderId='+folderId;
        if(searchValue) endpoint += '&searchValue=' + searchValue;
        endpoint += '&cache=' + Math.random();
        return this.http.get(endpoint,
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    updateDocuments(documents: any[]): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/documents/array',
            {
                documents: documents
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    updateTags(docId:number,tags:any[] ): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/documents/'+docId+'/tags',
            {
                tags: tags
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    restoreDocuments(documents: any[]): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/documents/array',
            {
                documents: documents,
                action: 'restoreDocument',
                requestAllDocumentsOfFolder: false
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    duplicateDocuments(body: any ): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/documents/array',
            {
               ... body
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    getDocumentById(id:number): Observable<any> {
        let url = environment.API_URL + 'v1/documents/'+id+'?cache=' + Math.random();

        return this.http.get(url,
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    deleteDocuments(documents: any[]): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/documents/array',
        {
            body: {
                documents: documents
            },
            headers: { sToken: this.apiService.getSToken() }
        }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    deleteDocumentsPermanently(documents: any[]): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/documents/array/purgeall',
        {
            body: {
                documents: documents
            },
            headers: { sToken: this.apiService.getSToken() }
        }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    pushToAnotherUser(documents: any[], email:string): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/documents/push?email='+email,
            {
                 documents
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                    
                })
            )
    }
    

    getFolders(isTemplate:boolean = false): Observable<any> {
        let url = environment.API_URL + 'v1/folders?cache=' + Math.random();
        if(isTemplate) {
            url = environment.API_URL + 'v1/folders/template?cache=' + Math.random();
        }
        return this.http.get(url,
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    createFolder(name: string, folderType:number): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/folders',
            {
                folder :{
                    name,
                    folderType
                }
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    renameFolder(name: string, id:number): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/folders/'+id,
            {
                folder :{
                    name,
                }
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    deleteFolder(id:number): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/folders/'+id,
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: $localize`You must move or delete all documents before deleting this folder`,
                        });
                    }
                    return res;
                })
            )
    }

}
