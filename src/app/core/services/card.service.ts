import { Injectable } from '@angular/core';
import {ApiService} from "@core/services/api.service";
import {UrlService} from "@core/services/url.service";

@Injectable({
  providedIn: 'root'
})
export class CardService {

    constructor(
        private api:ApiService,
        private url:UrlService
    ) {

      url.is_admin_console()

    }

    /**
     * Only customize should use this function
     *
     * Make a post request to api/rest/dict based on current page,
     *    { dictValue : array.ttoString , type: 2} if user dashboard,  otherwise { dictValue : array.ttoString , type: 3}
     * @param cards
     */
    update_cards(cards:string[]){}

    /**
     * dashboard and customized shortcut should call this function to get
     * Idea: function make an api call based on the current url,
     * if url.is_admin_console() is false, make a get request to api/rest/dict/2, if true then api/rest/dict/3
     * Then check if its empty array or not, if empty, then the current user haven't saved any previous layout setting
     * Then we call update_cards(default_cards()) and return default_cards()
     * If we get an non-empty array, we return that array
     */
    get_cards(){}

    default_cards(){
      // if(this.pageName==='admin-console'){
      //       switch (this.roleID){
      //           case Roles.SUPER_ADMIN:
      //               return ["Plan & Usage", "Document Report", "Billing" ,"Users", "Teams", "Shared Templates", "Branding", "Support Channels", "Recent Activities", "Add shortcut"]
      //           case Roles.TEAM_ADMIN:
      //               return ["Plan & Usage", "Document Report",  "Users", "Teams",  "Branding", "Support Channels", "Recent Activities", "Add shortcut"]
      //           case Roles.BILLING_ADMIN:
      //               return ["Plan & Usage", "Document Report", "Billing", "Support Channels", "Add shortcut" ]
      //           case Roles.DOC_ADMIN:
      //               return ["Plan & Usage", "Document Report",   "Shared Templates", "Support Channels", "Recent Activities", "Add shortcut"]
      //       }
      //   }
      //   if(this.isTeamPlan){
      //       if(this.roleID != Roles.REGULAR_USER){
      //           return ["Plan & Usage", "Document Report", "Shared Templates", "Support Channels", "Recent Activities", "Add shortcut"]
      //       }
      //       return ["Document Report", "Shared Templates", "Support Channels","Recent Activities", "Add shortcut"]
      //   }
      //   return ["Plan & Usage", "Billing","Document Report", "Billing", "Shared Templates", "Support Channels" , "Recent Activities"]

  }
}
