/**
 * Helper function that converts minutes and returns a human readible format.
 * This tool was used to test and validate the accuracy of the below formulas:
 * http://extraconversion.com/time/minutes
 * 
 * Translation reference for units:
 * https://github.com/unicode-org/cldr-json/blob/main/cldr-json/cldr-units-modern/main/en-CA/units.json
 * https://github.com/unicode-org/cldr-json/blob/main/cldr-json/cldr-units-modern/main/fr-CA/units.json
 * 
 * The narrow format was used as a standard for the units to allow us to keep
 * the return string as compact as possible.
 * 
 * TODO: In the future if the need arises, we can expand optional paramters
 *       to use different formats.
 * 
 * @param {number} minutes The number of minutes.
 * @returns {string} minutes in a human readible format or -- if nothing meaningful to display.
 */
export function minutesToReadible(minutes: number):string{
    const MINS_PER_YEAR    = 24 * 365 * 60, // 535,600
          MINS_PER_MONTH   = 24 * 30 * 60,  // 43,200
          MINS_PER_WEEK    = 24 * 7 * 60,   // 10,080
          MINS_PER_DAY     = 24 * 60,       // 1,440
          MINS_PER_HOUR    = 60,
          year_shorthand   = $localize `:@@year_shorthand:y`,
          month_shorthand  = $localize `:@@month_shorthand:m`,
          week_shorthand   = $localize `:@@week_shorthand:w`,
          day_shorthand    = $localize `:@@day_shorthand:d`,
          hour_shorthand   = $localize `:@@hour_shorthand:h`,
          minute_shorthand = $localize `:@@minute_shorthand:min`;

    var result = '--', // -- used to symbolize that the information is not available yet.
        y  = Math.floor(minutes / MINS_PER_YEAR),
        mo = Math.floor((minutes % MINS_PER_YEAR) / MINS_PER_MONTH),
        wk = Math.floor(((minutes % MINS_PER_YEAR) % MINS_PER_MONTH) / MINS_PER_WEEK),
        d  = Math.floor((((minutes % MINS_PER_YEAR) % MINS_PER_MONTH) % MINS_PER_WEEK) / MINS_PER_DAY),
        h  = Math.floor(((((minutes % MINS_PER_YEAR) % MINS_PER_MONTH) % MINS_PER_WEEK) % MINS_PER_DAY) / MINS_PER_HOUR),
        m  = Math.floor(((((minutes % MINS_PER_YEAR) % MINS_PER_MONTH) % MINS_PER_WEEK) % MINS_PER_DAY) % MINS_PER_HOUR);

    // Return minutes
    if (minutes < MINS_PER_HOUR) {
        result = m+minute_shorthand;
    }
    // Return hours and minutes
    else if (minutes < MINS_PER_DAY) {
        result = h+hour_shorthand;
        if (m > 0) {
            result += " "+m+minute_shorthand;
        }
    }
    // Return days and hours
    else if (minutes < MINS_PER_WEEK) {
        result = d+day_shorthand;
        if (h > 0) {
            result += " "+h+hour_shorthand;
        }
    }
    // Return weeks and days
    else if (minutes < MINS_PER_MONTH) {
        result = wk+week_shorthand;
        if (d > 0) {
            result += " "+d+day_shorthand;
        }
    }
    // Return months and weeks
    else if (minutes < MINS_PER_YEAR) {
        result = mo+month_shorthand;
        if (wk > 0) {
            result += " "+wk+week_shorthand;
        }
    }
    // Return years and months
    else if (minutes >= MINS_PER_YEAR) {
        result = y+year_shorthand;
        if (mo > 0) {
            result += " "+mo+month_shorthand;
        }
    }

    return result;
}

export function getCookie(name:any) {
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg))
        return unescape(arr[2]);
    else
        return null;
}