/**
 * All api calls is cached!
 * https://www.npmjs.com/package/ng-http-caching/v/13.0.10
 */


import { Injectable } from '@angular/core';
import { environment } from "@environment/environment";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import { serialize } from 'object-to-formdata';

@Injectable({
    providedIn: 'root'
})
export class ApiService {
    private sToken = '';
    constructor(
        private http: HttpClient
    ) { }

    get(path: string, params: HttpParams = new HttpParams()): Observable<any> {
        return this.http.get(`${environment.API_URL}${path}`, { params })
    }

    put(path: string, body: Object = {}): Observable<any> {
        return this.http.put(
            `${environment.API_URL}${path}`,
            serialize(body)
        )
    }

    getSToken() {
        if (environment.test)
            return environment.stoken
        else {
            if (this.sToken && this.sToken != '') {
                return this.sToken;
            }
            else {
                let name = "sToken=";
                let ca = document.cookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i].trim();
                    if (c.indexOf(name) == 0) { this.sToken = c.substring(name.length, c.length); }
                }
            }
            return this.sToken;
        }
    }

    post(path: string, body: Object = {}): Observable<any> {
        let header = new HttpHeaders()
        header.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
        // const header = {'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'}
        return this.http.post(
            `${environment.API_URL}${path}`,
            body
            // serialize(body)
            // {'headers': header}
        )
    }
    getQueryString(options: any) {
        let params = new URLSearchParams();
        for (let key in options) {
            if (options[key]) {
                params.set(key, options[key]);
            }
            else {
                params.set(key, '');
            }
        }
        return params.toString();
    }

}

