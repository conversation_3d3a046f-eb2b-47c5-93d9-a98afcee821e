import { Injectable } from '@angular/core';
import { map, Observable, of } from "rxjs";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { environment } from "@environment/environment";
import { NzModalService } from 'ng-zorro-antd/modal';
import {ApiService} from "@core/services/api.service";


@Injectable({
    providedIn: 'root'
})
export class ReportService {


    constructor(
        private http: HttpClient,
        private modal: NzModalService,
        private apiService:ApiService,

    ) { }

    // type 1 for usage report
    // type 2: security report 
    // type 3: cancel report 
    createReport(type: number, name: string, dateFrom: number, dateTo: number, dateIndex: string, user?: number, event?: number): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/report',
            {
                name: name,
                type: type,
                parameters: {
                    dateFrom: dateFrom,
                    dateTo: dateTo,
                    forType: event,
                    forUsers: user? [user]: null,
                    filterIndex: dateIndex
                }
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    deleteReports(ids: string): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/report?ids=' + ids,
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    renameReport(id: number, newName: string): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/report',
            {
                name: newName,
                id: id,
            },
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
                map((res: any) => {
                    if (!res.result.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    downloadReport(id:number) {
        window.open(environment.API_URL+'v1/report/download/'+id);
    }

    getReport(pageNumber: number, id: number): Observable<any> {
        return this.http.get(environment.API_URL + 'v1/report/data?&id=' + id + '&pageSize=10&page=' + pageNumber + '&timestamp=' + new Date().getTime(),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    // if (!res.seccuss) {
                    //     this.modal.error({
                    //         nzTitle: res.userMsg,
                    //     });
                    // }
                    return res;
                })
            )
    }

    listReports(pageNumber: number, type:number): Observable<any> {
        return this.http.get(environment.API_URL + 'v1/report/list?&type='+type+'&pageSize=10&page=' + pageNumber + '&timestamp=' + new Date().getTime(),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    getUsers(): Observable<any> {
        return this.http.get(environment.NEW_API_URL + 'v1/user/page-list?current=1&size=50&teamId=',
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res.data;
                })
            )
    }

    listBulkExports(pageNumber: number): Observable<any> {
        return this.http.get(environment.API_URL + 'v2/document/download/tasks?size=10&current=' + pageNumber + '&timestamp=' + new Date().getTime(),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    createExport(documentStatus: number, dateFrom: string, dateTo: string, exportToType: number, fileType: number, autoDelete: boolean): Observable<any> {
        return this.http.post(environment.API_URL + 'v2/document/download/task',
            {
                documentStatus: documentStatus,
                documentStartDate: dateFrom,
                documentEndDate: dateTo,
                exportToType: exportToType,
                fileType: fileType,
                automaticallyDelete: autoDelete
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    deleteExport(id: number): Observable<any> {
        return this.http.delete(environment.API_URL + 'v2/document/download/task/' + id,
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }

    getExport(id: number, current: number): Observable<any> {
        return this.http.get(environment.API_URL + 'v2/document/download/task/' + id + '?current='+current+'&size=10',
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: res.userMsg,
                        });
                    }
                    return res;
                })
            )
    }
    downloadExport(id: number): Observable<any> {
        return this.http.get(environment.API_URL + 'v2/document/download/task/export/'+id,
            { headers: { sToken: this.apiService.getSToken() }, responseType: 'blob' }).pipe(
                map((res: any) => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
                    const url= window.URL.createObjectURL(blob);
                    window.open(url,'_blank');
                })
            )
    }

    


}
