import { Injectable } from '@angular/core';
import {ApiService} from "@core/services/api.service";
import {map, Observable, of} from "rxjs";
import {Branding} from "@core/models";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { environment } from "@environment/environment";
import { NzModalService } from 'ng-zorro-antd/modal';

@Injectable({
    providedIn: 'root'
})
export class BrandingService {

    id:number= -1;

    constructor(
        private api:ApiService,
        private http: HttpClient,
        private modal: NzModalService
    ) {
        api.get('v1/user').subscribe(v => this.id = v.user.brandingId)
    }



    get_logo():Observable<string>{
        if(this.id === -1){
            return of("")
        }
        return this.api.get(`v1/brandings/${this.id}`).pipe(
            map(
                v => {
                    let res = <Branding> (v.branding)
                    if(res.UseSystemDefaultBrandingForTeam) {
                        return ""
                    }
                    return res.CompanyLogo
                }
            )
        )
    }

    get_branding_setting():Observable<any>{
        return this.http.get(environment.NEW_API_URL + 'v1/branding/?timestamp=' + new Date().getTime(),
            { headers: { sToken: this.api.getSToken() } }).pipe(
            map((res:any) => {
                if(!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }

    put_branding_setting(formData:object):Observable<any>{
        return this.http.put(environment.NEW_API_URL + 'v1/branding/',
            formData,
            { headers: { sToken: this.api.getSToken() } }).pipe(
            map((res:any) => {
                if(!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }

    put_test_email(formData:object):Observable<any>{
        return this.http.put(environment.API_URL + 'v1/brandings/test',
            {branding: formData},
            { headers: { sToken: this.api.getSToken() } }).pipe(
            map((res:any) => {
                if(!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

}
