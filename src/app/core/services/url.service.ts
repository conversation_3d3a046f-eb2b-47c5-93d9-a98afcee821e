import { Injectable } from '@angular/core';
// import { BreakpointObserver } from '@angular/cdk/layout';
import {Observable, map, BehaviorSubject, filter, startWith} from "rxjs";
import {ResolveEnd, Router} from "@angular/router";

@Injectable({
    providedIn: 'root'
})
export class UrlService {

    bSubject = new BehaviorSubject(this.url_is_admin_console(this.router.url));

    constructor(
         private router:Router
    ) {
      this.router.events.pipe(
             filter(event => event instanceof  ResolveEnd),
             map( event => event as ResolveEnd),
             map( event => {
                 return this.url_is_admin_console(event.url)
             })
         ).subscribe(v => this.bSubject.next(v))
    }


    private previousUrl: BehaviorSubject<string> = new BehaviorSubject<string>('');
    public previousUrl$: Observable<string> = this.previousUrl.asObservable();
    setPreviousUrl(previousUrl: string) {
        this.previousUrl.next(previousUrl);
    }


    is_admin_console():Observable<boolean>{
        return this.bSubject;
    }

    url_is_admin_console(url:string):boolean{
        return url.split('/')[1] === 'admin-console'
    }

}
