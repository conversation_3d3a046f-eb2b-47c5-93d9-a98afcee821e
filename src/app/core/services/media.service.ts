import { Injectable } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { distinctUntilChanged, tap} from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class MediaService {

    readonly breakpoint$ = this.breakpointObserver
    .observe([Breakpoints.Large, Breakpoints.Medium, Breakpoints.Small, '(max-width: 640px)'])
    .pipe(
      tap(value => console.log(value)),
      distinctUntilChanged()
    );
    currentBreakpoint:string = '';

    constructor(
        private breakpointObserver : BreakpointObserver
    ) {
        this.breakpoint$.subscribe(() =>
          this.breakpointChanged()
        );

      }
      public breakpointChanged() {
        if(this.breakpointObserver.isMatched(Breakpoints.Large)) {
          this.currentBreakpoint = Breakpoints.Large;
        } else if(this.breakpointObserver.isMatched(Breakpoints.Medium)) {
          this.currentBreakpoint = Breakpoints.Medium;
        } else if(this.breakpointObserver.isMatched(Breakpoints.Small)) {
          this.currentBreakpoint = Breakpoints.Small;
        }
        return this.isMobile();
     }

     public isMobile(): boolean {   
        return this.breakpointObserver.isMatched('(max-width: 640px)');
     }

     public isMobileOrTablet(): boolean {   
        return this.breakpointObserver.isMatched('(max-width: 1024px)');
     }

}
