import { Injectable, OnInit } from '@angular/core';
import { environment } from "@environment/environment";
import { Observable, map, first, tap, filter } from "rxjs";
import { ApiService } from "@core/services";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { NzModalService } from 'ng-zorro-antd/modal';



import { Accountdetails, NotificationMessage, User, UserRoot } from '@core/models/user'
import { Roles } from "@core/models";
import { NgHttpCachingService } from "ng-http-caching";


export interface BillingCycle {
    start: string
    end: string
    days_left: number
    total_days: number
    status: string
    stripeStatus: string
    stripeStart: string
    stripeEnd: any
    total_days_trial:number
    days_left_trial:number
}

export interface UsageInfo {
    total_pages: number
    page_used: number
    page_used_org: number
    page_used_team: number
}

export interface UserCount {
    activeCount: number
    pendingCount: number
    teamsCount: number
    settungsCount: number
}


@Injectable({
    providedIn: 'root'
})



export class UserService {

    constructor(
        private apiService: ApiService,
        private ngHttpCachingService: NgHttpCachingService,
        private http: HttpClient,
        private modal: NzModalService


    ) { }

    isAdmin(): Observable<boolean> {
        return this.apiService.get('v1/user').pipe(
            map(res => {
                localStorage.setItem("user",JSON.stringify(res.user));
                res = (<UserRoot>res)
                if (res.user.roles) {
                    return res.user.roles.id != Roles.REGULAR_USER
                }
                return false
            })
        )
    }
    getUser(): Observable<User> {
        return this.apiService.get('v1/user').pipe(
            map(res => {localStorage.setItem("user",JSON.stringify(res.user)); return (<UserRoot>res).user})
        )
    }

    getAccountDetails(): Observable<Accountdetails> {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => (<UserRoot>res).accountdetails)
        )
    }

    isTeamPlan(): Observable<boolean> {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => (<UserRoot>res).accountdetails!.isTeampPlan)
        )
    }

    isLogin(): Observable<boolean> {
        return this.apiService.get('v1/user').pipe(
            map(res => {
                function getCookie(cname:any){
                    let name = cname + "=";
                    let ca = document.cookie.split(';');
                    for(let i=0; i<ca.length; i++) {
                        let c = ca[i].trim();
                        if (c.indexOf(name)==0) { return c.substring(name.length,c.length); }
                    }
                    return "";
                }
                // if(getCookie("sToken") && getCookie("sToken") != localStorage.getItem("token")){
                //     localStorage.setItem("token", getCookie("sToken"));
                // }
                return res.message != 'You need to login first.'
            })
        )
    }

    login(userinfo: Object) {
        return this.apiService.post('v1/user/loginwith2fa', userinfo)
            .pipe(first())
    }


    getPlan(): Observable<string> {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => (<UserRoot>res).accountdetails.productname)
        )
    }

    getBillingCycle(): Observable<BillingCycle> {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => {
                localStorage.setItem("userInfo",JSON.stringify(res));
                let billing_cycle = {} as BillingCycle
                let detail = (<UserRoot>res).accountdetails;
                billing_cycle.start = detail.cycle_begin
                billing_cycle.end = detail.cycle_end.replace(/ /g, "/")
                let current_Date = new Date()
                let start_date = new Date(billing_cycle.start)
                let end_date = new Date(billing_cycle.end)
                billing_cycle.total_days = Math.floor(Math.abs(end_date.getTime() - start_date.getTime()) / (1000 * 3600 * 24));

                let diff = end_date.getTime() - current_Date.getTime()
                if (diff <= 0) {
                    billing_cycle.days_left = 0
                }
                else {
                    billing_cycle.days_left = Math.floor(Math.abs(diff) / (1000 * 3600 * 24));
                }
                //trial
                billing_cycle.status = detail.status;
                billing_cycle.stripeStatus = detail.stripeStatus;
                billing_cycle.stripeStart = detail.stripeTrialStart;
                billing_cycle.stripeEnd = detail.stripeTrialEnd;

                let start_date_trial = new Date(detail.stripeTrialStart)
                let end_date_trial = new Date(detail.stripeTrialEnd)
                billing_cycle.total_days_trial = Math.floor(Math.abs(end_date_trial.getTime() - start_date_trial.getTime()) / (1000 * 3600 * 24));
                let diff_trial = end_date_trial.getTime() - current_Date.getTime();

                if (diff_trial <= 0) {
                    billing_cycle.days_left_trial = 0
                }
                else {
                    billing_cycle.days_left_trial = Math.floor(Math.abs(diff_trial) / (1000 * 3600 * 24));
                }
                return billing_cycle
            })
        )
    }

    getUsageInfo(): Observable<UsageInfo> {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => {
                localStorage.setItem("userInfo",JSON.stringify(res));
                let usage = {} as UsageInfo
                res = (<UserRoot>res)
                usage.page_used_org = res.usageInfo.usageOfOrg;
                usage.page_used_team = res.usageInfo.usageOfTeam;
                usage.page_used = res.usageInfo.usageOfUser;
                let limit = res.accountdetails.transactionlimit
                usage.total_pages = res.accountdetails.billingCycle === "Annually" && res.accountdetails.stripeStatus != "trialing" ? limit * 12 : limit
                return usage
            })
        )
    }


    getBill(): Observable<number> {
        return this.apiService.get('v1/billing/count').pipe(
            map(res => {
                return res.data.estimateOverageCost;
            })
        )
    }

    getRole(): Observable<string> {
        return this.apiService.get('v1/user').pipe(
            map(res => {
                localStorage.setItem("user",JSON.stringify(res.user));
                return (<UserRoot>res).user.roles
                    ? (<UserRoot>res).user.roles.displayName
                    : ''
            })
        )
    }

    getRoleID(): Observable<number> {
        return this.apiService.get('v1/user').pipe(
            map(res => {
                localStorage.setItem("user",JSON.stringify(res.user));
                res = <UserRoot>res
                if (res.user.roles) {
                    return res.user.roles.id
                } else {
                    return -1
                }
            })
        )
    }

    getRoleNameFromId(id?:number) {
        switch(id) {
        case 1: return $localize `Regular User`;
        case 2: return $localize `Team Billing Admin`;
        case 3: return $localize `Team Admin`;
        case 4: return $localize `Super Admin`;
        case 5: return $localize `Doc Admin`;
        default: return '';
        }
    }

    // getActivities():Observable<string[]>{
    getActivities(pageIndex: number) {
        return this.apiService.get(`v1/user/getNotificationMessages?pageIndex=${pageIndex}&pageSize=5&maxRecords=25&maxSearchDays=60`).pipe(
            map(res => {
                res = <UserRoot>res
                // return res.notificationMessages.map((c:NotificationMessage) =>
                //     `${c.docTitle} was sent to ${c.recipientName} at ${new Date(c.actionTime).toString()}`
                // )
                return res;
            })
        )
    }

    logout(userGUID: string) {
        this.http.post(environment.API_URL + 'v1/user/logout/'+userGUID, {}, { headers: { sToken: this.apiService.getSToken() } }).subscribe(
            value => {
                this.ngHttpCachingService.clearCache()
                window.location.href = "/UI"
            }
        )
    }


    getUserCount(): Observable<UserCount> {
        return this.apiService.get('v1/user/count/v2').pipe(
            map(res => {
                let counts = {} as UserCount
                counts.activeCount = res.data.activeCount
                counts.pendingCount = res.data.pendingCount
                counts.teamsCount = res.data.teamsCount
                counts.settungsCount = res.data.settungsCount
                return counts
            })
        )
    }


    getUsersAndTeams(args?: any): Observable<any> {
        let options: any = {
            "teamId": args.teamId,
            "teamName": args.teamName,
            cache: Math.random()
        }
        return this.http.get(environment.NEW_API_URL + 'v1/team/list?' + this.apiService.getQueryString(options),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )

    }
    getUsers(current: number, size: number, args: any, teamId: any = ''): Observable<any> {
        let options: any = {
            "current": current,
            "size": size,
            "teamId": teamId,
            "inUserName": args.userName,
            "roleId": args.role,
            "email": args.email,
            test: Math.random()
        }
        return this.http.get(environment.NEW_API_URL + 'v1/user/page-list?' + this.apiService.getQueryString(options),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                res.data.records.forEach((e:any) => {
                    e.id = e.id + 0.1;
                });
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    getTeams(): Observable<any> {
        return this.http.get(environment.NEW_API_URL + 'v1/team/list-all/?cache' + Math.random(),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    createTeam(name: string, parentId: number): Observable<any> {
        return this.http.post(environment.NEW_API_URL + 'v1/team/add',
            {
                teamName: name,
                parentGroupId: parentId
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    editTeam(teamName: string, id: number, parentId: number): Observable<any> {
        return this.http.put(environment.NEW_API_URL + 'v1/team/edit',
            {
                "groupId": id,
                "teamName": teamName,
                "parentGroupId": parentId
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    deleteTeams(ids: number[]): Observable<any> {
        return this.http.delete(environment.NEW_API_URL + 'v1/team/remove',
            {
                body: {
                    ids: ids
                },
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    deleteUsers(users: any[]): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/groups/' + users[0].groupId + '/array',
            {
                body: {
                    usersGroupsRoles: users
                },
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

    reactivateUser(user: any): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/reactivate/' + Math.floor(user),
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

    moveTeams(ids: number[], parentId: number): Observable<any> {
        return this.http.put(environment.NEW_API_URL + 'v1/team/move',
            {
                ids: ids,
                parentGroupId: parentId
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }
    moveUsers(ids: number[], parentId: number): Observable<any> {
        return this.http.put(environment.NEW_API_URL + 'v1/user/move',
            {
                ids: ids,
                parentGroupId: parentId
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg,
                    });
                }
                return res;
            })
        )
    }

    createUser(name: string, lastName:string,  email: string, role: number, group: number, teams: string, queryScope: number): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/groups/' + group + '/request',
            {
                request: {
                    "firstname": name,
                    "lastname": lastName,
                    "email": email,
                    "roleId": role,
                    "groupId": group,
                    "sharedTeams": teams,
                    "queryScope": queryScope
                }
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message === "Error.Code.1016" ? "The Team Member's email already has an account." : res.message,
                    });
                }
                return res;
            })
        )
    }
    editUser(id: number, firstName: string, lastName: string, email: string, role: number, group: number, teams: string, queryScope: number): Observable<any> {
        return this.http.put(environment.NEW_API_URL + 'v1/user/modify-user-group',
            {
                "inUserId": id,
                "firstName": firstName,
                "lastName": lastName,
                "email": email,
                "roleId": role,
                "teamId": group,
                "sharedTeams": teams,
                "queryScope": queryScope,

            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.msg,
                    });
                }
                return res;
            })
        )
    }
    updatePassword(id: number, password: string): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/users/3/passwordUpdate',
            {
                usersGroupsRole: {
                    "id": id,
                    "password": password
                }
            },
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }
    transferDocs(fromId: number, toId: number): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/groups/transferdoc/' + Math.floor(fromId) + '/' + Math.floor(toId),
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

    resendActivationEmail(user: any): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/groups/' + user.groupId + '/array/resendActivationEmail',
            {
                usersGroupsRoles: [user]
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

    getBellContent() {
        return this.apiService.get('v1/user/getDashboardUserInfo').pipe(
            map(res => {
                localStorage.setItem("userInfo",JSON.stringify(res));
                return res;
            })
        )
    }

    getMaintenanceMessage() {
        return this.apiService.get('v1/systemInformation/maintenanceMessage').pipe(
            map(res => {
                return res;
            })
        )
    }
    getContacts() {
        return this.apiService.get('v1/user/contacts?cache' + Math.random()).pipe(
            map(res => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }
    getTeamContacts() {
        return this.apiService.get('v1/user/contacts?includeTeam=true&includeSelf=false&cache' + Math.random()).pipe(
            map(res => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }
    addContact(firstname: string, lastname:string, email:string, phoneCountryCode:number, phonenumber:string  ): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/user/contact/create',
            {   contact : {
                    firstname,
                    lastname,
                    email,
                    phoneCountryCode,
                    phonenumber
                }
            },
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message ? res.message : 'An error has occured',
                    });
                }
                return res;
            })
        )
    }
    editContact(firstname: string, lastname:string, email:string, phoneCountryCode:number, phonenumber:string, id:number  ): Observable<any> {
        return this.http.put(environment.API_URL + 'v1/user/contact/update',
            {   contact : {
                    firstname,
                    lastname,
                    email,
                    phoneCountryCode,
                    phonenumber,
                    id
                }
            },
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message ? res.message : 'An error has occured',
                    });
                }
                return res;
            })
        )
    }
    
    addContacts(contacts: any[]): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/user/contacts/create',
            contacts,
            {
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message ? res.message : 'An error has occured',
                    });
                }
                return res;
            })
        )
    }

    deleteContacts(contacts:any): Observable<any> {
        return this.http.delete(environment.API_URL + 'v1/user/contacts',
            {
                body: {
                    contacts
                },
                headers: { sToken: this.apiService.getSToken() }
            }).pipe(
            map((res: any) => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.userMsg || $localize `An error has occured`,
                    });
                }
                return res;
            })
        )
    }

    sendVerificationEmail(fromEmail: string): Observable<any> {
        return this.http.post(environment.API_URL + 'v1/user/resendValidation/',
            { email: fromEmail},
            { headers: { sToken: this.apiService.getSToken() } }).pipe(
                map((res: any) => {
                    if (!res.success) {
                        this.modal.error({
                            nzTitle: $localize `Your validation token could not be sent to the specified email address`,
                        });
                    }
                    return res;
                })
            )
    }
    getUserProduct() {
        return this.apiService.get('v1/user/products/?cache' + Math.random()).pipe(
            map(res => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }
    cancelPlan() {
        return this.apiService.put('v1/user/products').pipe(
            map(res => {
                if (!res.success) {
                    this.modal.error({
                        nzTitle: res.message,
                    });
                }
                return res;
            })
        )
    }

}
