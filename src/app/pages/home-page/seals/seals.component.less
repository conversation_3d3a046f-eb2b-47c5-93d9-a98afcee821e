// WCAG 2.1 – Added custom :focus styles for status buttons to satisfy SC 2.4.7 (Focus Visible)
@media (max-width:640px) {
    .mobile-title {
        /* WCAG 1.4.4 – use rem so heading scales with user text size */
        font-size: 3.125rem !important; // 50px → 3.125rem
        /* WCAG 1.4.12 – use a relative ≥1.5× line-height so text does not get clipped when users increase text spacing */
        line-height: 1.5em !important; // Assumption: 1.5× meets guideline while keeping visual balance
        padding-bottom: 1.25rem !important; // 20px → 1.25remWCAG 1.4.12 – neutralise negative Tailwind margin that could cause overlap when line-height increases
    }
  }

.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
    
}

.activeSeal { // WCAG 1.4.11 – Non-text Contrast: replaced low-contrast #bae0f8 background
    cursor: pointer;
    background-color: #1a73e8; // Assumption: brand focus blue (≈4.5 : 1 vs white)
    /* WCAG 1.4.4 – width 90px→5.625rem so component scales with text */
    width: 5.625rem; // 90px → 5.625rem – Assumption: 1rem = 16px
    font-weight: bold;
    text-align: center;
    border-radius: 0.3125rem; // 5px → 0.3125rem
    display: inline-flex; // ensures icon and text align horizontally
    align-items: center;
    justify-content: center;
    // Text colour switched to white so text contrast meets 4.5 : 1 on the new blue background
    color: #ffffff; // Assumption: #ffffff ≥ 4.5 : 1 versus #1a73e8

    // WCAG 2.4.7 Focus Visible – Provide a clear indicator when the button receives keyboard focus
    &:focus {
        outline: 2px solid #055485; // Darker blue outline (≈8 : 1 vs white) for clearly perceivable focus
        outline-offset: 2px;
    }
}

.inactiveSeal { // WCAG 1.4.11 – increase background contrast vs white page
    cursor: pointer;
    background-color: #757575; // Grey-600 (≈4.6 : 1 vs white)
    width: 5.625rem; // 90px → 5.625rem
    font-weight: bold;
    text-align: center;
    border-radius: 0.3125rem; // 5px → 0.3125rem

    display: inline-flex;
    align-items: center;
    justify-content: center;

    color: #ffffff; // White text for ≥ 4.5 : 1 contrast on grey background

    // WCAG 2.4.7 Focus Visible
    &:focus {
        outline: 2px solid #055485; // Consistent high-contrast focus ring
        outline-offset: 2px;
    }

}

// WCAG 2.4.7 – Ensure the large option buttons inside the “Add New Stamp”
// selection modal provide a clearly visible focus indicator for keyboard
// users.  The control previously relied on the browser’s default focus ring
// which can be suppressed by resets (`outline:none`) defined in sibling
// components (e.g. users-and-teams).  Adding an explicit high-contrast outline
// guarantees the indicator is always present within this component’s
// encapsulated stylesheet regardless of global overrides.
.big-button:focus,
.big-button:focus-visible {
  outline: 3px solid #055485; // High-contrast blue used across the app
  outline-offset: 2px;
}

// WCAG 2.4.7 – Some Ant Design buttons (e.g. “Add New Stamp” primary action
// in the page header) inherit `.ant-btn` styles that cancel the browser’s
// default outline on focus. Re-introduce a consistent, accessible focus ring
// limited to this component scope so functionality and visual design remain
// unaffected elsewhere in the application.

// Using plain selector is sufficient because Angular’s View Encapsulation
// rewrites it with a component-specific attribute, preventing leakage to
// other pages without resorting to `::ng-deep` (which is discouraged).
.ant-btn:focus,
.ant-btn:focus-visible {
  outline: 3px solid #055485;
  outline-offset: 2px;
}
// Orientation fix (WCAG 2.1 – 1.3.4):
// Do not rely on a single fixed-pixel width that could overflow the viewport in
// portrait orientation.  Use a fluid width that can shrink on small screens
// while preserving the previous visual size as a maximum.  Maintain the
// original height as a minimum so the internal layout does not break.
.big-button { // WCAG 1.4.11 – darken border for ≥ 3 : 1 contrast against white
    /* WCAG 1.4.4 – convert fixed pixel dimensions to rem for scalable button */
    width: 100%; // Assumption: allow the control to shrink responsively
    max-width: 16.875rem; // 270px → 16.875rem (previous design cap)
    /* Maintain visual balance: ensure the button does not collapse below the
       original height but can grow if needed. */
    min-height: 14.6875rem; // 235px → 14.6875rem
    border: 1px solid #757575; // Replaced low-contrast #eeeeee (≈1.1 : 1) with grey-600 (≈4.5 : 1)
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-evenly;
    cursor: pointer;
}

.big-button:hover{
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    -webkit-box-shadow: 0px 0px 5px 1px rgba(1, 131, 211, 0.67);
    -moz-box-shadow:    0px 0px 5px 1px rgba(1, 131, 211, 0.67);
    box-shadow:         0px 0px 5px 1px rgba(1, 131, 211, 0.67);

}
.stamp-image {
    /* WCAG 1.4.4 – relative sizing for stamp icon */
    width: 4.125rem; // 66px → 4.125rem
}
.stamp-description {
    color: #565c66;
    margin-top: 1.25rem; // 20px → 1.25rem
}

// WCAG 1.3.1 – visually hidden utility to hide elements (e.g., table caption) while keeping them accessible
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}



// WCAG 1.4.10 Reflow – ensure the main data table can be scrolled horizontally within
// its own region at narrow viewports (320 px) instead of forcing the entire page to
// scroll sideways or clipping content. The class is applied to a wrapping <div>
// around <nz-table> in the template.
.table-responsive { // Assumption: wrapper only used for tabular data on this page
  width: 100%;
  max-width: 100%;
  overflow-x: auto; // single-axis scroll container
  margin: 0; // Remove any default margins
  padding: 0 0.9375rem; // Add padding instead of margin for better control
}

/* Ensure the table itself doesn't exceed container width */
.table-responsive nz-table {
  width: 100%;
  max-width: 100%;
}

/* Focus-visible styles for better accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
[tabindex="0"]:focus-visible:not(.ant-upload) {
  outline: 3px solid #2D323D; // High-contrast dark outline
  outline-offset: 2px;
  border-radius: 2px;
}

// Accessibility: Override low-contrast Tailwind utility class inside this component (WCAG 1.4.3)
.text-red-500 {
  color: #D40D00; // High-contrast red (≈5.44:1 against white)
}
