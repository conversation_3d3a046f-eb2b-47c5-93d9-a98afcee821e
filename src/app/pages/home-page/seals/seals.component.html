

<!-- WCAG 2.1 – Converted spans/divs acting as controls into real <button> elements, added aria attributes & keyboard focus handling -->
<!-- Modification 2025-07-31: WCAG 1.4.13 – added [nzMaskClosable] and [nzKeyboard] to all nz-modal instances for ESC key and click outside dismissal -->

<!-- Accessibility: WCAG 1.1.1 compliance - added alt, aria-hidden, and aria-label attributes for non-text content -->
<!-- Update 2025-07-31: Added descriptive alt text and i18n-alt to stamp & encrypted stamp images (removed aria-hidden) to resolve Pa11y H37 errors -->
<!-- Accessibility: added aria-hidden to decorative chevron icons in add stamp modal -->
<!-- Accessibility: added accessible name to spinner elements (aria-label and i18n-aria-label) per WCAG 1.1.1 -->
<!-- Accessibility: Added explicit background colour to guarantee sufficient colour contrast for text (WCAG 1.4.3) -->
<!-- WCAG 1.4.4 – convert absolute pixel text sizes/spacings to rem so heading scales with user zoom -->
<!-- WCAG 1.4.12 fix: remove rigid line-height to allow user-defined text spacing; switch to relative 1.5em and neutralise negative Tailwind margin -->
<h1 style="font-size: 4.375rem; line-height: 1.5em; margin-top: -0.625em !important;font-weight: 275; color: #2D323D; padding-bottom: 3.4375rem;"
    class="ml-5 font-thin mobile-title" i18n>
    Settings
</h1>
<!-- Accessibility: Added explicit background colour to avoid colour contrast ambiguity (WCAG 1.4.3) -->
<!-- WCAG 1.4.4 – padding-bottom switched to rem so spacing enlarges with text -->
<h2 style="padding-bottom: 0.3125rem; border-bottom: grey solid 1px; display: flex; justify-content: space-between; align-items: baseline;"
    class="-mt-6 mx-5  ">
    <!-- WCAG 1.4.4 – font-size 35px→2.1875rem -->
    <div style="font-size: 2.1875rem; font-weight: 400; color: #2D323D;" i18n>
        Stamps
    </div>
    <!-- WCAG 2.4.6 – replace generic label with descriptive one so the button purpose is clear out of context -->
    <!-- Assumption: ensure button does not accidentally submit an ancestor form -->
    <!-- WCAG 2.1 SC 3.2.4 – Consistent Identification: ensure the visible label and the accessible name
         use identical phrasing (“Add New Stamp”) so the component is identified the same way for
         all users irrespective of modality (visual vs. assistive technology). -->
    <button nz-button nzType="primary" type="button" (click)="showSealTypeModal = true" i18n i18n-aria-label aria-label="Add New Stamp">Add New Stamp</button>
</h2>
<!-- PRIMARY CONTENT LANDMARK -->
<main id="seals-main" tabindex="-1" role="main">

    <!-- WCAG 4.1.2 – Explicitly expose the spinner's role so assistive technology recognises it as a live status region. -->
    <nz-spin [nzSpinning]="isSpinning" role="status" aria-live="polite" aria-label="Loading content" i18n-aria-label>
    <!-- Assumption: spinner indicates content is loading for assistive technology -->
    <!-- WCAG 1.4.10 – wrapped data table in a horizontally scrollable container so the page never requires two-axis scrolling on small viewports. -->
    <div class="table-responsive" role="region" aria-labelledby="sealsTableCaption">
    <!-- WCAG 1.3.1 – added semantic caption and scope attributes for header cells to convey table structure programmatically -->
    <!-- WCAG 1.4.4 – margin-top 30px→1.875rem -->
    <!-- Assumption: Adding an explicit id so it can be the skip-link target; does not affect existing
         template logic because no code references this id. -->
    <!-- WCAG 2.4.1 – Added tabindex="-1" so the table becomes a valid focus target when the skip
         link is activated. Without this attribute, focus would remain on the skip link itself,
         forcing keyboard users to Tab through intervening controls. Making the element
         programmatically focusable ensures focus lands inside the main content after activation
         of the “Skip to stamps list” link. -->
    <nz-table id="sealsTable" tabindex="-1" nzTemplateMode [nzShowPagination]="false" style=" margin-top: 1.875rem;" #basicTable [nzData]="seals">
        <!-- Assumption: visually-hidden caption keeps existing layout unchanged while exposing an accessible name -->
        <caption id="sealsTableCaption" class="sr-only" i18n>List of stamps with their type, ID and status</caption>
        <thead>
            <tr>
                <th scope="col" i18n>Name</th>
                <th scope="col" i18n>Type</th>
                <th scope="col" i18n>ID</th>
                <th scope="col" i18n>Status</th>
            </tr>
        </thead>
        <tbody>
        <tr *ngFor="let seal of seals">
            <td>{{seal.name}}</td>
            <td *ngIf="seal.encrypted" i18n>Encrypted Stamp</td>
            <td *ngIf="!seal.encrypted" i18n>Stamp</td>
            <td>{{seal.id}}</td>
            <td>
                <div style="display: flex; justify-content: space-between; align-items:center">
                    <!-- WCAG 1.4.1 – Use of Colour: Added shape/icon so seal status is not conveyed by
                         background colour alone.  A check-circle represents an active seal and a
                         minus-circle represents an inactive seal.  The accompanying text remains for
                         clarity.  // Assumption: ng-zorro standard icon set already loaded elsewhere. -->
                    <!-- WCAG 2.1 – Replace non-interactive <span> with <button> so stamp status can be toggled using keyboard -->
                    <!-- WCAG 4.1.2 – aria-pressed must be set to the string values "true" or "false"; numerical 0/1
                         violates the ARIA specification and can break assistive-technology parsing.  Replaced the
                         data-bound numeric value with the correct static token. -->
                    <button *ngIf="seal.status"
                            type="button"
                            class="activeSeal border-0"
                            (click)="selectedSeal = seal; setSealStatus(0)"
                            aria-pressed="true"
                            aria-label="Active – set stamp inactive"
                            i18n-aria-label
                            i18n><!-- WCAG 2.5.3 validation fix: comment moved outside of tag to avoid parse error -->

                            <!-- Validation fix: ng-zorro ThemeType uses 'fill', not 'filled' -->
                            <span nz-icon nzType="check-circle" nzTheme="fill" aria-hidden="true"></span>
                            <span class="ml-1">Active</span>
                    </button>
                    <button *ngIf="!seal.status"
                            type="button"
                            class="inactiveSeal border-0"
                            (click)="selectedSeal = seal; setSealStatus(1)"
                            aria-pressed="false"
                            aria-label="Inactive – set stamp active"
                            i18n-aria-label
                            i18n><!-- WCAG 2.5.3 validation fix: comment moved outside of tag to avoid parse error -->

                            <!-- Validation fix: ng-zorro ThemeType uses 'outline', not 'outlined' -->
                            <span nz-icon nzType="minus-circle" nzTheme="outline" aria-hidden="true"></span>
                            <span class="ml-1">Inactive</span>
                    </button>
                    <span class="flex">
                        <div class="ml-2">
                            <!-- WCAG 1.4.4 – icon font-size 20px→1.25rem -->
                            <!-- WCAG 2.1 – make edit control keyboard accessible by using real <button> element -->
                            <button type="button" class="cursor-pointer bg-transparent border-0 p-0" style="font-size: 1.25rem;"
                                (click)="selectedSeal = seal; setEditFormMode();" aria-label="Edit Stamp" i18n-aria-label>
                                <span nz-icon nzType="edit" nzTheme="outline" aria-hidden="true"></span>
                            </button>
                        </div>
                        <div class="ml-2">
                            <!-- WCAG 1.4.4 – icon font-size 20px→1.25rem -->
                            <button type="button" class="cursor-pointer bg-transparent border-0 p-0" style="font-size: 1.25rem;"
                                (click)="selectedSeal = seal; showDeleteModal = true" aria-label="Delete Stamp" i18n-aria-label>
                                <span nz-icon nzType="delete" nzTheme="outline" aria-hidden="true"></span>
                            </button>
                        </div>
                    </span>
                </div>
            </td>
        </tr>
        </tbody>
    </nz-table>
    </div>

    </nz-spin>

</main>


<!-- WCAG 1.4.13 – make dialog dismissible via ESC key and click outside -->
<!-- WCAG 2.4.6 – use specific dialog title and action label so users understand what is being confirmed -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel"
          i18n-nzOkText nzOkText="Delete"
          [(nzVisible)]="showDeleteModal"
          i18n-nzTitle nzTitle="Confirm Delete"
          (nzOnOk)="deleteSeal()"
          (nzOnCancel)="onDeleteModalCancel()"
          [nzMaskClosable]="true" [nzKeyboard]="true" [nzWidth]="'90vw'">
    
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – marked layout table as presentational to avoid misclassification as data table -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n>Delete the selected seal from the system?</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.4 – modal width 800px→50rem so dialog scales with root font size (assumes 1rem = 16 px) -->
<!-- WCAG 1.4.13 – enable ESC key and click outside dismissal for Add/Edit Encrypted Stamp modal -->
<nz-modal  i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="showAddSealModal"  (nzOnOk)="submitSealForm()"
    (nzOnCancel)="resetSealForm()" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true">
    <div *nzModalTitle>
        <span *ngIf="!editSealFormMode" i18n>Add Encrypted Stamp</span>
        <span *ngIf="editSealFormMode" i18n>Edit Encrypted Stamp</span>
      </div>
    <ng-container *nzModalContent>
        <form [formGroup]="addSealForm">
            <!-- WCAG 1.3.3 (Sensory Characteristics):
                 Added role="button" and programmatic indication of the expand/collapse
                 state so users are not required to rely on the arrow icon orientation
                 alone. -->
            <!-- WCAG 2.1 – 2.1.1 Keyboard & 2.4.7 Focus Visible: replace non-interactive <div> with a semantic <button> so the
                 disclosure control is naturally focusable and operable from the keyboard. -->
            <button
                type="button"
                class="font-bold text-sm mt-10 bg-transparent border-0 p-0"
                i18n
                style="display: none;"
                (click)="showFormProvider = !showFormProvider"
                [attr.aria-expanded]="showFormProvider"
                aria-label="Toggle Certificate Provider section" i18n-aria-label>
                Certificate Provider
                <i *ngIf="!showFormProvider" class="fa-regular fa-chevron-down ml-1 cursor-pointer" aria-hidden="true"></i>
                <i *ngIf="showFormProvider" class="fa-regular fa-chevron-up ml-1 cursor-pointer" aria-hidden="true"></i>
            </button>
            <!-- WCAG 4.1.2 – Provide role and live region semantics for the in-form spinner. -->
            <nz-spin [nzSpinning]="isSpinning" role="status" aria-live="polite" aria-label="Loading content" i18n-aria-label>
                <!-- Assumption: spinner indicates content is loading for assistive technology -->
                <!-- Layout table inside form → role presentation -->
                <table class="modal-table px-4" role="presentation">
                    <tr *ngIf="showFormProvider">
                        <td i18n>Provider</td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.provider.errors ? 'red':''"
                            nz-input formControlName="provider" i18n-aria-label aria-label="Provider" />
                        <div *ngIf="addSealForm.submitted && addSealForm.controls.provider.errors"
                        class="text-red-500" i18n>Provider is required</div>
                        </td>
                    </tr>
                    <tr *ngIf="showFormProvider">
                        <td i18n>OAuth Client ID</td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.oAuthClient.errors ? 'red':''" 
                            nz-input formControlName="oAuthClient" i18n-aria-label aria-label="OAuth Client ID" />
                        <div *ngIf="addSealForm.submitted && addSealForm.controls.oAuthClient.errors"
                            class="text-red-500" i18n>OAuth Client ID is required</div>   
                        </td>
                    </tr>
                    <tr *ngIf="showFormProvider">
                        <td i18n>OAuth Client Secret</td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.oAuthClientSecret.errors ? 'red':''"
                            nz-input formControlName="oAuthClientSecret" i18n-aria-label aria-label="OAuth Client Secret"/>
                        <div *ngIf="addSealForm.submitted && addSealForm.controls.oAuthClientSecret.errors"
                            class="text-red-500" i18n>OAuth Client Secret is required</div>   
                        </td>
                    </tr>
                    <tr *ngIf="showFormProvider">
                        <td i18n>Credential ID</td>
                        <!-- WCAG 3.3.2 – added programmatic label so screen-reader users understand the purpose of the input even when table cell association is lost. -->
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.credentialId.errors ? 'red':''"
                            nz-input formControlName="credentialId" i18n-aria-label aria-label="Credential ID" />
                        <div *ngIf="addSealForm.submitted && addSealForm.controls.credentialId.errors"
                            class="text-red-500" i18n>Credential ID is required</div>   
                        </td>
                    </tr>
                    <tr *ngIf="showFormProvider">
                        <td i18n>Credential PIN</td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.credentialPin.errors ? 'red':''"
                            nz-input formControlName="credentialPin" i18n-aria-label aria-label="Credential PIN"/>
                        <div *ngIf="addSealForm.submitted && addSealForm.controls.credentialPin.errors"
                            class="text-red-500" i18n>Credential PIN is required</div>   
                        </td>
                    </tr>
                    <!-- Assumption: Promote settings toggle into its own table row so screen-reader users encounter it in logical sequence (WCAG 1.3.2) -->
                    <tr>
                        <td colspan="2">
                            <!-- WCAG 1.3.3: Provide accessible toggle for Settings section -->
                            <!-- WCAG 2.1 – 2.1.1 Keyboard: use a <button> for Settings disclosure -->
                            <button
                                type="button"
                                class="font-bold cursor-pointer bg-transparent border-0 p-0"
                                style="margin-left: -0.9375rem;"
                                (click)="showFormSettings = !showFormSettings"
                                [attr.aria-expanded]="showFormSettings"
                                aria-label="Toggle Settings section"
                                i18n
                                i18n-aria-label>
                                Settings
                                <i *ngIf="!showFormSettings" class="fa-regular fa-chevron-down ml-1" aria-hidden="true"></i>
                                <i *ngIf="showFormSettings" class="fa-regular fa-chevron-up ml-1" aria-hidden="true"></i>
                            </button>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td i18n>Name</td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.name.errors ? 'red':''"
                            nz-input formControlName="name" i18n-aria-label aria-label="Name"/>
                            <div *ngIf="addSealForm.submitted && addSealForm.controls.name.errors"
                            class="text-red-500" i18n>Name is required</div>   
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                            <span i18n>Email</span>
                            <div class="text-xs mt-1" i18n>
                                    The email address of the person authorized to use this encrypted stamp
                            </div>
                        </td>
                        <!-- Added semantic type and autocomplete attributes for personal email input per WCAG 2.1 SC 1.3.5 Identify Input Purpose -->
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.email.errors ? 'red':''"
                            nz-input type="email" autocomplete="email" formControlName="email" i18n-aria-label aria-label="The email address of the person authorized to use this encrypted stamp"/>
                            <div *ngIf="addSealForm.submitted && addSealForm.controls.email.errors">
                                <div *ngIf="addSealForm.submitted && addSealForm.controls.email.errors.required" class="text-red-500" i18n>Email
                                    is required</div>
                                <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                                     Explain how to fix the invalid email error by showing an example of a correct format. -->
                                <div *ngIf="addSealForm.submitted && addSealForm.controls.email.errors.email" class="text-red-500" i18n>
                                    Enter a valid email such as <em><EMAIL></em>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                        <span i18n>Password</span>
                        <div class="text-xs mt-1" i18n>
                            The password required to use this encrypted stamp
                         </div>
                        </td>
                        <td aria-live="polite"><input [style.borderColor]="addSealForm.submitted && addSealForm.controls.password.errors ? 'red':''" i18n-aria-label aria-label="The password required to use this encrypted stamp"
                            nz-input formControlName="password"  [placeholder]="editSealFormMode ?  keepOldPwString :''"   [type]="'password'" autocomplete="new-password" />
                            <div *ngIf="addSealForm.submitted && addSealForm.controls.password.errors">
                                <div *ngIf="addSealForm.submitted && addSealForm.controls.password.errors.required" class="text-red-500" i18n>
                                    Password
                                    is required</div>
                                <span *ngIf="addSealForm.controls.password.errors.pattern" class="text-red-500" i18n>Password must be
                                    at least 8 ~ 20 characters
                                    and have at least 1 uppercase,
                                    1 lowercase, 1 number,
                                    and one of !@#$%^&*()_+=[]{{ '{' }}{{ '}' }}|-
                                    Cannot contain username.<br></span>
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td aria-live="polite">
                            <div i18n>Graphic</div>
                            <div class="mt-2" style="border-spacing: 0px;">
                                <nz-upload [nzAccept]="'image/png'" [(nzFileList)]="fileList" [nzBeforeUpload]="beforeUpload">
                                    <button nz-button [style.borderColor]="addSealForm.submitted && fileList.length < 1 ? 'red':''" i18n>
                                    <span nz-icon nzType="upload" aria-hidden="true"></span>
                                    Select File
                                    </button>
                                </nz-upload>
                                <!-- WCAG 3.3.2 – Provide explicit instruction about the required file type so users know what input is expected. -->
                                <div class="text-xs text-gray-600" i18n>PNG files only</div>
                                <div *ngIf="addSealForm.submitted && fileList.length < 1"
                                class="text-red-500" i18n>Graphic is required</div>   
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                            <mat-slide-toggle i18n-aria-label aria-label="Display Subject"
                             appToggleEnterKey color="primary" formControlName="displaySubject"> </mat-slide-toggle>
                            <span class="ml-1" i18n>Display Subject</span>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                            <mat-slide-toggle i18n-aria-label aria-label="Display Date"
                             appToggleEnterKey color="primary" formControlName="displayDate"> </mat-slide-toggle>
                            <span class="ml-1" i18n>Display Date</span>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                            <mat-slide-toggle i18n-aria-label aria-label="Display Time"
                             appToggleEnterKey color="primary" formControlName="displayTime"> </mat-slide-toggle>
                            <span class="ml-1" i18n>Display Time</span>
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td>
                            <mat-slide-toggle i18n-aria-label aria-label="Display Reason"
                             appToggleEnterKey color="primary" formControlName="displayReason"> </mat-slide-toggle>
                            <span class="ml-1" i18n>Display Reason</span>
                        </td>
                    </tr>
                    <!-- Wrapped textarea in a data cell so assistive technologies announce it at the correct point in the table reading order (WCAG 1.3.2) -->
                    <tr *ngIf="showFormSettings">
                        <td colspan="2">
                            <!-- WCAG 1.4.4 – margin-left 50px→3.125rem so textarea aligns under toggles even when text enlarged -->
                            <textarea (input)="removeNewLineCharacter($event)" i18n-aria-label aria-label="Reason"
                            nz-input formControlName="reason" i18n-placeholder placeholder="Enter reason for seal"></textarea>
                        </td>
                    </tr>
                </table>
            </nz-spin>
        </form>
        <!-- WCAG 1.3.3: Added role and aria-expanded so meaning is not conveyed by arrow icon alone -->
        <!-- WCAG 2.1 – replace div with semantic button for keyboard accessibility -->
        <button
            type="button"
            class="font-bold mb-2 cursor-pointer bg-transparent border-0 p-0"
            (click)="showFormUsersTeams = !showFormUsersTeams"
            [attr.aria-expanded]="showFormUsersTeams"
            aria-label="Toggle Users and Teams section"
            i18n
            i18n-aria-label>
            Users/Teams
            <i *ngIf="!showFormUsersTeams" class="fa-regular fa-chevron-down ml-1" aria-hidden="true"></i>
            <i *ngIf="showFormUsersTeams" class="fa-regular fa-chevron-up ml-1" aria-hidden="true"></i>
        </button>
        <div class="mb-1" i18n>Select which Users/Teams have access to this encrypted stamp when signing</div>
        <app-users-and-teams [selectedUsersAndTeams]="selectedUsersAndTeams" [tableOnly]="true" [hidden]="!showFormUsersTeams" (getSelectedIds)="setSelectedIds($event)"></app-users-and-teams>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.4 – modal width 610px→38.125rem -->
<!-- WCAG 1.4.13 – allow dismissal via ESC key and click outside for Stamp Type selection modal -->
<!-- WCAG 2.1 SC 3.2.4 – Consistent Identification: align the modal title with the triggering button
     label (“Add New Stamp”) so the same action is referenced consistently across the interface. -->
<nz-modal [(nzVisible)]="showSealTypeModal" i18n-nzTitle nzTitle="Add New Stamp" [nzWidth]="'90vw'"
    (nzOnCancel)="onSealTypeModalCancel()" [nzFooter]="null" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- Layout table presenting stamp type options – not a data table -->
        <!-- Converted inner flex container to semantic table row/cells for logical reading order (WCAG 1.3.2) -->
        <table class="modal-table" role="presentation">
            <tr>
                <td style="vertical-align: top;">
                    <!-- Added explicit type to prevent implicit form submission when keyboard users press Enter (WCAG 2.5.2 safety) -->
                    <!-- WCAG 2.1 SC 3.2.4 – Consistent Identification: make the visible heading inside
                         the option button match the accessible name ("Add Stamp") so users who see
                         the interface and those using screen readers are presented with the same
                         terminology for the action. -->
                    <button class="big-button" type="button" (click)="setAddStampFormMode(); showSealTypeModal=false" 
                    i18n-aria-label aria-label="Add Stamp">
                        <div i18n class="text-lg font-bold">Add Stamp</div>
                        <!-- Assumption: icon conveys meaning (type of stamp); provide descriptive, translatable alt text (WCAG 1.1.1) -->
                        <div><img class="stamp-image" src=".\assets\images\stamp.png" alt="Stamp illustration" i18n-alt /></div>
                    </button>
                    <div class="stamp-description" i18n>
                        <p>
                            Stamps are a form of authentication used to convey official authorization or approval.
                            They can be uploaded by their recipient or can be predefined to streamline the recipients workflow.
                        </p>
                        <p>
                            Teams/users assigned to a Predefined Stamp  will have access to that stamp when signing a document.
                        </p>
                    </div>
                </td>
                <!-- Orientation fix (WCAG 2.1 – 1.3.4): switched fixed pixel-based width
                     to a fluid width so the dialog can shrink in portrait view
                     without introducing horizontal scrolling. Preserve the previous
                     maximum via the updated .big-button rule. -->
                <td style="vertical-align: top;">
                    <!-- Added explicit type attribute (see reasoning above) -->
                    <!-- WCAG 2.1 SC 3.2.4 – same adjustment for Encrypted Stamp option. -->
                    <button class="big-button" type="button" (click)="setAddSealFormMode(); showSealTypeModal=false" 
                    i18n-aria-label aria-label="Add Encrypted Stamp">
                        <div i18n class="text-lg font-bold">Add Encrypted Stamp</div>
                        <!-- Assumption: icon conveys meaning (encrypted stamp); provide descriptive, translatable alt text (WCAG 1.1.1) -->
                        <div><img class="stamp-image" src=".\assets\images\shield.png" alt="Encrypted stamp illustration" i18n-alt/></div>
                    </button>
                    <div class="stamp-description" i18n>
                        <p>
                            Encrypted stamps add an additional level of authority. They apply an encrypted stamp to a document on behalf
                            of an organization. Encrypted stamps use various authentication methods to ensure they are applied by the person
                            who is authorized to use it.
                        </p>
                        <p>
                           Teams/users who are assigned to an encrypted stamp have the ability to request that stamp to be placed but
                           they can not apply it unless authorized.
                        </p>
                    </div>
                </td>
            </tr>
        </table>
    </ng-container>
</nz-modal>

<!-- WCAG 1.4.13 – ensure Add/Edit Stamp dialog can be dismissed via ESC key and click outside -->
<nz-modal  i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="showAddStampModal" [nzWidth]="'90vw'" 
(nzOnCancel)="resetStampForm()" (nzOnOk)="submitStampForm()" [nzMaskClosable]="true" [nzKeyboard]="true">
    <div *nzModalTitle>
        <span *ngIf="!editStampFormMode" i18n>Add Stamp</span>
        <span *ngIf="editStampFormMode" i18n>Edit Stamp</span>
      </div>
    <ng-container *nzModalContent>
        <form [formGroup]="addStampForm">
            <!-- WCAG 4.1.2 – Same live status semantics for spinner in Add/Edit Stamp dialog. -->
            <nz-spin [nzSpinning]="isSpinning" role="status" aria-live="polite" aria-label="Loading content" i18n-aria-label>
                <!-- Assumption: spinner indicates content is loading for assistive technology -->
                <!-- Layout table inside Add/Edit Stamp form -->
                <table class="modal-table px-4" role="presentation">
                    <tr *ngIf="showFormSettings">
                        <td i18n>Name</td>
                        <td aria-live="polite"><input [style.borderColor]="addStampForm.submitted && addStampForm.controls.name.errors ? 'red':''"
                            nz-input formControlName="name" i18n-aria-label aria-label="Name"/>
                            <div *ngIf="addStampForm.submitted && addStampForm.controls.name.errors"
                            class="text-red-500" i18n>Name is required</div>   
                        </td>
                    </tr>
                    <tr *ngIf="showFormSettings">
                        <td aria-live="polite">
                            <div i18n>Graphic</div>
                            <div class="mt-2" style="border-spacing: 0px;">
                                <nz-upload [nzAccept]="'image/png'" [(nzFileList)]="fileList" [nzBeforeUpload]="beforeUpload">
                                    <button nz-button [style.borderColor]="addStampForm.submitted && fileList.length < 1 ? 'red':''" i18n>
                                    <span nz-icon nzType="upload" aria-hidden="true"></span>
                                    Select File
                                    </button>
                                </nz-upload>
                                <!-- WCAG 3.3.2 – Provide explicit instruction about the required file type so users know what input is expected. -->
                                <div class="text-xs text-gray-600" i18n>PNG files only</div>
                                <div *ngIf="addStampForm.submitted && fileList.length < 1"
                                class="text-red-500" i18n>Graphic is required</div>   
                            </div>
                        </td>
                    </tr>
                </table>
            </nz-spin>
        </form>
        <!-- WCAG 1.3.3: Same accessible toggle for Users/Teams in Stamp modal -->
        <!-- WCAG 2.1 – replace div with semantic button for keyboard accessibility -->
        <button
            type="button"
            class="font-bold mb-2 cursor-pointer bg-transparent border-0 p-0"
            (click)="showFormUsersTeams = !showFormUsersTeams"
            [attr.aria-expanded]="showFormUsersTeams"
            aria-label="Toggle Users and Teams section"
            i18n
            i18n-aria-label>
            Users/Teams
            <i *ngIf="!showFormUsersTeams" class="fa-regular fa-chevron-down ml-1" aria-hidden="true"></i>
            <i *ngIf="showFormUsersTeams" class="fa-regular fa-chevron-up ml-1" aria-hidden="true"></i>
        </button>
        <div class="mb-1" i18n>Select which Users/Teams have access to this stamp when signing</div>
        <app-users-and-teams [selectedUsersAndTeams]="selectedUsersAndTeams" [tableOnly]="true" [hidden]="!showFormUsersTeams" (getSelectedIds)="setSelectedIds($event)"></app-users-and-teams>
    </ng-container>
</nz-modal>
