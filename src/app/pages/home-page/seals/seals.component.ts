// WCAG 2.4.6 – No functional changes in logic; file referenced by the template now exposes properties
// that are consumed by newly-added, more descriptive labels and headings in the HTML.  This comment
// records that the TypeScript remained behaviourally unchanged while supporting the accessibility
// improvements.

// Added Title import to set a descriptive page title (WCAG 2.4.2)
import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Validators, FormBuilder } from '@angular/forms';
import {  NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';
import { NzModalService } from 'ng-zorro-antd/modal';
import { SealService } from '@core/services/seal.service';

interface Seal {
  provider: string;
  oAuthClient: string;
  oAuthClientSecret: string;
  credentialId: string;
  credentialPin: string;
  name: string;
  encrypted: boolean;
  email: string;
  id: number;
  status: number;
  displaySubject: boolean;
  displayDate: boolean;
  displayTime: boolean;
  displayReason: boolean;
  reason: string;
  img:NzUploadFile;
  users: number[];
  teams: number[];
  password: string;
  imgData?:any;
}
@Component({
  selector: 'app-seals',
  templateUrl: './seals.component.html',
  styleUrls: ['./seals.component.less']
})
export class SealsComponent implements OnInit {

  public seals: Seal[] = [];
  public showDeleteModal: boolean = false;
  public showAddSealModal: boolean = false;
  public showAddStampModal: boolean = false;
  public showSealTypeModal: boolean = false;
  public selectedSeal: Seal | null = null;
  public selectedUsersAndTeams: number[] = [];
  public isSpinning: boolean = false;
  public addSealForm: any = this.formBuilder.group({
    provider: [''],
    oAuthClient: [''],
    oAuthClientSecret: [''],
    credentialId: [''],
    credentialPin: [''],
    name: ['', Validators.required],
    email: ['', [Validators.required, Validators.email]],
    password: ['', [Validators.required, Validators.pattern(/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*()_+=\[\]{}|'-])[a-zA-Z0-9!@#$%^&*()_+=\[\]{}|'-]{8,20}$/)]],
    displaySubject: [true],
    displayDate: [true],
    displayTime: [true],
    displayReason: [true],
    reason: [''],
  });
  public addStampForm: any = this.formBuilder.group({
    name: ['', Validators.required],
  });

  public keepOldPwString:string =  $localize`Keep old password`;

  public showFormProvider = false;
  public showFormSettings = true;
  public showFormUsersTeams = true;
  //used to reuse same form for both edit and add actions, true will change the form to edit mode
  public editSealFormMode = false;
  public editStampFormMode = false;
  public imageData:any;
  public fileList: NzUploadFile[] = [];

  // Assumption: BrowserModule is already loaded at application root so Angular's built-in
  // Title service is available for injection without adding any new dependencies.
  constructor(
    private formBuilder: FormBuilder,
    private modal: NzModalService,
    private sealService: SealService,
    private titleService: Title // Inject Title to satisfy WCAG 2.4.2
  ) {}

  // WCAG 2.1 SC 2.4.3 – Focus Order
  // Assumption: A "skip to content" link was added in the template so
  // keyboard users can bypass repeated navigation.  However, standard
  // in-page anchor navigation does not always move the *keyboard focus*
  // to the target element – the viewport scrolls but focus stays on the
  // link itself.  This breaks the expected focus order because the next
  // <Tab> would return to the navigation instead of continuing inside the
  // main content area.
  //
  // The helper below programmatically shifts focus to the <main> landmark
  // once the skip link is activated, ensuring a logical, predictable
  // sequence for all browsers and assistive technologies.
  ngOnInit(): void {
    // WCAG 2.4.2 – Page Titled: Each view must provide a descriptive <title> element so
    // assistive-technology users can quickly identify the purpose of the page.
    // The title combines the specific section (Stamp Settings) with the
    // application name for context.  The string is wrapped in $localize so it can be
    // extracted for translation like any other UI copy.
    this.titleService.setTitle($localize`Stamp Settings`);

    this.getSeals();
  }

  getSeals() {
    this.isSpinning = true;
    this.seals = [];
    this.sealService.listSeals().subscribe(seals=>{
      seals.result.data.records.forEach((seal: any)=>{
        this.seals.push({
          provider: seal.provider,
          oAuthClient: seal.oAuthClientId,
          oAuthClientSecret: seal.oAuthClientSecret,
          credentialId: seal.credentialId,
          credentialPin: seal.credentialPIN,
          password: seal.password,
          name: seal.sealName,
          encrypted: seal.encrypted,
          email: seal.displayEmail,
          id: seal.id,
          status: seal.sealStatus,
          displaySubject: seal.displaySubject,
          displayDate: seal.displayDate,
          displayTime: seal.displayTime,
          displayReason: seal.displayReason,
          reason: seal.enterReasonForSeal,
          img:  JSON.parse(seal.img),
          users: seal.userIds,
          teams: seal.teamIds,
          imgData: seal.imgDate,
        })
      })
      this.isSpinning = false;
    })
  }

  deleteSeal() {
    this.sealService.deleteSeals([this.selectedSeal!.id]).subscribe(e=>{
      this.getSeals();
    })
    this.showDeleteModal = false;
  }

  setSealStatus(status:number) {
    this.setEditFormMode();
    this.selectedSeal!.status = status;
    if( this.selectedSeal?.encrypted) {
      this.submitSealForm();
    }
    else {
      this.submitStampForm()
    }
  }

  submitSealForm() {
    this.addSealForm.submitted = true;
    //if form is valid
    if (!this.addSealForm.invalid && this.fileList.length > 0) {

      //split users and teams
      let users:number[] = [];
      let teams:number[]= [];
      this.selectedUsersAndTeams.forEach(element => {
        if(Number.isInteger(element)) {
          teams.push(element)
        }
        else {
          users.push(Math.floor(element))
        }
      });
      //add Seal
      if (!this.editSealFormMode) {
        this.sealService.addSeal(
          this.addSealForm.controls.provider.value,
          this.addSealForm.controls.oAuthClient.value,
          this.addSealForm.controls.oAuthClientSecret.value,
          this.addSealForm.controls.credentialId.value,
          this.addSealForm.controls.credentialPin.value,
          this.addSealForm.controls.name.value,
          this.addSealForm.controls.email.value,
          this.addSealForm.controls.password.value,
          1,
          this.addSealForm.controls.displaySubject.value,
          this.addSealForm.controls.displayDate.value,
          this.addSealForm.controls.displayTime.value,
          this.addSealForm.controls.displayReason.value,
          this.addSealForm.controls.reason.value,
          {name: this.fileList[0].name, uid: this.fileList[0].uid},
          this.imageData,
          true,
          users,
          teams
        ).subscribe(e=>{
          this.getSeals();
        });

      }
      //edit Seal
      else {
        //only change password if user entered a new one, otherwise leave it empty
        let password;
        if(this.addSealForm.controls.password.value != this.selectedSeal!.password) {
          password = this.addSealForm.controls.password.value;
        }
       
        this.selectedSeal!.provider = this.addSealForm.controls.provider.value;
        this.selectedSeal!.oAuthClient = this.addSealForm.controls.oAuthClient.value;
        this.selectedSeal!.oAuthClientSecret = this.addSealForm.controls.oAuthClientSecret.value;
        this.selectedSeal!.credentialId = this.addSealForm.controls.credentialId.value;
        this.selectedSeal!.credentialPin = this.addSealForm.controls.credentialPin.value;
        this.selectedSeal!.name = this.addSealForm.controls.name.value;
        this.selectedSeal!.email = this.addSealForm.controls.email.value;
        this.selectedSeal!.password = this.addSealForm.controls.password.value;
        this.selectedSeal!.displaySubject = this.addSealForm.controls.displaySubject.value;
        this.selectedSeal!.displayDate = this.addSealForm.controls.displayDate.value;
        this.selectedSeal!.displayTime = this.addSealForm.controls.displayTime.value;
        this.selectedSeal!.displayReason = this.addSealForm.controls.displayReason.value;
        this.selectedSeal!.reason = this.addSealForm.controls.reason.value;
        this.selectedSeal!.img = {name: this.fileList[0].name, uid: this.fileList[0].uid};
        this.selectedSeal!.users = users;
        this.selectedSeal!.teams = teams;
        if(this.imageData) this.selectedSeal!.imgData = this.imageData;
        this.sealService.editSeal(
          this.selectedSeal!.id,
          this.addSealForm.controls.provider.value,
          this.addSealForm.controls.oAuthClient.value,
          this.addSealForm.controls.oAuthClientSecret.value,
          this.addSealForm.controls.credentialId.value,
          this.addSealForm.controls.credentialPin.value,
          this.addSealForm.controls.name.value,
          this.addSealForm.controls.email.value,
          password,
          this.selectedSeal!.status,
          this.addSealForm.controls.displaySubject.value,
          this.addSealForm.controls.displayDate.value,
          this.addSealForm.controls.displayTime.value,
          this.addSealForm.controls.displayReason.value,
          this.addSealForm.controls.reason.value,
          {name: this.fileList[0].name, uid: this.fileList[0].uid},
          this.selectedSeal!.imgData,
          users,
          teams
        ).subscribe(e=>{
          this.getSeals();
        });
      }
      this.showAddSealModal = false;
      this.resetSealForm();
      this.addSealForm.submitted = false;

    }
    else {
      // this.showFormProvider = true;
      this.showFormSettings = true;

      // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
      // Provide a descriptive, actionable error dialog so users understand how to
      // correct the mistakes highlighted in the form.
      this.modal.error({
        nzTitle: $localize`Form contains errors`,
        nzContent: $localize`Review the highlighted fields and correct each one. For example, ensure that the email address follows <NAME_EMAIL> and that all required fields are completed.`
      });
    }

  }
  submitStampForm() {
    this.addStampForm.submitted = true;
    //if form is valid
    if (!this.addStampForm.invalid && this.fileList.length > 0) {

      //split users and teams
      let users:number[] = [];
      let teams:number[]= [];
      this.selectedUsersAndTeams.forEach(element => {
        if(Number.isInteger(element)) {
          teams.push(element)
        }
        else {
          users.push(Math.floor(element))
        }
      });
      //add Seal
      if (!this.editStampFormMode) {
        this.sealService.addSeal(
          '',
          '',
          '',
          '',
          '',
          this.addStampForm.controls.name.value,
          '',
          '',
          1, //status
          false,
          false,
          false,
          false,
          '',
          {name: this.fileList[0].name, uid: this.fileList[0].uid},
          this.imageData,
          false, //encrypted
          users,
          teams
        ).subscribe(e=>{
          this.getSeals();
        });

      }
      //edit Seal
      else {
        this.selectedSeal!.name = this.addStampForm.controls.name.value;
        this.selectedSeal!.img = {name: this.fileList[0].name, uid: this.fileList[0].uid};
        this.selectedSeal!.users = users;
        this.selectedSeal!.teams = teams;
        if(this.imageData) this.selectedSeal!.imgData = this.imageData;
        this.sealService.editSeal(
          this.selectedSeal!.id,
          '',
          '',
          '',
          '',
          '',
          this.addStampForm.controls.name.value,
          '',
          '',
          this.selectedSeal!.status,
          false,
          false,
          false,
          false,
          '',
          {name: this.fileList[0].name, uid: this.fileList[0].uid},
          this.selectedSeal!.imgData,
          users,
          teams
        ).subscribe(e=>{
          this.getSeals();
        });
      }
      this.showAddStampModal = false;
      this.resetStampForm();
      this.addStampForm.submitted = false;
    }
    else {
      // this.showFormProvider = true;
      this.showFormSettings = true;

      // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
      // Provide actionable guidance for correcting validation errors in the form.
      this.modal.error({
        nzTitle: $localize`Form contains errors`,
        nzContent: $localize`Review the highlighted fields and provide the missing information. Remember to upload a PNG graphic file and complete all required fields.`
      });
    }

  }

  //toggle seal form to add mode
  setAddSealFormMode() {
    this.addSealForm.submitted = false;
    this.editSealFormMode = false;
    this.showAddSealModal = true;
    
    // Update page title to reflect current action
    this.titleService.setTitle($localize`Add Encrypted Stamp - Stamp Settings`);

    //password is required in add mode
    this.addSealForm.get('password').clearValidators();  
    this.addSealForm.get('password').addValidators([Validators.required, Validators.pattern(/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*()_+=\[\]{}|'-])[a-zA-Z0-9!@#$%^&*()_+=\[\]{}|'-]{8,20}$/)]);               
  }
  //toggle stamp form to add mode
  setAddStampFormMode() {
    this.addStampForm.submitted = false;
    this.editStampFormMode = false;
    this.showAddStampModal = true;
    
    // Update page title to reflect current action
    this.titleService.setTitle($localize`Add Stamp - Stamp Settings`);
  }

  //toggle form to edit mode
  setEditFormMode() {
    if (this.selectedSeal?.encrypted) {
      this.addSealForm.submitted = false;
      //password is not requied in edit mode
      this.addSealForm.get('password').clearValidators();
      this.addSealForm.get('password').addValidators(Validators.pattern(/(^$|^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*()_+=\[\]{}|'-])[a-zA-Z0-9!@#$%^&*()_+=\[\]{}|'-]{8,20}$)/));

      this.editSealFormMode = true;
      this.showAddSealModal = true;
      
      // Update page title to reflect current action
      this.titleService.setTitle($localize`Edit Encrypted Stamp - Stamp Settings`);
      this.addSealForm.setValue({
        provider: this.selectedSeal!.provider,
        oAuthClient: this.selectedSeal!.oAuthClient,
        oAuthClientSecret: this.selectedSeal!.oAuthClientSecret,
        credentialId: this.selectedSeal!.credentialId,
        credentialPin: this.selectedSeal!.credentialPin,
        name: this.selectedSeal!.name,
        email: this.selectedSeal!.email,
        password: '',
        displaySubject: this.selectedSeal!.displaySubject,
        displayDate: this.selectedSeal!.displayDate,
        displayTime: this.selectedSeal!.displayTime,
        displayReason: this.selectedSeal!.displayReason,
        reason: this.selectedSeal!.reason,
      });
      this.fileList = [this.selectedSeal!.img];

      this.selectedSeal?.users.forEach(user => {
        this.selectedUsersAndTeams.push(user + 0.1)
      })
      this.selectedSeal?.teams.forEach(team => {
        this.selectedUsersAndTeams.push(team)
      })
    }
    else {
      this.addStampForm.submitted = false;
      //password is not requied in edit mode

      this.editStampFormMode = true;
      this.showAddStampModal = true;
      
      // Update page title to reflect current action
      this.titleService.setTitle($localize`Edit Stamp - Stamp Settings`);
      this.addStampForm.setValue({
        name: this.selectedSeal!.name,
      });
      this.fileList = [this.selectedSeal!.img];

      this.selectedSeal?.users.forEach(user => {
        this.selectedUsersAndTeams.push(user + 0.1)
      })
      this.selectedSeal?.teams.forEach(team => {
        this.selectedUsersAndTeams.push(team)
      })
    }
    
  }

  resetSealForm() {
    this.addSealForm.reset({
      displaySubject: true,
      displayDate: true,
      displayTime: true,
      displayReason: true,
    });
    this.fileList = [];
    this.showAddSealModal = false;
    this.selectedUsersAndTeams = [];
    this.imageData = undefined;
    
    // Restore original page title
    this.titleService.setTitle($localize`Stamp Settings`);
  }
  resetStampForm() {
    this.addStampForm.reset({
    });
    this.fileList = [];
    this.showAddStampModal = false;
    this.selectedUsersAndTeams = [];
    this.imageData = undefined;
    
    // Restore original page title
    this.titleService.setTitle($localize`Stamp Settings`);
  }

  setSelectedIds(ids: number[]) {
    this.selectedUsersAndTeams = ids;
  }
  
  // Handle seal type modal cancellation to restore title
  onSealTypeModalCancel() {
    this.showSealTypeModal = false;
    // Restore original page title
    this.titleService.setTitle($localize`Stamp Settings`);
  }
  
  // Handle delete modal cancellation to restore title
  onDeleteModalCancel() {
    this.showDeleteModal = false;
    // Restore original page title
    this.titleService.setTitle($localize`Stamp Settings`);
  }
  removeNewLineCharacter(event:any) {
    this.addSealForm.patchValue({'reason': event.target.value.replace(/(\r\n|\n|\r)/gm,"")});
  }

  beforeUpload = (file: NzUploadFile): boolean => {
   
    if (file.type !== 'image/png') {
      this.modal.error({
        nzTitle: $localize`The file must be a PNG`
      });

      return false;
    }
    
      //save image as base 64 for demo
      let reader = new FileReader();
      reader.readAsDataURL(file as unknown as File);
      let me = this
      reader.onload = function () {
        me.imageData = reader.result;
      };


    this.fileList = [];
    this.fileList = this.fileList.concat(file);
    return false;
  };

}
