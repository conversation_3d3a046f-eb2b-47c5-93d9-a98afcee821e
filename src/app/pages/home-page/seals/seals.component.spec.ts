// Added RouterTestingModule so the unit test compiles successfully after introducing routerLink
// attributes in the breadcrumb navigation (WCAG 2.4.5 compliance).
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { SealsComponent } from './seals.component';

describe('SealsComponent', () => {
  let component: SealsComponent;
  let fixture: ComponentFixture<SealsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      // Provide Title so the component's dependency injection succeeds after the
      // accessibility change that sets a descriptive page title (WCAG 2.4.2).
      // Without this provider the unit test would fail with a NullInjectorError.
      imports: [
        // Provides the RouterLink directive stub so the component's template that now includes
        // breadcrumb navigation with <a routerLink> compiles in the test environment.
        RouterTestingModule
      ],
      declarations: [ SealsComponent ],
      providers: [Title]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SealsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
