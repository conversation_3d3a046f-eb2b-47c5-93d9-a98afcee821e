import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzTableModule } from 'ng-zorro-antd/table';
import { UsersAndTeamsComponent } from './users-and-teams.component';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { ClickOutsideModule } from 'ng-click-outside';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { TeamSettingComponent } from './team-settings/team-settings.component';
import { BrandingComponent } from './branding/branding.component';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { SharedModule } from "@shared/shared.module";


import { SafeHtmlPipe } from './inner-html/innerhtmlpipe'






@NgModule({
    declarations: [
        UsersAndTeamsComponent,
        TeamSettingComponent,
        BrandingComponent,
        SafeHtmlPipe
    ],
    exports:[
        BrandingComponent,
        TeamSettingComponent,
        ClickOutsideModule,
        UsersAndTeamsComponent
    ],
    imports: [
        CommonModule,
        SharedModule,
        NzTableModule,
        NzButtonModule,
        NzIconModule,
        NzDropDownModule,
        ClickOutsideModule,
        FormsModule,
        NzModalModule,
        NzInputModule,
        ReactiveFormsModule,
        NzMenuModule,
        NzCollapseModule,
        NzFormModule,
        NzCheckboxModule,
        NzUploadModule,
        NzAnchorModule,
        NzInputNumberModule,
        NzToolTipModule
    ],

})
export class UsersAndTeamsModule { }
