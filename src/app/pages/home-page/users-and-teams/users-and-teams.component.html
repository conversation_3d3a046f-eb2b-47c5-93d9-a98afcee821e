<!-- Summary: WCAG 1.4.4 – Converted remaining absolute pixel font-sizes (70px/12px/10px), margins and max-heights (100px) to rem so interface scales with text zoom. -->
<!-- Added missing autocomplete attributes to personal info and password inputs (First Name, Last Name, Email, New Password, Confirm Password) to satisfy WCAG 2.1 SC 1.3.5 Identify Input Purpose.
     WCAG 1.4.3 contrast fix: darkened blue (#0083d2 → #017BC6) for all in-component accents. -->
<!-- Validation update: Added [nzWidth]="'90vw'" to remaining nz-modal dialogs and converted two fixed pixel widths to rem units to complete WCAG 1.4.10 Reflow compliance. -->
<!-- CHANGE (2025-08-06): WCAG 2.1 – SC 3.3.3 (Error Suggestion)
     Replaced vague “Invalid Email Format” messages with actionable guidance that
     includes an example of the correct format (e.g., <EMAIL>). -->
<div *ngIf="!tableOnly">
    <!-- WCAG 1.4.4 – switched absolute 70px heading to relative units so it scales with text resize -->
    <!-- WCAG 1.4.12 fix: removed fixed line-height; switched to 1.5em to accommodate user text spacing -->
    <h1 id="usersTeamsHeading" style="font-size: 4.375rem; line-height: 1em; font-weight: 275; color: #2D323D; padding-bottom: 5rem;"
        class="-mt-6 ml-5 font-thin mobile-title" i18n>
        Users & Teams
    </h1>
    <div class="m-3 inline-block">
        <span class="top-menu">
            <!-- Added explicit type attribute to ensure activation occurs on the up-event only and to prevent implicit form submission (WCAG 2.5.2 Pointer Cancellation) -->
            <button type="button" (click)="modals.addTeam.show=true"
                [class.disabled]="userRoleId == roles.REGULAR_USER || userRoleId == roles.DOC_ADMIN"
                [disabled]="userRoleId == roles.REGULAR_USER || userRoleId == roles.DOC_ADMIN">
                <span class="top-icon " nz-icon nzType="usergroup-add" nzTheme="outline" aria-hidden="true"></span>
                <span class="top-text" i18n>Add Team</span>
            </button>
        </span>
        <span class="top-menu">
            <!-- WCAG 2.5.2 fix – explicit type attribute -->
            <button type="button" (click)="modals.addTeamMember.show=true"
                [disabled]="userRoleId == roles.REGULAR_USER || userRoleId == roles.DOC_ADMIN"
                [class.disabled]="userRoleId == roles.REGULAR_USER || userRoleId == roles.DOC_ADMIN">
                <span class="top-icon" nz-icon nzType="user-add" nzTheme="outline" aria-hidden="true"></span>
                <span class="top-text" i18n>Add Team Members</span>
            </button>
        </span>
        <span class="top-menu">
            <!-- WCAG 2.5.2 fix – explicit type attribute -->
            <button type="button" (click)="showTeamSettingModal()"
                [class.disabled]="setOfCheckedId.length != 1 || !getItemByIndex(setOfCheckedId[0])?.teamName"
                [disabled]="setOfCheckedId.length != 1 || !getItemByIndex(setOfCheckedId[0])?.teamName">
                <span class="top-icon" nz-icon nzType="setting" nzTheme="outline" aria-hidden="true"></span>
                <span class="top-text" i18n>Settings</span>
                <team-settings [dialogType]="teamSettingsDialogType" [groupId]="currentlySelectedItem.id"
                    [parentGroupId]="currentlySelectedItem.parentGroupId" #teamSettings></team-settings>
            </button>
        </span>
        <span class="top-menu">
            <!-- WCAG 2.5.2 fix – explicit type attribute -->
            <button type="button" (click)="showBrandingModal()"
                [disabled]="userRoleId != roles.SUPER_ADMIN && userRoleId != roles.BILLING_ADMIN"
                [class.disabled]="userRoleId != roles.SUPER_ADMIN && userRoleId != roles.BILLING_ADMIN">
                <span class="top-icon" nz-icon nzType="star" nzTheme="outline" aria-hidden="true"></span>
                <span class="top-text" i18n>Branding</span>
                <branding [dialogType]="brandingDialogType" #branding></branding>
            </button>
        </span>
        <span class="top-menu">
            <!-- WCAG 2.4.4 – added explicit aria-label so the purpose of the generic “... More” control is clear when navigated out of context -->
            <!-- WCAG 2.5.2 fix – explicit type attribute -->
            <button type="button" nz-dropdown nzTrigger="click" [matMenuTriggerFor]="MoreMenu" [class.disabled]="disabledMoreButton()"
            [disabled]="disabledMoreButton()" i18n-aria-label aria-label="More actions">
                <span class="top-text" style="margin-right: 0.1875rem;" i18n>... More</span> <!-- 3px → 0.1875rem -->
                <span style="font-size: 0.625rem;" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span> <!-- 10px → 0.625rem -->
            </button>
        </span>
    </div>

    <div class="mb-3 px-3 pt-3" style="background-color: white;">
        <!-- WCAG 2.5.2 fix – explicit type attribute on interactive button -->
        <button nz-button nz-dropdown nzTrigger="click" [matMenuTriggerFor]="menu4" class="add-filter" nz-button
            nzType="dashed" type="button" i18n>+ Add Search Filter</button>
        <!-- WCAG 2.5.2 fix – explicit type attribute -->
        <div *ngFor="let item of activeFilters let i = index" class="filter-tag-container">
            <button type="button" (click)="removeFilter(i)" class="filter-tag-close" nz-button
                nzType="default" [attr.aria-label]="'Remove filter:' + item.filter">
                <i style="top: 0px; position: relative; font-size: 12px;" nz-icon nzType="close" nzTheme="outline" aria-hidden="true"></i>
            </button>
            <span class="filter-tag-text">
                {{item.filter}}
                <span *ngIf="item.value && item.id !='role'">:&nbsp; {{item.value}}</span>
                <span *ngIf="item.value && item.id =='role'">:&nbsp; {{item.roleName}}</span>
            </span>
        </div>
</div>
<mat-menu #menu4="matMenu">
        <button type="button" (click)="changeSearchBar(filterLabels.userName, 'userName')" mat-menu-item i18n>User Name</button>
        <button type="button" (click)="changeSearchBar(filterLabels.teamName, 'teamName')" mat-menu-item i18n>Team Name</button>
        <button type="button" (click)="changeSearchBar(filterLabels.email, 'email')" mat-menu-item>Email</button>
        <button type="button" [matMenuTriggerFor]="subRoles" mat-menu-item i18n>Roles</button>
        <button type="button" (click)="addFilter({filter: filterLabels.users, value: null, id:'users'})" mat-menu-item i18n>Show all Users</button>
        <button type="button" (click)="addFilter({filter:filterLabels.teams, value: null, id:'teams'})" mat-menu-item i18n>Show all Teams</button>
</mat-menu>
<mat-menu #subRoles="matMenu">
    <button type="button" (click)="addFilter({filter:'Role', value: roles.REGULAR_USER, id:'role',roleName:userService.getRoleNameFromId(roles.REGULAR_USER)})"
    mat-menu-item i18n>Regular User</button>
    <button type="button" (click)="addFilter({filter:'Role', value: roles.TEAM_ADMIN, id:'role',roleName:userService.getRoleNameFromId(roles.TEAM_ADMIN)})"
        mat-menu-item i18n>Team Admin</button>
    <button type="button" (click)="addFilter({filter:'Role', value: roles.DOC_ADMIN, id:'role',roleName:userService.getRoleNameFromId(roles.DOC_ADMIN)})"
        mat-menu-item i18n>Doc Admin</button>
    <button type="button" (click)="addFilter({filter:'Role', value: roles.SUPER_ADMIN, id:'role',roleName:userService.getRoleNameFromId(roles.SUPER_ADMIN)})"
        mat-menu-item>Super Admin</button>
</mat-menu>
<mat-menu #MoreMenu="matMenu">
    <span *ngIf="selectMoreDropdown() == 0">
        <button type="button" mat-menu-item (click)="modals.rename.show=true;updateTeamValues()">Rename</button>
        <button type="button" mat-menu-item *ngIf="currentlySelectedItem.parentGroupId != 0" (click)="modals.move.show=true" i18n>Move</button>
        <button type="button" mat-menu-item *ngIf="currentlySelectedItem.parentGroupId != 0 && !disableDelete" (click)="modals.delete.show=true" i18n>Delete</button>
        <button type="button" mat-menu-item *ngIf="currentlySelectedItem.parentGroupId != 0" (click)="modals.editTeam.show=true; updateTeamValues()" i18n>Edit</button>
    </span>
    <span *ngIf="selectMoreDropdown() == 1">
        <button type="button" *ngIf="currentlySelectedItem.status == codes.STATUS_PENDING && !(userRoleId == roles.TEAM_ADMIN && (currentlySelectedItem.roleId == roles.DOC_ADMIN || currentlySelectedItem.roleId == roles.SUPER_ADMIN))"
        mat-menu-item (click)="modals.activationEmail.show=true;" i18n>Resend Activation Email</button>
        <button type="button" *ngIf="currentlySelectedItem.status == codes.STATUS_ACTIVE && !(userRoleId == roles.TEAM_ADMIN && (currentlySelectedItem.roleId == roles.DOC_ADMIN || currentlySelectedItem.roleId == roles.SUPER_ADMIN))"
        mat-menu-item (click)="modals.changeRole.show=true;updateUserValues()" i18n>Change Role</button>
        <button type="button" *ngIf="currentlySelectedItem.status == codes.STATUS_ACTIVE" mat-menu-item (click)="modals.resetPassword.show=true" i18n>Reset Password</button>
        <button type="button" *ngIf="!(userRoleId == roles.TEAM_ADMIN && (currentlySelectedItem.roleId == roles.DOC_ADMIN || currentlySelectedItem.roleId == roles.SUPER_ADMIN)) &&
        currentlySelectedItem.status == codes.STATUS_DELETE"                    
         mat-menu-item (click)="reActivateUser()" i18n>Re-activate</button>  
        <button type="button" *ngIf="currentlySelectedItem.status == codes.STATUS_ACTIVE" mat-menu-item (click)="modals.documentTransfer.show=true; search('')" i18n>Transfer Documents</button>
        <button type="button" mat-menu-item (click)="modals.move.show=true">Move</button>
        <button type="button" *ngIf="!(userRoleId == roles.TEAM_ADMIN && (currentlySelectedItem.roleId == roles.DOC_ADMIN || currentlySelectedItem.roleId == roles.SUPER_ADMIN)) && 
        currentlySelectedItem.status != codes.STATUS_DELETE"
        mat-menu-item (click)="modals.delete.show=true" i18n>Deactivate</button>
        <button type="button" *ngIf="!(userRoleId == roles.TEAM_ADMIN && (currentlySelectedItem.roleId == roles.DOC_ADMIN || currentlySelectedItem.roleId == roles.SUPER_ADMIN)) &&
        currentlySelectedItem.status != codes.STATUS_DELETE"
        mat-menu-item (click)="modals.editUser.show=true;updateUserValues()" i18n>Edit</button>
    </span>
    <span *ngIf="selectMoreDropdown() == 2">
        <!-- WCAG 2.5.2 – explicit type attribute -->
        <button type="button" mat-menu-item (click)="modals.move.show=true" i18n>Move</button>
    </span>
</mat-menu>

    <!-- WCAG 1.4.13 – make search filter panel dismissible via ESC key and click outside,
         announce as dialog, and keep focusable so screen-reader users perceive it as additional content -->
    <div (clickOutside)="searchBarKey=''; searchValue=''; searchBarLabel=''; onSearchDialogDismiss()"
         (keydown.escape)="searchBarKey=''; searchValue=''; searchBarLabel=''; onSearchDialogDismiss()"
         [exclude]="'li'" class="search" *ngIf="searchBarKey" role="dialog"
         [attr.aria-label]="searchBarLabel || 'Search Filter'" tabindex="0">
        <div class="absolute">
            <div class="search-title">{{searchBarLabel}}</div>
            <div class="search-content">
                <!-- WCAG 1.4.3 contrast fix: darkened blue text colour for sufficient contrast on light background -->
                <div *ngIf="searchBarKey != 'teamName' && searchBarKey != 'userName'" style="font-size: 0.75rem; color:#017BC6; font-weight: 300;" i18n>
                    Starts with
                </div>
                <div *ngIf="searchBarKey == 'teamName' || searchBarKey == 'userName'" style="font-size: 0.75rem; color:#017BC6; font-weight: 300;" i18n>
                    Includes
                </div>
                <div style="border-bottom: 1px solid #017BC6; margin-top: 25px;">
                    <input #searchInput tabindex="0" [attr.aria-label]="searchBarLabel" 
                        (keyup.enter)="onSearchInputEnter()"
                        [(ngModel)]="searchValue" style="width: 100%">
                </div>
                <!-- WCAG 2.5.2 – explicit type attribute for apply action button -->
                <button type="button"
                    style="cursor: pointer; color:#017BC6;float: right;text-transform: uppercase;font-size: 0.75rem; margin-top: 0.625rem"
                    (click)="onSearchInputEnter()" i18n>
                    apply</button>
            </div>
        </div>
    </div>
</div>
<!-- WCAG 1.3.1 – added semantic <caption> for data table and removed role="presentation" from modal layout tables -->
<!-- WCAG 1.4.10 – wrapped data table in single-axis scroll region so page never requires two-direction scrolling on small viewports -->
<div class="table-responsive" role="region" aria-labelledby="usersTeamsHeading" tabindex="-1">
<nz-table [nzLoading]="tableLoading" *ngIf="listOfData.length >0" #expandTable nzShowSizeChanger
    [nzPageIndex]="paginationIndex" [nzPageSize]="paginationSize" [nzTotal]="paginationTotal" [nzData]="listOfData"
    [nzShowPagination]="args.showUsers" nzFrontPagination="false" (nzPageIndexChange)="paginationIndex=$event;getData()"
    (nzPageSizeChange)="paginationSize=$event;getData()" (nzCurrentPageDataChange)="onCurrentPageDataChange($event)">
    <thead>
        <tr>
            <th *ngIf="userRoleId != roles.REGULAR_USER && userRoleId != roles.DOC_ADMIN"
            [(nzChecked)]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)"></th>
            <th i18n>Name</th>
            <th i18n>Email</th>
            <th i18n>Role</th>
            <th i18n [nzShowFilter]="showStatusFilter && !tableOnly" [nzFilters]="statusFilter"  (nzFilterChange)="showDeletedUsers($event)">Status</th>
        </tr>
    </thead>
    <tbody>
        <ng-container *ngFor="let data of expandTable.data">
            <ng-container *ngFor="let item of mapOfExpandedData[data.id]">
                <tr [class.teamRow]="item.teamName && item.parentGroupId != 0" [class.rootRow]="item.teamName && item.parentGroupId == 0"
                    *ngIf="((item.parent && item.parent.expand) || !item.parent) && !item.more && (!item.status || showStatus.includes(item.status))">
                    <td *ngIf="(item.roleId != roles.BILLING_ADMIN || tableOnly) && userRoleId != roles.REGULAR_USER && userRoleId != roles.DOC_ADMIN"
                    [nzChecked]="setOfCheckedId.includes(item.id)"
                        (nzCheckedChange)="onItemChecked($event, item)">
            </td>
                    <td *ngIf="(item.roleId == roles.BILLING_ADMIN && !tableOnly) && userRoleId != roles.REGULAR_USER && userRoleId != roles.DOC_ADMIN"></td>
                    <td [nzIndentSize]="item.level! * 10" [nzShowExpand]="!!item.subTeamList && item.teamName && item.parentGroupId != 0"
                        [(nzExpand)]="item.expand"
                        (nzExpandChange)="collapse(mapOfExpandedData[data.id], item, $event);onExpand($event, item)" style="overflow-wrap: normal !important;"
                        [attr.aria-label]="(item.expand ? 'Collapse' : 'Expand') + ' ' + (item.teamName ? item.teamName : item.userName)">
                        {{ item.teamName ? item.teamName : item.userName }}
                    </td>
                    <td class="break-all">{{ item.email }}</td>
                    <td>{{ userService.getRoleNameFromId(item.roleId) }}</td>
                    <td >{{ item.displayStatus }}</td>
                </tr>
                <tr *ngIf="((item.parent && item.parent.expand) || !item.parent) && item.more" class="more">
                    <td colspan="5" (click)="onExpand($event, item, true)"><i
                            class="fa-regular fa-chevron-down mr-1" aria-hidden="true"></i><span i18n>More...</span></td>
                </tr>
            </ng-container>
        </ng-container>
    </tbody>
</nz-table>
</div>

<!-- Modals -->
<!-- WCAG 1.4.13 – enable dismissal of Add Team dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.addTeam.show" i18n-nzTitle nzTitle="Add Team"
    (nzOnOk)="addTeam()" (nzOnCancel)="resetForm('addTeam')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <form [formGroup]="addTeamForm">
            <table class="modal-table">
                <tr>
                    <td i18n>Team Name *</td>
                    <td>
                        <!-- WCAG 3.3.1 (Error Identification):
                             1. Use aria-invalid to programmatically convey the validation state.
                             2. Permanently keep the error message in the accessibility tree and
                                reference it via aria-describedby so assistive technology announces
                                the error when it appears. -->
                        <!-- WCAG 3.3.2 – indicate mandatory field programmatically so all users (including
                             those using assistive technologies) are informed that a value is required. -->
                        <input i18n-aria-label aria-label="Team Name"
                            [style.borderColor]="modals.addTeam.submitted && addTeamForm.controls.teamName.errors ? 'red':''"
                            formControlName="teamName" nz-input required aria-required="true"
                            [attr.aria-invalid]="modals.addTeam.submitted && addTeamForm.controls.teamName.invalid"
                            [attr.aria-describedby]="'add-teamname-error'" />
                        <div id="add-teamname-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeam.submitted && addTeamForm.controls.teamName.errors)" i18n>
                             Team name is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Parent Team *</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <!-- WCAG 3.3.2 – added required indication -->
                            <mat-select name="Parent Name" formControlName="parentName" i18n-aria-label aria-label="Parent Team" required aria-required="true"
                                [attr.aria-invalid]="modals.addTeam.submitted && addTeamForm.controls.parentName.invalid"
                                [attr.aria-describedby]="'add-parentteam-error'">
                                <mat-option *ngFor="let team of listOfAllTeamsRaw" [value]="team.id">{{team.teamName}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div id="add-parentteam-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeam.submitted && addTeamForm.controls.parentName.errors)" i18n>
                             Team is required
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Add Team Member dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.addTeamMember.show" i18n-nzTitle nzTitle="Add Team Member" (nzOnOk)="addTeamMember()"
    (nzOnCancel)="resetForm('addTeamMember')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="addTeamMemberForm">
            <table class="modal-table">
                <tr>
                    <td i18n>First Name *</td>
                    <!-- Assumption: collects user's given name -->
                    <td><input i18n-aria-label aria-label="First Name" autocomplete="given-name" required aria-required="true"
                            [style.borderColor]="modals.addTeamMember.submitted && addTeamMemberForm.controls.firstName.errors ? 'red':''"
                            formControlName="firstName" nz-input
                            [attr.aria-invalid]="modals.addTeamMember.submitted && addTeamMemberForm.controls.firstName.invalid"
                            [attr.aria-describedby]="'addmember-firstname-error'" />
                    <div id="addmember-firstname-error" role="alert" class="text-red-500"
                        [hidden]="!(modals.addTeamMember.submitted && addTeamMemberForm.controls.firstName.errors)" i18n>
                        First name is required
                    </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Last Name *</td>
                    <!-- Assumption: collects user's family name -->
                    <td><input i18n-aria-label aria-label="Last Name" autocomplete="family-name" required aria-required="true"
                            [style.borderColor]="modals.addTeamMember.submitted && addTeamMemberForm.controls.lastName.errors ? 'red':''"
                            formControlName="lastName" nz-input
                            [attr.aria-invalid]="modals.addTeamMember.submitted && addTeamMemberForm.controls.lastName.invalid"
                            [attr.aria-describedby]="'addmember-lastname-error'" />
                        <div id="addmember-lastname-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeamMember.submitted && addTeamMemberForm.controls.lastName.errors)" i18n>
                             Last name is required
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Email *</td>
                    <!-- Assumption: collects user's personal email -->
                    <td><input i18n-aria-label aria-label="Email" type="email" autocomplete="email" required aria-required="true"
                            [style.borderColor]="modals.addTeamMember.submitted && addTeamMemberForm.controls.email.errors ? 'red':''"
                            formControlName="email" nz-input
                            [attr.aria-invalid]="modals.addTeamMember.submitted && addTeamMemberForm.controls.email.invalid"
                            [attr.aria-describedby]="'addmember-email-error'" />
                        <div id="addmember-email-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeamMember.submitted && addTeamMemberForm.controls.email.errors)">
                             <span *ngIf="addTeamMemberForm.controls.email.errors?.required" i18n>Email is required</span>
                             <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                                  Replace vague “Invalid Email Format” with an *actionable* hint so
                                  users understand how to correct the error. -->
                             <span *ngIf="addTeamMemberForm.controls.email.errors?.email" i18n>
                                 Enter a valid email such as <em><EMAIL></em>
                             </span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Role</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Role" formControlName="role" i18n-aria-label aria-label="role">
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.SUPER_ADMIN" i18n>Super Admin</mat-option>
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.DOC_ADMIN" i18n>Doc Admin</mat-option>
                                <mat-option [value]="roles.TEAM_ADMIN" i18n>Team Admin</mat-option>
                                <mat-option [value]="roles.REGULAR_USER" i18n>Regular User</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
                <tr>
                    <td i18n>Team *</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Team Name" formControlName="team" i18n-aria-label aria-label="Team"
                                [attr.aria-invalid]="modals.addTeamMember.submitted && addTeamMemberForm.controls.team.invalid"
                                [attr.aria-describedby]="'addmember-team-error'">
                                <mat-option *ngFor="let team of listOfAllTeamsRaw" [value]="team.id">{{team.teamName}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div id="addmember-team-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeamMember.submitted && addTeamMemberForm.controls.team.errors)" i18n>
                             Team is required
                        </div>
                    </td>
                </tr>
                <tr style="vertical-align: text-top;" *ngIf="addTeamMemberForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Accessible Teams</td>
                    <td>
                            <input type="checkbox" [checked]="onAccessibleTeamsIsCheckedAll(addTeamMemberForm)" i18n-aria-label aria-label="Accessible teams Select all teams"
                             (click)="onAccessibleTeamsCheckAll($event,addTeamMemberForm)" i18n>
                            All
                            <div style="overflow: auto;border: 1px solid #797E8A;padding: 0.3125rem;max-height: 6.25rem;" tabindex="-1" [attr.aria-hidden]="!modals.addTeamMember.show">
                            <div *ngFor="let data of listOfAllTeamsRaw">
                                <input type="checkbox" [checked]="onAccessibleTeamsIsChecked(data.id,addTeamMemberForm)" [attr.aria-label]="'select team:'+data.teamName"
                                 (click)="onAccessibleTeamsChange(data.id, $event, addTeamMemberForm)">
                                {{data.teamName}}<br>
                            </div>
                        </div>
                        <br />
                    </td>
                </tr>
                <tr *ngIf="addTeamMemberForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Acessible Docs</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Documents" formControlName="docs" i18n-aria-label aria-label="Accessible Docs"
                                [attr.aria-invalid]="modals.addTeamMember.submitted && addTeamMemberForm.controls.docs.invalid"
                                [attr.aria-describedby]="'addmember-docs-error'">
                                <mat-option value="0" i18n>All Documents</mat-option>
                                <mat-option value="1" i18n>Only Shared Documents</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div id="addmember-docs-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.addTeamMember.submitted && addTeamMemberForm.controls.docs.errors)" i18n>
                             Accessible docs is required
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Move dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.move.show" i18n-nzTitle nzTitle="Move to Team" (nzOnOk)="moveTeamOrUser()"
    (nzOnCancel)="resetForm('move')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="moveForm">
            <table class="modal-table">
                <tr>
                    <td i18n>Move to</td>
                    <td >
                        <mat-form-field class="material-select-height">
                            <mat-select name="Team" formControlName="team" i18n-aria-label aria-label="Move to"
                                [attr.aria-invalid]="modals.move.submitted && moveForm.controls.team.invalid"
                                [attr.aria-describedby]="'move-team-error'">
                                <mat-option *ngFor="let team of listOfAllTeams" [value]="team.id" i18n>{{team.teamName}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div id="move-team-error" role="alert" class="text-red-500"
                             [hidden]="!(modals.move.submitted && moveForm.controls.team.errors)" i18n>
                             Team is required
                        </div>
                    </td>
                </tr>
            </table>
             <div style="font-size: 0.75rem; margin-top:0.625rem;" i18n>
                Moving teams(s)/user(s) to a different team may change the settings or branding available to them.
            </div>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Change Role dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.changeRole.show" i18n-nzTitle nzTitle="Change Role" (nzOnOk)="editUser()"
    (nzOnCancel)="resetForm('editUser'); modals.changeRole.show = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="editUserForm">
            <table class="modal-table">
                <tr>
                    <td i18n>User Name</td>
                    <td><input disabled nz-input [value]="currentlySelectedItem.firstName" i18n-aria-label aria-label="User Name"/>
                    </td>
                </tr>
                <tr>
                    <td i18n>Email</td>
                    <!-- Assumption: readonly user email still benefits from correct semantics -->
                    <td><input disabled nz-input type="email" autocomplete="email" 
                        [value]="currentlySelectedItem.email" i18n-aria-label aria-label="Email"/>
                    </td>
                </tr>
                <tr>
                    <td i18n>Role</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Role" formControlName="role" i18n-aria-label aria-label="Role">
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.SUPER_ADMIN" i18n>Super Admin</mat-option>
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.DOC_ADMIN" i18n>Doc Admin</mat-option>
                                <mat-option [value]="roles.TEAM_ADMIN" i18n>Team Admin</mat-option>
                                <mat-option [value]="roles.REGULAR_USER" i18n>Regular User</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
                <tr style="vertical-align: text-top;" *ngIf="editUserForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Accessible Teams</td>
                    <td>
                            <input type="checkbox" [checked]="onAccessibleTeamsIsCheckedAll(editUserForm)" i18n-aria-label aria-label="Accessible teams Select all teams"
                             (click)="onAccessibleTeamsCheckAll($event,editUserForm)" i18n>
                            All
                             <div style="overflow: auto;border: 1px solid #797E8A;padding: 0.3125rem;max-height: 6.25rem;" tabindex="-1">
                            <div *ngFor="let data of listOfAllTeamsRaw">
                                <input type="checkbox" [checked]="onAccessibleTeamsIsChecked(data.id,editUserForm)" [attr.aria-label]="'select team:'+data.teamName"
                                 (click)="onAccessibleTeamsChange(data.id, $event, editUserForm)">
                                {{data.teamName}}<br>
                            </div>
                        </div>
                        <br />
                    </td>
                </tr>
                <tr *ngIf="editUserForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Acessible Docs</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Documents" formControlName="docs" i18n-aria-label aria-label="Accessible Docs">
                                <mat-option value="0" i18n>All Documents</mat-option>
                                <mat-option value="1" i18n>Only Shared Documents</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.docs.errors"
                                class="text-red-500" i18n>Accessible docs is required</div>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Rename dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.rename.show" i18n-nzTitle nzTitle="Rename" (nzOnOk)="editTeam()"
    (nzOnCancel)="resetForm('editTeam'); modals.rename.show = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="editTeamForm">
            <table class="modal-table">
                <tr>
                    <td i18n>Current Name</td>
                    <td><input disabled nz-input [value]="currentlySelectedItem.teamName" />
                    </td>
                </tr>
                <tr>
                    <td i18n>Enter New Name</td>
                    <td>
                        <input i18n-aria-label aria-label="Enter new name"
                            [style.borderColor]="modals.editTeam.submitted && editTeamForm.controls.teamName.errors ? 'red':''"
                            formControlName="teamName" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.editTeam.submitted && editTeamForm.controls.teamName.errors"
                                class="text-red-500" i18n>Team name is required</div>
                        </div>
                    </td>
                </tr>

            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Reset Password dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.resetPassword.show" i18n-nzTitle nzTitle="Reset Password" (nzOnOk)="resetPassword()"
    (nzOnCancel)="resetForm('resetPassword')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="resetPasswordForm">
            <table class="modal-table">
                <tr>
                    <td i18n>Name</td>
                    <td><input disabled nz-input [value]="currentlySelectedItem.firstName" />
                    </td>
                </tr>
                <tr style="vertical-align: text-top;">
                    <td i18n>New Password</td>
                    <td><input type="password" i18n-aria-label aria-label="New password" autocomplete="new-password"
                            [style.borderColor]="modals.resetPassword.submitted && resetPasswordForm.controls.password.errors ? 'red':''"
                            formControlName="password" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.resetPassword.submitted && resetPasswordForm.controls.password.errors && resetPasswordForm.controls.password.errors.required"
                                class="text-red-500" i18n>Password is required</div>
                        </div>
                    </td>
                </tr>
                <tr style="vertical-align: text-top;">
                    <td style="min-width:7.8125rem" i18n>Confirm Password</td> <!-- WCAG 1.4.10 – convert fixed pixel min-width to rem so text scales and table reflows -->
                    <td><input type="password" i18n-aria-label aria-label="Confirm password" autocomplete="new-password"
                            [style.borderColor]="modals.resetPassword.submitted && resetPasswordForm.controls.confirmPassword.errors ? 'red':''"
                            formControlName="confirmPassword" nz-input />
                        <div  aria-live="polite"
                            *ngIf="modals.resetPassword.submitted && resetPasswordForm.controls.confirmPassword.errors">
                            <span *ngIf="resetPasswordForm.controls.confirmPassword.errors.required"
                                class="text-red-500" i18n>Password confirmation is required<br></span>
                            <span *ngIf="resetPasswordForm.controls.confirmPassword.errors.mismatch"
                                class="text-red-500" i18n>Passwords must match<br></span>
                            <span *ngIf="resetPasswordForm.controls.confirmPassword.errors.pattern"
                                class="text-red-500" i18n>Your password needs to meet the following requirements:<br>
                                -Between 8-20 characters long<br>
                                -1 uppercase letter<br>
                                -1 lowercase letter<br>
                                -1 number<br>
                                -1 special character of !&#64;#$%^&*()_+=[]{{ '{' }}{{ '}' }}|-<br>
                                -Cannot contain username.<br>
                                -Cannot contain a dictionary word.
                                <br></span>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Document Transfer dialog via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.documentTransfer.show" i18n-nzTitle nzTitle="Document Transfer" (nzOnOk)="documentTransfer()"
    (nzOnCancel)="resetForm('documentTransfer')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="documentTransferForm">
            <table class="modal-table" style="table-layout: fixed;">
                <tr>
                    <td i18n style="width:7.5rem">Name</td> <!-- WCAG 1.4.10 – convert fixed pixel width to rem for responsive reflow -->
                    <td><input disabled nz-input [value]="currentlySelectedItem.firstName" 
                        i18n-aria-label aria-label="Name"/>
                    </td>
                </tr>
                <tr>
                    <td i18n>Email</td>
                    <td><input disabled nz-input [value]="currentlySelectedItem.email" 
                        i18n-aria-label aria-label="Email"/>
                    </td>
                </tr>
                <tr>
                    <td i18n>Recipient</td>
                    <td >
                        <mat-form-field class="material-select-height">
                            <mat-select name="Recipient" formControlName="recipient" i18n-aria-label aria-label="Recipient">
                                <input class="yourClass" #transferDocsSearch placeholder ="search" nz-input
                                (keyup)="search(transferDocsSearch.value)" i18n-aria-label aria-label="Search"> 
                                <mat-option *ngFor="let user of transferDocUsers" [value]="user.id" i18n>{{user.userName + '('+user.email+')'}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div aria-live="polite">
                            <div *ngIf="modals.documentTransfer.submitted && documentTransferForm.controls.recipient.errors"
                                class="text-red-500" i18n>Recipient is required</div>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Delete confirmation dialog via ESC key and click outside -->
<!-- WCAG 2.4.6 – replace vague dialog title with a descriptive one so users immediately understand what is being confirmed -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.delete.show" i18n-nzTitle nzTitle="Confirm Delete / Deactivate" (nzOnOk)="deleteTeamOrUser()"
    (nzOnCancel)="modals.delete.show = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <table class="modal-table">
            <tr>
                <td *ngIf="getItemByIndex(setOfCheckedId[0])?.teamName" i18n>Delete the selected team(s) from the system?</td>
                <td *ngIf="!getItemByIndex(setOfCheckedId[0])?.teamName"i18n>Deactivate the selected user(s) from the system?<br>
                    Documents will remain with this user until you choose to move them to another user.</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Activation Email dialog via ESC key and click outside -->
<!-- WCAG 2.4.6 – provide clear, specific dialog heading for the activation-email confirmation -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.activationEmail.show" i18n-nzTitle nzTitle="Confirm Resend Activation Email" (nzOnOk)="resendActivationEmail()"
    (nzOnCancel)="modals.activationEmail.show = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <table class="modal-table">
            <tr>
                <td i18n>Send activation email to {{currentlySelectedItem.email}}?</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Edit Team Member dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.editUser.show" i18n-nzTitle nzTitle="Edit Team Member" (nzOnOk)="editUser()"
    (nzOnCancel)="resetForm('editUser')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="editUserForm">
            <table class="modal-table">
                <tr>
                    <td i18n>First Name</td>
                    <td><input i18n-aria-label aria-label="First name" autocomplete="given-name"
                            [style.borderColor]="modals.editUser.submitted && editUserForm.controls.firstName.errors ? 'red':''"
                            formControlName="firstName" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.firstName.errors"
                                class="text-red-500" i18n>First name is required</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Last Name</td>
                    <td><input i18n-aria-label aria-label="Last name" autocomplete="family-name"
                            [style.borderColor]="modals.editUser.submitted && editUserForm.controls.lastName.errors ? 'red':''"
                            formControlName="lastName" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.lastName.errors"
                                class="text-red-500" i18n>Last name is required</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Email</td>
                    <td><input i18n-aria-label aria-label="Email" type="email" autocomplete="email"
                            [style.borderColor]="modals.editUser.submitted && editUserForm.controls.email.errors ? 'red':''"
                            formControlName="email" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.email.errors">
                                <span *ngIf="editUserForm.controls.email.errors.required" class="text-red-500" i18n>Email is
                                    required</span>
                                <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                                     Provide explicit guidance so the user knows how to fix the
                                     error instead of only stating that the value is invalid. -->
                                <span *ngIf="editUserForm.controls.email.errors.email" class="text-red-500" i18n>
                                    Enter a valid email such as <em><EMAIL></em>
                                </span>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Role</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Role" formControlName="role" i18n-aria-label aria-label="Role">
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.SUPER_ADMIN" i18n>Super Admin</mat-option>
                                <mat-option *ngIf="userRoleId != roles.TEAM_ADMIN" [value]="roles.DOC_ADMIN" i18n>Doc Admin</mat-option>
                                <mat-option [value]="roles.TEAM_ADMIN" i18n>Team Admin</mat-option>
                                <mat-option [value]="roles.REGULAR_USER" i18n>Regular User</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
                <tr>
                    <td i18n>Team</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Team" formControlName="team" i18n-aria-label aria-label="Team">
                                <mat-option *ngFor="let team of listOfAllTeams" [value]="team.id" i18n>{{team.teamName}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.team.errors"
                                class="text-red-500" i18n>Team is required</div>
                        </div>
                    </td>
                </tr>
                <tr style="vertical-align: text-top;" *ngIf="editUserForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Accessible Teams</td>
                    <td>
                            <input type="checkbox" [checked]="onAccessibleTeamsIsCheckedAll(editUserForm)" i18n-aria-label aria-label="Accessible teams Select all teams"
                            (click)="onAccessibleTeamsCheckAll($event,editUserForm)" i18n>
                            All
                             <div style="overflow: auto;border: 1px solid #797E8A;padding: 0.3125rem;max-height: 6.25rem;" tabindex="-1">
                            <div *ngFor="let data of listOfAllTeamsRaw">
                                <input type="checkbox" [checked]="onAccessibleTeamsIsChecked(data.id,editUserForm)" [attr.aria-label]="'select team:'+data.teamName"
                                (click)="onAccessibleTeamsChange(data.id, $event, editUserForm)">
                                {{data.teamName}}<br>
                            </div>
                        </div>
                        <br />
                    </td>
                </tr>
                <tr *ngIf="editUserForm.controls.role.value == roles.DOC_ADMIN">
                    <td i18n>Acessible Docs</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Documents" formControlName="docs" i18n-aria-label aria-label="Accessible docs">
                                <mat-option value="0" i18n>All Documents</mat-option>
                                <mat-option value="1" i18n>Only Shared Documents</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div aria-live="polite">
                            <div *ngIf="modals.editUser.submitted && editUserForm.controls.docs.errors"
                                class="text-red-500" i18n>Accessible docs is required</div>
                        </div>
                    </td>
                </tr>
            </table>
            <div style="font-size: 12px; margin-top:10px;" i18n>
                Moving user(s) to a different team may change the settings or branding available to them.
            </div>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Edit Team dialog via ESC key and click outside -->
<nz-modal nzClassName="narrow-modal" i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="modals.editTeam.show" i18n-nzTitle nzTitle="Edit Team" (nzOnOk)="editTeam()"
    (nzOnCancel)="resetForm('editTeam')" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="editTeamForm">
            <table class="modal-table">
                <tr>
                    <td i18n>Team Name</td>
                    <td>
                        <input i18n-aria-label aria-label="Team name"
                            [style.borderColor]="modals.editTeam.submitted && editTeamForm.controls.teamName.errors ? 'red':''"
                            formControlName="teamName" nz-input />
                        <div aria-live="polite">
                            <div *ngIf="modals.editTeam.submitted && editTeamForm.controls.teamName.errors"
                                class="text-red-500" i18n>Team name is required</div>
                        </div>
                    </td>
                </tr>
                <tr *ngIf="currentlySelectedItem.parentGroupId != 0">
                    <td i18n>Parent Team</td>
                    <td>
                        <mat-form-field class="material-select-height">
                            <mat-select name="Team" formControlName="parentName" i18n-aria-label aria-label="Parent team">
                                <mat-option *ngFor="let team of listOfAllTeams" [value]="team.id" i18n>{{team.teamName}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                        <div aria-live="polite">
                            <div *ngIf="modals.editTeam.submitted && editTeamForm.controls.parentName.errors"
                                class="text-red-500" i18n>Team is required</div>
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
