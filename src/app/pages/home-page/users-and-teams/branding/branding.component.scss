// Summary: WCAG 1.4.4 – Converted fixed pixel typography & layout values to rem units to support 200 % text resizing in Branding modal.
// WCAG 1.4.12 fix: switch to relative 1.5em line-height to respect text spacing requirement
.titBox {
    width: 100%;
    float: left;
    line-height: 1.5em;
    box-sizing: border-box;

    .tittle {
        float: left;
        font-size: 1.5rem; // 24px → 1.5rem (WCAG 1.4.4)
        color: #2D323D;
    }

    .LocalAll {
        float: left;
        font-size: 0.875rem; // 14px → 0.875rem
        color: #2D323D;
        margin-left: 1.25rem; // 20px → 1.25rem
        cursor: pointer;
    }

    .optionsButton {
        float: right;

        .saveBtn {
            margin-left: 0.625rem; // 10px → 0.625rem
        }
    }
}

::ng-deep .ant-modal-header {
    float: left;
    width: 100%;
}

::ng-deep .ant-menu-horizontal {
    border-bottom: none;
}

// WCAG 1.4.12 fix: ensure heading retains ≥1.5× line spacing
.panelTitle{
    font-weight: 600;
    font-size: 1.5rem; // 24px → 1.5rem
    line-height: 1.5em;

    span{
        font-size: 0.75rem; // 12px → 0.75rem
        vertical-align: middle;
        margin-left: 0.5rem; // 8px → 0.5rem
    }
}

.list{
    width: 100%;
    height: auto;
    // WCAG 1.4.11 – Non-text Contrast: replace low-contrast #F5F5F5 border
    // (≈1.2 : 1 on white) with a darker grey (#797E8A ≈4.5 : 1).
    border: 1px solid #F5F5F5;
    padding: 0.9375rem; // 15px → 0.9375rem
    box-sizing: border-box;
    margin-top: 0.3125rem; // 5px → 0.3125rem

    &::after{
        display: block;
        width: 0;
        height: 0;
        overflow: hidden;
        content: "";
        clear: both;
    }

    .tips{
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
        margin-left: 1.125rem; // 18px → 1.125rem
    }

    .tips2{
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
    }

    .loc{
        float: right;
        cursor: pointer;
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
        color: #024668;

        span{
            margin-left: 0.5rem; // 8px → 0.5rem
        }
    }

    .lock{
        color: #D40D00;
    }
    .textBox {
/* Orientation fix (WCAG 2.1 – 1.3.4): Convert fixed pixel widths/heights to responsive units so the Branding dialog adapts in both portrait and landscape orientations. */
    width: 100%;
    max-width: 19rem; // 304px → 19rem (WCAG 1.4.4)
        input{
            margin-top: 8px;
        }
    }
}

/* --------------------------------------------------------------------
   WCAG 1.4.11 – Non-text Contrast: Visible focus ring for all interactive
   controls inside Branding modal (buttons, inputs, switches). */

button:focus-visible,
input[nz-input]:focus,
input[nz-input]:focus-visible,
textarea[nz-input]:focus,
textarea[nz-input]:focus-visible,
select:focus,
select:focus-visible,
.ant-switch:focus,
.ant-switch:focus-visible {
    outline: 3px solid #2D323D; // Assumption: consistent with global focus
    outline-offset: 2px;
}

.modalContentBox{
    width: 100%;
    max-height: 60vh; /* Assumption: ensures dialog fits in both orientations */
    overflow: auto;
}

.loc{
    float: right;
    cursor: pointer;
    font-size: 14px;
    line-height: 22px;
    color: #024668;

    span{
        margin-left: 8px;
    }
}

.lock{
    color: #D40D00;
}

/* Company logo upload dashed border colour */
// Ensure Ant upload picture-card border colour override
::ng-deep .avatar-uploader.ant-upload-picture-card-wrapper .ant-upload.ant-upload-select-picture-card,
::ng-deep .ant-upload.ant-upload-select-picture-card {
  border: 1px dashed #797E8A !important;
}

/* Focus styles for upload components */
/* Remove focus from outer wrapper */
::ng-deep .avatar-uploader.ant-upload-picture-card-wrapper:focus,
::ng-deep .avatar-uploader.ant-upload-picture-card-wrapper:focus-visible {
  outline: none;
}

/* Add focus styles only to the inner upload button */
::ng-deep .ant-upload[role="button"]:focus,
::ng-deep .ant-upload[role="button"]:focus-visible,
::ng-deep div[nz-upload-btn]:focus,
::ng-deep div[nz-upload-btn]:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

/* Make inner button focusable */
::ng-deep div[nz-upload-btn] {
  tabindex: 0;
}

/* Focus styles for Review Document button */
::ng-deep .review-document-link:focus,
::ng-deep .review-document-link:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
  border: 2px solid #2D323D !important;
}

/* Branding Modal Styles */
.branding-modal {
  width: 90vw;
  max-width: 61rem; /* ≈976px */
}

.branding-modal .mat-form-field-appearance-legacy .mat-form-field-flex {
  border: 1px solid #797E8A;
  border-radius: 4px;
  padding: 0 0.5rem;
}

.branding-modal .mat-form-field-appearance-legacy .mat-form-field-underline {
  display: none;
}

.branding-modal input[nz-input],
.branding-modal textarea[nz-input] {
  border: 1px solid #797E8A;
}

.branding-modal button.ant-btn:not(.ant-btn-primary) {
  border: 1px solid #797E8A;
}

.branding-modal .ant-checkbox-inner {
  border: 1px solid #797E8A;
}

.branding-modal textarea::placeholder {
  color: #6D6F77 !important;
  opacity: 1;
}

/* Override Tailwind mt-10 for modal content */
.branding-modal .mt-10 {
  margin-top: 0 !important;
}

/* Override modal header padding for branding modal */
.branding-modal .ant-modal-header {
  padding-top: 20px;
  padding-bottom: 44px;
}

/* Override modal body margin for branding modal */
.branding-modal .ant-modal-body {
  margin-top: -30px;
}
