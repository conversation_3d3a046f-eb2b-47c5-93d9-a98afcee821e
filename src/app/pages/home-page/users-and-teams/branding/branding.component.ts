import { Component,  Input, OnInit, ViewEncapsulation, ElementRef, Renderer2, AfterViewInit } from '@angular/core';
// CHANGE (2025-08-06): WCAG 4.1.2 – added post-render patch to label the hidden
// file inputs and their trigger buttons inside <nz-upload> so automated audits
// no longer flag unnamed controls.
import { Observable, Observer } from 'rxjs';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BrandingService } from "@core/services";
import { DatePipe } from '@angular/common'

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'branding',
    templateUrl: './branding.component.html',
    styleUrls: ['./branding.component.scss'],
    providers: [DatePipe],
    encapsulation: ViewEncapsulation.Emulated
})
export class BrandingComponent implements OnInit, AfterViewInit {

  @Input() dialogType:any;
  isConfirmLoading = false;
  isLocalAll = false;

  loading = false;
  avatarUrl?: string;

  formData = {
      "companyName": "",
      "useCompanyBranding": false,
      "companyLogointheEmailHeader": "",
      "companyLogoonSigningPage": "",
      "emailSubject": "",
      "emailFooter": "",
      "emailSignature": "",
      "emailTaglineBelowLogo": "",
    //   "signerHelpContent": "",
      "enableCustomTermsofService": false,
      "customTermsOfService": "",
      "customTermsOfUse": "",
      "teamName": "",
      "providerType": 0,
      "host": "",
      "port": "",
      "email": "",
      "userName": "",
      "password": "",
      "encryptionType": "",
      "targetEmail": "",
      "locks": [
          {
              "id": 53225,
              "brandingId": 4793,
              "itemId": 1,
              "itemName": "NotificationReminderEmailEnabled",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53226,
              "brandingId": 4793,
              "itemId": 2,
              "itemName": "DefaultReminderEmailInterval",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53227,
              "brandingId": 4793,
              "itemId": 3,
              "itemName": "DefaultMaxReminderEmailCount",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53228,
              "brandingId": 4793,
              "itemId": 4,
              "itemName": "DefaultExpireDaysAfterSent",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53229,
              "brandingId": 4793,
              "itemId": 5,
              "itemName": "NotificationExpiredDocEmailEnabled",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53230,
              "brandingId": 4793,
              "itemId": 6,
              "itemName": "DefaultWarnSender",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53231,
              "brandingId": 4793,
              "itemId": 7,
              "itemName": "ContactsSharingEnabled",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53232,
              "brandingId": 4793,
              "itemId": 8,
              "itemName": "DocSharingEnabled",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53233,
              "brandingId": 4793,
              "itemId": 9,
              "itemName": "TemplateSharingEnabled",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53234,
              "brandingId": 4793,
              "itemId": 10,
              "itemName": "TeamTwoFAType",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53235,
              "brandingId": 4793,
              "itemId": 11,
              "itemName": "DefaultTextSize",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53236,
              "brandingId": 4793,
              "itemId": 12,
              "itemName": "DefaultDateSize",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          },
          {
              "id": 53237,
              "brandingId": 4793,
              "itemId": 13,
              "itemName": "EmailSignature",
              "locked": 0,
              "lockedUserId": 1,
              "lockedUserName": "super t",
              "createTime": "2023-01-17 04:36:14",
              "updateTime": "2023-01-17 04:36:14"
          }
      ]
  };
  // public formData: any[] = [];

  passwordVisible = false;
  emailDialog = false;
  originallyDate = "";

  smtpCustomizationEnabled = false;
  productSmtpCustomizationEnabled = false;
  customTermsOfUseEnabled = false;
  dualTermOfServiceEnabledEnabled = false;
  emailSignature = "";
  lockedUserName="";
  lockedUserId=0;

  getData() {
      this.branding.get_branding_setting().subscribe(v => {
          this.formData = v.data
          //   this.formData.useCompanyBranding = !this.formData.useCompanyBranding
          this.formData.targetEmail = ""
          this.formData.emailSubject = this.htmlToString(this.formData.emailSubject);
          this.formData.emailFooter = this.htmlToString(this.formData.emailFooter);
          this.formData.emailSignature = this.htmlToString(this.formData.emailSignature);
          this.formData.emailTaglineBelowLogo = this.htmlToString(this.formData.emailTaglineBelowLogo);
          this.formData.customTermsOfUse = this.htmlToString(this.formData.customTermsOfUse);
          this.formData.companyName = this.htmlToString(this.formData.companyName);
      });
  }

  showModal(): void {
      this.getData();
      this.dialogType = true;
  }

  handleOk(): void {
      //   this.formData.useCompanyBranding = !this.formData.useCompanyBranding
      this.isConfirmLoading = true;
      this.branding.put_branding_setting(this.formData).subscribe(v => {
          if(v.success) {
            this.msg.success('Success');
          }
          this.dialogType = false;
          this.isConfirmLoading = false;
          this.getData();
      });
      // setTimeout(() => {
      //     this.dialogType = false;
      //     this.isConfirmLoading = false;
      // }, 1000);
  }

  handleCancel(): void {
      this.dialogType = false;
  }

  handleLock(n:number): void {
      this.formData.locks[n].locked = 2;
      this.formData.locks[n].lockedUserId = this.lockedUserId;
      this.formData.locks[n].lockedUserName = this.lockedUserName;
  }

  handleOpenLock(n:number): void {
      this.formData.locks[n].locked = 1;
  }

  // beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): Observable<boolean> =>
  //     new Observable(() => {
  //         const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  //         if (!isJpgOrPng) {
  //             this.msg.error('You can only upload JPG file!');
  //             // observer.complete();
  //             return false;
  //         }
  //         const isLt2M = file.size! / 1024 / 1024 < 5;
  //         if (!isLt2M) {
  //             this.msg.error('Image must smaller than 5MB!');
  //             // observer.complete();
  //             return false;
  //         }
  //         // observer.next(isJpgOrPng && isLt2M);
  //         // observer.complete();
  //         return false;
  //     });

  beforeUpload = (file: NzUploadFile): boolean => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
          this.msg.error('You can only upload JPG file!');
          return false;
      }
      const isLt2M = file.size! / 1024 / 1024 < 5;
      if (!isLt2M) {
          this.msg.error('Image must smaller than 5MB!');
          return false;
      }
      this.fileToBase64(file, (base64:any) => {
          this.formData.companyLogointheEmailHeader = base64;
      })
      return false;
  };

  beforeUploadCompanyLogoonSigningPage = (file: NzUploadFile): boolean => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
          this.msg.error('You can only upload JPG file!');
          return false;
      }
      const isLt2M = file.size! / 1024 / 1024 < 5;
      if (!isLt2M) {
          this.msg.error('Image must smaller than 5MB!');
          return false;
      }
      this.fileToBase64(file, (base64:any) => {
          this.formData.companyLogoonSigningPage = base64;
      })
      return false;
  };

  beforeUploadEmailTaglineBelowLogo = (file: NzUploadFile): boolean => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
          this.msg.error('You can only upload JPG file!');
          return false;
      }
      const isLt2M = file.size! / 1024 / 1024 < 5;
      if (!isLt2M) {
          this.msg.error('Image must smaller than 5MB!');
          return false;
      }
      this.fileToBase64(file, (base64:any) => {
          this.formData.emailTaglineBelowLogo = base64;
      })
      return false;
  };

  private getBase64(img: File, callback: (img: string) => void): void {
      const reader = new FileReader();
      reader.addEventListener('load', () => callback(reader.result!.toString()));
      reader.readAsDataURL(img);
  }

  fileToBase64 = (file:any, callback:any) =>{
      const reader = new FileReader()
      reader.onload = function(evt:any){
          if(typeof callback === 'function') {
              callback(evt.target.result)
          } else {
              console.log("我是base64:", evt.target.result);
          }

      }
      reader.readAsDataURL(file);
  }

  handleChange(info: { file: NzUploadFile }): void {
      switch (info.file.status) {
      case 'uploading':
          this.loading = true;
          break;
      case 'done':
          // Get this url from response in real world.
          this.getBase64(info.file!.originFileObj!, (img: string) => {
              this.loading = false;
              this.formData.companyLogointheEmailHeader = img;
          });
          break;
      case 'error':
          this.loading = false;
          break;
      }
  }

  handleChangeCompanyLogoonSigningPage(info: { file: NzUploadFile }): void {
      switch (info.file.status) {
      case 'uploading':
          this.loading = true;
          break;
      case 'done':
          // Get this url from response in real world.
          this.getBase64(info.file!.originFileObj!, (img: string) => {
              this.loading = false;
              this.formData.companyLogoonSigningPage = img;
          });
          break;
      case 'error':
          this.loading = false;
          break;
      }
  }

  handleChangeEmailTaglineBelowLogo(info: { file: NzUploadFile }): void {
      switch (info.file.status) {
      case 'uploading':
          this.loading = true;
          break;
      case 'done':
          // Get this url from response in real world.
          this.getBase64(info.file!.originFileObj!, (img: string) => {
              this.loading = false;
              this.formData.emailTaglineBelowLogo = img;
          });
          break;
      case 'error':
          this.loading = false;
          break;
      }
  }

  focusUploadButton(event: FocusEvent, uploadId: string): void {
      // Prevent the outer wrapper from getting focus
      event.preventDefault();
      event.stopPropagation();
      
      // Find the inner upload button and focus it
      const uploadElement = event.target as HTMLElement;
      const innerButton = uploadElement.querySelector('div[nz-upload-btn]') as HTMLElement;
      if (innerButton) {
          innerButton.focus();
      }
  }

  openReviewDocument(): void {
      const url = 'https://test.signority.net/UI/documentDesigner2.html?iid=&lang=en';
      window.open(url, '_blank', 'noopener');
  }









  handleEmailDialogOk(): void {
      this.emailDialog = false;
      this.isConfirmLoading = true;
      this.branding.put_test_email({
          SMTPProviderType: this.formData.providerType,
          SMTPEmail: this.formData.email,
          SMTPHost: this.formData.host,
          SMTPPort: this.formData.port,
          SMTPUserName: this.formData.userName,
          SMTPPassword: this.formData.password,
          SMTPEncryptionType: this.formData.encryptionType,
          targetEmail: this.formData.targetEmail
      }).subscribe(v => {
          console.log("sendFormData", v)
          if(v.success) this.msg.success('Success');
          this.emailDialog = false;
          this.isConfirmLoading = false;
      });
  }

  handleEmailDialogCancel(): void {
      this.emailDialog = false;
  }

  htmlToString(html: string): string {
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
  }

  constructor(
    private branding: BrandingService,
    private msg: NzMessageService,
    private datePipe: DatePipe,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
      //   this.getData()
      setInterval(() => {
          this.emailSignature = this.formData.emailSignature.replace(/\n/g,'<br/>')
      }, 100);
      let userInfo = localStorage.getItem("userInfo");
      let dateFormat = JSON.parse(userInfo || '0').accountdetails.branding?.DefaultDateFormat?.replace(/D/g, 'd');
      this.originallyDate = <string>this.datePipe.transform(new Date(), dateFormat ? dateFormat : "YYYY-MM-DD".replace(/D/g, 'd'));
      let smtpCustomizationEnabled = JSON.parse(userInfo || '0').accountdetails.smtpCustomizationEnabled;
      this.smtpCustomizationEnabled = !smtpCustomizationEnabled;
      let user = localStorage.getItem("user");
      this.productSmtpCustomizationEnabled = JSON.parse(user || '0').userProduct.smtpCustomizationEnabled;
      this.customTermsOfUseEnabled = JSON.parse(user || '0').userProduct.customNoticeEnabled;
      this.dualTermOfServiceEnabledEnabled = JSON.parse(user || '0').userProduct.dualTermOfServiceEnabled;
      let lockedUserId = JSON.parse(user || '0').id;
      let lockedUserName = JSON.parse(user || '0').firstname + ' ' + JSON.parse(user || '0').lastname;
      this.lockedUserId = lockedUserId;
      this.lockedUserName = lockedUserName;
  }

  /**
   * WCAG 4.1.2 (Name, Role, Value)
   * ---------------------------------
   * NG-ZORRO’s <nz-upload> renders a visually hidden <input type="file"> and a
   * <div role="button"> trigger with no accessible name.  Automated audits
   * therefore flag both as unnamed widgets.  After the view initialises we
   * iterate over each upload component and:
   *   • Copy any `aria-label` present on the host <nz-upload> or outer <label>
   *     to the generated file input and trigger.
   *   • Ensure both elements are keyboard-focusable.
   *
   * Assumption: Internal markup uses the selectors 'input[type="file"]' and
   * 'div[role="button"], div.ant-upload' (depending on NG-ZORRO versions).
   */
  ngAfterViewInit(): void {
    // Defer to allow NG-ZORRO to finish rendering its internal template.
    setTimeout(() => {
      const uploadHosts: NodeListOf<HTMLElement> = this.elementRef.nativeElement.querySelectorAll('nz-upload');
      uploadHosts.forEach(host => {
        const hostLabel = host.getAttribute('aria-label') || host.closest('label')?.getAttribute('aria-label') || 'File upload';

        // Patch hidden file input.
        const fileInput: HTMLInputElement | null = host.querySelector('input[type="file"]');
        if (fileInput && !fileInput.hasAttribute('aria-label')) {
          this.renderer.setAttribute(fileInput, 'aria-label', hostLabel);
        }

        // Patch clickable trigger – NG-ZORRO wraps content in a div that has
        // role="button" once interactive.
        const trigger: HTMLElement | null = host.querySelector('div[role="button"], div.ant-upload, div.ant-upload-select');
        if (trigger && !trigger.hasAttribute('aria-label')) {
          this.renderer.setAttribute(trigger, 'aria-label', hostLabel);
        }
        // Ensure trigger is keyboard-operable when it is a DIV.
        if (trigger && trigger.tagName === 'DIV' && !trigger.hasAttribute('tabindex')) {
          this.renderer.setAttribute(trigger, 'tabindex', '0');
          // Attach keyboard handler once per element.
          if (!(trigger as any).__keyboardHandler) {
            const handler = (event: KeyboardEvent) => {
              if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                trigger.click();
              }
            };
            (trigger as any).__keyboardHandler = handler;
            this.renderer.listen(trigger, 'keydown', handler);
          }
        }
      });
    });
  }

}
