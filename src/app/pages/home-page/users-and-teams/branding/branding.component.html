<style>
  nz-form-label[aria-label="Company Logo in the Email Header"],
  nz-form-label[aria-label="Company Logo on Signing Page"] {
    text-wrap: unset;
  }
</style>

<!-- Summary: WCAG 1.4.4 – Converted fixed 340px widths & 8px margins to rem units (21.25rem/0.5rem) for scalable text; comments placed outside tags -->
<!-- CHAN<PERSON> (2025-08-06): Added aria-label attributes to all <nz-form-label> instances so that the
     accessible name of each form control now begins with the exact visible label text, satisfying
     WCAG 2.5.3 “Label in Name”. -->
<!-- Added autocomplete attributes (organization, email, username, current-password, off for technical fields) per WCAG 2.1 SC 1.3.5 Identify Input Purpose -->
<!-- WCAG 1.3.3 – Reworded instructional text to remove reliance on spatial cues (e.g., "below", "above", "upper right") and reference controls by label instead. -->
<!-- Orientation fix (WCAG 2.1 – 1.3.4): switched to viewport-relative width to avoid horizontal
     overflow on small portrait devices. -->
<!-- WCAG 1.4.13 – enable dismissal of Branding dialog via ESC key and click outside -->
<nz-modal nzClassName="branding-modal wide-modal" [(nzVisible)]="dialogType" [nzTitle]="modalTitle" [nzContent]="modalContent" [nzFooter]="null"
    (nzOnCancel)="handleCancel()" [nzClosable]="false" [nzWidth]="'95vw'" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-template #modalTitle>
<!-- WCAG 1.4.3 contrast fix: replaced #0083d2 link/text colour with #017BC6 for sufficient contrast -->
<div class="titBox">
            <!-- WCAG 1.3.1 – promote dialog title to <h1> to maintain heading hierarchy -->
            <h1 class="tittle" i18n>Branding</h1>
            <div class="optionsButton">
                <!-- WCAG 2.5.2 – add explicit type attribute to ensure activation on up-event and prevent implicit form submission -->
                <button nz-button nzType="default" type="button" (click)="handleCancel()" i18n>Cancel</button>
                <button nz-button nzType="primary" type="button" (click)="handleOk()" [nzLoading]="isConfirmLoading" class="saveBtn"
                    i18n>Save</button>
            </div>
        </div>
    </ng-template>

    <ng-template #modalContent>
        <div class="modalContentBox" tabindex="-1">
            <form nz-form >
                <nz-collapse nzGhost >
                    <nz-collapse-panel  [nzActive]="true" [nzShowArrow]="false" [nzHeader]="modalBrandingSetting">
                        <ng-template #modalBrandingSetting >
                            <h3  class="panelTitle" i18n>Settings</h3>
                        </ng-template>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label so that the programmatic name of the
                                 underlying <label> element now begins with the exact visible text
                                 "Company Name" – satisfies WCAG 2.5.3 ‘Label in Name’. -->
                            <nz-form-label [nzSpan]="8" nzFor="companyName" i18n i18n-aria-label aria-label="Company Name">Company Name</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- Assumption: id must match nzFor attribute for proper programmatic association -->
                                <!-- Assumption: collects organization name, add autocomplete token -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <input nz-input name="companyName" id="companyName" style="max-width: 21.25rem;" autocomplete="organization"
                                    [(ngModel)]="formData.companyName" i18n-aria-label aria-label="Company Name">
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item class="xs:inline-block" style="width: 100%;">
                            <!-- WCAG 1.3.1 – removed mismatched nzFor to avoid NonExistentFragment warning -->
                            <!-- CHANGE (2025-08-06): Repeat visible text inside aria-label for WCAG 2.5.3 compliance. -->
                            <nz-form-label [nzSpan]="8" i18n i18n-aria-label aria-label="Use Company Branding">Use Company
                                Branding</nz-form-label>
                            <nz-form-control class="xs:inline-block ml-2">
                                <label nz-checkbox name="useCompanyBranding" i18n-aria-label aria-label="Use company branding"
                                    [(ngModel)]="formData.useCompanyBranding"></label>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label equal to visible string (line breaks and
                                 colon removed) so that accessible name contains visible words. -->
                            <nz-form-label [nzSpan]="8" nzFor="avatar1" [nzNoColon]="true" i18n i18n-aria-label aria-label="Company Logo in the Email Header">Company Logo in the
                                Email Header:<br />(suggested size: 200px*83px)</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- WCAG 1.3.1 – wrap file upload in a label so the hidden file input receives an accessible name -->
                                <label aria-label="Company Logo in the Email Header" style="display:block;">
                                    <!-- WCAG 4.1.2 – supply accessible name for the custom upload control so screen readers announce its purpose -->
                                    <nz-upload id="avatar1" class="avatar-uploader" nzName="avatar1" nzListType="picture-card"
                                        aria-label="Upload company logo in the email header"  
                                        [nzShowUploadList]="false" [nzBeforeUpload]="beforeUpload"
                                        (nzChange)="handleChange($event)" tabindex="-1" (focus)="focusUploadButton($event, 'avatar1')">
                                        <ng-container *ngIf="!formData.companyLogointheEmailHeader">
                                            <i class="fa-solid fa-plus" aria-hidden="true"></i>
                                        </ng-container>
                                        <img *ngIf="formData.companyLogointheEmailHeader" i18n-aria-label aria-label="Company Logo in the Email Header" i18n-alt alt="Company logo in email header" [src]="formData.companyLogointheEmailHeader" style="width: 100%" />
                                    </nz-upload>
                                </label>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label matching visible text for WCAG 2.5.3. -->
                            <nz-form-label [nzSpan]="8" nzFor="avatar" [nzNoColon]="true" i18n i18n-aria-label aria-label="Company Logo on Signing Page">Company Logo on Signing
                                Page:<br />(suggested size: 140px*48px)</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <label aria-label="Company Logo on Signing Page" style="display:block;">
                                    <!-- WCAG 4.1.2 – accessible name for second upload control -->
                                    <nz-upload id="avatar" class="avatar-uploader" nzName="avatar" nzListType="picture-card"
                                        aria-label="Upload company logo on signing page"
                                        [nzShowUploadList]="false" [nzBeforeUpload]="beforeUploadCompanyLogoonSigningPage"
                                        (nzChange)="handleChangeCompanyLogoonSigningPage($event)" tabindex="-1" (focus)="focusUploadButton($event, 'avatar')">
                                        <ng-container *ngIf="!formData.companyLogoonSigningPage">
                                            <i class="fa-solid fa-plus" aria-hidden="true"></i>
                                        </ng-container>
                                        <img *ngIf="formData.companyLogoonSigningPage" i18n-aria-label aria-label="Company Logo on Signing Page" i18n-alt alt="Company logo on signing page" [src]="formData.companyLogoonSigningPage" style="width: 100%" />
                                    </nz-upload>
                                </label>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label equal to visible label text. -->
                            <nz-form-label [nzSpan]="8" nzFor="emailSubject" i18n i18n-aria-label aria-label="Email Subject">Email Subject</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <textarea id="emailSubject" rows="4" nz-input name="emailSubject"
                                    [(ngModel)]="formData.emailSubject"  i18n-aria-label aria-label="Email Subject"></textarea>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): WCAG 2.5.3 compliance. -->
                            <nz-form-label [nzSpan]="8" nzFor="emailFooter" i18n i18n-aria-label aria-label="Email Footer">Email Footer</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <textarea id="emailFooter" rows="4" nz-input name="emailFooter" i18n-aria-label aria-label="Email Footer"
                                [(ngModel)]="formData.emailFooter"></textarea>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label for complex label containing lock icons to
                                 ensure programmatic name starts with visible text. -->
                            <nz-form-label [nzSpan]="8" nzFor="emailSignature" i18n-aria-label aria-label="Email Signature">
                                <div *ngIf="formData.locks[12].locked === 0 || formData.locks[12].locked === 1"
                                    class="loc" (click)="handleLock(12)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"
                                        style="margin:0 0.5rem;"></i></div> <!-- WCAG 1.4.4 – margin 8px→0.5rem -->
                                <div *ngIf="formData.locks[12].locked === 2" class="loc lock"
                                    (click)="handleOpenLock(12)" i18n>Locked by {{ formData.locks[12].lockedUserName
                                    }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin:0 0.5rem;"></i></div> <!-- WCAG 1.4.4 – margin 8px→0.5rem -->
                                <span i18n>Email Signature</span>
                            </nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <textarea id="emailSignature" rows="8" nz-input name="emailSignature" [(ngModel)]="formData.emailSignature"
                                    [disabled]="formData.locks[12].locked === 2 ? true : false" i18n-aria-label aria-label="Email Signature"></textarea>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): align label in name. -->
                            <nz-form-label [nzSpan]="8" nzFor="emailTaglineBelowLogo" i18n i18n-aria-label aria-label="Email Tagline Below Logo">Email Tagline Below Logo</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <textarea nz-input name="emailTaglineBelowLogo" id="emailTaglineBelowLogo" style="max-width: 21.25rem;"
                                    [(ngModel)]="formData.emailTaglineBelowLogo"  i18n-aria-label aria-label="Email Tagline Below Logo"></textarea>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- Label associates with preview container for screen reader context -->
                            <!-- CHANGE (2025-08-06): Add aria-label for Email Preview. -->
                            <nz-form-label [nzSpan]="8" nzFor="content" i18n i18n-aria-label aria-label="Email Preview">Email Preview</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <div id="content" class="netease_mail_readhtml netease_mail_readhtml_webmail">
                                    <table
                                        style="word-break: break-word; margin: 0; padding: 0; width: 100%; line-height: 100% !important; background-color: #efefef; border-width: 1.5pt; border-style: solid; border-bottom-color:#aaa; border-right-color:#aaa; border-top-color:#ccc; border-left-color:#ccc; border-radius: 5px; -moz-border-radius: 5px; -webkit-border-radius: 5px;"
                                        cellpadding="0" cellspacing="10" border="1">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <table cellpadding="10" cellspacing="0" border="0" align="left"
                                                        width="100%">
                                                        <tbody>
                                                            <tr>
                                                                <td max-width="200px" valign="top"
                                                                    style="background-color: #FFF; border-width:2px; border-style:solid; border-bottom-color:#aaa; border-right-color:#aaa; border-top-color:#ccc; border-left-color:#ccc; border-radius:5px;">
                                                                    <img *ngIf="!formData.companyLogointheEmailHeader || !formData.useCompanyBranding"
                                                                        src="assets/images/BasicSiteCompanyLogoRegular.png?version=3"
                                                                        max-width="200" height="83" title="Signority"
                                                                        i18n-alt alt="Default company logo">
                                                                    <img *ngIf="formData.companyLogointheEmailHeader && formData.useCompanyBranding"
                                                                        [src]="formData.companyLogointheEmailHeader"
                                                                        max-width="200" height="83" title="Signority"
                                                                        i18n-alt alt="Company logo in email header"> <br> <span
                                                                        *ngIf="formData.emailTaglineBelowLogo && formData.useCompanyBranding" 
                                                                        [innerHtml]="formData.emailTaglineBelowLogo"></span>
                                                                    <hr><br><br> <span
                                                                        style=" font-family: Arial,sans-serif; font-size: 10.5pt; color: #575747"
                                                                        i18n>Dear
                                                                        <b>XXX<span style="display: none;"
                                                                                name="{[GUID_5abf2aec-ffbf-4573-a586-2a85de0b552e_GUID]}>"></span></b>,</span>
                                                                    <br> <span
                                                                        style=" font-family: Arial,sans-serif; font-size: 10.5pt; line-height: 1.3; color: #575747"></span>
                                                                    <span i18n
                                                                        style=" font-family: Arial,sans-serif; font-size: 10.5pt; line-height: 1.3; display:block; color: #575747"><br><span>You
                                                                            have been sent the following document for
                                                                            your
                                                                            electronic signature:
                                                                            <b>Document name</b> </span><br><span> The
                                                                            document
                                                                            was originally sent to you
                                                                            on: <b>{{ originallyDate
                                                                                }}</b></span><br><span> This
                                                                            document was sent to you by:
                                                                            <b>XXX</b></span><br><span>Please access the
                                                                            document using the following link.</span> <!-- Assumption: Replaced spatial reference "below" to comply with WCAG 1.3.3 -->
                                                                        </span> <!-- Validation fix: closed outer <span> before introducing block-level <div> to maintain proper HTML structure -->
                                                                        <br><br>
                                                                        <!-- WCAG 1.4.3 contrast fix: darkened blue background (#0082d2 → #017BC6) so white text meets ≥4.5:1 ratio -->
                                                                        <div
                                                                            style="width: 100%; height:20px; line-height:20px;background-color:#017BC6; display:inline-block; color:#ffffff; font-size:10.5pt; font-weight:bold;text-align:center;"
                                                                            class="review-document-button" tabindex="-1">
                                                                            <!-- <div
                                                                                style="width: 100%; height:20px; line-height:20px;background-color:#017BC6; display:inline-block; color:#ffffff; font-family:&quot;Arial&quot;,sans-serif; font-size:10.5pt; font-weight:bold;text-align:center;"> -->
                                                                            <a href="javascript:void(0)" #reviewDocumentLink
                                                                                style=" color: #ffffff; display: block; width: 100%; height: 100%; text-decoration: none;" class="review-document-link" tabindex="0"
                                                                                (click)="openReviewDocument()" (keydown.enter)="openReviewDocument()"><span
                                                                                    style="color: #fffffe;">Review
                                                                                    Document</span></a>
                                                                        </div><br><br>
                                                                        <span
                                                                            style=" font-family: Arial,sans-serif; font-size: 10.5pt; line-height: 1.2; color: #575747">
                                                                            <!-- WCAG 1.3.3 – Removed spatial reference "above" by identifying link by its text label -->
                                                                            If the "Review Document" link does not work, you can
                                                                            paste
                                                                            the following address into
                                                                            your browser address bar:
                                                                            https://test.signority.net/UI/documentDesigner2.html?iid=&amp;lang=en
                                                                            <br> <br> If you are not the signing
                                                                            authority
                                                                            please Reject the Document or
                                                                            Change the Signer. To do this, open the
                                                                            document by clicking "Review
                                                                            Document" and then selecting the "More" menu on the page.
                                                                            <!-- Assumption: Removed "upper right" spatial reference and clarified action -->
                                                                            In the
                                                                            menu select either the “Change Signer” or
                                                                            “Reject” option depending on what
                                                                            you want to do. If you don't see these
                                                                            options,
                                                                            contact the document sender
                                                                            for assistance.To stop future email
                                                                            reminders,
                                                                            <!-- WCAG 2.4.4 – replace generic “here” link text with descriptive wording so the link’s purpose is clear in isolation -->
                                                                            click <a target="_blank"
                                                                                href="https://test.signority.net/UI/invitationStopReminders.html?guid="
                                                                                i18n-aria-label aria-label="Stop email reminders – opens in new tab" tabindex="-1">Stop email reminders</a>.
                                                                            <br><br> </span><br><br><br>
                                                                        <span
                                                                            *ngIf="!formData.emailSignature || !formData.useCompanyBranding">Regards,<br><br>Signority
                                                                            Team</span><span
                                                                            *ngIf="formData.emailSignature && formData.useCompanyBranding"
                                                                        [innerHTML]="emailSignature | safeHtml"></span>
                                                                        <br>
                                                                        <!-- Validation fix: removed extraneous closing </span> that caused NG5002 parse error -->
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td max-width="200px" valign="top"
                                                                    style="background-color: #FFF; border-width:2px; border-style:solid; border-bottom-color:#aaa; border-right-color:#aaa; border-top-color:#ccc; border-left-color:#ccc; border-radius:5px;">
                                                                    <span
                                                                        *ngIf="!formData.emailFooter || !formData.useCompanyBranding"
                                                                        i18n
                                                                        style=" font-family: Arial,sans-serif; font-size: 9pt; color: #575747">
                                                                        This
                                                                        message was intended for you and sent by
                                                                        Signority,
                                                                        a secure electronic
                                                                        signature service. To launch the site
                                                                        successfully,
                                                                        please use Firefox, Chrome,
                                                                        Safari, IE10 or above.<br>To read more about us,
                                                                        follow <a target="_blank"
                                                                            href="http://signority.com/how-it-works/"
                                                                            style="text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#017BC6">How
                                                                            it works</a>. Stay informed by following us
                                                                        on
                                                                        <a target="_blank"
                                                                            href="http://www.linkedin.com/company/signority"
                                                                            style="text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#017BC6">LinkedIn</a>
                                                                        and <a target="_blank"
                                                                            href="https://twitter.com/signority/"
                                                                            style="text-decoration:underline;font-family:Open Sans,sans-serif;font-size:12px;color:#017BC6">Twitter</a>.<br><br>
                                                                        This message was sent to you from
                                                                        <!-- href="mailto:<EMAIL>" -->
                                                                        <a style="color: #017BC6">Signority Team</a>
                                                                        who
                                                                        is using Signority
                                                                        Electronic Signature Service. If you do not want
                                                                        to
                                                                        receive email from this
                                                                        sender, please contact the sender with your
                                                                        request.
                                                                    </span>
                                                                    <span
                                                                        *ngIf="formData.emailFooter && formData.useCompanyBranding"
                                                                        [innerHTML]="formData.emailFooter | safeHtml"></span>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table> <img
                                        src="https://u3830612.ct.sendgrid.net/wf/open?upn=IHemr7LRWYOwin1XTucyKKao1V-2BpJZV2CW-2Ftb4h7IlSwGuJ6DM-2F7lUS0lgIiD9KB3RICAgmnerVryMXRUaxiqCE-2F49cwnkK6Iw-2FGOK-2BedQCYrpF-2FPVKhtwtco4scspWG0PmvqME6YftZhn-2Fj0saml5KJp1FiPIvX7fag9-2FVCZ3db1we6CiaeMW7cCRURPw1a2wfF8TXhKQb6a2MmEUnMWjjSmTPVVLRlA9wbBv5g6UI-3D"
                                        alt="" width="1" height="1" border="0"
                                        style="height:1px !important;width:1px !important;border-width:0 !important;margin-top:0 !important;margin-bottom:0 !important;margin-right:0 !important;margin-left:0 !important;padding-top:0 !important;padding-bottom:0 !important;padding-right:0 !important;padding-left:0 !important;">
                                    <div style="clear:both;height:1px;"></div>
                                </div>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-collapse-panel>
                </nz-collapse>
                    
                <nz-collapse nzGhost>
                    <nz-collapse-panel *ngIf="customTermsOfUseEnabled" [nzActive]="true" [nzShowArrow]="false"
                        [nzHeader]="modalLegal">
                        <ng-template #modalLegal>
                            <h3 class="panelTitle" i18n>Legal Notices</h3>
                        </ng-template>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label containing visible text sans line break to meet WCAG 2.5.3. -->
                            <nz-form-label [nzSpan]="8" nzFor="customTermsOfUse" [nzNoColon]="true" i18n i18n-aria-label aria-label="Custom Terms of Use">Custom Terms of Use: <br> (Notice of
                                eSignature Use)</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <textarea id="customTermsOfUse" rows="4" maxlength="10000" nz-input name="customTermsOfUse" i18n-placeholder
                                    placeholder="Leave empty to use system default" [(ngModel)]="formData.customTermsOfUse" i18n-aria-label aria-label="Custom Terms of use"></textarea>
                            </nz-form-control>
                        </nz-form-item>
                    </nz-collapse-panel>
                </nz-collapse>
                        <!--
                            The following two form-items are retained for future use (dual terms-of-service
                            feature) but are currently not rendered in the UI. They are wrapped in a single
                            HTML comment block to avoid Angular parsing errors (WCAG validation refactor).

                            <nz-form-item class="xs:inline-block" *ngIf="dualTermOfServiceEnabledEnabled">
                                <nz-form-label [nzSpan]="8" nzFor="email" i18n i18n-aria-label aria-label="Enable Custom Terms of Service">Enable Custom Terms of Service</nz-form-label>
                                <nz-form-control class="xs:inline-block ml-2">
                                    <label nz-checkbox name="enableCustomTermsofService"
                                        [(ngModel)]="formData.enableCustomTermsofService"></label>
                                </nz-form-control>
                            </nz-form-item>

                            <nz-form-item *ngIf="dualTermOfServiceEnabledEnabled">
                                <nz-form-label [nzSpan]="8" nzFor="email" i18n i18n-aria-label aria-label="Custom Terms of Service">Custom Terms of Service</nz-form-label>
                                <nz-form-control [nzSpan]="16">
                                    <textarea rows="4" nz-input name="customTermsOfService"
                                        [(ngModel)]="formData.customTermsOfService"></textarea>
                                </nz-form-control>
                            </nz-form-item>
                        -->

                <nz-collapse nzGhost>
                    <nz-collapse-panel *ngIf="productSmtpCustomizationEnabled" [nzActive]="true" [nzShowArrow]="false"
                        [nzHeader]="modalSMTP">
                        <ng-template #modalSMTP>
                            <h3 class="panelTitle">SMTP</h3>
                        </ng-template>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label matching visible text "Provider Type" for WCAG 2.5.3 compliance. -->
                            <!-- FIX (Validation 2025-08-06): Removed nested `i18n` from <mat-label> to avoid NG5002
                                 error – Angular does not permit translation markers inside an already
                                 translatable section. -->
                            <nz-form-label [nzSpan]="8"  i18n i18n-aria-label aria-label="Provider Type">
                                <mat-label>Provider Type</mat-label>
                            </nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <mat-form-field  class="material-select-height">
                                    <!-- <mat-label>Choose one</mat-label> -->
                                    <mat-select name="providerType"  [(ngModel)]="formData.providerType" i18n-aria-label aria-label="Provider Type"
                                     [disabled]="smtpCustomizationEnabled ? true : false">
                                        <mat-option [value]="400" i18n>Signority Standard SMTP</mat-option>
                                        <mat-option [value]="402" i18n>Team Customized SMTP</mat-option>
                                    </mat-select>
                                  </mat-form-field>
                            </nz-form-control>
                        </nz-form-item>

                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label "Port". -->
                            <nz-form-label [nzSpan]="8" nzFor="port" i18n-aria-label aria-label="Port">Port</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- Assumption: not personal data, disable autocomplete -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <input nz-input name="port" id="port" style="max-width: 21.25rem;" autocomplete="off"
                                    [(ngModel)]="formData.port" [disabled]="smtpCustomizationEnabled ? true : false" 
                                    i18n-aria-label aria-label="Port">
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label "Email". -->
                            <!-- WCAG 4.1.1 – The generic id value "email" is reused by multiple
                                 dialogs across the application which results in duplicate ID
                                 values when the components coexist in the DOM (e.g. multiple
                                 nz-modal instances are rendered but merely hidden with CSS).
                                 To guarantee programmatic uniqueness we prefix the identifier
                                 with the component name.  The associated <label> uses the same
                                 value via `nzFor` so the form control remains explicitly
                                 associated. -->
                            <nz-form-label [nzSpan]="8" nzFor="brandingEmail" i18n-aria-label aria-label="Email">Email</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- Assumption: requires valid email address for SMTP settings -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <!-- Assumption: Keep the `name` attribute unchanged because it is
                                     likely referenced by back-end form processing.  Only the `id`
                                     is updated to avoid duplication. -->
                                <input nz-input name="email" id="brandingEmail" style="max-width: 21.25rem;" type="email" autocomplete="email"
                                    [(ngModel)]="formData.email" [disabled]="smtpCustomizationEnabled ? true : false" i18n-aria-label aria-label="Email">
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label "Host". -->
                            <nz-form-label [nzSpan]="8" nzFor="host" i18n-aria-label aria-label="Host" i18n>Host</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- Assumption: hostname is technical; disable autocomplete -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <input nz-input name="host" id="host" style="max-width: 21.25rem;" autocomplete="off"
                                    [(ngModel)]="formData.host" [disabled]="smtpCustomizationEnabled ? true : false" i18n-aria-label aria-label="Host">
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label "User Name". -->
                            <nz-form-label [nzSpan]="8" nzFor="userName" i18n-aria-label aria-label="User Name" i18n>User Name</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- Assumption: credential username, apply username token -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <input nz-input name="userName" id="userName" style="max-width: 21.25rem;" autocomplete="username"
                                    [(ngModel)]="formData.userName" i18n-aria-label aria-label="Username"
                                    [disabled]="smtpCustomizationEnabled ? true : false">
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label "Password". -->
                            <nz-form-label [nzSpan]="8" nzFor="password" i18n i18n-aria-label aria-label="Password">Password</nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <!-- <input nz-input name="password" id="password" style="width: 340px;"  [(ngModel)]="formData.password"> -->
                                <!-- WCAG 1.4.4 – converted max-width from 340px to 21.25rem -->
                                <nz-input-group [nzSuffix]="suffixTemplate" style="max-width: 21.25rem;">
                                    <!-- Assumption: credential password field, using current-password token -->
                                    <input [type]="passwordVisible ? 'text' : 'password'" nz-input name="password" autocomplete="current-password"
                                        i18n-aria-label aria-label="Password" placeholder="input password"
                                        [(ngModel)]="formData.password"
                                        [disabled]="smtpCustomizationEnabled ? true : false" />
                                </nz-input-group>
                                <ng-template #suffixTemplate>
                                    <!-- <i nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" (click)="passwordVisible = !passwordVisible"></i> -->
                                    <!-- Accessibility: added aria-label for password visibility toggle and hid decorative icon -->
                                    <i class="fa-solid fa-eye" [attr.aria-label]="passwordVisible ? 'Hide password' : 'Show password'"
                                        [class]="passwordVisible ? 'fa-solid fa-eye' : 'fa-solid fa-eye-slash'"
                                        (click)="passwordVisible = !passwordVisible"></i>
                                </ng-template>
                            </nz-form-control>
                        </nz-form-item>
                        <nz-form-item>
                            <!-- CHANGE (2025-08-06): Added aria-label matching visible text "Encryption Type" to meet WCAG 2.5.3. -->
                            <!-- FIX (Validation 2025-08-06): Same nested i18n removal as above for "Encryption Type". -->
                            <nz-form-label [nzSpan]="8"  i18n i18n-aria-label aria-label="Encryption Type">
                                <mat-label>Encryption Type</mat-label>
                            </nz-form-label>
                            <nz-form-control [nzSpan]="16">
                                <mat-form-field class="material-select-height">
                                    <mat-select name="encryptionType"  [(ngModel)]="formData.encryptionType" i18n-aria-label aria-label="Encryption Type"
                                     [disabled]="smtpCustomizationEnabled ? true : false">
                                        <mat-option value="None" i18n>None</mat-option>
                                        <mat-option value="TLS" i18n>TLS</mat-option>
                                        <mat-option value="SSL" i18n>SSL</mat-option>
                                    </mat-select>
                                  </mat-form-field>
                            </nz-form-control>
                        </nz-form-item>
                        <div class="flex justify-center">
                            <!-- WCAG 2.5.2 – explicit type attribute -->
                            <button nz-button nzType="primary" type="button" (click)="emailDialog = true"
                                [disabled]="smtpCustomizationEnabled ? true : false" i18n>Test SMTP Setting</button>
                        </div>
                    </nz-collapse-panel>
                </nz-collapse>
            </form>
        </div>
    </ng-template>

</nz-modal>

<nz-modal [(nzVisible)]="emailDialog" [nzTitle]="emailModalTitle" [nzContent]="emailModalContent"
    [nzFooter]="emailModalFooter" (nzOnCancel)="emailDialog = false">
    <ng-template #emailModalTitle i18n>Target Email Address</ng-template>

    <ng-template #emailModalContent>
        <!-- Email: <input nz-input name="targetEmail" type="email" [(ngModel)]="formData.targetEmail"> -->
        <nz-input-group nzAddOnBefore="Email:" style="margin-top: 20px;">
            <!-- Assumption: collects target email address for testing, use semantic type with autocomplete -->
            <input type="email" nz-input autocomplete="email" [(ngModel)]="formData.targetEmail" i18n-aria-label aria-label="Target Email Address" />
        </nz-input-group>
    </ng-template>

    <ng-template #emailModalFooter>
        <!-- Accessibility: hide decorative icons and ensure button has textual label -->
        <!-- WCAG 2.5.2 – explicit type attribute -->
        <button nz-button nzType="primary" type="button" (click)="handleEmailDialogOk()" [nzLoading]="isConfirmLoading"><i
                class="fa-solid fa-check" aria-hidden="true"></i> OK</button>
        <!-- WCAG 2.5.2 – explicit type attribute -->
        <button nz-button nzType="default" type="button" (click)="handleEmailDialogCancel()" i18n><i class="fa-solid fa-ban" aria-hidden="true"></i>
            Cancel</button>
    </ng-template>
</nz-modal>
