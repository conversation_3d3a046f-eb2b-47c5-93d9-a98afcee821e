// WCAG 1.4.13 – import HostListener so the ESC key can dismiss additional content such as the search filter panel
import { Component, EventEmitter, Input, OnInit, Output, ViewChild, HostListener, ElementRef, Renderer2, AfterViewInit } from '@angular/core';
// CHANGE (2025-08-06): WCAG 4.1.2 – added helper to inject accessible names and
// keyboard support into tree expander buttons and “More…” row so controls are
// correctly announced by assistive technologies.
import { FormControl, Validators, FormBuilder } from '@angular/forms';
import { Roles } from "@core/models";
import { Code } from "@core/models";
import { UserService } from '@core/services';
import { Title } from '@angular/platform-browser';

interface ItemData {
  displayStatus: string;
  id: number
  teamName?: string;
  userName?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  roleName: string;
  status: string | number;
  subTeamList?: ItemData[];
  parent?: ItemData;
  expand?: boolean;
  roleId?: number;
  level?: number;
  teamId?: number;
  more?: boolean;
  queryScope?: string;
  sharedTeams?: string;
  userPagination?: number;
  parentGroupId?: number;
  expandDataCalled?: boolean;
}
@Component({
  selector: 'app-users-and-teams',
  templateUrl: './users-and-teams.component.html',
  styleUrls: ['./users-and-teams.component.less']
})
export class UsersAndTeamsComponent implements OnInit, AfterViewInit {
  @ViewChild('teamSettings') teamSettings:any;
  @ViewChild('branding') branding:any;
  @ViewChild('searchInput') searchInput: any;
  //use to only show table when exporting component
  @Input() tableOnly = false;
  @Input() selectedUsersAndTeams:number[] = [];

  //used to access selected ids by parent element
  @Output() getSelectedIds = new EventEmitter<number[]>();

  public teamSettingsDialogType = false;
  public brandingDialogType = false;

  public userRoleId:number =-1;
  public user:any={};
  public subTeamsEnabled = 0;
  readonly roles = Roles;
  readonly codes = Code;
  // check all is checked
  public checked = false;
  // check all is indeterminate
  public indeterminate = false;
  // use to get un-nested list of all page data
  public listOfCurrentPageData: readonly ItemData[] = [];

  public listOfData: ItemData[] = [];
  public listOfAllTeamsRaw: any[] = [];
  public listOfAllTeams: any[] = [];
  //checked items
  public setOfCheckedId: number[] = [];
  public mapOfExpandedData: { [id: string]: ItemData[] } = {};
  public activeFilters: any[] = [];
  public searchBarKey: string = '';
  public searchBarLabel: string = '';
  public searchValue: string = '';
  public tableLoading = false;
  public expandedRows: number[] = [];
  public expandedUsers: any = {};

  public filterLabels = {'users': $localize`Users` , 'teams': $localize `Teams`, 'userName': $localize `User Name`, 
  'teamName': $localize `Team Name`, 'email': $localize `Email`  }

  //check if selection array contains certain roles
  public containsDocBillingSuperAdmins:boolean = false;

  //pagination values
  public paginationTotal = 0;
  public paginationIndex: number = 1;
  public paginationSize = 10;
  public searchTimeout: any;

  public args: any = { teamId: null, showUsers: false, showTeams: false, teamName: '', email: '', userName: '' };
  public currentlySelectedItem: ItemData = {
    id: -1,
    email: '',
    roleName: '',
    status: '',
    displayStatus:''
  };
  public transferDocUsers: any[] = [];

  //toggle different modals 
  public modals: any = {
    addTeam: { submitted: false, show: false },
    addTeamMember: { submitted: false, show: false },
    move: { submitted: false, show: false },
    changeRole: {  show: false },
    rename: {  show: false },
    resetPassword: { submitted: false, show: false },
    documentTransfer: { submitted: false, show: false },
    delete: { submitted: false, show: false },
    activationEmail: { submitted: false, show: false },
    editUser: { submitted: false, show: false },
    editTeam: { submitted: false, show: false },
  }
  public showStatus:any[] = this.tableOnly ? [2] : [1,2];
  public showStatusFilter= true;
  public statusFilter = [
    { text: $localize `Active Users`, value: 2, byDefault: this.showStatus.includes(2) },
    { text: $localize `Pending Users`, value: 1, byDefault: this.showStatus.includes(1) },
    { text: $localize `Deactivated Users`, value: 4,  byDefault: this.showStatus.includes(4) }
  ];
  public disableDelete = false;
  public addTeamForm: any;
  public addTeamMemberForm: any;
  public moveForm: any;
  public resetPasswordForm: any;
  public documentTransferForm: any;
  public editUserForm: any;
  public editTeamForm: any;

  constructor(
    private formBuilder: FormBuilder,
    public userService: UserService,
    private titleService: Title,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) { }

  /**
   * WCAG 4.1.2 (Name, Role, Value) – NG-ZORRO renders the tree expander control
   * as an <button class="ant-table-row-expand-icon"> with no textual content
   * which therefore lacks an accessible name.  This helper injects
   * `aria-label` so screen-reader users understand the purpose of the control.
   *
   * Assumption: The selector '.ant-table-row-expand-icon' remains stable across
   * NG-ZORRO versions used by this application.
   */
  private patchExpandButtons(): void {
    // Defer so the current change detection cycle completes and DOM is ready.
    setTimeout(() => {
      const expandIcons: NodeListOf<HTMLElement> = this.elementRef.nativeElement.querySelectorAll('button.ant-table-row-expand-icon');
      expandIcons.forEach(icon => {
        if (!icon.hasAttribute('aria-label')) {
          // Provide a generic name – state (expanded/collapsed) is conveyed via
          // aria-expanded which NG-ZORRO already toggles for us.
          this.renderer.setAttribute(icon, 'aria-label', 'Toggle row'); // Assumption: English string acceptable – component already localised elsewhere
        }
        // Ensure the element is focusable when rendered as <button disabled="">
        if (!icon.hasAttribute('tabindex')) {
          this.renderer.setAttribute(icon, 'tabindex', '0');
        }
      });

      // Patch the custom “More…” row which is implemented as a <td> acting as
      // a button so that assistive technology recognises it as interactive.
      const moreRows: NodeListOf<HTMLTableCellElement> = this.elementRef.nativeElement.querySelectorAll('tr.more > td');
      moreRows.forEach(td => {
        if (!td.hasAttribute('role')) {
          this.renderer.setAttribute(td, 'role', 'button');
        }
        if (!td.hasAttribute('tabindex')) {
          this.renderer.setAttribute(td, 'tabindex', '0');
        }
        if (!td.hasAttribute('aria-label')) {
          this.renderer.setAttribute(td, 'aria-label', 'Show more actions');
        }
        // Forward keyboard activation (Enter/Space) to mimic click.
        if (!(td as any).__keyboardHandler) {
          const handler = (event: KeyboardEvent) => {
            if (event.key === 'Enter' || event.key === ' ') {
              event.preventDefault();
              (td as HTMLElement).click();
            }
          };
          (td as any).__keyboardHandler = handler;
          this.renderer.listen(td, 'keydown', handler);
        }
      });
    });
  }

  // WCAG 1.4.13 – allow users to dismiss the search filter panel with the ESC key from anywhere in the page
  // Assumption: pressing ESC while the panel is open should hide it and clear the temporary input value but
  // must not interfere with other keyboard interactions when the panel is not present.
  @HostListener('document:keydown.escape')
  handleEscKey(): void {
    if (this.searchBarKey) {
      this.searchBarKey = '';
      this.searchValue = '';
      this.searchBarLabel = '';
    }
  }

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    // Only set title when component is used as a standalone page, not when embedded in other components
    if (!this.tableOnly) {
      this.titleService.setTitle($localize`Users & Teams`);
    }
    
    this.setOfCheckedId = this.selectedUsersAndTeams;
    this.userService.getUser().subscribe((user:any) => {
      this.userRoleId = user.roles.id;
      this.user = user;
      this.subTeamsEnabled = user.userProduct.subteam;
      //team admin can only see his team and subteams
      if(this.userRoleId == this.roles.TEAM_ADMIN || this.userRoleId == this.roles.REGULAR_USER || this.userRoleId == this.roles.DOC_ADMIN) {
        this.args.teamId = this.user.groupId;
      }

      this.getTeams();
      this.getData();
      
      // Add keyboard handlers for checkboxes after data loads
      setTimeout(() => {
        this.addCheckboxKeyboardHandlers();
      }, 1000);
    })

    //forms 
    this.addTeamForm = this.formBuilder.group({
      teamName: ['', Validators.required],
      parentName: ['', Validators.required],
    });

    this.addTeamMemberForm = this.formBuilder.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      role: [this.roles.REGULAR_USER, Validators.required],
      team: ['', [Validators.required]],
      teams: this.formBuilder.array([]),
      docs: ['1', [Validators.required]],
    });

    this.moveForm = this.formBuilder.group({
      team: ['', Validators.required]
    });

    this.resetPasswordForm = this.formBuilder.group({
      password: ['', [Validators.required,Validators.pattern(/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*()_+=\[\]{}|'-])[a-zA-Z0-9!@#$%^&*()_+=\[\]{}|'-]{8,20}$/)]],
      confirmPassword: ['', [Validators.required,
      () => {
        if (this.resetPasswordForm) {
          return this.resetPasswordForm.controls.password.value === this.resetPasswordForm.controls.confirmPassword.value
            ? null : { 'mismatch': true };
        }
        else return null;
      },
      Validators.pattern(/^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*()_+=\[\]{}|'-])[a-zA-Z0-9!@#$%^&*()_+=\[\]{}|'-]{8,20}$/)]],
    });

    this.documentTransferForm = this.formBuilder.group({
      recipient: ['', Validators.required],
    });

    this.editUserForm = this.formBuilder.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      role: [this.roles.REGULAR_USER, Validators.required],
      team: ['', Validators.required],
      teams: this.formBuilder.array([]),
      docs: ['1', [Validators.required]],
    });

    this.editTeamForm = this.formBuilder.group({
      teamName: ['', Validators.required],
      parentName: ['', Validators.required],
    });

    //add form control to forms object
    this.modals.addTeam.form = this.addTeamForm;
    this.modals.addTeamMember.form = this.addTeamMemberForm;
    this.modals.move.form = this.moveForm;
    this.modals.resetPassword.form = this.resetPasswordForm;
    this.modals.documentTransfer.form = this.documentTransferForm;
    this.modals.editUser.form = this.editUserForm;
    this.modals.editTeam.form = this.editTeamForm;

    //keep default values when resetting form
    this.modals.addTeam.initialValues = this.addTeamForm.value;
    this.modals.addTeamMember.initialValues = this.addTeamMemberForm.value;
    this.modals.move.initialValues = this.moveForm.value;
    this.modals.resetPassword.initialValues = this.resetPasswordForm.value;
    this.modals.documentTransfer.initialValues = this.documentTransferForm.value;
    this.modals.editUser.initialValues = this.editUserForm.value;
    this.modals.editTeam.initialValues = this.editTeamForm.value;

    this.listOfData = [];

  }

  // WCAG 4.1.2 – patch after initial view rendering so that expander controls
  // are labelled before the component becomes interactive.
  ngAfterViewInit(): void {
    this.patchExpandButtons();
  }
  
  showTeamSettingModal(): void {
    this.teamSettings.showModal();
  }
  showBrandingModal(): void {
    this.branding.showModal();
  }
  getData() {
    // this.expandedRows = [];
    this.tableLoading = true;
    this.listOfData = [];
    // if user filters on, switch to user view
    if (this.args.showUsers || this.args.email || this.args.role || this.args.userName) {
      this.userService.getUsers(this.paginationIndex, this.paginationSize, this.args).subscribe(v => {
        this.listOfData = v.data.records;
        this.paginationTotal = v.data.total;
        this.tableLoading = false;
        this.listOfData.forEach(item => {
          item.displayStatus = this.getUserStatusString(item.status),
          // item.firstName = item.userName,
          this.mapOfExpandedData[item.id] = this.convertTreeToList(item);
        });
      });
      this.showStatusFilter = false;
      this.showStatus = [1,2,4];
    }
    // standard teams and users view (tree)
    else {
      if(this.args.showTeams) {
        //reset expanded rows if showing teams only
        this.expandedRows = [];
      }
      this.userService.getUsersAndTeams(this.args).subscribe(v => {
        this.listOfData = v.data;
        this.tableLoading = false;

        //expand first group by default
        if(this.listOfData[0]?.id) {
          this.expandedRows.push(this.listOfData[0]?.id)
        }

        this.listOfData.forEach(item => {
          this.mapOfExpandedData[item.id] = this.convertTreeToList(item,this.args.showTeams);
        });
            //expand all team in expandedrows array( previously expanded row)
            let currentExpandedRows = this.expandedRows.slice();
            currentExpandedRows.forEach((teamId)=>{
              this.onExpand(true, this.findNode(teamId,this.listOfData));
            })
      });
      this.showStatusFilter = true;
      this.showStatus =  this.tableOnly ? [2] : [1,2];
      this.statusFilter = [
      { text: $localize `Active Users`, value: 2, byDefault: this.showStatus.includes(2) },
      { text: $localize `Pending Users`, value: 1, byDefault: this.showStatus.includes(1) },
      { text: $localize `Deactivated Users`, value: 4,  byDefault: this.showStatus.includes(4) }
    ];
    }
  }

  getTeams() {
    if(this.userRoleId != this.roles.REGULAR_USER && this.userRoleId != this.roles.DOC_ADMIN) {
      this.userService.getTeams().subscribe(v => {
        // listOfAllTeamsRaw is all teams, listOfAllTeams changes depending on selection
        this.listOfAllTeamsRaw = v.data;
        this.listOfAllTeams = v.data;
      })
    }
  }
  removeFilter(index: number) {
    this.paginationIndex = 1;
    this.activeFilters.splice(index, 1);
    this.updateArgs();
    this.getData();
  }

  changeSearchBar(label: string, key: string) {
    setTimeout(() => {
      this.searchBarKey = key;  
      this.searchBarLabel = label;
      // Focus the input field after the search dialog appears
      setTimeout(() => {
        if (this.searchInput && this.searchInput.nativeElement) {
          this.searchInput.nativeElement.focus();
        }
      }, 100);
    }, 0);
  }

  // Method to handle focus when search dialog is dismissed
  onSearchDialogDismiss() {
    // Focus should return to the Add Search Filter button
    const addFilterButton = this.elementRef.nativeElement.querySelector('.add-filter');
    if (addFilterButton) {
      addFilterButton.focus();
    }
  }

  // Method to handle Enter key in search input
  onSearchInputEnter() {
    if (this.searchValue && this.searchValue.trim()) {
      this.addFilter({filter: this.searchBarLabel, value: this.searchValue, id: this.searchBarKey});
      this.searchValue = '';
      this.searchBarKey = '';
      this.searchBarLabel = '';
    }
  }

  addFilter(val: any) {
    this.paginationIndex = 1;
    //replace filter value if filter is already in array
    let activeFilter = this.activeFilters.find(item => { return val.id == item.id })
    if (activeFilter) {
      activeFilter.value = val.value;
      if (activeFilter.roleName) activeFilter.roleName = val.roleName;
    }
    //otherwise, add new filter
    else {
      this.activeFilters.push(val);
    }
    //cant have show user and show teams active at the same time
    if (val.id == 'users') {
      this.activeFilters = this.activeFilters.filter(function (obj) {
        return obj.id !== 'teams';
      });
    }
    if (val.id == 'teams') {
      this.activeFilters = this.activeFilters.filter(function (obj) {
        return obj.id !== 'users';
      });
    }

    //update args object for api call
    this.updateArgs();
    this.getData();

    // Clear search dialog and return focus to Add Search Filter button
    if (this.searchBarKey) {
      this.searchBarKey = '';
      this.searchValue = '';
      this.searchBarLabel = '';
      this.onSearchDialogDismiss();
    }
  }
  disabledMoreButton() {
    this.containsDocBillingSuperAdmins = false;
    let containsRoot = false;
    this.disableDelete = false;
    this.setOfCheckedId.forEach(v=>{
      let node = this.findNode(v,this.listOfData);
      if(node?.parentGroupId == 0) containsRoot = true;
      if(node?.status == this.codes.STATUS_DELETE) this.disableDelete = true;
      if(node?.roleId == this.roles.DOC_ADMIN || node?.roleId == this.roles.BILLING_ADMIN || node?.roleId == this.roles.SUPER_ADMIN) {
        this.containsDocBillingSuperAdmins = true;
      }

    })
    return this.setOfCheckedId.length < 1 || (this.setOfCheckedId.length > 1 && containsRoot);
  }

  updateArgs() {
    this.args = {
      showUsers: this.activeFilters.some(e => e.id == 'users'),
      showTeams: this.activeFilters.some(e => e.id == 'teams'),
      teamId: null,
      role: this.activeFilters.find(e => e.id == 'role') ? this.activeFilters.find(e => e.id == 'role').value : undefined,
      teamName: this.activeFilters.find(e => e.id == 'teamName') ? this.activeFilters.find(e => e.id == 'teamName').value : '',
      email: this.activeFilters.find(e => e.id == 'email') ? this.activeFilters.find(e => e.id == 'email').value : '',
      userName: this.activeFilters.find(e => e.id == 'userName') ? this.activeFilters.find(e => e.id == 'userName').value : '',
    }
  }

  collapse(array: ItemData[], data: ItemData, $event: boolean): void {
    if (!$event) {
      if (data.subTeamList) {
        data.subTeamList.forEach(d => {
          const target = array.find(a => a.id === d.id)!;
          target.expand = false;
          this.collapse(array, target, false);
        });
      } else {
        return;
      }
    }
  }

  convertTreeToList(root: ItemData, expandRows: boolean = false): ItemData[] {
    const stack: ItemData[] = [];
    const array: ItemData[] = [];
    const hashMap = {};
    stack.push({ ...root, level: 0, expand: this.expandedRows.includes(root.id) && expandRows ? true : false });

    while (stack.length !== 0) {
      const node = stack.pop()!;
      this.visitNode(node, hashMap, array);
      if (node.subTeamList) {
        for (let i = node.subTeamList.length - 1; i >= 0; i--) {
          stack.push({ ...node.subTeamList[i], level: node.level! + 1, expand: this.expandedRows.includes(node.subTeamList[i].id) && expandRows ? true : false, parent: node });
        }
      }
    }
    return array;
  }

  onExpand(event: any, item: ItemData, more: boolean = false) {
    if(!item) {return}
    //remove id from expandedRows array
    const index = this.expandedRows.indexOf(item?.id);
    if (index >= 0) this.expandedRows.splice(index, 1);
    let pagination: any = this.paginationIndex;
    //add id to expanded rows if true
    if (event) {
      this.expandedRows.push(item.id);
    }
    if (more) {
      let parentId = item.parent!.id;
      this.expandedUsers[parentId] ? this.expandedUsers[parentId]++ : this.expandedUsers[parentId] = 2;
      pagination = this.expandedUsers[parentId];
    }

    // check for children, add to data if found
    if (event && !item.expandDataCalled) {
      //dont get users if show teams filter is active
      if (!this.args.showTeams) {
        this.tableLoading = true;
        //get expanded team data
        let teamId = more ? item.parent?.id : item.id;
        this.userService.getUsers(pagination, this.paginationSize, this.args, teamId).subscribe(v => {
          let temp: ItemData[] = [];
          v.data.records.forEach((record: any, index: number) => {
            //check data if check all is checked
            if (this.checked && (record.roleId != this.roles.BILLING_ADMIN && !this.tableOnly) ) {
              this.setOfCheckedId.push(record.id);
            }
            temp.push({
              id: record.id,
              firstName: record.firstName,
              lastName: record.lastName,
              userName:record.userName,
              email: record.email,
              roleName: record.roleName,
              roleId: record.roleId,
              status: record.status,
              displayStatus: this.getUserStatusString(record.status),
              teamId: record.teamId,
              queryScope: record.queryScope,
              sharedTeams: record.sharedTeams,
            })
          })
          //add more button if more data
          if (v.data.pages > v.data.current) {
            temp.push({
              id: Math.abs(item.id) * -1,
              teamName: '',
              email: '',
              roleName: '',
              status: '',
              displayStatus: '',
              more: true
            })
          }
          if (temp.length > 0) {
            let node;

            //if loading more, insert after current user list but before other teams
            if (more) {
              node = this.findNode(item.parent?.id, this.listOfData);
              //remove more button
              node.subTeamList.splice(node.subTeamList.findIndex((v: any) => v.id < 0), 1);
              //append new data
              let endUsersIndex = node.subTeamList.findIndex((v: any) => v.teamName);
              if (endUsersIndex >= 0) {
                node.subTeamList.splice(endUsersIndex, 0, ...temp);
              }
              else {
                node.subTeamList = temp.concat(node.subTeamList);
              }
            }
            //else, insert at the top
            else {
              node = this.findNode(item.id, this.listOfData);
              node.subTeamList = temp.concat(node.subTeamList);
            }
            //prevent calling api agains
            node.expandDataCalled = true;
            this.listOfData.forEach(item => {
              this.mapOfExpandedData[item.id] = this.convertTreeToList(item, true);
            });
          }
          else {
            item.expandDataCalled = true;
          }

          this.getListData();
          this.tableLoading = false;
          
          // Re-apply keyboard handlers after expanding sub-teams
          setTimeout(() => {
            this.addCheckboxKeyboardHandlers();
          }, 100);
        })
      }
    }
  }

  findNode(id: any, array: any[]): any {
    for (const node of array) {
      if (node.id === id) return node;
      if (node.subTeamList) {
        const child = this.findNode(id, node.subTeamList);
        if (child) return child;
      }
    }
  }

  visitNode(node: ItemData, hashMap: { [id: string]: boolean }, array: ItemData[]): void {
    if (!hashMap[node.id]) {
      hashMap[node.id] = true;
      array.push(node);
    }
  }
  //search recipients for file transfer
  search($event: string) {
    //only send request if user hasnt typed for 1.5 seconds
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.userService.getUsers(1, 20, { "teamId": "", "userName": $event, "role": "", "email": "" }).subscribe(v => {
        this.transferDocUsers = v.data.records.filter((u:any)=>{return u.status == 2});
      })
    }, 1000);
  }

  onAccessibleTeamsChange(item:string, e: Event, form:any) {
    const emailFormArray = form.controls.teams;
    let target = e.target as HTMLInputElement;
    if(target.checked) {
      emailFormArray.push(new FormControl(item));
    } else {
      let index = emailFormArray.controls.findIndex((x:any) => x.value == item)
      emailFormArray.removeAt(index);
    }
  }
  onAccessibleTeamsIsChecked(id:any, form:any) {
    return form.controls.teams.value.includes(id);
  }
  onAccessibleTeamsIsCheckedAll(form:any) {
    return this.listOfAllTeamsRaw.every((x:any)=>{return form.controls.teams.value.includes(x.id)});
  }
  onAccessibleTeamsCheckAll(e: Event, form:any) {
    let emailFormArray = form.controls.teams;
    let target = e.target as HTMLInputElement;
    if(target.checked) {
      this.listOfAllTeamsRaw.forEach(v=>{
        emailFormArray.push(new FormControl(v.id));
      })
    } else {
      form.controls.teams = this.formBuilder.array([])
    }
  }


  onAllChecked(value: boolean): void {
    if (!value) {
      this.setOfCheckedId = [];
      this.getSelectedIds.emit(this.setOfCheckedId);
    }
    else {
      this.listOfCurrentPageData.forEach(item => this.updateCheckedSet(item, value));
    }
    this.refreshCheckedStatus();

    // WCAG 4.1.2 – ensure newly rendered rows after pagination/filtering also
    // receive accessible names/roles.
    this.patchExpandButtons();
  }

  updateCheckedSet(item: ItemData, checked: boolean ): void {
    if((item.roleId == this.roles.BILLING_ADMIN && !this.tableOnly) || item.id < 0) return;
    if (checked) {
      this.setOfCheckedId.push(item.id);

    } else {
      var index = this.setOfCheckedId.indexOf(item.id);
      this.setOfCheckedId.splice(index, 1);
    }
    //setcurrentitem
    if (this.setOfCheckedId.length == 1) {
      this.currentlySelectedItem = this.findNode(this.setOfCheckedId[0], this.listOfData);
    }
    //update listOfAllTeams
    this.listOfAllTeams = this.listOfAllTeamsRaw.filter(v => { return !this.setOfCheckedId.some(id => { return v.id == id }) });
    //emit list to parent
    this.getSelectedIds.emit(this.setOfCheckedId);
  }

  onItemChecked(checked: boolean, item: ItemData): void {
    this.updateCheckedSet(item, checked);
    this.refreshCheckedStatus();

  }

  onCurrentPageDataChange($event: readonly ItemData[]): void {
    this.listOfCurrentPageData = $event;
    this.refreshCheckedStatus();
    
    // Re-apply keyboard handlers after table data changes
    setTimeout(() => {
      this.addCheckboxKeyboardHandlers();
    }, 100);
  }

  // Use to have a single list of all data for checkbox logic
  getListData() {
    let tempList: any = [];
    this.listOfData.forEach(item => {
        tempList = [...tempList, this.convertTreeToList(this.mapOfExpandedData[item.id][0])];
      this.listOfCurrentPageData = tempList.flat(1);
    });
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    this.checked = this.listOfCurrentPageData.every(item => this.setOfCheckedId.includes(item.id) ||
     (item.roleId == this.roles.BILLING_ADMIN && !this.tableOnly) || item.id < 0);
    this.indeterminate = this.listOfCurrentPageData.some(item => this.setOfCheckedId.includes(item.id)) && !this.checked;
  }

  getItemByIndex(index: number) {
    return this.listOfCurrentPageData.find((x) => { return x.id == index });
  }

  // chose menu for '../ more' dropdown according to selections
  selectMoreDropdown() {
    if (this.setOfCheckedId.length == 1) {
      if (this.getItemByIndex(this.setOfCheckedId[0])?.teamName) return 0;
      else return 1;
    }
    return 2;
  }

  updateTeamValues() {
    this.editTeamForm.patchValue({ teamName: this.currentlySelectedItem.teamName });
    this.editTeamForm.patchValue({ parentName: this.currentlySelectedItem.parentGroupId });

  }
  updateUserValues() {
    this.editUserForm.patchValue({ firstName: this.currentlySelectedItem.firstName });
    this.editUserForm.patchValue({ lastName: this.currentlySelectedItem.lastName });
    this.editUserForm.patchValue({ email: this.currentlySelectedItem.email });
    this.editUserForm.patchValue({ role: this.currentlySelectedItem.roleId });
    this.editUserForm.patchValue({ team: this.currentlySelectedItem.teamId });
    this.editUserForm.patchValue({ docs: this.currentlySelectedItem.queryScope?.toString() });
    this.currentlySelectedItem.sharedTeams?.split(',').forEach(v=>{
      if(v) {
        this.editUserForm.controls.teams.push(new FormControl(parseInt(v)));
      }
    })
    
  }

  resetForm(formName: string) {
    this.modals[formName].show = false;
    this.modals[formName].submitted = false;
    this.modals[formName].form.reset(this.modals[formName].initialValues);
  }
  addTeam() {
    this.modals.addTeam.submitted = true;
    if (!this.addTeamForm.invalid) {
      this.userService.createTeam(this.addTeamForm.controls.teamName.value, this.addTeamForm.controls.parentName.value).subscribe(v => {
        this.getData();
        this.getTeams();
        this.resetForm('addTeam');
      });
    }

  }
  addTeamMember() {
    let queryScope = 1;
    let sharedTeams = ""
    //ony set queryScope and shared teams if doc admin
    if(this.addTeamMemberForm.controls.role.value == this.roles.DOC_ADMIN) {
      queryScope = this.addTeamMemberForm.controls.docs.value;
      this.addTeamMemberForm.controls.teams.value.forEach((team:any)=>{
        sharedTeams = sharedTeams +','+team;
      })
    }

    this.modals.addTeamMember.submitted = true;
    if (!this.addTeamMemberForm.invalid) {
      this.userService.createUser(this.addTeamMemberForm.controls.firstName.value,
        this.addTeamMemberForm.controls.lastName.value,
        this.addTeamMemberForm.controls.email.value,
        this.addTeamMemberForm.controls.role.value,
        this.addTeamMemberForm.controls.team.value,
        sharedTeams,
        queryScope
      ).subscribe(v => {
        this.getData();
        this.resetForm('addTeamMember');
      });
    }
  }
  moveTeamOrUser() {

    this.modals.move.submitted = true;

    let userIds: any[] = [];
    let teamIds: number[] = [];
    this.setOfCheckedId.forEach(val => {
      let node = this.findNode(val, this.listOfData)
      if (node?.teamName) {
        teamIds.push(node.id);
      }
      else {
        userIds.push(node.id);
      }
    })
    if (teamIds.length > 0) {
      if (!this.moveForm.invalid) {
        this.userService.moveTeams(teamIds, this.moveForm.controls.team.value).subscribe(v => {
          this.getData();
          this.resetForm('move');
        });
      }

    }
    if (userIds.length > 0) {
      if (!this.moveForm.invalid) {
        this.userService.moveUsers(userIds, this.moveForm.controls.team.value).subscribe(v => {
          this.getData();
          this.resetForm('move');
        });
      }

    }
  }
  
  resetPassword() {
    this.modals.resetPassword.submitted = true;
    if (!this.resetPasswordForm.invalid) {
      this.userService.updatePassword(this.setOfCheckedId[0], this.resetPasswordForm.controls.password.value).subscribe(v => {
        this.getData();
        this.resetForm('resetPassword');
      });
    }
  }
  editUser() {
    let sharedTeams = "";
    let queryScope = 1;
    //ony set queryScope and shared teams if doc admin
    if(this.editUserForm.controls.role.value == this.roles.DOC_ADMIN) {
      queryScope = this.editUserForm.controls.docs.value;
      this.editUserForm.controls.teams.value.forEach((team:any)=>{
        if(team) {
          sharedTeams = sharedTeams +','+team;
        }
      })
    }
    this.modals.editUser.submitted = true;
    if (!this.editUserForm.invalid) {
      this.userService.editUser(this.setOfCheckedId[0],
        this.editUserForm.controls.firstName.value,
        this.editUserForm.controls.lastName.value,
        this.editUserForm.controls.email.value,
        this.editUserForm.controls.role.value,
        this.editUserForm.controls.team.value,
        sharedTeams,
        queryScope,
      ).subscribe(v => {
        this.getData();
        this.modals.changeRole.show = false;
        this.resetForm('editUser');
        this.setOfCheckedId = [];
      });
    }
  }
  editTeam() {
    this.modals.editTeam.submitted = true;
    if (!this.editTeamForm.invalid) {
      this.userService.editTeam(this.editTeamForm.controls.teamName.value, this.setOfCheckedId[0], this.editTeamForm.controls.parentName.value).subscribe((v) => {
        this.getData();
        this.getTeams();
        this.resetForm('editTeam');
        this.modals.rename.show = false;
      });
    }
  }

  documentTransfer() {
    this.modals.documentTransfer.submitted = true;
    if (!this.documentTransferForm.invalid) {
      this.userService.transferDocs(this.setOfCheckedId[0], this.documentTransferForm.controls.recipient.value).subscribe(v => {
        this.getData();
        this.resetForm('documentTransfer');
      });
    }
  }

  deleteTeamOrUser() {
    let users: any[] = [];
    let teamIds: number[] = [];
    this.setOfCheckedId.forEach(val => {
      let node = this.findNode(val, this.listOfData)
      if (node?.teamName) {
        teamIds.push(node.id);
      }
      else {
        users.push({
          id: node.id,
          groupId: node.teamId,
          name: node.userName,
          email: node.email,
          roleId: node.roleId,
          role: node.roleName,
          status: 'pending',
          statusId: 1
        });
      }
    })
    if (teamIds.length > 0) {
      this.userService.deleteTeams(teamIds).subscribe(v => {
        this.getData();
        this.getTeams();
        this.setOfCheckedId = [];
        this.modals.delete.show = false;
      });
    }
    if (users.length > 0) {
      this.userService.deleteUsers(users).subscribe(v => {
        this.getData();
        this.setOfCheckedId = [];
        this.modals.delete.show = false;
      });
    }
  }

  reActivateUser() {
    this.userService.reactivateUser(this.setOfCheckedId[0]).subscribe(val=>{
      this.getData();
      this.setOfCheckedId = [];
    });
  }

  resendActivationEmail() {
    let user = {
      id: this.currentlySelectedItem.id,
      groupId: this.currentlySelectedItem.teamId,
      name: this.currentlySelectedItem.userName,
      email: this.currentlySelectedItem.email,
      roleId: this.currentlySelectedItem.roleId,
      role: this.currentlySelectedItem.roleName,
      status: 'pending',
      statusId: 1
    }
    this.userService.resendActivationEmail(user).subscribe(v => {
      this.modals.activationEmail.show = false;
    });
  }

  showDeletedUsers(event:any) {
    this.showStatus = event;
  }

  getUserStatusString(statusId:number|string) {
    switch(statusId) {
      case this.codes.STATUS_PENDING: return $localize `Pending`;
      case this.codes.STATUS_ACTIVE: return $localize `Active`;
      case this.codes.STATUS_DELETE: return $localize `Deactivated`;
      default: return '';
    }
  }

  addCheckboxKeyboardHandlers() {
    // Handle all checkbox inputs directly
    const allCheckboxes = this.elementRef.nativeElement.querySelectorAll('input[type="checkbox"], .ant-checkbox-input');
    allCheckboxes.forEach((checkbox: HTMLElement) => {
      // Remove any existing keyboard handlers to prevent duplicates
      if ((checkbox as any).__keyboardHandler) {
        checkbox.removeEventListener('keydown', (checkbox as any).__keyboardHandler);
      }
      
      const handler = (event: KeyboardEvent) => {
        if (event.key === 'Enter') {
          event.preventDefault();
          event.stopPropagation();
          checkbox.click();
        }
        // Don't handle Space key - let default behavior work
      };
      
      // Store reference and add listener
      (checkbox as any).__keyboardHandler = handler;
      checkbox.addEventListener('keydown', handler);
    });
  }

}
