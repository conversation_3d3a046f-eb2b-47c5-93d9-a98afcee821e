// Summary: WCAG 1.4.4 – Converted fixed pixel heights, font sizes and spacing to relative rem units so text can scale to 200 % without clipping. Also kept previous WCAG 1.4.1 colour cues.
// WCAG 1.4.1 – Use of Colour
// Previously, hierarchy (root team vs. sub-team) was indicated only via different
// background colours (.rootRow vs. .teamRow).  Screen-grabbed or colour-blind
// users could lose that distinction.  The update adds a non-colour visual cue –
// a left-hand bar – so the hierarchy remains perceivable without relying on
// colour.  No functional logic is changed. // Assumption: 4 px bar acceptable.

.teamRow::before,
.rootRow::before {
    content: none; /* override earlier declaration */
}

/*
 * 2) Re-create the hierarchy bar inside the first <td> so its position is
 *    consistent whether that cell contains a checkbox or is empty.  anchoring
 *    it here means we don't have to guess the selection-column width, and the
 *    bar will always be flush with the true left edge of the table.
 */
.teamRow td:first-child,
.rootRow td:first-child {
    position: relative; /* local stacking context for the bar */
}

.teamRow td:first-child::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

/* Root rows use a double line so keep that visual differentiation */
.rootRow td:first-child::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0; /* width comes from border */
    border-left: 4px double currentColor;
}

/*
 * Hide the bar when the first cell is a checkbox (selection column) – in that
 * case the cell already contains a visual affordance and adding the bar would
 * misalign/obscure the content.
 */
.rootRow td.ant-table-selection-column:first-child::before,
.teamRow td.ant-table-selection-column:first-child::before {
  display: none; /* remove bar */
}

.teamRow {
    position: relative; // ensure ::before positions correctly – affects visuals only
    background-color: #028fe6;
    color: white;
}

.teamRow:hover td {
    /* WCAG 1.4.3: hover highlight colour darkened to maintain ≥4.5:1 contrast with white text */
    background-color: #017BC6; // Assumption: text within hover row remains white
}

.rootRow {
    position: relative;
    background-color: #017BC6;
    color: white;
}

.rootRow:hover td {
    background-color: #017BC6;
}

/* WCAG 1.4.4 – replaced fixed pixel height & spacing with relative units so the button scales with text */
.add-filter { // Assumption: 40px ≈ 2.5rem on default 16 px root font
    min-height: 2.5rem;
    /* Changed back to original blue color #0083d2 */
    border-color: #0083d2;
    color: #0083d2;
    margin-right: 1.25rem; // 20px → 1.25rem
    margin-bottom: 0.625rem; // 10px → 0.625rem
}

.add-filter:hover {
    /* Maintain visual hover affordance while preserving contrast */
    border-color: #0083d2;
    color: #0083d2;
}

button.disabled {
    color: #797E8A;
    cursor: not-allowed;
    pointer-events: none;
}

/* Ensure disabled attribute also picks up colour */
button[disabled],
.ant-btn[disabled] {
  color: #797E8A !important;
}

/* WCAG 1.4.4 – convert absolute pixel offsets to rem so panel position respects enlarged text */
.search {
    position: relative;
    top: -1.375rem; // -22px → -1.375rem
    left: 0.625rem; // 10px → 0.625rem
    z-index: 1;
}

/* WCAG 1.4.4 – changed absolute px values to relative units for scalable search title */
.search-title {
    /* WCAG 1.4.3: darken header background colour for sufficient white-on-blue contrast */ // Assumption: header text is white
    background-color: #017BC6;
    min-height: 2.5rem; // 40px → 2.5rem
    font-size: 1rem; // 16px → 1rem
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0.9375rem; // 15px → 0.9375rem
    border-radius: 0.1875rem; // 3px → 0.1875rem
    // Orientation fix (WCAG 2.1 – 1.3.4): use fluid width so the panel adapts to both
    width: 100%; // Assumption: stretching to container width prevents horizontal scroll on mobile
    max-width: 17.5rem; // 280px → 17.5rem
}

/* WCAG 1.4.4 – allow search content to grow with text */
.search-content {
    background-color: white;
    min-height: 7.5rem; // 120px → 7.5rem
    font-size: 1rem; // 16px → 1rem
    padding: 0.9375rem; // 15px → 0.9375rem
    border-radius: 0.1875rem; // 3px → 0.1875rem
    box-shadow: rgba(0, 0, 0, 0.24) 0px 0.1875rem 0.5rem; // 3px/8px → rem
    input {
        outline: none;
    }
}

/* WCAG 1.4.4 – replaced fixed pixel dimensions with relative units so tags resize with text */
.filter-tag-container {
    display: inline-flex;
    align-items: center;
    min-height: 2.5rem; // 40px → 2.5rem
    background-color: #EEEEEE;
    color: #017BC6; /* WCAG 1.4.3: ensure blue text on light grey meets 4.5:1 */
    border: none;
    border-radius: 0;
    margin-right: 0.9375rem; // 15px → 0.9375rem
    margin-bottom: 0.625rem; // 10px → 0.625rem
    padding: 0 0.75rem; // 12px → 0.75rem
}

.filter-tag-text {
    margin-left: 0.5rem; // 8px → 0.5rem
}

.filter-tag-close {
    background: transparent;
    border: none;
    color: #017BC6;
    padding: 0.25rem; // 4px → 0.25rem
    min-width: auto;
    height: auto;
    order: -1; // Move to the beginning (left side)
    box-shadow: none;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-tag-close:focus {
    box-shadow: none;
    outline: 3px solid #2D323D;
    outline-offset: 2px;
}

.filter-tag-close:hover {
    background-color: #F5F5F5;
    color: #0083d2; /* Changed to match original blue color */
}

/* Override Ant Design button styles to remove borders */
.filter-tag-close.ant-btn {
    border: none !important;
    box-shadow: none !important;
}

.filter-tag-close.ant-btn:not(:focus) {
    border: none !important;
    box-shadow: none !important;
}

/* Additional overrides to ensure no borders */
.filter-tag-close.ant-btn.ant-btn-default {
    border: none !important;
    box-shadow: none !important;
}

.filter-tag-close.ant-btn-icon-only {
    border: none !important;
    box-shadow: none !important;
}

/* Legacy filter-tag class for backward compatibility */
.filter-tag {
    min-height: 2.5rem; // 40px → 2.5rem
    background-color: #EEEEEE;
    color: #017BC6; /* WCAG 1.4.3: ensure blue text on light grey meets 4.5:1 */
    border: none;
    border-radius: 0;
    margin-right: 0.9375rem; // 15px → 0.9375rem
    margin-bottom: 0.625rem; // 10px → 0.625rem
}

.filter-tag:hover {
    background-color: #F5F5F5;
    color: #0083d2; /* Changed to match original blue color */
}

/* ------------------------------------------------------------------------
   WCAG 1.4.11 – Non-text Contrast
   The default ng-zorro / Ant Design theme provides only a faint outline (or
   none at all) for keyboard-focused elements and uses light-grey (#d9d9d9)
   borders on many secondary buttons and inputs – both fall below the 3 : 1
   contrast ratio required for non-textual UI components.  The following
   additions introduce:
   1. A consistent 3 px blue focus ring (#2D323D ≥ 3 : 1 against both white
      and the dark blue table rows) for every interactive control in the
      Users & Teams module.
   2. Special-case override for controls that sit on the dark .teamRow / 
      .rootRow backgrounds where the blue outline would fall below 3 : 1 – the
      outline is switched to white (#ffffff) so it remains perceivable.
   3. Darkened default button borders that previously inherited the low-
      contrast #d9d9d9 from Ant Design.
   No functional logic or layout is changed.         // Assumption: colour
   tokens are not centrally themed so hard-coded values are acceptable here. */

/* Generic focus indicator for all buttons, inputs, table expand icons, etc. */
button:focus-visible,
.ant-btn:focus-visible,
[tabindex="0"]:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
select:focus-visible,
.ant-table-row-expand-icon:focus-visible {
    outline: 3px solid #2D323D; // WCAG 1.4.11 – high-contrast blue ring
    outline-offset: 2px;
}

/* Override focus ring colour for controls that sit on dark blue table rows */
.teamRow button:focus-visible,
.teamRow .ant-table-row-expand-icon:focus-visible,
.rootRow button:focus-visible,
.rootRow .ant-table-row-expand-icon:focus-visible {
    outline: 3px solid #ffffff; // Assumption: white outline ≥ 3 : 1 on #017BC6
    outline-offset: 2px;
}

/* Darken low-contrast secondary button borders (Ant default: #d9d9d9) */
button.ant-btn:not(.ant-btn-primary) {
    border: 1px solid #797E8A;
}

/* Override border color for add-filter button to use original blue */
button.ant-btn.add-filter:not(.ant-btn-primary) {
    border: 1px solid #0083d2;
}

/* Ensure input component boundaries meet ≥ 3 : 1 contrast on white */
input[nz-input],
textarea[nz-input] {
    border: 1px solid #797E8A;
}

/* WCAG 1.4.4 – relative spacing so wrapped header items don't overlap */
.top-text {
    vertical-align: middle;
    font-weight: bold;
    margin-right: 1.25rem; // 20px → 1.25rem
}

.top-icon {
    font-size: 1.125rem; // 18px → 1.125rem
    margin-right: 0.3125rem; // 5px → 0.3125rem
}
.top-menu {
    float: left;
    padding: 0 0 0 0.625rem; // 10px → 0.625rem
    color: #125b7e;
}

.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
}

::ng-deep .ant-modal-title {
    font-weight: bold;
}
.more {
    cursor: pointer;
    text-align: center;
}

@media (max-width:640px) {
    .mobile-title {
        font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // Assumption: original 50px (~3.125rem)
        /* WCAG 1.4.12 fix: increase line-height to support custom text spacing */
        line-height: 1.5em !important; // Assumption: 1.5× meets guideline while maintaining layout
        padding-bottom: 20px !important;
    }
  }

/* Responsive width for small/simple dialogs */
::ng-deep .narrow-modal {
  width: 90vw;      /* fits tiny viewports */
  max-width: 32.5rem; /* ≈520 px on larger screens */
}

/* Full border for selects inside narrow modals */
::ng-deep .narrow-modal .mat-form-field-appearance-legacy .mat-form-field-flex {
  border: 1px solid #797E8A;
  border-radius: 4px;
  padding: 0 0.5rem;
}
::ng-deep .narrow-modal .mat-form-field-appearance-legacy .mat-form-field-underline {
  display: none;
}

::ng-deep .narrow-modal input[nz-input],
::ng-deep .narrow-modal textarea[nz-input] {
  border: 1px solid #797E8A;
}

/* Per-component modal table column widths for users-and-teams modals */
/* Applies to: Change Role, Reset Password, Move to Team, Edit User, and Edit Team modals */
.narrow-modal .modal-table td:first-child {
  width: 33.33%;
  white-space: nowrap;
}
.narrow-modal .modal-table td:last-child {
  width: 66.66%;
}