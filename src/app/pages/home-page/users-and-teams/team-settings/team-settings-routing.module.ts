import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { TeamSettingComponent } from './team-settings.component';

// Single comment: Removed stray top-level `imports:` statement and moved
// `anchorScrolling` configuration into the NgModule so the file compiles.

const routes: Routes = [
  { path: '', component: TeamSettingComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TeamSettingRoutingModule { }
