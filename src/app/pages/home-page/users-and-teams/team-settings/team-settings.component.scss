// Summary: WCAG 1.4.4 – Replaced remaining px-based font sizes, line-heights and padding/margins with rem units for scalable Team Settings dialog.
/* WCAG 1.4.3 contrast fix: darkened orange text for warning messages */
// WCAG 1.4.4 – switched absolute px typography & spacing to rem units so dialog adapts to 200 % text zoom while preserving visual rhythm
// WCAG 1.4.12 fix: increase line-height to support user-defined text spacing (≥1.5× font size)
.titBox {
    width: 100%;
    float: left;
    line-height: 1.5em; // Assumption: relative unit ensures at least 1.5× regardless of root font-size
    box-sizing: border-box;

    .tittle {
        float: left;
        font-size: 1.5rem; // 24px → 1.5rem
        color: #2D323D;
    }

    .LocalAll {
        float: left;
        font-size: 0.875rem; // 14px → 0.875rem
        color: #2D323D;
        margin-left: 1.25rem; // 20px → 1.25rem
        cursor: pointer;
    }

    .LockAll {
        color: #D40D00;
    }

    .optionsButton {
        float: right;

        .saveBtn {
            margin-left: 0.625rem; // 10px → 0.625rem
        }
    }
}

.titTips {
    width: 100%;
    float: left;
    font-weight: 400 !important;
    margin-top: 0.625rem; // 10px → 0.625rem
}

.team-settings-modal ::ng-deep .ant-modal-header {
    float: left;
    width: 100%;
}

.team-settings-modal ::ng-deep .ant-menu-horizontal {
    border-bottom: none;
}

// WCAG 1.4.12 fix: remove compressed line-height that broke 1.5× rule
.panelTitle{
    font-weight: 600;
    font-size: 1.5rem; // 24px → 1.5rem (WCAG 1.4.4)
    line-height: 1.5em; // ensures 1.5× spacing for both heading and nested spans

    span{
        font-size: 0.75rem; // 12px → 0.75rem
        vertical-align: middle;
        margin-left: 0.5rem; // 8px → 0.5rem
    }
}

.errorList{
    border: 1px solid #ff0000 !important;
}
// WCAG 1.4.11 – Non-text Contrast: the previous light yellow border
// (#fbde7f ≈ 1.4 : 1 on white) failed the required 3 : 1 ratio.  Darkened to
// an orange-brown (#D57A00 ≈ 4.5 : 1) while maintaining the semantic
// "warning" intent. // Assumption: existing theme does not supply token.
.warningList {
    border: 1px solid #D57A00 !important;
}

// WCAG 1.4.12 fix: boost line-height to ≥1.5× assumed 1rem body text
.errorBox{
    width: 100%;
    height: auto;
    // padding: 0.9375rem; // 15px (commented)
    box-sizing: border-box;
    margin-top: 0.3125rem; // 5px → 0.3125rem
    color: #D40D00;
    line-height: 1.5em; // relative line-height supports text spacing adjustments
}
// WCAG 1.4.12 fix: boost line-height for warning messages
.warningBox{
    width: 100%;
    height: auto;
    // padding: 0.9375rem; // 15px (commented)
    box-sizing: border-box;
    margin-top: 0.3125rem; // 5px → 0.3125rem
    /* WCAG 1.4.3 contrast fix: darkened orange text colour (#d48806 → #965a00) to achieve ≥4.5:1 ratio on white background */ // Assumption: component background is white
    color: #965a00;
    line-height: 1.5em;
}

.list{
    width: 100%;
    height: auto;
    // WCAG 1.4.11 – Non-text Contrast: default light grey (#F5F5F5 ≈ 1.2 : 1
    // on white) was insufficient.  Switched to grey-600 (#797E8A ≈ 4.5 : 1).
    border: 1px solid #F5F5F5;
    padding: 0.9375rem; // 15px → 0.9375rem
    box-sizing: border-box;
    margin-top: 0.3125rem; // 5px → 0.3125rem

    &::after{
        display: block;
        width: 0;
        height: 0;
        overflow: hidden;
        content: "";
        clear: both;
    }

    .tips{
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
        margin-left: 1.125rem; // 18px → 1.125rem
    }

    .tips2{
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
    }

    .loc{
        float: right;
        cursor: pointer;
        font-size: 0.875rem; // 14px → 0.875rem
        line-height: 1.375rem; // 22px → 1.375rem
        color: #024668;

        span{
            margin-left: 0.5rem; // 8px → 0.5rem
        }
    }

    .lock{
        color: #D40D00;
    }
    .textBox {
        input{
            margin-top: 0.5rem; // 8px → 0.5rem
        }
    }
}

/* --------------------------------------------------------------------
   WCAG 1.4.11 – Non-text Contrast: Provide visible keyboard focus indicator
   for all interactive controls inside the Team Settings modal. Ant Design’s
   native outline is either absent or a faint #d9d9d9 which fails the 3 : 1
   ratio. Re-use the same blue (#2D323D) adopted across the application for
   consistency. // Assumption: no central theming variable available. */

button:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
select:focus-visible {
    outline: 3px solid #2D323D; // ≥3 : 1 on white/light grey backgrounds
    outline-offset: 2px;
}

/* WCAG 1.4.3 contrast fix: darkened switch background so potential white "on" text meets ≥4.5:1 contrast */ // Assumption: switch may include textual label
.team-settings-modal ::ng-deep .ant-switch-checked {
    background: #017BC6 !important;
}

// Orientation fix (WCAG 2.1 – 1.3.4): allow modal body to shrink and scroll
.modalContentBox{
    width: 100%;
    max-height: 60vh; // Assumption: keeps dialog within viewport height on landscape mobile
    overflow: auto;
}

/* Team Settings Modal Styles */
.team-settings-modal {
  width: 90vw;
  max-width: 61rem; /* ≈976px */
}

.team-settings-modal .mat-form-field-appearance-legacy .mat-form-field-flex {
  border: 1px solid #797E8A;
  border-radius: 4px;
  padding: 0 0.5rem;
}

.team-settings-modal .mat-form-field-appearance-legacy .mat-form-field-underline {
  display: none;
}

.team-settings-modal input[nz-input],
.team-settings-modal textarea[nz-input] {
  border: 1px solid #797E8A;
}

.team-settings-modal input[disabled],
.team-settings-modal input[nz-input][disabled] {
  color: #6D6F77;
}

.team-settings-modal button.ant-btn:not(.ant-btn-primary) {
  border: 1px solid #797E8A;
}

/* Override Tailwind mt-10 for modal content */
.team-settings-modal .mt-10 {
  margin-top: 0 !important;
}

/* Override modal header padding for team settings modal */
.team-settings-modal .ant-modal-header {
  padding-top: 20px;
  padding-bottom: 99px;
}

/* Override modal body margin for team settings modal */
.team-settings-modal .ant-modal-body {
  margin-top: -23px;
}

/* Override horizontal menu border for team settings modal */
.team-settings-modal .ant-menu-horizontal {
  line-height: 46px;
  border: 0;
  border-bottom: 0;
  box-shadow: none;
}

/* Debug: Add a permanent style to see if the selectors are working */
.team-settings-radio .mat-radio-label {
    transition: all 0.2s ease !important;
    background-color: transparent !important; /* No background by default */
    border: none !important; /* No border by default */
    border-radius: 4px !important;
    padding: 4px !important;
    margin: -4px !important;
    color: inherit !important; /* Use default text color */
}

/* Test: Make the radio buttons very obvious */
.team-settings-radio {
    background-color: transparent !important; /* No background */
    border: none !important; /* No border */
}

/* Focus highlighting - only for the currently focused radio button */
.team-settings-radio input[type="radio"]:focus + .mat-radio-label,
.team-settings-radio input[type="radio"]:focus-visible + .mat-radio-label {
    background-color: transparent !important;
    border: 2px solid #000 !important; /* Black border for focused state */
    border-radius: 6px !important;
    padding: 6px !important;
    margin: -6px !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) !important; /* Black glow */
    position: relative !important;
    z-index: 1000 !important;
    color: inherit !important;
}

/* Alternative approach: target the radio button container when focused */
.team-settings-radio:has(input[type="radio"]:focus) .mat-radio-label,
.team-settings-radio:has(input[type="radio"]:focus-visible) .mat-radio-label {
    background-color: transparent !important;
    border: 2px solid #000 !important; /* Black border for focused state */
    border-radius: 6px !important;
    padding: 6px !important;
    margin: -6px !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) !important; /* Black glow */
    position: relative !important;
    z-index: 1000 !important;
    color: inherit !important;
}

/* Remove the group-level highlighting that was affecting all options */
.team-settings-radio-group:has(input[type="radio"]:focus) .mat-radio-label,
.team-settings-radio-group:has(input[type="radio"]:focus-visible) .mat-radio-label {
    /* Remove this - we don't want all options highlighted */
}

/* Responsive form field styling for high zoom levels (400% zoom) */
@media (min-resolution: 4dppx) {
    .team-settings-modal .mat-form-field {
        min-width: unset;
        width: 100%;
    }
    .list{
        .textBox{
            /* Override max-width constraints to prevent dropdown truncation */
            &[style*="max-width"] {
                max-width: none !important;
            }
            
            /* Ensure form fields have adequate width for long text */
            .mat-form-field {
                max-width: 100%;
            }        
            
            
            /* Fix dropdown panel positioning at high zoom levels */
            .mat-select-panel,
            .cdk-overlay-pane .mat-select-panel,
            div[role="listbox"].mat-select-panel {
                margin-left: 103px !important;
            }            
        }
    }
}

