import { Component,  Input, OnInit, ElementRef, ViewChild, TemplateRef, ViewEncapsulation } from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal'
import { TeamSettingsService } from "@core/services";
import { CookieService } from "ngx-cookie-service";

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'team-settings',
    templateUrl: './team-settings.component.html',
    styleUrls: ['./team-settings.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class TeamSettingComponent implements OnInit {
[x: string]: any;


  @Input() dialogType:any;
  @Input() groupId:any;
  @Input() parentGroupId:any;
  isConfirmLoading = false;
  isLocalAll = false;
  retentionEnabled = false;
  maxRetentionDays:number = 0;
  enableDelegate:boolean = true;
  hsmDsignenabled = true;
  shareContactsEnabled = false;
  shareDocTemplateEnabled = false;
  pdfaEnabled = false;
  initialRetentionInterval = 0;
  anchorTagsEnabled = false;
  stampsEnabled = false;
  formData =  {
      "generalSetting": {
          "enableHelp": true,
          "timeZone": "Eastern Time Zone",
          "language": 550,
          "usageWarnShowNum": 1,
          "usageWarn": 1
      },
      "securitySetting": {
          "sessionTimeout": 1,
          "sessionTimeoutUnit": 1,
          "smsTimeout": 1,
          "smsTimeoutUnit": 1,
          "adminSecNotif": true,
          "enforce2fa": true,
          "rememberDeviceDays": 0,
          "newDevice": true
      },
      "documentSetting": {
          "pdfEncryption": true,
          "usePdfa":false,
          "mergeAuditTrail": true,
          "attachAuditTrail": true,
          "combineEmailAttachment": true,
          "defaultExpireIntervalAfterSent": 1,
          "poweredBySignority": true,
          "defaultDateFormat": "YYYY-MM-DD",
          "displaySignDateTime": false,
          "forceRegulatory": false,
          "defaultTextHeight": 100,
          "defaultTextWidth": 100,
          "defaultDateHeight": 100,
          "defaultDateWidth": 100,
          "defaultFontSize": '12pt',
          "defaultAnchor": false,
          "esign": true,
          "dsign": true
      },
      "notificationsSetting": {
          "notifReminderEmail": 1,
          "defaultReminderEmailInterval": 1,
          "defaultSendReminderPeriodUnit": 1,
          "defaultMaxReminderEmailCount": 3,
          "notifView": true,
          "notifSign": true,
          "finalNotifSender": 1,
          "finalNotifRecipient": 1,
          "notifExpiredEmail": true,
          "defaultLastReminderIntervalBeforeExpire": 3,
          "defaultLastReminderIntervalBeforeExpireUnit": null,
          "finalSendCopyEmail": true,
          "finalCopyEmail": "",
          "sendInvoicesOverageNotifinMultiple": false,
          "sendInvoicesOverageNotifinMultipleText": "",
          "sendDocRecipient": true
      },
      "sharingSetting": {
          "shareAddressBookToTeam": true,
          "shareAutomaticallyDocToTeam": true,
          "shareAutomaticallyTemplateToTeam": true
      },
      "signerOption": {
          "hideSigningButtonDrawingSignature": true,
          "hideSigningButtonTypeSignature": false,
          "hideSigningButtonUploadSignature": true,
          "hideButtonChangeSigner": true,
          "hideButtonSave": false,
          "hideButtonDownload": false,
          "hideButtonReject": false,
          "allowDelegateSign": false,
          "allowStamps": false,
          "allowStampUpload": false,
        //   "hideButtonStatus": false,
          "hideButtonThumbnail": false,
        //   "hideButtonHelp": false,
          "hideViewDocumentButton": false,
          "continueAfterSignerRejection":'0'
      },
      "retentionBackup": {
          "retention": false,
          "retentionInterval": 1,
          "retentionSaveBeforePurge": false,
          "retentionType": 1,
          "enableUseDocRetention": false
      },
      "id": 2,
      "hostId": 19894,
      "groupId":0,
      "locks": [
          {
              "id": 1,
              "itemId": 1,
              "itemName": "enableHelp",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 2,
              "itemId": 2,
              "itemName": "timeZone",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 3,
              "itemId": 3,
              "itemName": "language",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 4,
              "itemId": 4,
              "itemName": "usageWarn",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 5,
              "itemId": 5,
              "itemName": "sessionTimeout",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 6,
              "itemId": 6,
              "itemName": "smsTimeout",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 7,
              "itemId": 7,
              "itemName": "adminSecNotif",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 8,
              "itemId": 8,
              "itemName": "enforce2fa",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 9,
              "itemId": 9,
              "itemName": "newDevice",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 10,
              "itemId": 10,
              "itemName": "pdfEncryption",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 11,
              "itemId": 11,
              "itemName": "eSign",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 12,
              "itemId": 12,
              "itemName": "dSign",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 13,
              "itemId": 13,
              "itemName": "mergeAuditTrail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 14,
              "itemId": 14,
              "itemName": "attachAuditTrail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 15,
              "itemId": 15,
              "itemName": "combineEmailAttachment",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 16,
              "itemId": 16,
              "itemName": "poweredBySignority",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 17,
              "itemId": 17,
              "itemName": "defaultExpireIntervalAfterSent",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 18,
              "itemId": 18,
              "itemName": "defaultDateFormat",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 19,
              "itemId": 19,
              "itemName": "defaultTextTag",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 20,
              "itemId": 20,
              "itemName": "defaultDateTag",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 21,
              "itemId": 21,
              "itemName": "notifReminderEmail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 22,
              "itemId": 22,
              "itemName": "defaultReminder",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 23,
              "itemId": 23,
              "itemName": "defaultMaxReminderEmailCount",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 24,
              "itemId": 24,
              "itemName": "notifRecipientFinalEmail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 25,
              "itemId": 25,
              "itemName": "notifView",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 26,
              "itemId": 26,
              "itemName": "notifSign",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 27,
              "itemId": 27,
              "itemName": "finalNotifSender",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 28,
              "itemId": 28,
              "itemName": "finalNotifRecipient",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 29,
              "itemId": 29,
              "itemName": "notifExpiredEmail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 30,
              "itemId": 30,
              "itemName": "Warning of document expiration before it expires",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 31,
              "itemId": 31,
              "itemName": "finalSendCopyEmail",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 32,
              "itemId": 32,
              "itemName": "Send invoices and overage notifications to multiple email addresses)",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 33,
              "itemId": 33,
              "itemName": "shareAddressBookToTeam",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 34,
              "itemId": 34,
              "itemName": "shareAutomaticallyDocToTeam",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 35,
              "itemId": 35,
              "itemName": "Automatically share templates with team members",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 36,
              "itemId": 36,
              "itemName": "hideButtonReject",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 37,
              "itemId": 37,
              "itemName": "hideButtonChangeSigner",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 38,
              "itemId": 38,
              "itemName": "hideButtonSave",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 39,
              "itemId": 39,
              "itemName": "hideButtonDownload",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 40,
              "itemId": 40,
              "itemName": "hideViewDocumentButton",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 41,
              "itemId": 41,
              "itemName": "hideButtonStatus",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 42,
              "itemId": 42,
              "itemName": "Hide Thumbnail Button",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 43,
              "itemId": 43,
              "itemName": "hideButtonHelp",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 44,
              "itemId": 44,
              "itemName": "hideSigningButtonDrawingSignature",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 45,
              "itemId": 45,
              "itemName": "hideSigningButtonTypeSignature",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 46,
              "itemId": 46,
              "itemName": "hideSigningButtonUploadSignature",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 47,
              "itemId": 47,
              "itemName": "retention",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 48,
              "itemId": 48,
              "itemName": "retentionInterval",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 49,
              "itemId": 49,
              "itemName": "retentionType",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 50,
              "itemId": 50,
              "itemName": "retentionSaveBeforePurge",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 51,
              "itemId": 51,
              "itemName": "enableUseDocRetention",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
              "id": 52,
              "itemId": 52,
              "itemName": "allowDelegateSign",
              "locked": 0,
              "lockedUserId": 21688,
              "lockedRoleId": null,
              "lockedUserName": "super t",
              "lockedDateTime": "",
              "lockedHint": "",
              "operate": false
          },
          {
            "id": 53,
            "itemId": 53,
            "itemName": "defaultFontSize",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 53,
            "itemId": 53,
            "itemName": "defaultFontSize",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 54,
            "itemId": 54,
            "itemName": "defaultAnchor",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 55,
            "itemId": 55,
            "itemName": "continueAfterSignerRejection",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 56,
            "itemId": 56,
            "itemName": "forceRegulatory",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 57,
            "itemId": 57,
            "itemName": "allowStamps",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        },
        {
            "id": 58,
            "itemId": 58,
            "itemName": "allowStampUpload",
            "locked": 0,
            "lockedUserId": 21688,
            "lockedRoleId": null,
            "lockedUserName": "super t",
            "lockedDateTime": "",
            "lockedHint": "",
            "operate": false
        }
      ]
  };

  mnusStatus="General";

  tittle="";
  titTips="";
  dateWidthMin = 0;

  lockedUserName="";
  lockedUserId=0;

  getData(groupId:any) {

      this.TeamSettings.get_group_setting(groupId).subscribe(v => {
          this.formData = v.data;
          //make sure sendDocRecipient has a value
          if(typeof this.formData.notificationsSetting.sendDocRecipient == 'undefined') {
            this.formData.notificationsSetting.sendDocRecipient = true;
          }
          if(v.data.documentSetting.defaultDateFormat === "MMMM DD, YYYY") {
              this.dateWidthMin = 107;
          }else {
              this.dateWidthMin = 53;
          }

          //keep track of initial retention interval
          this.initialRetentionInterval = v.data.retentionBackup.retentionInterval

          //set pdf type
          if(v.data.documentSetting.pdfEncryption) {
            this.pdfType = '2'
          }
          else if(v.data.documentSetting.usePdfa) {
            this.pdfType = '3'
          }
          else {
            this.pdfType = '1'
          }

          this.formData.signerOption.continueAfterSignerRejection = this.formData.signerOption.continueAfterSignerRejection.toString()

          //set retain type
          if(v.data.retentionBackup.retentionType == 75) {
            this.preventDocDeletion = false;
          }
          else if(v.data.retentionBackup.retentionType == 76) {
            this.preventDocDeletion = true
          }
          else if(v.data.retentionBackup.retentionType == 77) {
            this.preventDocDeletion = true;
          }


          const len = this.formData.locks.length;
          for(let i = 0;i < len; i++){
              if(this.formData.locks[i].locked != 2){
                  return;
              }
          }

          this.isLocalAll = true;
      });
  }

  showModal(): void {
      this.dialogType = true;
      this.getData(this.groupId);
      this.signatureMethod = false;
      if(this.parentGroupId === 0) {
          this.tittle= $localize`Global Settings`;
          this.titTips= $localize`The Global Settings are inherited by all of your teams. Locking any will prevent your teams and sub-teams from changing them at their team level.`;
      } else {
          this.tittle=$localize`Team Settings`;
          this.titTips=$localize`The Team Settings are inherited by your sub-teams. Locking any will prevent your sub-teams from changing them at their team level.`;
      }
  }
  pdfType = "1";
  preventDocDeletion = false;
  signatureMethod = false;
  DocumentExpirationAfterBeingSent = false;
  DefaultTextHeight = false;
  DefaultTextWidth = false;
  DefaultDateHeight = false;
  DefaultDateWidth = false;
  RetentionSaveBeforePurge = false;
  SessionLogout = false;
  SMSError = false;
  DefaultReminderEmailInterval = false;
  DefaultMaxReminderEmailCount = false;
  RetentionInterval = false;
  retentionMaxError = false;
  FinalCopyEmail = false;
  SendInvoicesOverageNotifinMultipleText = false;
  modalService: any;
  SyncEnabled:boolean = false;
  handleOk(): void {
      if(this.parentGroupId === 0) {
        this.modalService = this.modal.warning({
              nzTitle: this.globalModalTitle,
              nzOkText: $localize `Continue`,
              nzCancelText: $localize `No`,
              nzOnOk: () => this.putData()
          });
      }else {
        this.modalService = this.modal.warning({
              nzTitle:  this.settingsModalTitle,
              nzOkText: $localize `Continue`,
              nzCancelText: $localize `No`,
              nzOnOk: () => this.putData()
          });
      }
  }
  cloudRetentionWarning() {
    if(this.formData.retentionBackup.retentionSaveBeforePurge) {
        this.modalService = this.modal.warning({
            nzTitle: $localize `Turning this setting off will no longer push finalized documents to your storage integration when your documents are purged. You will be at risk of losing your files if you have not backed it through other means.`,
            nzOkText: $localize `Continue`,
            nzCancelText: $localize `Cancel`,
            nzOnCancel: () => {this.formData.retentionBackup.retentionSaveBeforePurge = true;}
        });
    }
  }
  retentionDaysWarning() {
    if(this.formData.retentionBackup.retentionInterval < this.initialRetentionInterval) {
        this.modalService = this.modal.warning({
            nzTitle: $localize `You are about to reduce the retention period to ${this.formData.retentionBackup.retentionInterval} day(s). Documents finalized in the last ${this.formData.retentionBackup.retentionInterval} day(s) will be impacted.`,
            nzOkText: $localize `Continue`,
            nzCancelText: $localize `Cancel`,
            nzOnCancel: () => {this.formData.retentionBackup.retentionInterval = this.initialRetentionInterval;}
        });
    }
  }
  putData(): void {
      this.signatureMethod = false;
      this.DocumentExpirationAfterBeingSent = false;
      this.DefaultTextHeight = false;
      this.DefaultTextWidth = false;
      this.DefaultDateHeight = false;
      this.DefaultDateWidth = false;
      this.RetentionSaveBeforePurge = false;
      this.SessionLogout = false;
	  this.SMSError = false;
      this.DefaultReminderEmailInterval = false;
      this.DefaultMaxReminderEmailCount = false;
      this.RetentionInterval = false;
      this.retentionMaxError = false;
      this.FinalCopyEmail = false;
      this.SendInvoicesOverageNotifinMultipleText = false;
      let sendStatus = true;

      //set pdf type
      if (this.pdfType == '2') {
          this.formData.documentSetting.pdfEncryption = true;
          this.formData.documentSetting.usePdfa = false;
      }
      else if (this.pdfType == '3') {
        this.formData.documentSetting.pdfEncryption = false;
        this.formData.documentSetting.usePdfa = true;
      }
      else {
        this.formData.documentSetting.pdfEncryption = false;
        this.formData.documentSetting.usePdfa = false;
      }

      //set retention type
      if(this.formData.retentionBackup.retention && !this.preventDocDeletion) {
        this.formData.retentionBackup.retentionType = 75; //formerly "retain only"
      }
      else if(this.formData.retentionBackup.retention && this.preventDocDeletion) {
        this.formData.retentionBackup.retentionType = 76; //formerly "retain and purge"
      }
      else if(!this.formData.retentionBackup.retention && this.preventDocDeletion) {
        this.formData.retentionBackup.retentionType = 77; //formerly "purge only"
      }
      else {
        this.formData.retentionBackup.retentionType = 0;
      }


      if (this.formData.documentSetting.esign === false && (this.formData.documentSetting.dsign === false || !this.hsmDsignenabled)) {
          this.signatureMethod = true;
          sendStatus = false;
      }
      if(this.formData.documentSetting.defaultExpireIntervalAfterSent < 1 || this.formData.documentSetting.defaultExpireIntervalAfterSent > 365){
          this.DocumentExpirationAfterBeingSent = true;
          sendStatus = false;
      }
      if(this.formData.documentSetting.defaultTextHeight < 13 || this.formData.documentSetting.defaultTextHeight > 792){
          this.DefaultTextHeight = true;
          sendStatus = false;
      }
      if(this.formData.documentSetting.defaultTextWidth < 13 || this.formData.documentSetting.defaultTextWidth > 612){
          this.DefaultTextWidth = true;
          sendStatus = false;
      }
      if(this.formData.documentSetting.defaultDateHeight < 13 || this.formData.documentSetting.defaultDateHeight > 792){
          this.DefaultDateHeight = true;
          sendStatus = false;
      }
      if(this.formData.documentSetting.defaultDateWidth < this.dateWidthMin || this.formData.documentSetting.defaultDateWidth > 612){
          this.DefaultDateWidth = true;
          sendStatus = false;
      }
      if(this.formData.retentionBackup.retentionSaveBeforePurge && !this.SyncEnabled){
          this.RetentionSaveBeforePurge = true;
          sendStatus = false;
      }
      if(this.formData.notificationsSetting.notifReminderEmail){
          if(this.formData.notificationsSetting.defaultSendReminderPeriodUnit === 1 && (Number(this.formData.notificationsSetting.defaultReminderEmailInterval) < 1 || Number(this.formData.notificationsSetting.defaultReminderEmailInterval) > 365)) {
              this.DefaultReminderEmailInterval = true;
              sendStatus = false;
          }
          if(this.formData.notificationsSetting.defaultSendReminderPeriodUnit === 2 && (Number(this.formData.notificationsSetting.defaultReminderEmailInterval) < 1 || Number(this.formData.notificationsSetting.defaultReminderEmailInterval) > 168)) {
              this.DefaultReminderEmailInterval = true;
              sendStatus = false;
          }
          if(this.formData.notificationsSetting.defaultSendReminderPeriodUnit === 3 && (Number(this.formData.notificationsSetting.defaultReminderEmailInterval) < 1 || Number(this.formData.notificationsSetting.defaultReminderEmailInterval) > 10080)) {
              this.DefaultReminderEmailInterval = true;
              sendStatus = false;
          }
          if(Number(this.formData.notificationsSetting.defaultMaxReminderEmailCount) < 1 || Number(this.formData.notificationsSetting.defaultMaxReminderEmailCount) > 365) {
              this.DefaultMaxReminderEmailCount = true;
              sendStatus = false;
          }
      }
      if(this.parentGroupId === 0){
          if(this.formData.securitySetting.sessionTimeoutUnit == 1){
              if(Number(this.formData.securitySetting.sessionTimeout) < 1 || Number(this.formData.securitySetting.sessionTimeout) > 365){
                  this.SessionLogout = true;
                  sendStatus = false;
              }
          }
          if(this.formData.securitySetting.sessionTimeoutUnit == 2){
              if(Number(this.formData.securitySetting.sessionTimeout) < 1 || Number(this.formData.securitySetting.sessionTimeout) > 876){
                  this.SessionLogout = true;
                  sendStatus = false;
              }
          }
          if(this.formData.securitySetting.sessionTimeoutUnit == 3){
              if(Number(this.formData.securitySetting.sessionTimeout) < 5 || Number(this.formData.securitySetting.sessionTimeout) > 525600){
                  this.SessionLogout = true;
                  sendStatus = false;
              }
          }
		  
		  if(this.formData.securitySetting.smsTimeoutUnit == 1){
              if(Number(this.formData.securitySetting.smsTimeout) < 1 || Number(this.formData.securitySetting.smsTimeout) > 365){
                  this.SMSError = true;
                  sendStatus = false;
              }
          }
          if(this.formData.securitySetting.smsTimeoutUnit == 2){
              if(Number(this.formData.securitySetting.smsTimeout) < 1 || Number(this.formData.securitySetting.smsTimeout) > 876){
                  this.SMSError = true;
                  sendStatus = false;
              }
          }
          if(this.formData.securitySetting.smsTimeoutUnit == 3){
              if(Number(this.formData.securitySetting.smsTimeout) < 5 || Number(this.formData.securitySetting.smsTimeout) > 525600){
                  this.SMSError = true;
                  sendStatus = false;
              }
          }
      }
      if(this.formData.notificationsSetting.finalSendCopyEmail && this.formData.notificationsSetting.finalCopyEmail === ""){
          this.FinalCopyEmail = true;
          sendStatus = false;
      }
      if(this.formData.notificationsSetting.sendInvoicesOverageNotifinMultiple && this.formData.notificationsSetting.sendInvoicesOverageNotifinMultipleText === ""){
          this.SendInvoicesOverageNotifinMultipleText = true;
          sendStatus = false;
      }
      if(this.formData.retentionBackup.retention && (Number(this.formData.retentionBackup.retentionInterval) < 1 || Number(this.formData.retentionBackup.retentionInterval) > 1000)){
          this.RetentionInterval = true;
          sendStatus = false;
      }
      if(this.formData.retentionBackup.retention && (this.formData.retentionBackup.retentionInterval > this.maxRetentionDays)){
          this.retentionMaxError = true;
          sendStatus = false;
      }
      if(!sendStatus){
          if(this.SessionLogout){
              this.modalContentBox!.nativeElement.scrollTop =  451;
              return;
          }
		  if(this.SMSError){
              this.modalContentBox!.nativeElement.scrollTop =  451;
              return;
          }
          if(this.signatureMethod){
            this.modalContentBox!.nativeElement.scrollTop =  960;
            return;
          }
          if(this.DocumentExpirationAfterBeingSent){
            this.modalContentBox!.nativeElement.scrollTop =  1320;
            return;
          }
          if(this.DefaultTextHeight || this.DefaultTextWidth){
            this.modalContentBox!.nativeElement.scrollTop =  1532;
            return;
          }
          if(this.DefaultDateHeight || this.DefaultDateWidth){
            this.modalContentBox!.nativeElement.scrollTop =  1623;
            return;
          }
          if(this.DefaultReminderEmailInterval || this.DefaultMaxReminderEmailCount){
              this.modalContentBox!.nativeElement.scrollTop =  1739;
              return;
          }
          if(this.FinalCopyEmail || this.SendInvoicesOverageNotifinMultipleText){
              this.modalContentBox!.nativeElement.scrollTop =  2700;
              return;
          }
          if(this.RetentionSaveBeforePurge){
            this.modalContentBox!.nativeElement.scrollTop =  3780;
            return;
          }
          if(this.RetentionInterval || this.retentionMaxError){
              this.modalContentBox!.nativeElement.scrollTop =  3780;
              return;
          }
      }
      this.isConfirmLoading = true;
      this.formData.groupId = this.groupId;
      this.TeamSettings.put_group_setting(this.formData).subscribe(v => {

          if(v.success) this.msg.success('Success');
          this.dialogType = false;
          this.isConfirmLoading = false;
          this.getData(this.groupId);

          let urlArray = window.location.href.split("/")
          let goURl = ``
          for(let i = 0; i < urlArray.length; i++) {
              if(this.formData.generalSetting.language == 550){
                  this.setCookie('lang', 'en')
                  if(urlArray[i] == 'fr') {
                      urlArray[i] = 'en'
                  }
              }else if(this.formData.generalSetting.language == 551) {
                  this.setCookie('lang', 'fr')
                  if(urlArray[i] == 'en') {
                      urlArray[i] = 'fr'
                  }
              }
              this.setCookie('langCode', this.formData.generalSetting.language)
              if(i != (urlArray.length - 1)) {
                  goURl += `${urlArray[i]}/`
              }else{
                  goURl += `${urlArray[i]}`
              }
          }
          goURl = goURl.replace("settings=true", "");
          window.location.href = goURl
      });
  }

  handleCancel(): void {
      this.dialogType = false;
      this.signatureMethod = false;
      this.DocumentExpirationAfterBeingSent = false;
      this.DefaultTextHeight = false;
      this.DefaultTextWidth = false;
      this.DefaultDateHeight = false;
      this.DefaultDateWidth = false;
      this.RetentionSaveBeforePurge = false;
      this.SessionLogout = false;
      this.DefaultReminderEmailInterval = false;
      this.DefaultMaxReminderEmailCount = false;
      this.RetentionInterval = false;
      this.FinalCopyEmail = false;
      this.SendInvoicesOverageNotifinMultipleText = false;
  }
  //prevent bug where dropdown menu scrolls down with page
  hideDropdowns() {
    document
    .querySelectorAll<HTMLElement>('.titBox')
    .forEach(node => node.click())
  }

  handleLock(n:number): void {
      this.formData.locks[n].locked = 2;
      this.formData.locks[n].lockedUserId = this.lockedUserId;
      this.formData.locks[n].lockedUserName = this.lockedUserName;
      const len = this.formData.locks.length;
      for(let i = 0;i < len; i++){
          if(this.formData.locks[i].locked != 2){
              return;
          }
      }
      this.isLocalAll = true;
  }

  handleOpenLock(n:number): void {
      if(!this.formData.locks[n].operate){
          return;
      }
      this.formData.locks[n].locked = 1;
      this.isLocalAll = false;
  }

  lockAll(): void {
      const len = this.formData.locks.length;
      for(let i = 0;i < len; i++){
          if(this.formData.locks[i].operate){
              this.formData.locks[i].lockedUserId = this.lockedUserId;
              this.formData.locks[i].lockedUserName = this.lockedUserName;
          }
          this.formData.locks[i].locked = 2;
      }
      this.isLocalAll = true;
  }

  unLockAll(): void {
      const len = this.formData.locks.length;
      for(let i = 0;i < len; i++){
          if(this.formData.locks[i].operate){
              this.formData.locks[i].locked = 1;
          }
      }
      this.isLocalAll = false;
  }

  @ViewChild('modalContentBox')  modalContentBox:ElementRef | undefined;
  @ViewChild('globalModalTitle', { read: TemplateRef }) globalModalTitle:TemplateRef<any> | undefined;
  @ViewChild('settingsModalTitle', { read: TemplateRef }) settingsModalTitle:TemplateRef<any> | undefined;

  goToScroll(n:number): void {
      this.modalContentBox!.nativeElement.scrollTop =  n;
  }
  scrollTo(id:string) {
    let el = document.getElementById(id);
    el?.scrollIntoView({behavior: 'smooth'});
  }

  ngScroll() {
      let scrollTop = this.modalContentBox?.nativeElement.scrollTop;
      if(scrollTop >= 0 && scrollTop < 390) {
          this.mnusStatus = 'General'
      }else if(scrollTop >= 390 && scrollTop < 853) {
          this.mnusStatus = 'Security'
      }else if(scrollTop >= 853 && scrollTop < 1739) {
          this.mnusStatus = 'Document'
      }else if(scrollTop >= 1739 && scrollTop < 2922) {
          this.mnusStatus = 'Notification'
      }else if(scrollTop >= 2922 && scrollTop < 3156) {
          this.mnusStatus = 'Sharing'
      }else if(scrollTop >= 3156 && scrollTop < 3780) {
          this.mnusStatus = 'SignerOptions'
      }else {
          this.mnusStatus = 'RetentionBackup'
      }
  }

  usageWarnChange(value:any) {
      if(value === 1) {
          this.formData.generalSetting.usageWarnShowNum = 20;
      }
  }

  defaultDateFormatChange(value:any) {
      if(value === "MMMM DD, YYYY") {
          this.dateWidthMin = 107;
      }else {
          this.dateWidthMin = 53;
      }
  }

  setCookie(cname:any, cvalue:any){
      document.cookie = cname+"="+cvalue + ";path=/";
  }

  constructor(
    private TeamSettings:TeamSettingsService,
    private msg:NzMessageService,
    private cookieService:CookieService,
    private modal: NzModalService,
    private elementRef: ElementRef
  ) {}

  // eslint-disable-next-line @angular-eslint/no-empty-lifecycle-method
  ngOnInit() {
      //   this.getData()
      let userInfo = JSON.parse(localStorage.getItem("user")|| '0' );
      this.retentionEnabled  = userInfo.userProduct.isRetentionEnabled;
      this.maxRetentionDays  = userInfo.userProduct.RetentionMaxPeriodInDays;
      this.enableDelegate  = userInfo.userProduct.delegateSignEnabled;
      this.anchorTagsEnabled  = userInfo.userProduct.addTagsFromPdfAnchorTextEnabled;
      this.stampsEnabled  = userInfo.userProduct.sealEnabled;
      this.SyncEnabled = JSON.parse(localStorage.getItem("userInfo") || '0').accountdetails?.branding?.SyncEnabled
      setInterval(() => {
          this.ngScroll()
      }, 100);
      let user = localStorage.getItem("user");
      this.shareContactsEnabled = JSON.parse(user || '0').userProduct.shareAddressBookEnabled;
      this.shareDocTemplateEnabled = JSON.parse(user || '0').userProduct.teamTemplateDocumentSharingEnabled;
      this.pdfaEnabled = JSON.parse(user || '0').userProduct.usePdfa;
      let lockedUserId = JSON.parse(user || '0').id;
      let lockedUserName = JSON.parse(user || '0').firstname + ' ' + JSON.parse(user || '0').lastname;
      this.lockedUserId = lockedUserId;
      this.lockedUserName = lockedUserName;
      this.hsmDsignenabled = userInfo.userProduct.hsmDigitalSignatureEnabled;
  }

}
