<!-- Summary: WCAG 1.4.4 – Converted inline pixel widths/heights and font-sizes (96px,22px,400px etc.) to rem units; ensured comments outside tags to avoid Angular parse errors. -->
<!-- Accessibility: added aria-labels and aria-hidden attributes to icons for WCAG 1.1.1 compliance -->
<!-- Orientation fix (WCAG 2.1 – 1.3.4): changed fixed pixel width to viewport-relative units so
     the dialog remains within the viewport in both portrait and landscape orientations. -->
<!-- WCAG 1.4.13 – enable dismissal of Team Settings dialog via ESC key and click outside -->
<nz-modal nzClassName="team-settings-modal wide-modal"
    [(nzVisible)]="dialogType"
    [nzTitle]="modalTitle"
    [nzContent]="modalContent"
    [nzFooter]="null"
    (nzOnCancel)="handleCancel()"
    [nzClosable]="false"
    [nzWidth]="'90vw'"
    [nzMaskClosable]="true"
    [nzKeyboard]="true"
    >
    <ng-template #modalTitle>
        <div class="titBox">
            <!-- WCAG 1.3.1 – promote dialog title to <h1> to maintain correct heading hierarchy -->
            <h1 class="tittle">{{ tittle }}</h1>
            <!-- Accessibility: added aria-label and aria-hidden for lock/unlock icons (WCAG 1.1.1) -->
            <div *ngIf="!isLocalAll" class="LocalAll" (click)="lockAll()" i18n-aria-label aria-label="Lock all settings" i18n>Lock All Settings <i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
            <!-- WCAG 1.4.4 – margin-left converted from 8px to 0.5rem for scalable spacing -->
            <!-- CHANGE (2025-08-06 VALIDATION): Updated aria-label so that it now begins with the exact
                 visible label text ("Lock All Settings") thereby satisfying WCAG 2.5.3 "Label in Name". -->
            <div *ngIf="isLocalAll" class="LocalAll LockAll" (click)="unLockAll()" i18n-aria-label aria-label="Lock All Settings – unlock" i18n>Lock All Settings <i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 0.5rem;"></i></div>
            <div class="optionsButton">
                <!-- WCAG 2.5.2 – add explicit type attribute to ensure activation on up-event and avoid default submit behaviour -->
                <button nz-button nzType="default" type="button" (click)="handleCancel()" i18n>Cancel</button>
                <button nz-button nzType="primary" type="button" (click)="handleOk()" [nzLoading]="isConfirmLoading" class="saveBtn" i18n>Save</button>
            </div>
        </div>
        <div class="titTips">{{ titTips }}</div>
    </ng-template>

    <ng-template #modalContent>
        <ul nz-menu nzMode="horizontal">
            <li tabindex="0" nz-menu-item [nzSelected]="mnusStatus === 'General' ? true : false"><a (click)="scrollTo('GeneralList')" i18n>General</a></li>
            <li tabindex="0"nz-menu-item [nzSelected]="mnusStatus === 'Security' ? true : false"><a (click)="scrollTo('SecurityList')" i18n>Security</a></li>
            <li tabindex="0"nz-menu-item [nzSelected]="mnusStatus === 'Document' ? true : false"><a (click)="scrollTo('DocumentList')" i18n>Document</a></li>
            <li tabindex="0"nz-menu-item [nzSelected]="mnusStatus === 'Notification' ? true : false"><a (click)="scrollTo('NotificationList')" i18n>Notification</a></li>
            <li tabindex="0"nz-menu-item [nzSelected]="mnusStatus === 'Sharing' ? true : false"><a (click)="scrollTo('SharingList')" i18n>Sharing</a></li>
            <li tabindex="0"nz-menu-item [nzSelected]="mnusStatus === 'SignerOptions' ? true : false"><a (click)="scrollTo('SignerOptionsList')" i18n>Signer Options</a></li>
            <li tabindex="0" *ngIf="retentionEnabled && parentGroupId === 0" nz-menu-item [nzSelected]="mnusStatus === 'RetentionBackup' ? true : false"><a (click)="scrollTo('RetentionList')" i18n>Retention & Backup</a></li>
        </ul>
        <div class="modalContentBox" #modalContentBox (scroll)="hideDropdowns()" tabindex="-1">
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderGeneral"
            >
                <ng-template #modalHeaderGeneral>
                    <h3 class="panelTitle" id="GeneralList" i18n>General<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable Help Instructions" appToggleEnterKey color="primary" [(ngModel)]="formData.generalSetting.enableHelp" color="primary"
                    [disabled]="formData.locks[0].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable Help Instructions</span>
                    <!-- Accessibility: provide label for interactive lock icon and hide decorative icon itself -->
                    <div *ngIf="formData.locks[0].locked === 0 || formData.locks[0].locked === 1" class="loc" (click)="handleLock(0)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <!-- CHANGE (2025-08-06 VALIDATION): Accessible name now contains the visible text string
                         "Locked by …" to satisfy WCAG 2.5.3. -->
                    <!-- Validation fix: use bound attribute with proper i18n marker to avoid
                         Angular compilation error (NG8002) – WCAG meta unchanged. -->
                    <!-- WCAG 2.5.3 (Label in Name): ensure programmatic name begins with the
                         visible text.  Removed `i18n-attr.aria-label` because Angular does not
                         allow combining the i18n attribute with a bound value on the same
                         attribute; using the bound value alone avoids a compilation error
                         (NG8002 Illegal combination of i18n and binding). -->
                    <div *ngIf="formData.locks[0].locked === 2" class="loc lock" (click)="handleOpenLock(0)"
                         [attr.aria-label]="'Locked by ' + formData.locks[0].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[0].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                </div>
                <div class="list">
                    <span class="tips2" i18n>Timezone</span>
                    <div *ngIf="formData.locks[1].locked === 0 || formData.locks[1].locked === 1" class="loc" (click)="handleLock(1)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <!-- CHANGE (2025-08-06 VALIDATION): same WCAG 2.5.3 fix as above for lock index 1. -->
                    <!-- Validation fix: bound aria-label with i18n to resolve compile error. -->
                    <!-- Same rationale as above – remove `i18n-attr.aria-label` to prevent
                         invalid combination with bound attribute. -->
                    <div *ngIf="formData.locks[1].locked === 2" class="loc lock" (click)="handleOpenLock(1)"
                         [attr.aria-label]="'Locked by ' + formData.locks[1].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[1].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                    <div class="textBox">
                        <mat-form-field class="material-select-height">
                            <mat-select i18n-aria-label aria-label="Timezone" name="timeZone"  [(ngModel)]="formData.generalSetting.timeZone" [disabled]="formData.locks[1].locked === 2 ? true : false">
                                <mat-option value="Pacific Time Zone" i18n>Pacific Time Zone</mat-option>
                                <mat-option value="Mountain Time Zone" i18n>Mountain Time Zone</mat-option>
                                <mat-option value="Central Time Zone" i18n>Central Time Zone</mat-option>
                                <mat-option value="Eastern Time Zone" i18n>Eastern Time Zone</mat-option>
                                <mat-option value="Atlantic Time Zone" i18n>Atlantic Time Zone</mat-option>
                                <mat-option value="Newfoundland Time Zone" i18n>Newfoundland Time Zone</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="list">
                    <span class="tips2"i18n>Default Language</span>
                    <div *ngIf="formData.locks[2].locked === 0 || formData.locks[2].locked === 1" class="loc" (click)="handleLock(2)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[2].locked === 2" class="loc lock" (click)="handleOpenLock(2)"
                         [attr.aria-label]="'Locked by ' + formData.locks[2].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[2].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                    <div class="textBox">
                        <mat-form-field class="material-select-height">
                            <mat-select i18n-aria-label aria-label="Default Language" name="language"  [(ngModel)]="formData.generalSetting.language" [disabled]="formData.locks[2].locked === 2 ? true : false">
                                <mat-option [value]="550" i18n>English</mat-option>
                                <mat-option [value]="551" i18n>Français</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="list" *ngIf="parentGroupId === 0">
                    <span class="tips2" i18n>Document remaining usage warning threshold</span>
                    <div *ngIf="formData.locks[3].locked === 0 || formData.locks[3].locked === 1" class="loc" (click)="handleLock(3)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[3].locked === 2" class="loc lock" (click)="handleOpenLock(3)"
                         [attr.aria-label]="'Locked by ' + formData.locks[3].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[3].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                    <div class="textBox">
                        <input *ngIf="formData.generalSetting.usageWarn === 2" nz-input placeholder="" nzSize="default"
                         [(ngModel)]="formData.generalSetting.usageWarnShowNum" style="width: 6rem;" 
                         [disabled]="formData.locks[3].locked === 2 ? true : false" i18n-aria-label aria-label="Document remaining usage warning threshold"/>
                        <input *ngIf="formData.generalSetting.usageWarn === 1" nz-input placeholder="" i18n-aria-label aria-label="Document remaining usage warning threshold"
                        nzSize="default" [(ngModel)]="formData.generalSetting.usageWarnShowNum" style="width: 6rem;" [disabled]="true" /> <!-- WCAG 1.4.4 – width from 96px to 6rem so it scales with root font size -->
                        <mat-form-field class="material-select-height" class="ml-2">
                            <mat-select i18n-aria-label aria-label="Document remaining usage warning threshold unit" name="Document remaining usage warning threshold unit"  [(ngModel)]="formData.generalSetting.usageWarn"
                            [disabled]="formData.locks[3].locked === 2 ? true : false" (ngModelChange)="usageWarnChange($event)">
                                <mat-option [value]="1" i18n>%</mat-option>
                                <mat-option [value]="2" i18n>Document</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeader"
            >
                <ng-template #modalHeader>
                    <h3 class="panelTitle" id="SecurityList" i18n>Security<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list" [class]="SessionLogout ? 'errorList' : ''">
                    <span class="tips2" i18n>Set session logout timeout period</span>
                    <div *ngIf="formData.locks[4].locked === 0 || formData.locks[4].locked === 1" class="loc" (click)="handleLock(4)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[4].locked === 2" class="loc lock" (click)="handleOpenLock(4)"
                         [attr.aria-label]="'Locked by ' + formData.locks[4].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[4].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.securitySetting.sessionTimeout" style="width: 6rem;" 
                        [disabled]="formData.locks[4].locked === 2 ? true : false" i18n-aria-label aria-label="Set session logout timeout period"/>
                        <!-- WCAG 1.4.4 – converted max-width from 304px to 19rem -->
                        <mat-form-field class="material-select-height" class="ml-2" style="max-width: 19rem;">
                            <mat-select i18n-aria-label aria-label="Session timeout unit"  name="Session timeout unit"  [(ngModel)]="formData.securitySetting.sessionTimeoutUnit"
                            [disabled]="formData.locks[4].locked === 2 ? true : false">
                                <mat-option [value]="3" i18n>minute(s)</mat-option>
                                <mat-option [value]="2" i18n>hour(s)</mat-option>
                                <mat-option [value]="1" i18n>day(s)</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="SessionLogout" class="errorBox" i18n>min 5 minute, max 365 day!</div></div>
                <div *ngIf="parentGroupId === 0" class="list" [class]="SMSError ? 'errorList' : ''">
                    <span class="tips2" i18n>Authentication code expiration period</span>
                    <!--<div class="loc"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>-->
                    <div *ngIf="formData.locks[5].locked === 0 || formData.locks[5].locked === 1" class="loc" (click)="handleLock(5)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[5].locked === 2" class="loc lock" (click)="handleOpenLock(5)"
                         [attr.aria-label]="'Locked by ' + formData.locks[5].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[5].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.securitySetting.smsTimeout" 
                        style="width: 6rem;" [disabled]="formData.locks[5].locked === 2 ? true : false" i18n-aria-label aria-label="Authentication code expiration period"/> <!-- WCAG 1.4.4 – width 96px→6rem -->
                        <mat-form-field class="material-select-height" class="ml-2">
                            <mat-select i18n-aria-label aria-label="Authentication code expiration period unit" name="Authentication code expiration period unit" [(ngModel)]="formData.securitySetting.smsTimeoutUnit"
                                [disabled]="formData.locks[5].locked === 2 ? true : false">
                                <mat-option [value]="3" i18n>minute(s)</mat-option>
                                <mat-option [value]="2" i18n>hour(s)</mat-option>
                                <mat-option [value]="1" i18n>day(s)</mat-option>
                            </mat-select>
                        </mat-form-field>

						<span nz-icon nzType="question-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                        [nzTooltipTitle]="smsToolTip" [nzTooltipOverlayStyle]="{maxWidth:'500px'}" style="font-size: 1.375rem;" class="ml-2"  
                        i18n-aria-label aria-label="More information" ></span>
                    </div>
                    <ng-template #smsToolTip>
                        <div class="mb-2" i18n>
                            Access codes sent via SMS or Email will expire after this amount of time. Default is 20 minutes.
                        </div>
                      </ng-template>
                </div>
				<div aria-live="polite"><div *ngIf="SMSError" class="errorBox" i18n>min 5 minute, max 365 day!</div></div>
                <!-- <div class="list"> -->
                    <!-- <nz-switch [(ngModel)]="formData.securitySetting.adminSecNotif" [nzDisabled]="formData.locks[6].locked === 2 ? true : false"></nz-switch> -->
                    <!-- <nz-switch [(ngModel)]="formData.securitySetting.adminSecNotif"  [disabled]="true"></nz-switch> -->
                    <!-- <span class="tips" i18n>Admin security notifications</span> -->
                    <!-- <div class="loc"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div> -->
                    <!-- <div *ngIf="formData.locks[6].locked === 0 || formData.locks[6].locked === 1" class="loc" (click)="handleLock(6)"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[6].locked === 2" class="loc lock" (click)="handleOpenLock(6)"> [attr.aria-label]="'Locked by ' + formData.locks[6].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[6].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div> -->
                <!-- </div> -->
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Enforce 2FA/MFA" appToggleEnterKey color="primary" [(ngModel)]="formData.securitySetting.enforce2fa"
                     [disabled]="formData.locks[7].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enforce 2FA/MFA </span>
                    <div *ngIf="formData.locks[7].locked === 0 || formData.locks[7].locked === 1" class="loc" (click)="handleLock(7)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[7].locked === 2" class="loc lock" (click)="handleOpenLock(7)"
                         [attr.aria-label]="'Locked by ' + formData.locks[7].lockedUserName + ' – unlock setting'">
                        Locked by {{ formData.locks[7].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i>
                    </div>
                </div>
                <div class="list">
                    <span class="tips2" i18n>Set 2FA/MFA "Remember Device" period to </span>
                    <!-- <div *ngIf="formData.locks[56].locked === 0 || formData.locks[56].locked === 1" class="loc" (click)="handleLock(56)"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[56].locked === 2" class="loc lock" (click)="handleOpenLock(56)" i18n>Locked by {{ formData.locks[7].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div> -->
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.securitySetting.rememberDeviceDays"
                         style="width: 6rem;" i18n-aria-label aria-label="2FA/MFA period"/> <!-- WCAG 1.4.4 – width 96px→6rem -->
                        <span i18n> days</span>
                        <span nz-icon nzType="question-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                        [nzTooltipTitle]="rememberDeviceToolTip" [nzTooltipOverlayStyle]="{maxWidth:'500px'}" style="font-size: 1.375rem;" class="ml-2" i18n-aria-label aria-label="More information"></span> <!-- WCAG 1.4.4 – font-size 22px→1.375rem -->
                    </div>
                </div>
                <ng-template #rememberDeviceToolTip>
                    <div class="mb-2" i18n>
                        Number of days a user can bypass 2FA/MFA after succesful login. Set value to 0 (Zero) to disable.
                    </div>
                </ng-template>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable new device login notification" appToggleEnterKey color="primary" [(ngModel)]="formData.securitySetting.newDevice"
                     [disabled]="formData.locks[8].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable new device login notification</span>
                    <div *ngIf="formData.locks[8].locked === 0 || formData.locks[8].locked === 1" class="loc" (click)="handleLock(8)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[8].locked === 2" class="loc lock" (click)="handleOpenLock(8)"  [attr.aria-label]="'Locked by ' + formData.locks[8].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[8].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderDocument"
            >
                <ng-template #modalHeaderDocument>
                    <h3 class="panelTitle" id="DocumentList" i18n>Document<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list">
                    <span class="tips2" i18n>PDF type output for finalized documents</span>
                    <div *ngIf="formData.locks[9].locked === 0 || formData.locks[9].locked === 1" class="loc" (click)="handleLock(9)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[9].locked === 2" class="loc lock" (click)="handleOpenLock(9)"  [attr.aria-label]="'Locked by ' + formData.locks[9].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[9].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <!-- <nz-switch [(ngModel)]="formData.documentSetting.pdfEncryption" [nzDisabled]="formData.locks[9].locked === 2 ? true : false"></nz-switch> -->
                    <div class="textBox">
                        <mat-form-field class="material-select-height">
                            <mat-select i18n-aria-label aria-label="PDF type output for finalized documents" name="PDF type output for finalized documents" [(ngModel)]="pdfType"
                                [disabled]="formData.locks[9].locked === 2 ? true : false">
                                <mat-option value="1" i18n>Standard PDF</mat-option>
                                <mat-option value="2" i18n>PDF Encryption</mat-option>
                                <mat-option *ngIf="pdfaEnabled" value="3" i18n>PDF/A-2b</mat-option>
                            </mat-select>
                        </mat-form-field>
                        <span nz-icon nzType="question-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                        [nzTooltipTitle]="pdfToolTip" [nzTooltipOverlayStyle]="{maxWidth:'500px'}" style="font-size: 1.375rem;" class="ml-2" i18n-aria-label aria-label="More information"></span>
                    </div>
                    <ng-template #pdfToolTip>
                        <div class="mb-2" i18n>
                            <strong>Standard PDF:</strong>
                            No additional security measures.
                        </div>
                        <div class="mb-2" i18n>
                            <strong>Encrypted PDF:</strong>
                            Protect the document's contents from unauthorized access.
                        </div>
                        <div *ngIf="pdfaEnabled" class="mb-2" i18n>
                            <strong>PDF/A-2b:</strong>
                            PDF/A-2b is a format for archiving documents. It requires fonts and graphics to be embedded and cannot be encrypted.
                        </div>

                      </ng-template>
                </div>
                <div class="list" [class]="signatureMethod ? 'errorList' : ''">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable eSignature for all users" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.esign"
                     [disabled]="formData.locks[10].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable eSignature for all users</span>
                    <div *ngIf="formData.locks[10].locked === 0 || formData.locks[10].locked === 1" class="loc" (click)="handleLock(10)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[10].locked === 2" class="loc lock" (click)="handleOpenLock(10)"  [attr.aria-label]="'Locked by ' + formData.locks[10].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[10].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div *ngIf="hsmDsignenabled" class="list" [class]="signatureMethod ? 'errorList' : ''">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable Digital Signatures for all users" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.dsign"
                     [disabled]="formData.locks[11].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable Digital Signatures for all users</span>
                    <div *ngIf="formData.locks[11].locked === 0 || formData.locks[11].locked === 1" class="loc" (click)="handleLock(11)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[11].locked === 2" class="loc lock" (click)="handleOpenLock(11)"  [attr.aria-label]="'Locked by ' + formData.locks[11].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[11].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div aria-live="polite"><div *ngIf="signatureMethod" class="errorBox" i18n>Please enable at least one signature method for your team!</div></div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Merge finalized document and audit trail into one PDF" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.mergeAuditTrail"
                     [disabled]="formData.locks[12].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Merge finalized document and audit trail into one PDF</span>
                    <div *ngIf="formData.locks[12].locked === 0 || formData.locks[12].locked === 1" class="loc" (click)="handleLock(12)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[12].locked === 2" class="loc lock" (click)="handleOpenLock(12)"  [attr.aria-label]="'Locked by ' + formData.locks[12].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[12].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Attach document audit trail to final email attachment" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.attachAuditTrail"
                     [disabled]="formData.locks[13].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Attach document audit trail to final email attachment</span>
                    <div *ngIf="formData.locks[13].locked === 0 || formData.locks[13].locked === 1" class="loc" (click)="handleLock(13)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[13].locked === 2" class="loc lock" (click)="handleOpenLock(13)"  [attr.aria-label]="'Locked by ' + formData.locks[13].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[13].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Combine final email attachment into one PDF file" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.combineEmailAttachment"
                     [disabled]="formData.locks[14].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Combine final email attachment into one PDF file</span>
                    <div *ngIf="formData.locks[14].locked === 0 || formData.locks[14].locked === 1" class="loc" (click)="handleLock(14)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[14].locked === 2" class="loc lock" (click)="handleOpenLock(14)"  [attr.aria-label]="'Locked by ' + formData.locks[14].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[14].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Display Sign powered by Signority on signature tags" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.poweredBySignority"
                     [disabled]="formData.locks[15].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Display "Sign Powered by Signority" on signature tags </span>
                    <div *ngIf="formData.locks[15].locked === 0 || formData.locks[15].locked === 1" class="loc" (click)="handleLock(15)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[15].locked === 2" class="loc lock" (click)="handleOpenLock(15)"  [attr.aria-label]="'Locked by ' + formData.locks[15].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[15].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div *ngIf="parentGroupId === 0 && anchorTagsEnabled" class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable Anchor text import by default when adding new files" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.defaultAnchor"
                     [disabled]="formData.locks[54].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable "Anchor Text" import by default when adding new files</span>
                    <!-- <div *ngIf="formData.locks[54].locked === 0 || formData.locks[54].locked === 1" class="loc" (click)="handleLock(54)"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div> -->
                    <div *ngIf="formData.locks[54].locked === 2" class="loc lock" (click)="handleOpenLock(54)"  [attr.aria-label]="'Locked by ' + formData.locks[54].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[54].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list" [class]="DocumentExpirationAfterBeingSent ? 'errorList' : ''">
                    <span class="tips2" i18n>Document expiration after being sent</span>
                    <div *ngIf="formData.locks[16].locked === 0 || formData.locks[16].locked === 1" class="loc" (click)="handleLock(16)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[16].locked === 2" class="loc lock" (click)="handleOpenLock(16)"  [attr.aria-label]="'Locked by ' + formData.locks[16].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[16].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox" i18n>
                        <!-- <nz-input-number [(ngModel)]="formData.documentSetting.defaultExpireIntervalAfterSent" style="width: 96px;" [nzMin]="1" [nzMax]="365" [nzStep]="1" [disabled]="formData.locks[16].locked === 2 ? true : false"></nz-input-number> Days -->
                        <input nz-input placeholder="" nzSize="default" [(ngModel)]="formData.documentSetting.defaultExpireIntervalAfterSent"
                         style="width: 6rem;" [disabled]="formData.locks[16].locked === 2 ? true : false" i18n-aria-label aria-label="Document expiration after being sent"/> <!-- WCAG 1.4.4 – width 96px→6rem --> Days
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="DocumentExpirationAfterBeingSent" class="errorBox" i18n>Please set a default number of days a document should expire after it is sent. (This value can be between 1 to 365)</div></div>
                <div class="list">
                    <span class="tips2" i18n>Default Date Format</span>
                    <div *ngIf="formData.locks[17].locked === 0 || formData.locks[17].locked === 1" class="loc" (click)="handleLock(17)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[17].locked === 2" class="loc lock" (click)="handleOpenLock(17)"  [attr.aria-label]="'Locked by ' + formData.locks[17].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[17].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <mat-form-field class="material-select-height" >
                            <mat-select i18n-aria-label aria-label="Default date format" name="Default date format" [(ngModel)]="formData.documentSetting.defaultDateFormat"
                                [disabled]="formData.locks[17].locked === 2 ? true : false" (ngModelChange)="defaultDateFormatChange($event)">
                                <mat-option value="YYYY-MM-DD" i18n>YYYY-MM-DD</mat-option>
                                <mat-option value="YYYY/MM/DD" i18n>YYYY/MM/DD</mat-option>
                                <mat-option value="MM-DD-YYYY" i18n>MM-DD-YYYY</mat-option>
                                <mat-option value="MM/DD/YYYY" i18n>MM/DD/YYYY</mat-option>
                                <mat-option value="DD-MM-YYYY" i18n>DD-MM-YYYY</mat-option>
                                <mat-option value="DD/MM/YYYY" i18n>DD/MM/YYYY</mat-option>
                                <mat-option value="MMMM DD, YYYY" i18n>MMMM DD, YYYY</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Display the time on sign date tags" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.displaySignDateTime"></mat-slide-toggle>
                    <span class="tips" i18n>Display the time on sign date tags</span>
                </div>
                <div *ngIf="parentGroupId === 0" class="list" [class]="DefaultTextHeight || DefaultTextWidth ? 'errorList' : ''">
                    <span class="tips2" i18n>Default Text Tag Size</span>
                    <div *ngIf="formData.locks[18].locked === 0 || formData.locks[18].locked === 1" class="loc" (click)="handleLock(18)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[18].locked === 2" class="loc lock" (click)="handleOpenLock(18)"  [attr.aria-label]="'Locked by ' + formData.locks[18].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[18].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox" style="max-width: 25rem;" i18n> <!-- WCAG 1.4.4 – max-width 400px→25rem -->
                        <span class="xs:block mr-2">Height <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.documentSetting.defaultTextHeight"
                              style="width: 6rem;" [disabled]="formData.locks[18].locked === 2 ? true : false" i18n-aria-label aria-label="Default text height"/> <!-- WCAG 1.4.4 – width 96px→6rem -->
                              px </span>
                        <span>Width <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.documentSetting.defaultTextWidth"
                              style="width: 6rem;" [disabled]="formData.locks[18].locked === 2 ? true : false" i18n-aria-label aria-label="Default text width"/> px </span> <!-- WCAG 1.4.4 – width 96px→6rem -->
                    </div>
                    <div aria-live="polite"><div *ngIf="DefaultTextHeight" class="errorBox" i18n>The default Text tag height should be between 13 and 792 pixel high.</div></div>
                    <div aria-live="polite"><div *ngIf="DefaultTextWidth" class="errorBox" i18n>The default Text tag width should be between 13 and 612 pixel wide.</div></div>
                </div>
                <div *ngIf="parentGroupId === 0" class="list" [class]="DefaultDateHeight || DefaultDateWidth ? 'errorList' : ''">
                    <span class="tips2" i18n>Default Date Tag Size</span>
                    <div *ngIf="formData.locks[19].locked === 0 || formData.locks[19].locked === 1" class="loc" (click)="handleLock(19)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[19].locked === 2" class="loc lock" (click)="handleOpenLock(19)"  [attr.aria-label]="'Locked by ' + formData.locks[19].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[19].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox" style="max-width: 25rem;" i18n>
                        <span class="xs:block mr-2">Height <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.documentSetting.defaultDateHeight"
                              style="width: 6rem;" [disabled]="formData.locks[19].locked === 2 ? true : false" i18n-aria-label aria-label="Default date tag height"/> px</span> <!-- WCAG 1.4.4 – width 96px→6rem -->
                        <span>Width <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.documentSetting.defaultDateWidth"
                              style="width: 6rem;" [disabled]="formData.locks[19].locked === 2 ? true : false" i18n-aria-label aria-label="Default date tag width"/> px</span> <!-- WCAG 1.4.4 – width 96px→6rem -->
                    </div>
                </div>
                <div *ngIf="parentGroupId === 0" class="list" >
                    <span class="tips2" i18n>Default Font Size for Tags</span>
                    <div *ngIf="formData.locks[53].locked === 0 || formData.locks[53].locked === 1" class="loc" (click)="handleLock(53)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[53].locked === 2" class="loc lock" (click)="handleOpenLock(53)"  [attr.aria-label]="'Locked by ' + formData.locks[53].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[53].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox" style="max-width: 25rem;"> <!-- WCAG 1.4.4 – max-width 400px→25rem -->
                        <mat-form-field class="material-select-height" >
                            <mat-select i18n-aria-label aria-label="Default font size" name="Default font size" [(ngModel)]="formData.documentSetting.defaultFontSize"
                                [disabled]="formData.locks[53].locked === 2 ? true : false">
                                <mat-option value="8pt" i18n>8pt</mat-option>
                                <mat-option value="10pt" i18n>10pt</mat-option>
                                <mat-option value="12pt" i18n>12pt</mat-option>
                                <mat-option value="14pt" i18n>14pt</mat-option>
                                <mat-option value="16pt" i18n>16pt</mat-option>
                                <mat-option value="18pt" i18n>18pt</mat-option>
                                <mat-option value="20pt" i18n>20pt</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div *ngIf="parentGroupId === 0" class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Force regulatory information on signature tags" appToggleEnterKey color="primary" [(ngModel)]="formData.documentSetting.forceRegulatory"
                     [disabled]="formData.locks[55].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Force regulatory information on signature tags</span>
                    <div *ngIf="formData.locks[55].locked === 0 || formData.locks[55].locked === 1" class="loc" (click)="handleLock(55)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[55].locked === 2" class="loc lock" (click)="handleOpenLock(55)"  [attr.aria-label]="'Locked by ' + formData.locks[55].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[55].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div aria-live="polite"> <div *ngIf="DefaultDateHeight" class="errorBox" i18n>The default Date tag height should be between 13 and 792 pixel high.</div></div>
                <div aria-live="polite"><div *ngIf="DefaultDateWidth && dateWidthMin === 53" class="errorBox" i18n>The default Date tag width should be between 53 and 612 pixel wide.</div></div>
                <div aria-live="polite"><div *ngIf="DefaultDateWidth && dateWidthMin === 107" class="errorBox" i18n>The default Date tag width should be between 107 to 612 pixels wide to accommodate for the date format 'MMMM DD, YYYY'.</div></div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderNotification"
            >
                <ng-template #modalHeaderNotification>
                    <h3 class="panelTitle" id="NotificationList" i18n>Notification<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Send reminder emails" appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.notifReminderEmail"
                     [disabled]="formData.locks[20].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send Reminder Emails</span>
                    <div *ngIf="formData.locks[20].locked === 0 || formData.locks[20].locked === 1" class="loc" (click)="handleLock(20)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[20].locked === 2" class="loc lock" (click)="handleOpenLock(20)"  [attr.aria-label]="'Locked by ' + formData.locks[20].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[20].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list" [class]="DefaultReminderEmailInterval ? 'errorList' : ''">
                    <span class="tips2" i18n>Send email reminders every</span>
                    <div *ngIf="formData.locks[21].locked === 0 || formData.locks[21].locked === 1" class="loc" (click)="handleLock(21)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[21].locked === 2" class="loc lock" (click)="handleOpenLock(21)"  [attr.aria-label]="'Locked by ' + formData.locks[21].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[21].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default" [(ngModel)]="formData.notificationsSetting.defaultReminderEmailInterval"
                         style="width:96px;" [disabled]="formData.locks[21].locked === 2 ? true : false" i18n-aria-label aria-label="Send reminder emails every"/>
                        <mat-form-field class="material-select-height" class="ml-2">
                            <mat-select i18n-aria-label aria-label="Email reminder interval unit"
                             [(ngModel)]="formData.notificationsSetting.defaultSendReminderPeriodUnit"
                                [disabled]="formData.locks[21].locked === 2 ? true : false">
                                <mat-option [value]="1" i18n>day(s)</mat-option>
                                <mat-option [value]="2" i18n>hour(s)</mat-option>
                                <mat-option [value]="3" i18n>minute(s)</mat-option>
                            </mat-select>
                        </mat-form-field>
                        
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="DefaultReminderEmailInterval" class="errorBox" i18n>Please enter a value between 1 and <span *ngIf="formData.notificationsSetting.defaultSendReminderPeriodUnit === 1">365</span> <span *ngIf="formData.notificationsSetting.defaultSendReminderPeriodUnit === 2">168</span> <span *ngIf="formData.notificationsSetting.defaultSendReminderPeriodUnit === 3">10080</span>.</div></div>  
                <div class="list" [class]="DefaultMaxReminderEmailCount || DefaultMaxReminderEmailCount ? 'errorList' : ''">
                    <span class="tips2" i18n>Maximum number of emails sent</span>
                    <div *ngIf="formData.locks[22].locked === 0 || formData.locks[22].locked === 1" class="loc" (click)="handleLock(22)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[22].locked === 2" class="loc lock" (click)="handleOpenLock(22)"  [attr.aria-label]="'Locked by ' + formData.locks[22].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[22].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default"  [(ngModel)]="formData.notificationsSetting.defaultMaxReminderEmailCount"
                         style="width: 6rem;" [disabled]="formData.locks[22].locked === 2 ? true : false" i18n-aria-label aria-label="Maximum number of emails sent"/> <!-- WCAG 1.4.4 – width 96px→6rem -->
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="DefaultMaxReminderEmailCount" class="errorBox" i18n>Please enter a value between 1 and 365.</div></div>
                <div class="list" [class]="!formData.notificationsSetting.sendDocRecipient ? 'warningList' : ''">
                    <mat-slide-toggle i18n-aria-label aria-label="Send each document recipient an invitation email when the documents are sent out for signing"
                     appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.sendDocRecipient"
                     [disabled]="formData.locks[23].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send each document recipient an invitation email when documents are sent out for signing</span>
                    <div *ngIf="formData.locks[23].locked === 0 || formData.locks[23].locked === 1" class="loc" (click)="handleLock(23)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[23].locked === 2" class="loc lock" (click)="handleOpenLock(23)"  [attr.aria-label]="'Locked by ' + formData.locks[23].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[23].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div *ngIf="!formData.notificationsSetting.sendDocRecipient" class="warningBox" i18n><b>BE CAREFUL.</b> Turning this setting off will disable emails from being sent for documents for all of your account's/team's users.</div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Send viewed notification to document sender" appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.notifView"
                     [disabled]="formData.locks[24].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send "Viewed" notification to document sender</span>
                    <div *ngIf="formData.locks[24].locked === 0 || formData.locks[24].locked === 1" class="loc" (click)="handleLock(24)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[24].locked === 2" class="loc lock" (click)="handleOpenLock(24)"  [attr.aria-label]="'Locked by ' + formData.locks[24].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[24].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Send Signed notification to document sender" appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.notifSign"
                     [disabled]="formData.locks[25].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send "Signed" notification to document sender</span>
                    <div *ngIf="formData.locks[25].locked === 0 || formData.locks[25].locked === 1" class="loc" (click)="handleLock(25)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[25].locked === 2" class="loc lock" (click)="handleOpenLock(25)"  [attr.aria-label]="'Locked by ' + formData.locks[25].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[25].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <span class="tips2" i18n>Notification email to document sender when document is finalized</span>
                    <div *ngIf="formData.locks[26].locked === 0 || formData.locks[26].locked === 1" class="loc" (click)="handleLock(26)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[26].locked === 2" class="loc lock" (click)="handleOpenLock(26)"  [attr.aria-label]="'Locked by ' + formData.locks[26].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[26].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <mat-radio-group [(ngModel)]="formData.notificationsSetting.finalNotifSender"
                         i18n-aria-label aria-label="Notification email to document sender when document is finalized"
                        [disabled]="formData.locks[26].locked === 2 ? true : false" class="team-settings-radio-group">
                            <mat-radio-button class="w-full team-settings-radio" [value]="1" i18n>Attach a PDF of the final document</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="2" i18n>Provide a link to final document</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="3" i18n>Send an email only</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="4" i18n>Do not send any email</mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                <div class="list">
                    <span class="tips2" i18n>Notification email to document recipients when document is finalized</span>
                    <div *ngIf="formData.locks[27].locked === 0 || formData.locks[27].locked === 1" class="loc" (click)="handleLock(27)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[27].locked === 2" class="loc lock" (click)="handleOpenLock(27)"  [attr.aria-label]="'Locked by ' + formData.locks[27].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[27].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <mat-radio-group [(ngModel)]="formData.notificationsSetting.finalNotifRecipient"
                         i18n-aria-label aria-label="Notification email to document recipients when document is finalized"
                        [disabled]="formData.locks[27].locked === 2 ? true : false" class="team-settings-radio-group">
                            <mat-radio-button class="w-full team-settings-radio" [value]="1" i18n>Attach a PDF of the final document</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="2" i18n>Provide a link to final document</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="3" i18n>Send an email only</mat-radio-button>
                            <mat-radio-button class="w-full team-settings-radio" [value]="4" i18n>Do not send any email</mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Send an expiration email to the document sender right after document expires"
                     appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.notifExpiredEmail"
                     [disabled]="formData.locks[28].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send an expiration email to the document sender right after a document expires</span>
                    <div *ngIf="formData.locks[28].locked === 0 || formData.locks[28].locked === 1" class="loc" (click)="handleLock(28)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[28].locked === 2" class="loc lock" (click)="handleOpenLock(28)"  [attr.aria-label]="'Locked by ' + formData.locks[28].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[28].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <span class="tips2" i18n>Warning of document expiration before it expires</span>
                    <div *ngIf="formData.locks[29].locked === 0 || formData.locks[29].locked === 1" class="loc" (click)="handleLock(29)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[29].locked === 2" class="loc lock" (click)="handleOpenLock(29)"> [attr.aria-label]="'Locked by ' + formData.locks[29].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[29].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default" [(ngModel)]="formData.notificationsSetting.defaultLastReminderIntervalBeforeExpire"
                         style="width: 6rem;" [disabled]="formData.locks[29].locked === 2 ? true : false" i18n-aria-label aria-label="Warning of document expiration before it expires"/> <!-- WCAG 1.4.4 – width 96px→6rem -->
                        <mat-form-field class="material-select-height" class="ml-2">
                            <mat-select i18n-aria-label aria-label="Warning of document expiration before it expires unit"
                             [(ngModel)]="formData.notificationsSetting.defaultLastReminderIntervalBeforeExpireUnit"
                                [disabled]="formData.locks[29].locked === 2 ? true : false">
                                <mat-option [value]="1" i18n>day(s)</mat-option>
                                <mat-option [value]="2" i18n>hour(s)</mat-option>
                                <mat-option [value]="3" i18n>minute(s)</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="list" [class]="FinalCopyEmail ? 'errorList' : ''">
                    <mat-slide-toggle i18n-aria-label aria-label="Send a copy of all email notifications to the following emial address:"
                     appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.finalSendCopyEmail"
                     [disabled]="formData.locks[30].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send a copy of all email notifications to the following email address:</span>
                    <div *ngIf="formData.locks[30].locked === 0 || formData.locks[30].locked === 1" class="loc" (click)="handleLock(30)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[30].locked === 2" class="loc lock" (click)="handleOpenLock(30)"  [attr.aria-label]="'Locked by ' + formData.locks[30].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[30].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default" [(ngModel)]="formData.notificationsSetting.finalCopyEmail" 
                        [disabled]="formData.locks[30].locked === 2 ? true : false" i18n-aria-label aria-label="Send a copy of all email notifications to the following email address:"/>
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="FinalCopyEmail" class="errorBox" i18n>This field is required.</div></div>
                <div class="list" [class]="SendInvoicesOverageNotifinMultipleText ? 'errorList' : ''" *ngIf="parentGroupId === 0">
                    <mat-slide-toggle i18n-aria-label aria-label='Send invoices and overage notifications to multiple email addresses. (Maximum 20, separated by comma)'
                     appToggleEnterKey color="primary" [(ngModel)]="formData.notificationsSetting.sendInvoicesOverageNotifinMultiple"
                     [disabled]="formData.locks[31].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Send invoices and overage notifications to multiple email addresses. (Maximum 20, seperated by ",")</span>
                    <div *ngIf="formData.locks[31].locked === 0 || formData.locks[31].locked === 1" class="loc" (click)="handleLock(31)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[31].locked === 2" class="loc lock" (click)="handleOpenLock(31)"> [attr.aria-label]="'Locked by ' + formData.locks[31].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[31].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox">
                        <input nz-input placeholder="" nzSize="default" i18n-aria-label aria-label="Send invoices and overage notifications to multiple email addresses. (Maximum 20, seperated by comma)"
                        [(ngModel)]="formData.notificationsSetting.sendInvoicesOverageNotifinMultipleText" [disabled]="formData.locks[31].locked === 2 ? true : false" />
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="SendInvoicesOverageNotifinMultipleText" class="errorBox" i18n>This field is required.</div></div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderSharing"
            >
                <ng-template #modalHeaderSharing>
                    <h3 class="panelTitle" id="SharingList" i18n>Sharing<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Automatically share contacts with team" appToggleEnterKey color="primary" [(ngModel)]="formData.sharingSetting.shareAddressBookToTeam"
                     [disabled]="formData.locks[32].locked === 2 ? true : false || !shareContactsEnabled"></mat-slide-toggle>
                    <span class="tips" i18n>Automatically share contacts with team</span>
                    <div *ngIf="formData.locks[32].locked === 0 || formData.locks[32].locked === 1" class="loc" (click)="handleLock(32)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[32].locked === 2" class="loc lock" (click)="handleOpenLock(32)"> [attr.aria-label]="'Locked by ' + formData.locks[32].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[32].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Automatically share documents with team" appToggleEnterKey color="primary" [(ngModel)]="formData.sharingSetting.shareAutomaticallyDocToTeam"
                     [disabled]="formData.locks[33].locked === 2 ? true : false || !shareDocTemplateEnabled"></mat-slide-toggle>
                    <span class="tips" i18n>Automatically share documents with team</span>
                    <div *ngIf="formData.locks[33].locked === 0 || formData.locks[33].locked === 1" class="loc" (click)="handleLock(33)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[33].locked === 2" class="loc lock" (click)="handleOpenLock(33)"  [attr.aria-label]="'Locked by ' + formData.locks[33].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[33].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Automatically share templates with team" appToggleEnterKey color="primary" [(ngModel)]="formData.sharingSetting.shareAutomaticallyTemplateToTeam"
                     [disabled]="formData.locks[34].locked === 2 ? true : false || !shareDocTemplateEnabled"></mat-slide-toggle>
                    <span class="tips" i18n>Automatically share templates with team</span>
                    <div *ngIf="formData.locks[34].locked === 0 || formData.locks[34].locked === 1" class="loc" (click)="handleLock(34)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[34].locked === 2" class="loc lock" (click)="handleOpenLock(34)"  [attr.aria-label]="'Locked by ' + formData.locks[34].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[34].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderSignerOptions"
            >
                <ng-template #modalHeaderSignerOptions>
                    <h3 class="panelTitle" id="SignerOptionsList" i18n>Signer Options<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list"  *ngIf="parentGroupId === 0">
                    <span class="tips2" i18n>Signing flow behavior when a recipient rejects</span>
                    <div class="textBox" style="max-width: 25rem;"> <!-- WCAG 1.4.4 – max-width 400px→25rem -->
                        <mat-form-field class="material-select-height">
                            <mat-select i18n-aria-label aria-label="Signing flow behavior when a recipient rejects" name="Signing flow behavior when a recipient rejects" 
                            [(ngModel)]="formData.signerOption.continueAfterSignerRejection">
                                <mat-option value=0 i18n>Continue with signing flow</mat-option>
                                <mat-option value=1 i18n>Cancel the document and send an email notifying the signers</mat-option>
                                <mat-option value=2 i18n>Cancel the document and do not send an email to the signers</mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Reject Button" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideSigningButtonDrawingSignature"
                     [disabled]="formData.locks[35].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Reject Button</span>
                    <div *ngIf="formData.locks[35].locked === 0 || formData.locks[35].locked === 1" class="loc" (click)="handleLock(35)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[35].locked === 2" class="loc lock" (click)="handleOpenLock(35)"  [attr.aria-label]="'Locked by ' + formData.locks[35].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[35].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Change Signer Button" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideButtonChangeSigner"
                     [disabled]="formData.locks[36].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Change Signer Button</span>
                    <div *ngIf="formData.locks[36].locked === 0 || formData.locks[36].locked === 1" class="loc" (click)="handleLock(36)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[36].locked === 2" class="loc lock" (click)="handleOpenLock(36)"> [attr.aria-label]="'Locked by ' + formData.locks[36].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[36].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Save Button" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideButtonSave"
                     [disabled]="formData.locks[37].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Save Button</span>
                    <div *ngIf="formData.locks[37].locked === 0 || formData.locks[37].locked === 1" class="loc" (click)="handleLock(37)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[37].locked === 2" class="loc lock" (click)="handleOpenLock(37)"  [attr.aria-label]="'Locked by ' + formData.locks[37].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[37].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Download Button" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideButtonDownload"
                     [disabled]="formData.locks[38].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Download Button</span>
                    <div *ngIf="formData.locks[38].locked === 0 || formData.locks[38].locked === 1" class="loc" (click)="handleLock(38)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[38].locked === 2" class="loc lock" (click)="handleOpenLock(38)"  [attr.aria-label]="'Locked by ' + formData.locks[38].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[38].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide View Document Button" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideViewDocumentButton"
                     [disabled]="formData.locks[39].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide View Document Button</span>
                    <div *ngIf="formData.locks[39].locked === 0 || formData.locks[39].locked === 1" class="loc" (click)="handleLock(39)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[39].locked === 2" class="loc lock" (click)="handleOpenLock(39)"  [attr.aria-label]="'Locked by ' + formData.locks[39].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[39].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <!-- <div class="list">
                    <nz-switch [(ngModel)]="formData.signerOption.hideButtonStatus" [nzDisabled]="formData.locks[40].locked === 2 ? true : false"></nz-switch>
                    <span class="tips" i18n>Hide Status Button</span>
                    <div *ngIf="formData.locks[40].locked === 0 || formData.locks[40].locked === 1" class="loc" (click)="handleLock(40)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[40].locked === 2" class="loc lock" (click)="handleOpenLock(40)"  [attr.aria-label]="'Locked by ' + formData.locks[40].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[40].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div> -->
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Thumbnail" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideButtonThumbnail"
                     [disabled]="formData.locks[41].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Thumbnail </span>
                    <div *ngIf="formData.locks[41].locked === 0 || formData.locks[41].locked === 1" class="loc" (click)="handleLock(41)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[41].locked === 2" class="loc lock" (click)="handleOpenLock(41)"  [attr.aria-label]="'Locked by ' + formData.locks[41].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[41].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <!-- <div class="list">
                    <nz-switch [(ngModel)]="formData.signerOption.hideButtonHelp" [nzDisabled]="formData.locks[42].locked === 2 ? true : false"></nz-switch>
                    <span class="tips" i18n>Hide Help Button</span>
                    <div *ngIf="formData.locks[42].locked === 0 || formData.locks[42].locked === 1" class="loc" (click)="handleLock(42)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[42].locked === 2" class="loc lock" (click)="handleOpenLock(42)"  [attr.aria-label]="'Locked by ' + formData.locks[42].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[42].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div> -->
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Draw Signature Option" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideSigningButtonUploadSignature"
                     [disabled]="formData.locks[43].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Draw Signature Option</span>
                    <div *ngIf="formData.locks[43].locked === 0 || formData.locks[43].locked === 1" class="loc" (click)="handleLock(43)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[43].locked === 2" class="loc lock" (click)="handleOpenLock(43)"  [attr.aria-label]="'Locked by ' + formData.locks[43].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[43].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Type Signature Option" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideSigningButtonTypeSignature"
                     [disabled]="formData.locks[44].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Type Signature Option</span>
                    <div *ngIf="formData.locks[44].locked === 0 || formData.locks[44].locked === 1" class="loc" (click)="handleLock(44)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[44].locked === 2" class="loc lock" (click)="handleOpenLock(44)"  [attr.aria-label]="'Locked by ' + formData.locks[44].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[44].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Hide Upload Signature Option" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.hideButtonReject"
                     [disabled]="formData.locks[45].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Hide Upload Signature Option</span>
                    <div *ngIf="formData.locks[45].locked === 0 || formData.locks[45].locked === 1" class="loc" (click)="handleLock(45)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[45].locked === 2" class="loc lock" (click)="handleOpenLock(45)"  [attr.aria-label]="'Locked by ' + formData.locks[45].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[45].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list" *ngIf="stampsEnabled">
                    <mat-slide-toggle i18n-aria-label aria-label="Allow recipient to add stamp tags to the document" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.allowStamps"
                     [disabled]="formData.locks[56].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Allow recipient to add stamp tags to the document</span>
                    <div *ngIf="formData.locks[56].locked === 0 || formData.locks[56].locked === 1" class="loc" (click)="handleLock(56)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[56].locked === 2" class="loc lock" (click)="handleOpenLock(56)"  [attr.aria-label]="'Locked by ' + formData.locks[56].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[56].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list" *ngIf="stampsEnabled">
                    <mat-slide-toggle i18n-aria-label aria-label="Allow recipient to upload their own stamp" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.allowStampUpload"
                     [disabled]="formData.locks[57].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Allow recipient to upload their own stamp</span>
                    <div *ngIf="formData.locks[57].locked === 0 || formData.locks[57].locked === 1" class="loc" (click)="handleLock(57)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[57].locked === 2" class="loc lock" (click)="handleOpenLock(57)"  [attr.aria-label]="'Locked by ' + formData.locks[57].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[57].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <span nz-icon nzType="question-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                    [nzTooltipTitle]="stampUploadToolTip" [nzTooltipOverlayStyle]="{maxWidth:'500px'}" style="font-size: 1.375rem;" class="ml-2" i18n-aria-label aria-label="More information"></span>
                    <ng-template #stampUploadToolTip>
                        <div class="mb-2" i18n>
                            Recipient uploaded stamps are only used for their signing session and are not saved into your account
                        </div>
                    </ng-template>
                </div>
                <div class="list" *ngIf="enableDelegate" >
                    <mat-slide-toggle i18n-aria-label aria-label="Allow users to delegate signing authority" appToggleEnterKey color="primary" [(ngModel)]="formData.signerOption.allowDelegateSign"
                     [disabled]="formData.locks[51].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Allow users to delegate signing authority</span>
                    <div *ngIf="formData.locks[51].locked === 0 || formData.locks[51].locked === 1" class="loc" (click)="handleLock(51)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[51].locked === 2" class="loc lock" (click)="handleOpenLock(51)"  [attr.aria-label]="'Locked by ' + formData.locks[51].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[51].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
            </nz-collapse-panel>
        </nz-collapse>
        <nz-collapse *ngIf="retentionEnabled && parentGroupId === 0" nzGhost>
            <nz-collapse-panel
              [nzActive]="true"
              [nzShowArrow]="false"
              [nzHeader]="modalHeaderRetention"
            >
                <ng-template #modalHeaderRetention>
                    <h3 class="panelTitle" id="RetentionList" i18n>Retention and Backup<span nz-icon nzType="down" nzTheme="outline" aria-hidden="true"></span></h3>
                </ng-template>
                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Enable Document Retention" appToggleEnterKey color="primary" [(ngModel)]="formData.retentionBackup.retention"
                     [disabled]="formData.locks[46].locked === 2 ? true : false"></mat-slide-toggle>
                    <span class="tips" i18n>Enable Document Retention</span>
                    <div *ngIf="formData.locks[46].locked === 0 || formData.locks[46].locked === 1" class="loc" (click)="handleLock(46)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[46].locked === 2" class="loc lock" (click)="handleOpenLock(46)"  [attr.aria-label]="'Locked by ' + formData.locks[46].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[46].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div class="list" [class]="RetentionInterval || retentionMaxError  ? 'errorList' : ''">
                    <span class="tips2" i18n>Documents will be permanently deleted after</span>
                    <div *ngIf="formData.locks[47].locked === 0 || formData.locks[47].locked === 1" class="loc" (click)="handleLock(47)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[47].locked === 2" class="loc lock" (click)="handleOpenLock(47)"  [attr.aria-label]="'Locked by ' + formData.locks[47].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[47].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                    <div class="textBox" i18n>
                        <input nz-input (change)="retentionDaysWarning()" placeholder="" nzSize="default" 
                        [(ngModel)]="formData.retentionBackup.retentionInterval" style="width: 6rem;" 
                        [disabled]="formData.locks[47].locked === 2 ? true : false || !formData.retentionBackup.retention" i18n-aria-label aria-label="Documents will be permanently deleted after"/> Days
                    </div>
                </div>
                <div aria-live="polite"><div *ngIf="RetentionInterval" class="errorBox" i18n>Please enter a value between 1 and 1000.</div></div>
                <div aria-live="polite"><div *ngIf="retentionMaxError" class="errorBox" i18n>Your plan allows a maximum retention period of {{maxRetentionDays}} days </div></div>

                <!-- <div class="list" [class]="RetentionSaveBeforePurge ? 'errorList' : ''">
                    <nz-switch [(ngModel)]="formData.retentionBackup.retentionSaveBeforePurge" (click)="cloudRetentionWarning()" [nzDisabled]="formData.locks[49].locked === 2 ? true : false"></nz-switch>
                    <span class="tips" i18n>Save finalized documents to the cloud before deleting</span>
                    <span *ngIf="!SyncEnabled" nz-icon nzType="info-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                    [nzTooltipTitle]="retentionTooltip" [nzTooltipOverlayStyle]="{maxWidth:'250px'}" style="font-size: 1.375rem;" class="ml-2"  ></span>

                    <div *ngIf="formData.locks[49].locked === 0 || formData.locks[49].locked === 1" class="loc" (click)="handleLock(49)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[49].locked === 2" class="loc lock" (click)="handleOpenLock(49)"  [attr.aria-label]="'Locked by ' + formData.locks[49].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[49].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <div *ngIf="RetentionSaveBeforePurge" class="errorBox" i18n>Saving documents to the cloud cannot be enabled without an integration. Please go to the Integration page to set one up before trying again.</div> -->
                
                <ng-template #retentionTooltip>
                    <div class="mb-2" i18n>
                        "Storage integration" must be set up. Go to <a href="/UI/userSettings.html">Settings</a>
                    </div>

                  </ng-template>

                <div class="list">
                    <mat-slide-toggle i18n-aria-label aria-label="Prevent documents from being deleted by users until deletion date" appToggleEnterKey color="primary" [(ngModel)]="preventDocDeletion"
                     [disabled]="formData.locks[48].locked === 2 ? true : formData.retentionBackup.retention ? false : true"></mat-slide-toggle>
                    <span class="tips" i18n>Prevent documents from being deleted by users until deletion date</span>
                    <div *ngIf="formData.locks[48].locked === 0 || formData.locks[48].locked === 1" class="loc" (click)="handleLock(48)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[48].locked === 2" class="loc lock" (click)="handleOpenLock(48)"  [attr.aria-label]="'Locked by ' + formData.locks[48].lockedUserName + ' – unlock setting'" i18n>Locked by {{ formData.locks[48].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div>
                <!-- <div class="list">
                    <nz-switch [(ngModel)]="formData.retentionBackup.retention" [nzDisabled]="formData.locks[50].locked === 2 ? true : false"></nz-switch>
                    <span class="tips">Enable user to set document retention</span>
                    <div *ngIf="formData.locks[50].locked === 0 || formData.locks[50].locked === 1" class="loc" (click)="handleLock(50)" i18n-aria-label aria-label="Lock setting"><i class="fa-solid fa-lock-open" aria-hidden="true"></i></div>
                    <div *ngIf="formData.locks[50].locked === 2" class="loc lock" (click)="handleOpenLock(50)"> [attr.aria-label]="'Locked by ' + formData.locks[50].lockedUserName + ' – unlock setting'"Locked by {{ formData.locks[50].lockedUserName }}<i class="fa-sharp fa-solid fa-lock" aria-hidden="true" style="margin-left: 8px;"></i></div>
                </div> -->
            </nz-collapse-panel>
        </nz-collapse>
    </div>
    </ng-template>

</nz-modal>
<ng-template #globalModalTitle>
    <i i18n>
        The settings you are about to save will be applied to all of your organization's users. Please make sure all settings are correct and communicate any major changes to your organization.
        <div *ngIf="!formData.notificationsSetting.sendDocRecipient">
        You will be disabling email notifications for documents for all your users. To change this, go to
        <a (click)="modalService.close();goToScroll(1739)">Notifications settings.</a>
        </div>
    </i>
</ng-template>
<ng-template #settingsModalTitle>
    <i i18n>
        The settings you are about to save will be applied to all of the team's users. Please make sure all settings are correct and communicate any major changes to the team.
        <div *ngIf="!formData.notificationsSetting.sendDocRecipient">
        You will be disabling email notifications for documents for all your users. To change this, go to
        <a (click)="modalService.close();goToScroll(1739)">Notifications settings.</a>
        </div>
    </i>
</ng-template>
