
<!-- File updated for WCAG 1.4.4 – converted fixed pixel font-sizes, widths and positional offsets to relative rem units so header controls and sidebar scale with text resizing up to 200 %. -->
<!-- WCAG 2.1 – added role="button", tabindex and keydown handlers to links acting as buttons to ensure keyboard accessibility (SC 2.1.1 / 2.4.7) -->
<!-- CHANGE (2025-07-25): WCAG 2.5.3 “Label in Name” – aligned aria-label values with visible text for
     “+ New” controls, user menu button (includes user initial) and “New Template” modal to ensure the
     accessible names contain the exact visible labels. -->
<!-- CHANGE (2025-07-25): WCAG 4.1.2 – added explicit keyboard handlers (Enter & Space) and tabindex to custom
     button-like <a> elements and submenu toggles so they expose correct role and are operable via keyboard. -->
<!-- WCAG 2.4.1 – Added a skip link as the very first focusable element so keyboard users can bypass
     repeated navigation and jump directly to the main content area. The link is visually hidden until
     it receives focus via keyboard. (See accompanying styles in the *.less file.) -->
<a href="#main" class="skip-link" (keydown.space)="handleSpaceKeyPress($event)" i18n>Skip to main content</a>

<!-- WCAG 2.1 – Success Criterion 2.4.5 (Multiple Ways)
     Provide a second navigation mechanism in addition to the sidebar so that
     users can locate pages via an alternative method (for example when the
     sidebar is collapsed or hidden on mobile).  The following “Quick links”
     list is visually hidden until it receives keyboard focus, similar to the
     existing skip-link pattern.  This keeps the visual design unchanged while
     exposing the extra navigation path to assistive technologies and
     keyboard-only users.  // Assumption: these core routes are always
     available in the application.
-->
<nav aria-label="Quick links" class="visually-hidden quick-links" [class.hidden-mobile]="mediaService.isMobileOrTablet()">
    <ul>
        <!-- User Section Quick Links -->
        <li *ngIf="!isAdminConsole()"><a routerLink="/dashboard" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-dashboard"
               id="quick-dashboard"
               i18n>Dashboard</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/documents" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-documents"
               id="quick-documents"
               i18n>Documents</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/templates" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-templates"
               id="quick-templates"
               i18n>Templates</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/contacts" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-contacts"
               id="quick-contacts"
               i18n>Contacts</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/inbox" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-inbox"
               id="quick-inbox"
               i18n>Inbox</a></li>
        <li *ngIf="!isAdminConsole()"><a role="button" tabindex="0"
               appFocusTracker
               elementId="quick-reports"
               id="quick-reports"
               i18n>Reports</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/reports/security" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-security-report"
               id="quick-security-report"
               i18n>Security Report</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/reports/usage" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-usage-report"
               id="quick-usage-report"
               i18n>Usage Report</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/reports/cancel" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-cancel-report"
               id="quick-cancel-report"
               i18n>Cancellation Report</a></li>
        <li *ngIf="!isAdminConsole()"><a routerLink="/admin-console" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin"
               id="quick-admin"
               i18n>Admin</a></li>
        
        <!-- Admin Section Quick Links -->
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/dashboard" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-dashboard"
               id="quick-admin-dashboard"
               i18n>Admin Dashboard</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/teams" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-teams"
               id="quick-admin-teams"
               i18n>Users & Teams</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/bulk-export" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-bulk"
               id="quick-admin-bulk"
               i18n>Bulk Export</a></li>

        <li *ngIf="isAdminConsole()"><a role="button" tabindex="0"
               (click)="showBrandingModal()"
               (keydown.enter)="showBrandingModal()"
               (keydown.space)="$event.preventDefault(); showBrandingModal()"
               appFocusTracker
               elementId="quick-admin-branding"
               id="quick-admin-branding"
               i18n>Branding</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/plan" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-plan-manage"
               id="quick-admin-plan-manage"
               i18n>Plan - Manage Plan</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/payment" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-plan-payment"
               id="quick-admin-plan-payment"
               i18n>Plan - Payment Method</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/admin-console/invoice/list" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-admin-plan-invoice"
               id="quick-admin-plan-invoice"
               i18n>Plan - Invoice</a></li>
        <li *ngIf="isAdminConsole()"><a role="button" tabindex="0"
               appFocusTracker
               elementId="quick-admin-settings"
               id="quick-admin-settings"
               i18n>Settings</a></li>
        <li *ngIf="isAdminConsole()"><a role="button" tabindex="0"
               (click)="showTeamSettingModal()"
               (keydown.enter)="showTeamSettingModal()"
               (keydown.space)="$event.preventDefault(); showTeamSettingModal()"
               appFocusTracker
               elementId="quick-admin-settings-global"
               id="quick-admin-settings-global"
               i18n>Settings - Global Settings</a></li>
        <li *ngIf="isAdminConsole() && showSeals && (roleID == roles.BILLING_ADMIN || roleID == roles.SUPER_ADMIN)"><a routerLink="/admin-console/seals" 
               (keydown.space)="handleSpaceKeyPress($event)"
               (click)="expandSettingsSubmenu()"
               appFocusTracker
               elementId="quick-admin-settings-stamps"
               id="quick-admin-settings-stamps"
               i18n>Settings - Stamps</a></li>
        <li *ngIf="isAdminConsole()"><a routerLink="/dashboard" 
               (keydown.space)="handleSpaceKeyPress($event)"
               appFocusTracker
               elementId="quick-user-dashboard"
               id="quick-user-dashboard"
               i18n>User Dashboard</a></li>
    </ul>
</nav>

<nz-layout class="app-layout">
    <div *ngIf="showMaintenanceMsg" class="maintenance-msg-header">
        <div style="font-weight:bold; color:black">{{maintenanceMsgTitle}}</div>
        <!-- <div>{{maintenanceBody}}</div> -->
        <div [innerHTML]="maintenanceBody"></div>
    </div>
    <header>
        <nz-header [ngStyle]="{'top': showMaintenanceMsg ? '60px' : '0'}">
            <div class="app-header" [class.header-sandbox]="sandBoxView">
                <div style="display: flex;flex-direction: row; align-items: center;">
                    <!-- WCAG 2.4.7 – make the mobile sidebar toggle keyboard-operable so a focus indicator can appear. -->
                    <!-- Validation fix: removed comment from inside attribute list which caused Angular template parse
                         error NG5002 (unterminated tag). The comment was relocated above the element tag. -->
                    <span *ngIf="mediaService.isMobile()" class="menu-sidebar-mobile" nz-icon nzType="menu"
                    nzTheme="outline"
                    role="button" tabindex="0"
                    (click)="showSideBarMobile = !showSideBarMobile; isCollapsed = !showSideBarMobile"
                    (keydown.enter)="showSideBarMobile = !showSideBarMobile; isCollapsed = !showSideBarMobile"
                    (keydown.space)="$event.preventDefault(); showSideBarMobile = !showSideBarMobile; isCollapsed = !showSideBarMobile"
                    aria-label="Toggle navigation menu" i18n-aria-label></span>
                    <span *ngIf="mediaService.isMobile()" class="vertical-line2" >&nbsp;</span>
                    <a id="homepage-logo" 
                       routerLink="/dashboard" 
                       (click)="navigateToHomepage()"
                       appFocusTracker
                       elementId="homepage-logo"
                       tabindex="0"
                       role="button"
                       i18n-aria-label 
                       aria-label="Signority logo - Go to homepage">
                        <img *ngIf="!sandBoxView" i18n-alt alt="Signority logo" src="assets/images/BasicSiteCompanyLogoRegular.png?version=3" class="logo">
                        <!-- Accessibility: Provide empty alt text for decorative sandbox logo (WCAG 1.1.1 compliance) -->
                        <img *ngIf="sandBoxView" class="logo" alt="" aria-hidden="true">
                    </a>

                </div>
                <span class="vertical-line" *ngIf="!mediaService.isMobile()">
                    <ng-container *ngIf="pageName==='User Dashboard'" i18n>User Dashboard</ng-container>
                    <ng-container *ngIf="pageName === 'Admin Console'" i18n>Admin Console</ng-container>
                    <ng-container *ngIf="pageName==='teams'" i18n>Users & Teams</ng-container>
                    <ng-container *ngIf="pageName==='cancel-report'" i18n>User Dashboard</ng-container>
                    <ng-container *ngIf="pageName==='usage-report'" i18n>User Dashboard</ng-container>
                    <ng-container *ngIf="pageName==='security-report'" i18n>User Dashboard</ng-container>
                    <ng-container *ngIf="pageName==='bulk-export'" i18n>Bulk Export</ng-container>
                    <ng-container *ngIf="pageName==='change-plan'" i18n>Change Plan</ng-container>
                </span>
                <!-- WCAG 1.4.4 – switch absolute 25px size to relative so icons scale with user text zoom -->
                <span *ngIf="!mediaService.isMobile()" class="right" style="font-size: 1.5625rem"><!-- 25px → 1.5625rem -->
                    <!-- <span class="vertical-line-right mr-4"> -->
                    <!-- WCAG 2.4.4 – ensure the button label conveys purpose in isolation -->
                    <!-- Assumption: The visible label for this control is “+ New”. To comply with WCAG 2.5.3 (Label in Name)
                         the accessible name must contain the same visible text, including the plus symbol. -->
                    <button [matMenuTriggerFor]="newDoc" class="ant-btn ant-btn-primary  float-right new_button"
                    #menuTrigger="matMenuTrigger"
                    (mouseenter)="mouseEnter(menuTrigger)"
                    (mouseleave)="mouseLeave(menuTrigger)"
                    aria-label="+ New" i18n-aria-label i18n>
                        + New
                    </button>
                    <mat-menu #newDoc="matMenu" [hasBackdrop]="false" [overlapTrigger]="false">
                        <div (mouseenter)="mouseEnter(menuTrigger)" (mouseleave)="mouseLeave(menuTrigger)">
                            <button mat-menu-item (click)="createDocument()" i18n>New Document</button>
                            <button mat-menu-item (click)="newTemplate()" i18n>New Template</button>
                        </div>
                    </mat-menu>
                    <span class="vertical-line" style="margin-left: 24px; padding-left: 0; padding-right: 12px;">&nbsp;</span>
                    <button i18n-aria-label aria-label="Help" (click)="goHelp()">
                        <i *ngIf="!sandBoxView" class="fa-regular fa-circle-question cursor-pointer mr-4" ></i>
                    </button>
                    <!-- <button i18n-aria-label aria-label="Help">
                        <i *ngIf="sandBoxView" class="fa-regular fa-circle-question cursor-pointer mr-4"
                        style="color: #fff" (click)="goHelp()"></i>
                    </button> -->
                    <button i18n-aria-label aria-label="Release notes" (click)="showBellDialog()">
                        <!-- WCAG 1.4.3: Changed icon colour to high-contrast blue -->
                        <i class="fa-regular fa-bell cursor-pointer mr-4" style="color: #DB7C00" ></i>
                    </button>

                    <!-- WCAG 2.4.6 – provide an explicit accessible name for the user account menu button -->
                    <!-- Assumption: Include user initial inside accessible name so the name contains the full
                         visible string when only the initial “F” (or other letter) is shown (WCAG 2.5.3). -->
                    <button [matMenuTriggerFor]="menu" i18n-aria-label
                            [attr.aria-label]="userFirstName.charAt(0) + ' user menu'">
                        <!-- WCAG 1.4.4 – convert fixed font-size & width to rem so avatar scales with text -->
                        <span class="fa-stack fa-1x mr-4 mb-2" style="font-size: 0.875rem; width: 1.75rem"><!-- 14px/28px → 0.875rem/1.75rem -->
                            <!-- WCAG 1.4.3: Updated circle fill to high-contrast blue -->
                            <i class="fa-solid  fa-circle cursor-pointer fa-stack-2x" style="color: #055485"></i>
                            <i class="fa-regular fa-stack-1x" style="color: #fff;">{{userFirstName}}</i> <!-- font-size inherited from parent after conversion -->
                        </span>
                    </button>
                    <mat-menu #menu="matMenu">
                        <button id="userSettings" mat-menu-item nzIcon="user" style="color: #2D323D;" (click)="settings()">
                            <i class="fa-regular fa-user mr-2 -ml-1.5 fa-solid fa-gauge-high"></i>
                            <ng-container i18n>Profile Settings</ng-container>
                        </button>
                        <button id="userLogout" mat-menu-item nzIcon="poweroff" style="color: #2D323D;" (click)="logout()">
                            <i class="fa-solid fa-power-off mr-2 -ml-1.5 fa-solid fa-gauge-high"></i>
                            <ng-container i18n>Logout</ng-container>
                        </button>
                    </mat-menu>

                </span>
                <!-- WCAG 1.4.4 – relative icon sizing for mobile header -->
                <span *ngIf="mediaService.isMobile()" class="right" style="font-size: 1.5625rem"><!-- 25px → 1.5625rem -->
                    <!-- WCAG 1.3.3 (Sensory Characteristics): Added accessible label so users are not required to rely on the
                         plus symbol alone to understand the purpose of the control. -->
                    <!-- Assumption: Keep visible label “+” only while collapsed; nevertheless include the plus sign in the
                         accessible name for the same reason as above (WCAG 2.5.3). -->
                    <a nz-dropdown [nzDropdownMenu]="newDocMobile" i18n-aria-label aria-label="+ New" role="button" tabindex="0">
                        <!-- WCAG 1.4.4 – convert fixed 18px to rem -->
                        <span class="fa-stack fa-1x mr-4 mb-2" style="font-size: 1.125rem;">
                        <!-- WCAG 1.4.3: Updated circle fill to high-contrast blue -->
                        <i class="fa-solid  fa-circle cursor-pointer fa-stack-2x" style="color: #055485"></i>
                        <i class="fa-regular fa-stack-1x" style="color: #fff; font-weight: bold;">+</i>
                    </span>
                </a>
                </span>
                <nz-dropdown-menu #newDocMobile="nzDropdownMenu">
                    <ul nz-menu>
                        <!-- WCAG 1.4.4 – convert fixed height 50px to relative min-height so menu items scale with text resize -->
                        <li nz-menu-item style="color: #2D323D; min-height:3.125rem;" (click)="createDocument()" i18n>New Document</li>
                        <li nz-menu-item style="color: #2D323D; min-height:3.125rem;" (click)="newTemplate()" i18n>New Template</li>
                    </ul>
                </nz-dropdown-menu>
            </div>
        </nz-header>
    </header>


    <!-- Validation fix: moved WCAG comment outside of <nz-sider> start tag to prevent Angular template parse error (NG5002) caused by comment inside attribute list. -->
    <!-- WCAG 1.4.4 – convert fixed sidebar width 256px → 16rem so it scales with root font-size -->
    <nz-sider (clickOutside)="showSideBarMobile = false; mediaService.isMobile()? isCollapsed = true:'' "
              [excludeBeforeClick]="true"
              [exclude]="'nz-header,app-doc-sidebar *'"
              [hidden]="mediaService.isMobile() && !showSideBarMobile"
              [ngStyle]="{'margin-top': showMaintenanceMsg ? '124px' : '64px'}"
              class="menu-sidebar"
              nzCollapsible
              nzWidth="16rem"
              [(nzCollapsed)]="isCollapsed"
              [nzTrigger]="null">
        <nav>
            <!-- WCAG 1.3.3 (Sensory Characteristics):
                 Ensure the sidebar toggle control does not rely on arrow orientation
                 alone. Added role, keyboard support and an aria-label that is
                 programmatically updated using the same state variable so the
                 purpose is clear without referencing visual cues. -->
            <div class="sidebar-logo" *ngIf="!mediaService.isMobile()">
                <!-- Assumption: space key acts as toggle like a button; comment placed outside tag to avoid parser errors -->
                <i
                    role="button" tabindex="0"
                    (click)="isCollapsed = !isCollapsed"
                    (keydown.enter)="isCollapsed = !isCollapsed"
                    (keydown.space)="$event.preventDefault(); isCollapsed = !isCollapsed"
                    [attr.aria-expanded]="!isCollapsed"
                    aria-label="Toggle sidebar" i18n-aria-label
                    [ngClass]="isCollapsed
                        ? 'fa fa-chevron-right cursor-pointer'
                        : 'fa fa-chevron-left cursor-pointer float-right p-4'
                    "></i>
            </div>
            <ul nz-menu nzTheme="dark" nzMode="inline" [nzInlineCollapsed]="isCollapsed" >
                <ng-container *ngFor="let title of getNavConfig()" [ngSwitch]="title" class="sidebar-items" >

                    <li nzMatchRouter  nzSelected nz-menu-item class="sidebar-item" *ngSwitchCase="'Dashboard'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Dashboard"  
                           routerLink="./dashboard"
                           appFocusTracker
                           elementId="nav-dashboard"
                           id="nav-dashboard"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-gauge-high fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Dashboard </ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Documents'"   [ngClass]=" 'hover-selected'">
                        <!-- CHANGE (2025-07-25): WCAG 2.5.3 “Label in Name” – Make aria-label dynamic so the accessible
                             name always contains the exact visible label text which varies based on user role. -->
                        <a [attr.aria-label]="roleID == roles.DOC_ADMIN ? 'My Documents' : 'Documents'" 
                           routerLink="/documents"
                           appFocusTracker
                           elementId="nav-documents"
                           id="nav-documents"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-page fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed && roleID == roles.DOC_ADMIN" i18n>My Documents</ng-container>
                            <ng-container *ngIf="!isCollapsed && roleID != roles.DOC_ADMIN" i18n>Documents</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Templates'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Templates" 
                           routerLink="/templates"
                           appFocusTracker
                           elementId="nav-templates"
                           id="nav-templates"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-file-lines fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Templates</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Contacts'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Contacts" 
                           routerLink="/contacts"
                           appFocusTracker
                           elementId="nav-contacts"
                           id="nav-contacts"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-address-book fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Contacts</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Inbox'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Inbox"  
                           routerLink="inbox"
                           appFocusTracker
                           elementId="nav-inbox"
                           id="nav-inbox"
                           (keydown.space)="handleSpaceKeyPress($event)">

                            <i class="fa-solid fa-envelope fa-xl fa-fw -ml-1.5"></i>

                            <ng-container *ngIf="isCollapsed && (inbox_count | async) !== 0 && (inbox_count | async) !== null">
                                <nz-badge nzDot style="width: 0; position: relative; top: -6px; right: 20px"></nz-badge>
                            </ng-container>


                            <ng-container *ngIf="!isCollapsed" i18n>Inbox</ng-container>
                        </a>
                        <span *ngIf="!isCollapsed && (inbox_count | async) !== 0 && (inbox_count | async) !== null" class="badge">
                            <!-- <ng-container *ngIf="(inbox_count | async) as count; else loading">{{count}}</ng-container> -->
                            <!-- <ng-template #loading>...</ng-template> -->
                            {{((inbox_count | async) !== 0 && (inbox_count | async) !== null) ? (inbox_count | async) : '...'}}
                        </span>
                    </li>


                    <ng-container *ngSwitchCase="'Reports'">
                        <li>
                            <!-- Validation fix: moved inline comment outside of <a> tag to avoid NG5002 parse error -->
                            <!-- Removed duplicate tabindex attribute to avoid HTML validation/Angular template errors -->
                            <a tabindex="0" i18n-aria-label aria-label="Reports" [attr.aria-expanded]="report_submenu_opened"
                               [ngStyle]="{'position':'relative', 'top':'1rem', 'left': isCollapsed ? '1.5625rem' : '1.125rem'}"
                               class="hover-report" role="button"
                               (mousedown)="handleExpandableMouseDown()"
                               (click)="report_submenu_opened = !report_submenu_opened"
                               (keydown.enter)="report_submenu_opened = !report_submenu_opened"
                               (keydown.space)="$event.preventDefault(); report_submenu_opened = !report_submenu_opened"
                               (focus)="handleExpandableFocus()"
                               appFocusTracker
                               elementId="nav-reports"
                               id="nav-reports">
                               <!-- WCAG 1.4.4 – convert absolute px offsets to rem -->
                                <i class="fa-solid fa-chart-column fa-xl fa-fw " style="margin-right: 24px; margin-left: .35em;"></i>
                                <span *ngIf="!isCollapsed" i18n>Reports</span>
                            </a>
                        </li>
                        <li nz-submenu class="-mt-2" [(nzOpen)]="report_submenu_opened" #reportsSubmenu>
                            <ul>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Security Report" class="ml-2" routerLink="reports/security" 
                                   [attr.tabindex]="report_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Security Report</a>
                            </li>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Usage Report" class="ml-2" routerLink="reports/usage" 
                                   [attr.tabindex]="report_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Usage Report</a>
                            </li>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Cancellation Report" class="ml-2" routerLink="reports/cancel" 
                                   [attr.tabindex]="report_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Cancellation Report</a>
                            </li>
                            </ul>
                        </li>
                    </ng-container>

                    <ng-container *ngSwitchCase="'Plan2'">
                        <li>
                            <!-- Validation fix: moved WCAG comment outside <a> tag to avoid template parse issue -->
                            <!-- Removed duplicate tabindex attribute to avoid HTML validation/Angular template errors -->
                            <a tabindex="0" i18n-aria-label aria-label="Plan"
                               [ngStyle]="{'position':'relative', 'top':'1rem', 'left': isCollapsed ? '2rem' : '1.5rem'}"
                               class="hover-report" role="button"
                               (mousedown)="handleExpandableMouseDown()"
                               (click)="plan_submenu_opened = !plan_submenu_opened"
                               (keydown.enter)="plan_submenu_opened = !plan_submenu_opened"
                               (keydown.space)="$event.preventDefault(); plan_submenu_opened = !plan_submenu_opened"
                               (focus)="handleExpandableFocus()"
                               [attr.aria-expanded]="plan_submenu_opened">
                               <!-- WCAG 1.4.4 – px → rem -->
                                <i class="fa-solid fa-credit-card fa-xl fa-fw -ml-1.5" style="margin-right: 24px;"></i>
                                <span *ngIf="!isCollapsed" i18n>Plan</span>
                            </a>
                        </li>
                        <li nz-submenu class="-mt-2" [(nzOpen)]="plan_submenu_opened" #planSubmenu>
                            <ul>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Manage Plan" class="ml-2" [routerLink]="isAdminConsole() ? '/admin-console/plan' : '/plan'" 
                                   [attr.tabindex]="plan_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Manage Plan</a>
                            </li>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Payment Method" class="ml-2" [routerLink]="isAdminConsole() ? '/admin-console/payment' : '/payment'" 
                                   [attr.tabindex]="plan_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Payment method</a>
                            </li>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Invoice" class="ml-2" [routerLink]="isAdminConsole() ? '/admin-console/invoice/list' : '/invoice/list'" 
                                   [attr.tabindex]="plan_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Invoice</a>
                            </li>
                            </ul>
                        </li>
                    </ng-container>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Plan'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Plan" 
                           [routerLink]="isAdminConsole() ? '/admin-console/plan' : '/plan'"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-credit-card fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Plan</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Branding'"   [ngClass]="'hover-selected'">
                        <a tabindex="0" i18n-aria-label aria-label="Branding" role="button" aria-haspopup="dialog"
                           (click)="showBrandingModal()"
                           (keydown.enter)="showBrandingModal()"
                           (keydown.space)="$event.preventDefault(); showBrandingModal()">
                            <i class="fa-solid fa-star fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Branding</ng-container>
                        </a>

                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Users & Teams'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Users & Teams" 
                           [routerLink]="isAdminConsole() ? '/admin-console/teams' : '/teams'"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-users fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Users & Teams</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Documents & Templates'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Documents" 
                           routerLink="/documents"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-file-lines fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Documents</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Integrations'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Integrations"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-plug fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Integrations</ng-container>
                        </a>
                    </li>

                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Bulk'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Bulk Export" 
                           [routerLink]="isAdminConsole() ? '/admin-console/bulk-export' : '/bulk-export'"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-regular fa-file-export fa-xl fa-fw -ml-1.5 pl-1"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Bulk Export</ng-container>
                        </a>
                    </li>
                    <ng-container *ngSwitchCase="'Settings'">
                        <li>
                            <!-- Validation fix: extract comment outside tag to resolve parse error -->
                            <!-- Removed duplicate tabindex attribute to avoid HTML validation/Angular template errors -->
                            <a  tabindex="0" i18n-aria-label aria-label="Settings" [attr.aria-expanded]="settings_submenu_opened"
                               [ngStyle]="{'position':'relative', 'top':'1rem', 'left': isCollapsed ? '1.5625rem' : '1.125rem'}"
                               class="hover-report" role="button"
                               (mousedown)="handleExpandableMouseDown()"
                               (click)="settings_submenu_opened = !settings_submenu_opened"
                               (keydown.enter)="settings_submenu_opened = !settings_submenu_opened"
                               (keydown.space)="$event.preventDefault(); settings_submenu_opened = !settings_submenu_opened"
                               (focus)="handleExpandableFocus()" >
                               <!-- WCAG 1.4.4 – px → rem -->
                                <i class="fa-solid fa-chart-column fa-xl fa-fw " style="margin-right: 24px; margin-left: .35em;"></i>
                                <span *ngIf="!isCollapsed" i18n>Settings</span>
                            </a>
                       </li>
                        <li nz-submenu class="-mt-2" [(nzOpen)]="settings_submenu_opened" #settingsSubmenu>
                            <ul>
                            <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Global Settings" class="ml-2" aria-haspopup="dialog"
                                   [attr.tabindex]="settings_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)"
                                (click)="showTeamSettingModal()" i18n>Global Settings</a>
                            </li>
                            <li nzMatchRouter nz-menu-item *ngIf="showSeals && (roleID == roles.BILLING_ADMIN || roleID == roles.SUPER_ADMIN)" class="sidebar-item hover-selected">
                                <a i18n-aria-label aria-label="Stamps" class="ml-2" [routerLink]="isAdminConsole() ? '/admin-console/seals' : '/seals'" 
                                   [attr.tabindex]="settings_submenu_opened ? '0' : '-1'"
                                   (keydown.space)="handleSpaceKeyPress($event)" i18n>Stamps</a>
                            </li>
                            </ul>
                        </li>
                    </ng-container>
                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'App'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Applications" 
                           routerLink="/applications"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-regular fa-table-list fa-xl fa-fw -ml-1.5 pl-1"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Applications</ng-container>
                        </a>
                    </li>
                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Manage Documents'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Team Documents" 
                           routerLink="/manage-documents"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-regular fa-book-bookmark fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Team Documents</ng-container>
                        </a>
                    </li>
                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'Admin'" [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Admin" 
                           routerLink="/admin-console"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-user-gear fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Admin</ng-container>
                        </a>
                    </li>


                    <li nzMatchRouter nzSelected nz-menu-item class="sidebar-item" *ngSwitchCase="'Admin Dashboard'"  [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="Admin Dashboard" 
                           routerLink="/admin-console/dashboard"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-user-gear fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Admin Dashboard</ng-container>
                        </a>
                    </li>

                    <!-- Removed duplicate aria-label attributes to avoid template parse errors (WCAG validation) -->
                    <li nzMatchRouter nz-menu-item class="sidebar-item" *ngSwitchCase="'User Dashboard'"   [ngClass]="'hover-selected'">
                        <a i18n-aria-label aria-label="User Dashboard" 
                           routerLink="/dashboard"
                           (keydown.space)="handleSpaceKeyPress($event)">
                            <i class="fa-solid fa-gauge-high fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>User Dashboard</ng-container>
                        </a>
                    </li>

                </ng-container>
                <span *ngIf="mediaService.isMobile()">
                    <!-- <li nzMatchRouter nz-menu-item class="sidebar-item" style="padding-left: 10px;">

                    </li> -->
                    <li nzMatchRouter nz-menu-item class="sidebar-item">
                        <!-- WCAG 2.1 – convert presentation-only link into actionable control with role, label and keyboard handler -->
                        <a role="button" tabindex="0" i18n-aria-label aria-label="Help"
                           (click)="goHelp()"
                           (keydown.enter)="goHelp()"
                           (keydown.space)="$event.preventDefault(); goHelp()">
                            <i class="fa-solid fa-circle-question fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Help</ng-container>
                        </a>
                    </li>
                    <li nzMatchRouter nz-menu-item class="sidebar-item">
                    <!-- WCAG 3.2.4 – Unified accessible name and visible label to “Release notes” so the
                         header button and sidebar item that trigger the same dialog are identified
                         consistently across the page. -->
                    <a role="button" tabindex="0" i18n-aria-label aria-label="Release notes"
                           (click)="showBellDialog()"
                           (keydown.enter)="showBellDialog()"
                           (keydown.space)="$event.preventDefault(); showBellDialog()">
                            <i class="fa-solid fa-bell fa-xl fa-fw -ml-1.5"></i>
                            <ng-container *ngIf="!isCollapsed" i18n>Release notes</ng-container>
                        </a>
                    </li>
                    <ng-container>
                        <li>
                            <!-- WCAG 2.1 – make interactive element keyboard accessible by adding proper role, tabindex and Enter key handler -->
                            <a i18n-aria-label aria-label="Profile" style="padding-left: 10px;" class="hover-report" role="button" tabindex="0"
                               (click)="profile_submenu_opened = !profile_submenu_opened"
                               (keydown.enter)="profile_submenu_opened = !profile_submenu_opened"
                               (keydown.space)="$event.preventDefault(); profile_submenu_opened = !profile_submenu_opened">
                                <span class="fa-stack fa-xl fa-fw -ml-1.5" style="margin-right: 10px;" [attr.aria-expanded]="profile_submenu_opened">
                                    <i class="fa-solid  fa-circle  fa-stack-1x"></i>
                                    <!-- WCAG 1.4.4 – font-size 14px → 0.875rem so initials scale with text -->
                                    <i class="fa-solid fa-stack-1x" style="color: #2d323d; font-size: 0.875rem;">{{userFirstName}}</i>
                                </span>
                                <span *ngIf="!isCollapsed" i18n>Profile</span>
                            </a>
                        </li>
                        <li nz-submenu style="margin-top: -45px" [(nzOpen)]="profile_submenu_opened">
                            <ul>
                                <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                    <a class="ml-2" (click)="settings()" 
                                       [attr.tabindex]="profile_submenu_opened ? '0' : '-1'"
                                       (keydown.space)="handleSpaceKeyPress($event)" i18n>Profile Settings</a>
                                </li>
                                <li nzMatchRouter nz-menu-item class="sidebar-item hover-selected">
                                    <a class="ml-2" (click)="logout()" 
                                       [attr.tabindex]="profile_submenu_opened ? '0' : '-1'"
                                       (keydown.space)="handleSpaceKeyPress($event)" i18n>Logout</a>
                                </li>
                            </ul>
                        </li>
                    </ng-container>
                </span>

            </ul>
        </nav>
    </nz-sider>   
    <app-doc-sidebar *ngIf="showDocBar" [type]="docBarType" (docBarCollapse)="isDocBarCollapsed = $event" (menuSelect)="passDataToChild($event)"
    [isParentCollapsed]="mediaService.isMobile() ? !showSideBarMobile : isCollapsed"
    [headerOffset]="showMaintenanceMsg ? 124 : 64"></app-doc-sidebar>

    <nz-layout class="main-layout" [ngClass]="isCollapsed ? 'right-layout-collapsed ' : 'right-layout-extended'"
    [style.paddingLeft.px]="showDocBar && !mediaService.isMobile()  ? isDocBarCollapsed  ? 80 : 190 : 0">

    <nz-alert *ngIf="showWarningBar" class="warningMessage" nzType="warning" [ngClass]="showMaintenanceMsg ? 'mt-[84px]':'mt-[24px]'"
    [nzMessage]="warningMessage" nzShowIcon></nz-alert>
    <ng-template [ngClass]="showMaintenanceMsg ? 'mt-[84px]':'mt-[24px]'" #warningMessage i18n>
        Your email has not yet been confirmed, please check your email.
        <!-- WCAG 2.1 – make ‘Resend verification email’ control accessible to keyboard users -->
        <a role="button" tabindex="0" i18n-aria-label aria-label="Resend verification email"
           (click)="resendVerificationEmail()" (keydown.enter)="resendVerificationEmail()"
           (keydown.space)="$event.preventDefault(); resendVerificationEmail()">Resend verification email</a>
    </ng-template>
    <div [ngStyle]="{'margin-top': showMaintenanceMsg ? '124px' : '64px'}">
            <span *ngIf="isEnterprise" class="float-right mr-6 mt-5 sandbox-switch" i18n>Sandbox View <mat-slide-toggle appToggleEnterKey color="primary" class="ml-2"
                    [(ngModel)]="sandBoxView">
            </mat-slide-toggle>
            </span>
        </div>
        <nz-header *ngIf="isEnterprise && scrolled" class="app-header2">
            <span class="float-right mr-6 sandbox-switch" i18n>Sandbox View <mat-slide-toggle appToggleEnterKey color="primary" class="ml-2"
                    [(ngModel)]="sandBoxView">
            </mat-slide-toggle>
            </span>
        </nz-header>
        <nz-content >
            <!-- Added id and tabindex so the skip link target is focusable and programmatically
                 reachable. -->
            <main id="main" tabindex="-1" role="main"><!-- Assumption: only one main landmark per page -->
                <router-outlet (activate)="activatePage = $event"></router-outlet>
            </main>
        </nz-content>
    </nz-layout>

    <!-- WCAG 1.4.13 – make dialog dismissible by click outside and ESC key -->
    <!-- WCAG 1.4.10 – set responsive modal width so dialog fits within 320 px viewport -->
    <nz-modal [(nzVisible)]="bellDialog"
  nzTitle="New Release Notification!"
  (nzOnCancel)="bellDialogCancel()"
  (nzOnOk)="bellDialogOk()"
  [nzAutofocus]="null"
  [nzMaskClosable]="true"
  [nzKeyboard]="true" [nzWidth]="'90vw'"
  role="dialog"
  aria-label="New Release Notification!">
  <ng-container *nzModalContent>
    <div [innerHTML]="bellContent | safeHtml"></div>
  </ng-container>
  <div *nzModalFooter>
    <button id="closeNewReleaseModal" nz-button nzType="primary" (click)="bellDialogOk()">
      <!-- WCAG 1.4.12 fix: switch from fixed 20 px line-height to relative unit so icon can grow when
           users apply custom text-spacing. Using `normal` lets the browser calculate an appropriate
           value based on the current font metrics and any user style sheet overrides. -->
      <span nz-icon nzType="check" nzTheme="outline" style="font-weight: bold; line-height: normal; vertical-align: top;" i18n></span> Close
    </button>
  </div>
</nz-modal>

    <!-- WCAG 1.4.13 – enable dismissal of New Template dialog via ESC key and click outside -->
    <!-- Assumption: Added explicit aria-label that matches the visible title to satisfy WCAG 2.5.3 for the modal dialog. -->
    <!-- WCAG 1.4.10 – responsive width; swapped fixed 900 px for 90 vw so modal never causes horizontal scroll on small screens -->
    <nz-modal [(nzVisible)]="templateDialog" nzTitle="New Template" aria-label="New Template"
              (nzOnCancel)="templateDialogCancel()" nzFooter="null" [nzWidth]="'90vw'" [nzAutofocus]="null"
              [nzMaskClosable]="true" [nzKeyboard]="true">
        <ng-container *nzModalContent>
            <!-- WCAG 1.4.10 – add responsive grid spans so columns stack at xs/sm viewports, preventing horizontal scroll inside modal -->
            <div nz-row [nzGutter]="20" class=" TemplatesContainer" id="new_template01">
                <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="8" class="TemplateItem">
                    <div class="widget text-center">
                        <div tabindex="0" role="button" id="RegularTemplate" class="card_size" (click)="createRegularTemplate()">
                            <h4 class="pt-30 fontstyle " i18n>Regular Template</h4>
                            <div class="template-icons">
                                <!-- <span nz-icon nzType="file" nzTheme="outline"></span> -->
                                <i class="fa-light fa-file xs:text-5xl"></i>
                            </div>
                        </div>

                    </div>
                    <!-- WCAG 1.4.10 – convert fixed min-height to rem so vertical size scales with user zoom without clipping -->
                    <p style="min-height:7.5rem" i18n>Create a template to re-use it on new documents.
                        <br>
                        <br><span>Best for saving time on frequently-used documents.</span>
                    </p>
                    <div class="text-center">
                        <button *ngIf="!userProduct?.templateEnabled && roleID == roles.BILLING_ADMIN" style="width: 150px"
                         class="ant-btn ant-btn-primary upgrade-btn"  i18n>
                            <a href="/UI/changePlan2.html?lang=fr">Upgrade</a>
                        </button>
                        <nz-alert *ngIf="!userProduct?.templateEnabled && roleID != roles.BILLING_ADMIN"  nzType="info" i18n-nzMessage nzMessage="Speak to your billing admin about this premium feature." nzShowIcon></nz-alert>
                    </div>
                </div>
                <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="8" class="TemplateItem">
                    <div class="widget text-center">
                        <div tabindex="0" role="button" id="TemplateLink" class="card_size" (click)="createTemplateLink()">
                            <h4 class="pt-30 fontstyle " i18n>Template Link</h4>
                            <div class="template-icons">
                                <!-- <span nz-icon nzType="link" nzTheme="outline"></span> -->
                                <i class="fa-light fa-link xs:text-5xl"></i>
                            </div>
                        </div>
                    </div>
                    <!-- WCAG 1.4.10 – converted to rem for consistent scaling -->
                    <p style="min-height:7.5rem" i18n>Publish a document as a Template Link to make it available to anyone.
                        <br>
                        <br><span>Best for accepting applications online.</span></p>
                    <div class="text-center">
                        <button *ngIf="!userProduct?.templateLinkEnabled && roleID == roles.BILLING_ADMIN" style="width: 150px"
                            class="ant-btn ant-btn-primary upgrade-btn"  i18n>
                            <a href="/UI/changePlan2.html?lang=fr" >Upgrade</a>
                        </button>
                        <nz-alert *ngIf="!userProduct?.templateLinkEnabled && roleID != roles.BILLING_ADMIN"  nzType="info" i18n-nzMessage nzMessage="Speak to your billing admin about this premium feature." nzShowIcon></nz-alert>

                    </div>
                </div>
                <div nz-col nzXs="24" nzSm="24" nzMd="24" nzLg="8" class="TemplateItem">
                    <div class="widget text-center">
                        <div tabindex="0" role="button" id="BulkSignTemplate" class="card_size" (click)="createBulkSign()">
                            <h4 class="pt-30 fontstyle " i18n>Bulk Sign</h4>
                            <div class="template-icons">
                                <!-- <i class="ti-layout-grid3"></i> -->
                                <i class="fa-light fa-grid xs:text-5xl"></i>
                            </div>
                        </div>
                    </div>
                    <!-- WCAG 1.4.10 – converted to rem for consistent scaling -->
                    <p style="min-height:7.5rem" i18n>Easily send a document to hundreds of people at once.
                        <br>
                        <br><span>Best for mass signings of individual documents.</span></p>
                    <div class="text-center">
                        <button *ngIf="!bulksignEnabled && roleID == roles.BILLING_ADMIN" style="width: 150px"
                            class="ant-btn ant-btn-primary upgrade-btn"  i18n>
                            <a href="/UI/changePlan2.html?lang=fr">Upgrade</a>
                        </button>
                        <nz-alert *ngIf="!bulksignEnabled && roleID != roles.BILLING_ADMIN"  nzType="info" i18n-nzMessage nzMessage="Speak to your billing admin about this premium feature." nzShowIcon></nz-alert>
                    </div>
                </div>
            </div>
        </ng-container>
        <div *nzModalFooter></div>
    </nz-modal>

</nz-layout>
<branding [dialogType]="brandingDialogType" #branding></branding>
<team-settings [dialogType]="teamSettingsDialogType" [groupId]="rootGroupId" [parentGroupId]="0" #teamSettings></team-settings>
