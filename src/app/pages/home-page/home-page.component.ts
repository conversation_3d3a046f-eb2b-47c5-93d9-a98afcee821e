import {Accountdetails, ConsoleType, Plan, Plans, Role, Roles, User} from '@core/models';
// WCAG 3.2.3 – Refactored getNavConfig() so that all repeated navigation
// items follow a single master order, ensuring consistent navigation order
// across roles/pages while preserving existing visibility rules.
// Added Title service to be able to programmatically set the <title> element – WCAG 2.4.2
import { Component, HostListener, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { DocumentService, UserService, TabIndexService, UrlService } from "@core/services";
import { Title } from '@angular/platform-browser';
import { Router, ResolveEnd, ActivatedRoute } from '@angular/router';
import { Observable, of } from "rxjs";
import {NzMessageService} from "ng-zorro-antd/message";
import {Code} from "@core/models";
import { MediaService } from '@core/services';
import { DocumentListComponent } from './documents/document-list/document-list.component';
import { TemplateListComponent } from './documents/template-list/template-list.component';


// Renamed type alias to Nav<PERSON><PERSON><PERSON> to avoid clashing with <PERSON><PERSON>’s Title service (WCAG 2.4.2 fix added) – keeps existing functionality.
const TitleArr = ["Dashboard" ,  "Documents" , "Templates" , "Contacts" ,
    "Inbox" , "Reports" , "Plan" , "Plan2", "Branding" , "Users & Teams",
    "Documents & Templates" , "Integrations" , "Settings" , "Admin" ,
    "User Dashboard" , "Admin Dashboard", "Bulk", "App","Manage Documents",''] as const;

type NavTitle = typeof TitleArr[number];


interface Navigation {
    icon:string
    link:string
    is_old_page:boolean
    name:string
}

// WCAG 3.1.2 – Language of Parts
// Expose the component’s primary language (English) to assistive
// technologies by setting the lang attribute on the host element. This
// ensures screen-reader pronunciation rules are applied correctly even when
// the surrounding document is delivered in a different locale. // Assumption: the
// visible strings inside HomePageComponent are currently provided in English.
@Component({
    templateUrl: './home-page.component.html',
    styleUrls: ['./home-page.component.less'],
    // Set lang attribute on the component’s root element
    host: { 'lang': 'en' }
})
export class HomePageComponent implements OnInit {
    @ViewChild('branding') branding:any;
    @ViewChild('teamSettings') teamSettings:any;
    @ViewChild('reportsSubmenu') reportsSubmenu: any;
    @ViewChild('planSubmenu') planSubmenu: any;
    @ViewChild('settingsSubmenu') settingsSubmenu: any;
    public brandingDialogType = false;
    public teamSettingsDialogType = false;
    public rootGroupId = -1;
    isCollapsed = false;
    scrolled = false;

    // TODO: Implement switching function later
    isEnterprise = false;
    sandBoxView = false;
    //

    inbox_count:Observable<number>;
    isTeamPlan:boolean = false;
    teamPlanReady:boolean = false;
    roleID:number = -1
    pageName:string =`User Dashboard`

    bellDialog:boolean = false
    bellContent:string = ""

    templateDialog:boolean = false
    accountDetials?:Accountdetails
    userProduct:any;
    userFirstName:string = ""
    userGUID:string = ""
    readonly roles = Roles
    readonly plans = Plans
    report_submenu_opened = false
    settings_submenu_opened = false
    plan_submenu_opened = false
    bulksignEnabled = false;
    profile_submenu_opened = false

    userInfo:any = {}
    showWarningBar:boolean = false
    temp = false
    showMaintenanceMsg=false;
    maintenanceMsgTitle='';
    maintenanceBody='';
    showSideBarMobile = false;

    activatePage:Component | null = null;

    showDocBar = this.activatePage instanceof DocumentListComponent || this.activatePage instanceof TemplateListComponent;
    docBarType = '';
    isDocBarCollapsed = false;
    showSeals = true;
    showApplications = this.getCookie('showApplications') == 'true';
    timedOutCloser:any;
    
    // Track admin console state
    isAdminConsoleState: boolean = false;
    
    // Track if a click just happened to prevent focus interference
    private justClicked: boolean = false;
    private isInitialLoad: boolean = true;

    constructor(
        private userService: UserService,
        private router: Router,
        private route: ActivatedRoute,
        private doc:DocumentService,
        private message:NzMessageService,
        public mediaService: MediaService,
        private titleService: Title, // Assumption: App name is Signority; update titles accordingly
        private tabIndexService: TabIndexService,
        private urlService: UrlService,
        private cdr: ChangeDetectorRef
    ) {
        this.inbox_count =  doc.get_inbox_badge_count()
        this.getBellcontent()
        this.userService.getAccountDetails().subscribe(
            v => {this.accountDetials = v}
        )

        this.userService.getMaintenanceMessage().subscribe(v => {
                if(v.message) {
                    this.showMaintenanceMsg = true;
                    this.maintenanceMsgTitle = v.message.title;
                    this.maintenanceBody = v.message.announcement;
                }
            }
        )
    }

    // WCAG 2.4.1 – Moves keyboard focus to the <main> landmark after page load
    // and after each client-side navigation so that keyboard users who activated
    // the skip link (or who rely on landmark shortcuts) start at the new content
    // rather than at the top banner. // Assumption: Home page is an SPA route container
    private focusMain(): void {
        // Using setTimeout to ensure the element exists in the DOM when focus is called.
        // But we prioritize navigation focus over main content focus
        // Don't restore focus on initial page load to prevent unwanted focus-visible styling
        if (!this.isInitialLoad) {
            setTimeout(() => {
                // Let the tab index service handle focus restoration to navigation
                this.tabIndexService.restoreFocus();
            });
        }
    }

    @HostListener('window:scroll', ['$event'])
    onWindowScroll() {
        this.scrolled = window.scrollY != 0;
    }

    lang = "en"
    ngOnInit() {
        this.userInfo = JSON.parse(localStorage.getItem("user")|| '0' );
//         if 2fa enforced and not setup yet

        // let count = 0;
        // if (this.userInfo?.userSettingsSecurity?.TwoFAPhoneNumber) {
        //     count += 1;
        // }
        // if (this.userInfo?.userSettingsSecurity?.TwoFAEmail) {
        //     count += 1;
        // }
        // if (this.userInfo?.userSettingsSecurity?.SecurityAnswer) {
        //     count += 1;
        // }
        // if (this.userInfo?.userSettingsSecurity?.TwoFASecurityAnswer1) {
        //     count += 1;
        // }
        // if(this.userInfo?.userSettingsSecurity?.enforce2fa && count < 2) {
        //     window.location.href = ("/UI/twofa.html");
        // }
        if(!this.userInfo.userSetting.EmailAddressConfirmed) {
            this.showWarningBar = true;
        }

        // Set initial admin console state
        this.isAdminConsoleState = this.urlService.url_is_admin_console(this.router.url);
        console.log('Initial admin console state:', this.isAdminConsoleState, 'Current URL:', this.router.url);
        
        // Subscribe to admin console state changes
        this.urlService.is_admin_console().subscribe(isAdmin => {
            this.isAdminConsoleState = isAdmin;
            console.log('Admin console state changed:', isAdmin, 'Current URL:', this.router.url);
        });

        this.setHeaderPageName(this.router.url);
        this.showDocBar = this.activatePage instanceof DocumentListComponent || this.activatePage instanceof TemplateListComponent;
        if(this.activatePage instanceof DocumentListComponent ) {
            this.docBarType = 'document';
        }
        if(this.activatePage instanceof TemplateListComponent) {
            this.docBarType = 'template';
        }
        this.router.events.subscribe(
            event => {
                  // this.isCollapsed = false;
                  this.showDocBar = this.activatePage instanceof DocumentListComponent || this.activatePage instanceof TemplateListComponent;

                  if (this.activatePage instanceof DocumentListComponent) {
                      this.docBarType = 'document'
                  }
                  if (this.activatePage instanceof TemplateListComponent) {
                      this.docBarType = 'template'
                  }
                if(event instanceof ResolveEnd) {
                    this.setHeaderPageName(event.url);
                    this.showSideBarMobile = false;
                    // WCAG 2.4.1 – after every client-side navigation restore focus to saved position
                    // Temporarily disabled to test if this is causing the issue
                    // setTimeout(() => {
                    //     this.tabIndexService.restoreFocus();
                    // }, 100);
                }
            }
        )
        this.userService.getRoleID().subscribe(id => this.roleID = id);
        this.userService.isTeamPlan().subscribe(val => {
            this.isTeamPlan = val;
            this.teamPlanReady = true;
        })
        this.userService.getUser().subscribe(val => {
            this.rootGroupId = val.rootGroupId;
            let inUrlArray = window.location.href.split("/");
            let goUrl = "";
            let goUrlStatus = false;
            let langCode = val.userSetting.LanguageLocalization;
            let lan = "";
            this.userProduct = val.userProduct;
            this.bulksignEnabled = val.BulksignEnabled;
            this.showSeals = this.userProduct.sealEnabled;
            this.userGUID = val.userSetting.GUID;
            if(langCode === 550) {
                lan = "en";
                this.lang = 'en';
                this.setCookie2('lang', 'en');
                this.setCookie2('langCode', 550);
            }
            if(langCode === 551) {
                lan = "fr";
                this.lang = 'fr';
                this.setCookie2('lang', 'fr');
                this.setCookie2('langCode', 551);
            }
            for(let i = 0; i < inUrlArray.length; i++) {
                if(inUrlArray[i] == 'en' && inUrlArray[i] != lan) {
                    this.lang = lan;
                    goUrlStatus = true;
                    inUrlArray[i] = lan;
                }
                if(inUrlArray[i] == 'fr' && inUrlArray[i] != lan) {
                    this.lang = lan;
                    goUrlStatus = true;
                    inUrlArray[i] = lan;
                }
                if(i != (inUrlArray.length - 1)) {
                    goUrl += `${inUrlArray[i]}/`;
                }else{
                    goUrl += `${inUrlArray[i]}`;
                }
            }
            if(goUrlStatus === true){
                window.location.href = goUrl;
            }
        })
        let urlArray = window.location.href.split("/");
        for(let i = 0; i < urlArray.length; i++) {
            if(urlArray[i] == 'fr') {
                this.lang = 'fr';
                this.setCookie2('lang', 'fr')
                this.setCookie2('langCode', 551)
            }
            if(urlArray[i] == 'en') {
                this.lang = 'en';
                this.setCookie2('lang', 'en')
                this.setCookie2('langCode', 550)
            }
        }
        this.mediaService.breakpoint$.subscribe(breakpoint=>{
            this.showSideBarMobile = !this.mediaService.isMobile();
            if(!this.showSideBarMobile) {
                this.isCollapsed = true;
            }
        })
        
        // Mark initial load as complete after a short delay
        setTimeout(() => {
            this.isInitialLoad = false;
        }, 100);

    }

    ngAfterViewInit() {
        // WCAG 2.4.1 – ensure initial load sets focus to main so keyboard users land inside content
        this.focusMain();
    }

    showBrandingModal(): void {
        this.branding.showModal();
    }
    showTeamSettingModal(): void {
        this.teamSettings.showModal();
    }

    // WCAG 2.4.2 – Page Titled
    // The application has two root paths that render the same HomePageComponent:
    //   1.  /<childRoute>          – e.g. /dashboard, /documents …
    //   2.  /admin-console/<childRoute>
    // The previous implementation looked only at the very first URL segment
    // which meant that every "admin-console/*" page produced the generic title
    // "Admin Console – Signority", even when the user was viewing
    // "/admin-console/reports/security" or "/admin-console/inbox".  This violates
    // WCAG 2.4.2 because the <title> element no longer describes the specific
    // page content.
    //
    // Fix: Determine the *effective* route segment by skipping the optional
    // "admin-console" prefix so sub-pages once again receive descriptive titles.
    setHeaderPageName(url:string) : void{
        const allSegments = url.split('/').filter(Boolean); // remove leading empty segment
        
        // Check if we're in admin console first
        if (allSegments[0] === 'admin-console') {
            this.pageName = 'Admin Console';
            // If there are additional segments after admin-console, handle them
            if (allSegments.length > 1) {
                const secondSegment = allSegments[1] || '';
                switch (secondSegment) {
                    case 'dashboard':
                        this.pageName = 'Admin Console';
                        break;
                    case 'teams':
                        this.pageName = 'teams';
                        break;
                                    case 'plan':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu
                    break;
                case 'payment':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu for payment
                    break;
                case 'invoice':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu for invoice
                    break;
                    case 'bulk-export':
                        this.pageName = 'bulk-export';
                        break;
                    case 'change-plan':
                        this.pageName = 'Admin Console';
                        break;
                    case 'reports':
                        const thirdSegment = allSegments[2] || '';
                        if (thirdSegment === 'cancel') {
                            this.pageName = 'cancel-report';
                            this.report_submenu_opened = true; // Auto-expand reports submenu
                        }
                        else if (thirdSegment === 'security') {
                            this.pageName = 'security-report';
                            this.report_submenu_opened = true; // Auto-expand reports submenu
                        }
                        else if (thirdSegment === 'usage') {
                            this.pageName = 'usage-report';
                            this.report_submenu_opened = true; // Auto-expand reports submenu
                        }
                        break;
                }
            }
        } else {
            // Non-admin console paths
            const firstSegment = allSegments[0] || '';
            switch (firstSegment){
                case 'user':
                    this.pageName = 'User Dashboard';
                    break;
                case 'dashboard':
                    this.pageName = 'User Dashboard';
                    break;
                case 'teams':
                    this.pageName = 'teams';
                    break;
                case 'plan':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu
                    break;
                case 'payment':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu for payment
                    break;
                case 'invoice':
                    this.pageName = 'Admin Console';
                    this.plan_submenu_opened = true; // Auto-expand plan submenu for invoice
                    break;
                case 'bulk-export':
                    this.pageName = 'bulk-export';
                    break;
                case 'change-plan':
                    this.pageName = 'Admin Console';
                    break;
                case 'reports':
                    const secondSegment = allSegments[1] || '';
                    if (secondSegment === 'cancel') {
                        this.pageName = 'cancel-report';
                        this.report_submenu_opened = true; // Auto-expand reports submenu
                    }
                    else if (secondSegment === 'security') {
                        this.pageName = 'security-report';
                        this.report_submenu_opened = true; // Auto-expand reports submenu
                    }
                    else if (secondSegment === 'usage') {
                        this.pageName = 'usage-report';
                        this.report_submenu_opened = true; // Auto-expand reports submenu
                    }
                    break;
            }
        }

        // Fallback: if no explicit mapping matched, derive a title from the URL segment
        if (!this.pageName || this.pageName.trim() === '') {
            const firstSegment = allSegments[0] || '';
            if (!firstSegment) {
                this.pageName = 'Home';
            } else {
                // Transform "documents" -> "Documents"
                this.pageName = firstSegment.charAt(0).toUpperCase() + firstSegment.slice(1);
            }
        }

        // After determining the human-readable page name, update the <title> element.
        // WCAG 2.4.2 – Ensure each view has a descriptive, unique title.
        // Note: Individual components now handle their own titles to avoid flashing
        // this.titleService.setTitle(`${this.pageName} – ${baseAppName}`);
    }
    // WCAG 3.2.3 (Consistent Navigation) – Ensure that any navigation item that
    // is repeated across multiple pages always appears in the same *relative*
    // DOM order.  The previous implementation returned several completely
    // different hard-coded arrays which could change the ordering of common
    // items such as “Documents”, “Templates”, “Reports”, etc. depending on the
    // user role or plan.  This violates the guideline because users who rely
    // on spatial memory (or assistive technology shortcuts) would encounter
    // the same link in a different position when navigating between views.  
    //
    // Fix: Define a single master navigation order and then filter that list
    // according to the current visibility rules.  This guarantees the
    // *relative* order of any shared link never changes, while still hiding
    // items the user should not see. // Assumption: The master order defined
    // below reflects the intended logical grouping used across the
    // application.  
    getNavConfig(): NavTitle[] {
        // Master order that should stay identical across roles/pages.
        const masterOrder: NavTitle[] = [
            "Admin Dashboard",
            "Dashboard",
            "Users & Teams",
            "Documents",
            "Templates",
            "Contacts",
            "Manage Documents",
            "Bulk",
            "Inbox",
            "Reports",
            "App",
            "Branding",
            "Plan2", // experimental / billing admin version of Plan
            "Plan",
            "Settings",
            "Integrations",
            "Documents & Templates",
            "Admin",
            "User Dashboard",
            "" // keep empty string placeholder to satisfy TitleArr typing
        ];

        // Helper that builds a *set* of allowed titles for current context.
        const allowed = new Set<NavTitle>();

        // Individual user (no team plan)
        if (!this.isTeamPlan && this.teamPlanReady) {
            ["Dashboard", "Documents", "Templates", "Contacts", "Bulk", "Inbox", "Reports", this.showApplications ? "App" : '', "Plan2"]
                .forEach(t => allowed.add(t as NavTitle));
        } else if (this.pageName !== 'Admin Console') {
            // Not admin console – decide based on role
            if (this.roleID !== Roles.REGULAR_USER && this.roleID !== Roles.DOC_ADMIN && this.pageName !== 'teams' && this.pageName !== 'bulk-export') {
                // Admin role (but not doc/regular user)
                ["Dashboard", "Documents", "Templates", "Contacts", "Inbox", "Reports", this.showApplications ? "App" : '', "Admin"]
                    .forEach(t => allowed.add(t as NavTitle));
            } else if (this.roleID === Roles.DOC_ADMIN) {
                ["Dashboard", "Users & Teams", "Documents", "Templates", "Contacts", "Inbox", "Reports", this.showApplications ? "App" : '', "Manage Documents"]
                    .forEach(t => allowed.add(t as NavTitle));
            } else if (this.roleID === Roles.REGULAR_USER) {
                ["Dashboard", "Users & Teams", "Documents", "Templates", "Contacts", "Inbox", "Reports", this.showApplications ? "App" : '']
                    .forEach(t => allowed.add(t as NavTitle));
            } else {
                // Handle teams page for other roles (like SUPER_ADMIN, TEAM_ADMIN, BILLING_ADMIN)
                // These roles should see the same navigation as in admin console
                switch (this.roleID) {
                    case Roles.SUPER_ADMIN:
                        ["Admin Dashboard", "Users & Teams", "Branding", "Plan", "Bulk", "Settings", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                        break;
                    case Roles.TEAM_ADMIN:
                        ["Admin Dashboard", "Users & Teams", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                        break;
                    case Roles.BILLING_ADMIN:
                        ["Admin Dashboard", "Users & Teams", "Branding", "Plan2", "Bulk", "Settings", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                        break;
                    default:
                        // fallback for other roles
                        ["Dashboard", "Users & Teams", "Documents", "Templates", "Contacts", "Inbox", "Reports", this.showApplications ? "App" : '']
                            .forEach(t => allowed.add(t as NavTitle));
                        break;
                }
            }
        } else {
            // Admin console paths
            switch (this.roleID) {
                case Roles.SUPER_ADMIN:
                    ["Admin Dashboard", "Users & Teams", "Branding", "Plan", "Bulk", "Settings", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                    break;
                case Roles.TEAM_ADMIN:
                    ["Admin Dashboard", "Users & Teams", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                    break;
                case Roles.BILLING_ADMIN:
                    ["Admin Dashboard", "Users & Teams", "Branding", "Plan2", "Bulk", "Settings", "User Dashboard"].forEach(t => allowed.add(t as NavTitle));
                    break;
                default:
                    // fallback – keep empty
                    break;
            }
        }

        // Return master order filtered by the allowed set.
        return masterOrder.filter(t => allowed.has(t));
    }

    logout(){
        this.userService.logout(this.userGUID)
    }

    settings(){
        window.location.href="/UI/userSettings.html?lang=" + this.lang;
    }

    createDocument() {
        if(this.accountDetials!.storageusage >= this.accountDetials!.storagelimit){
            this.message.create('error', $localize`Your current plan supports up to ${this.accountDetials!.storagelimit} documents.
             Please delete older documents you no longer need and try again.`);
            return
        }
        let newDocument = {
            title: "Unnamed Document " + (new Date()).getTime(),
            // invitations: [{
            //     id: 0,
            //     actionType: Code.INVITATION_TYPE_SIGNER,
            //     sequence: 1
            // }],
            // documentType: Code.DOCUMENT_TYPE_DOCUMENT,
            emailSubject:""
        };
        this.doc.create_new_doc(newDocument)
    }



    createRegularTemplate(){
        console.log("regular template")
        if(this.accountDetials!.storageusage >= this.accountDetials!.storagelimit){
            this.message.create('error', $localize`Your current plan supports up to ${this.accountDetials!.storagelimit} documents. Please delete older documents you no longer need and try again.`);
            return
        }
        if (!this.accountDetials!.templateEnabled){
            this.message.create('error', $localize`Please upgrade your plan to use this feature.`)
            return;
        }
         let newDocument = {
            title: "Unnamed Document " + (new Date()).getTime(),
            invitations : [{
                recipientName: "Role 1",
                recipientEmail: '',
                actionType: Code.INVITATION_TYPE_SIGNER,
                sequence: 1
            }],
            documentType: Code.DOCUMENT_TYPE_REGULAR_TEMPLATE
        };
        this.doc.create_new_doc(newDocument)
    }

    createTemplateLink(){
        console.log("template link")
         if(this.accountDetials!.storageusage >= this.accountDetials!.storagelimit){
            this.message.create('error', $localize`Your current plan supports up to ${this.accountDetials!.storagelimit} documents. Please delete older documents you no longer need and try again.`);
            return
        }
        if (!this.accountDetials!.templateLinkEnabled){
            this.message.create('error', $localize`Please upgrade your plan to use this feature.`)
            return;
        }
         let newDocument = {
            title: "Unnamed Document " + (new Date()).getTime(),
            invitations : [{
                isPlaceHolder: true,
                recipientName: "Initial Link Signer",
                recipientEmail: '<EMAIL>',
                actionType: Code.INVITATION_TYPE_OPEN_SIGNER,
                sequence: 1
            }],
            documentType : Code.DOCUMENT_TYPE_TEMPLATE_LINK
        };
        this.doc.create_new_doc(newDocument)
    }
    createBulkSign(){
        console.log("bulk sign")
        if(this.accountDetials!.storageusage >= this.accountDetials!.storagelimit){
            this.message.create('error', $localize`Your current plan supports up to ${this.accountDetials!.storagelimit} documents. Please delete older documents you no longer need and try again.`);
            return
        }
        if (!this.accountDetials!.BulksignEnabled){
            this.message.create('error', $localize`Please upgrade your plan to use this feature.`)
            return;
        }
         let newDocument = {
            title: "Unnamed Document " + (new Date()).getTime(),
            invitations : [{
                actionType: Code.INVITATION_TYPE_SIGNER,
                isBulkSigner: true,
                recipientEmail: '<EMAIL>',
                recipientName: "Bulk Signer List",
                sequence: 1
            }],
            documentType: Code.DOCUMENT_TYPE_BULK_SIGN_TEMPLATE
        };
        this.doc.create_new_doc(newDocument)
    }

    newTemplate(): void {
        this.templateDialog = true;
    }

    templateDialogCancel(): void {
        this.templateDialog = false;
    }

    goHelp(){
        // window.location.href="http://signority.com/help/"
        window.open("http://signority.com/help/");
    }

    showBellDialog(): void {
        this.bellDialog = true;
    }

    bellDialogOk(): void {
        this.bellDialog = false;
    }

    bellDialogCancel(): void {
        this.bellDialog = false;
    }
    setCookie(name:any, value:any, days:number, path:any)
    {
        var exp = new Date();
        exp.setTime(exp.getTime() + days*24*60*60*1000);
        document.cookie = name + "="+ escape (value) + ";expires=" + exp.toString() + ";path=" + path;
    }
    setCookie2(cname:any, cvalue:any){
        document.cookie = cname+"="+ cvalue + ";path=/";
        // var exp = new Date();
        // exp.setTime(exp.getTime() + 365*24*60*60*1000);
        // document.cookie = name + "="+ escape (value) + ";expires=" + exp.toString() + ";path=" + "/UI";
    }

    getCookie(name:any) {
        var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
        if(arr=document.cookie.match(reg))
            return unescape(arr[2]);
        else
            return null;
    }

    getBellcontent() {
        // var res = await this.userService.getBellContent()
        // console.log("Bellcontent", res)
        this.userService.getBellContent().subscribe((res) => {
            console.log("Bellcontent", res)
            this.userFirstName = res.accountdetails.firstname.substring(0, 1).toUpperCase();

            /*
             * WCAG 4.1.1 – Parsing (duplicate id attributes)
             * ------------------------------------------------
             * The release-notes HTML returned by the back-end may contain headings/anchors that reuse
             * the same id value across different dialogs (e.g. “+ New” and “Release notes” modals),
             * triggering a duplicate-id error (F77) detected by Pa11y.  To guarantee id uniqueness once
             * the markup is injected with [innerHTML], we rewrite every id (and its corresponding
             * fragment links) with a unique, prefixed identifier while preserving functionality.
             *
             * Assumption: The HTML snippet is self-contained (no external CSS/JS relying on the exact
             * id values).  Therefore, renaming ids is safe and does not break behaviour.
             */
            this.bellContent = this.ensureUniqueIds(res.newFeatures.url);
            var R522 = this.getCookie("newRelease")
            // Only show bell dialog on dashboard routes
            if((Number(res.newFeatures.nums) != Number(R522) || !R522) && this.isDashboardRoute()) {
                this.showBellDialog()
            }
            this.setCookie("newRelease", res.newFeatures.nums, 365, "/UI")
        })
    }

    /**
     * Ensures that every element inside the provided HTML string has a unique id.
     * Matching anchor href attributes are updated accordingly.
     *
     * This runs on the client only; DOMParser is part of the browser API.  On the
     * (theoretical) server environment the function falls back to a simple string
     * return avoiding reference errors.
     */
    // Assumption: Running in browser context; DOMParser exists.
    private ensureUniqueIds(html: string): string {
        if (typeof window === 'undefined' || typeof DOMParser === 'undefined') {
            return html;
        }

        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        const idMap = new Map<string, string>();
        let counter = 0;

        doc.querySelectorAll('[id]').forEach((el) => {
            const originalId = el.id;
            // Skip if this id has not appeared yet – keep first occurrence intact
            if (!idMap.has(originalId)) {
                idMap.set(originalId, originalId);
                return;
            }
            // Duplicate id detected → generate new one and remember mapping
            const uniqueId = `rel-${counter++}-${originalId}`;
            idMap.set(originalId + '::dup' + counter, uniqueId); // store with unique key to not override
            // Update element id
            el.id = uniqueId;

            // Update any local anchor links pointing to the old id
            doc.querySelectorAll(`a[href="#${originalId}"]`).forEach((a) => {
                a.setAttribute('href', `#${uniqueId}`);
            });
        });

        return doc.body.innerHTML;
    }

    passDataToChild(data:string[]) {
        //pass docbar nav value to doc list component
        if (this.activatePage instanceof DocumentListComponent || this.activatePage instanceof TemplateListComponent) {
            this.activatePage.status = data;
        }
    }

    resendVerificationEmail() {
        this.userService.sendVerificationEmail(this.userInfo.email).subscribe()
    }

    mouseEnter(trigger:any) {
        if (this.timedOutCloser) {
          clearTimeout(this.timedOutCloser);
        }
        trigger.openMenu();
      }
    
      mouseLeave(trigger:any) {
        this.timedOutCloser = setTimeout(() => {
          trigger.closeMenu();
        }, 50);
      }

    /**
     * Handle space key press for submenu items
     * @param event Event
     */
    handleSpaceKeyPress(event: Event): void {
        event.preventDefault();
        const target = event.currentTarget as HTMLElement;
        if (target) {
            // Save current position before navigation
            this.tabIndexService.saveTabPosition();
            target.click();
        }
    }



    /**
     * Navigate to homepage and reset tab index
     */
    navigateToHomepage(): void {
        this.tabIndexService.saveTabPosition();
        this.router.navigate(['/dashboard']);
        this.tabIndexService.resetToLogo();
    }

    /**
     * Check if current context is admin console
     * @returns boolean indicating if in admin console
     */
    isAdminConsole(): boolean {
        // Check URL directly for immediate response
        const url = this.router.url;
        const isAdmin = url.startsWith('/admin-console');
        return isAdmin;
    }

    /**
     * Check if current route is a dashboard route
     * @returns boolean indicating if on dashboard page
     */
    isDashboardRoute(): boolean {
        const url = this.router.url;
        return url === '/dashboard' || url === '/admin-console/dashboard';
    }

    /**
     * Expand reports submenu when focused
     */
    expandReportsSubmenu(): void {
        console.log('expandReportsSubmenu called');
        this.report_submenu_opened = true;
        this.cdr.detectChanges();
    }

    /**
     * Expand plan submenu when focused
     */
    expandPlanSubmenu(): void {
        console.log('expandPlanSubmenu called');
        this.plan_submenu_opened = true;
        this.cdr.detectChanges();
    }

    /**
     * Expand settings submenu when focused
     */
    expandSettingsSubmenu(): void {
        console.log('expandSettingsSubmenu called');
        this.settings_submenu_opened = true;
        this.cdr.detectChanges();
    }

    /**
     * Handle mousedown on expandable menu items to set flag before focus
     */
    handleExpandableMouseDown(): void {
        this.justClicked = true;
        // Reset the flag after a short delay
        setTimeout(() => {
            this.justClicked = false;
        }, 100);
    }

    /**
     * Handle focus on expandable menu items with click detection
     */
    handleExpandableFocus(): void {
        // Only expand if we didn't just click
        if (!this.justClicked) {
            // Determine which submenu to expand based on the focused element
            const focusedElement = document.activeElement;
            if (focusedElement) {
                const ariaLabel = focusedElement.getAttribute('aria-label');
                if (ariaLabel === 'Reports') {
                    this.expandReportsSubmenu();
                } else if (ariaLabel === 'Plan') {
                    this.expandPlanSubmenu();
                } else if (ariaLabel === 'Settings') {
                    this.expandSettingsSubmenu();
                }
            }
        }
    }

}
