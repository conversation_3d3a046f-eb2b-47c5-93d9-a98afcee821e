
// WCAG 1.4.4 – Converted remaining fixed pixel font-sizes, widths and spacings to rem units so header, sidebar and interactive controls scale to 200 % text zoom without loss of content.
// Assumption: base browser font-size = 16 px.

::ng-deep .cdk-global-scrollblock {
	position: initial;
	width: initial;
	overflow: hidden;
}

// WCAG 2.4.1 – styles for the "Skip to main content" link
.skip-link {
  position: absolute;
  left: -9999px; // hides element off-screen but keeps it focusable
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  z-index: 1000;

  &:focus-visible {
    left: 0;
    top: 0;
    width: auto;
    height: auto;
    margin: 0.5rem;
    padding: 0.5rem 1rem;
    background: #ffffff;
    color: #000000;
    font-weight: 600;
    outline: 2px solid #055485; // brand colour, meets contrast requirements
  }
}

// WCAG 2.4.5 – visually hidden container for the additional “Quick links”
// navigation list that provides an alternative way to reach core pages.  The
// block becomes visible when any child link is focused so it stays out of the
// way for sighted mouse users while remaining discoverable for keyboard and
// assistive technology users.
.visually-hidden.quick-links {
  position: absolute;
  left: -9999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
  z-index: 1000;

  &:focus-within {
    left: 0;
    top: 3.5rem; // Appears below the skip link and before the header
    width: auto;
    height: auto;
    margin: 0.5rem;
    padding: 0.5rem 1rem;
    background: #ffffff;
    color: #000000;
    font-weight: 600;
    outline: 2px solid #055485;
  }

  ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    gap: 1rem;
  }

  a {
    text-decoration: underline;
    color: #055485;

    &:focus-visible {
      outline: #2D323D 3px solid;
      outline-offset: 2px;
    }
  }
}

// Hide quick links on mobile devices
.visually-hidden.quick-links.hidden-mobile {
  display: none !important;
}

:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@base-color: #2D323D;

::ng-deep .ant-menu-dark .ant-menu-inline.ant-menu-sub {
  background-color: @base-color;
}

.new_button{ // WCAG 1.4.4 – convert fixed pixel dimensions to rem so button scales with text
  min-width: 7.5rem; // 120px → 7.5rem
  min-height: 2.5rem; // 40px → 2.5rem
  font-size: 1.5rem; // 24px → 1.5rem
  font-weight: 700;
  // WCAG 1.4.12 fix: ensure at least 1.5 × line-height relative to current font size so
  // text is not clipped when users apply custom text-spacing. Using `1.5em` keeps the
  // proportion even if the base font size changes. // Assumption: component inherits
  // the browser default line-height unless overridden by the user.
  line-height: 1.5rem;
  /* WCAG 1.4.3: Replace with high-contrast blue */ // Assumption
  background-color: #055485;

}

/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   ---------------------------------------------------------------------------
   Home-page header & sidebar contain multiple interactive controls (buttons, links
   with role="button", icon toggles) that previously relied on the default
   browser focus outline which is thin and low-contrast (usually #333 dotted
   outline) and sometimes suppressed by the Ant Design reset.

   Provide a consistent high-contrast 3 px blue focus ring so keyboard users can
   reliably perceive keyboard focus across all header and sidebar controls.
   The colour #1a73e8 was already introduced by earlier fixes and provides a
   ≥ 3 : 1 contrast ratio on both the white header background and the dark
   #2D323D sidebar background.                                              */

button:focus-visible,
[role="button"]:focus-visible,
.sidebar-item a:focus-visible,
.hover-report:focus-visible,
.menu-sidebar-mobile:focus-visible {
  outline: 3px solid #2D323D; // Assumption: #1a73e8 ≥ 3 : 1 against adjacent colours
  outline-offset: 2px;
}

/* WCAG 1.4.11 – Override focus ring colour for controls that sit on the dark
   sidebar background (#2D323D).  The generic blue outline above only achieves
   a 2.85 : 1 contrast ratio which falls slightly below the required ≥ 3 : 1.
   Switch to white so the focus indicator is clearly perceivable.              */

/* show ring only for keyboard focus */
.menu-sidebar button:focus-visible,
.menu-sidebar [role="button"]:focus-visible,
.menu-sidebar .sidebar-item a:focus-visible,
.ant-menu-root li:focus-visible,
.ant-menu-submenu-title:focus-visible {
  outline: 3px solid #fff !important;  /* 12.8 : 1 vs #2D323D */
  outline-offset: -2px !important;
}

/* optional: clear the ring when focus came from a click
   (covers old styles that might add an outline on plain :focus) */
.menu-sidebar button:focus:not(:focus-visible),
.menu-sidebar [role="button"]:focus:not(:focus-visible),
.menu-sidebar .sidebar-item a:focus:not(:focus-visible),
.ant-menu-root li:focus:not(:focus-visible),
.ant-menu-submenu-title:focus:not(:focus-visible) {
  outline: none !important;
}

.menu-sidebar .sidebar-item a:focus-visible {
  width: 100% !important;
}

.new_button:focus-visible {
  outline-offset: 2px !important;
}

.ant-menu-dark.ant-menu-inline:focus-visible {
  outline: 3px solid #ffffff !important; // Assumption: white outline ≈12.8 : 1 vs #2D323D
  outline-offset: 2px !important;
}

#homepage-logo:focus-visible {
  outline-offset: -2px !important;
}

/* Sidebar arrow icon toggle (chevron) – ensure focusable i element is also
   visible when keyboard-focused. */
.sidebar-logo i:focus-visible {
  outline: #2D323D 3px solid; // Same colour for consistency
  outline-offset: 2px;
}

/* -----------------------------
   Improve non-text contrast for the vertical divider lines in the header that
   visually separate groups of controls. The previous light grey (#bfbfbf)
   provided only ≈ 1.25 : 1 contrast on white, falling below the required
   ≥ 3 : 1 ratio for graphical objects important for understanding content.
   Darken to #757575 (≈ 4.5 : 1 on white) which is already used elsewhere in
   the application for the same purpose.                              */

.vertical-line,
.vertical-line2 {
  border-left: 1px solid #757575; // Assumption: #757575 ≥ 3 : 1 on white
}
.upgrade-btn{ // WCAG 1.4.4 – use relative units so upgrade button grows with user text zoom
  width: 7.5rem; // 120px → 7.5rem
  min-height: 2.5rem; // 40px → 2.5rem
  font-size: 1.5rem; // 24px → 1.5rem
  // WCAG 1.4.12 fix: mirror change made for `.new_button` above. Keep relative spacing so
  // the button can accommodate increased word/letter spacing without clipping. // Assumption
  line-height: 1.5em;
  /* WCAG 1.4.3: Replace with high-contrast blue */ // Assumption
  background-color: #055485;

}

.menu-sidebar {
  z-index: 10;
  // min-height: 100vh;
  margin-top: 64px;
  box-shadow: 2px 0 6px rgba(0,21,41,.35);
  overflow: auto;
  height: 100%;
  position: fixed;
  left: 0;
  background-color: @base-color;
}

.menu-sidebar ul {
  background-color: @base-color;
}

.submenu-sidebar ul {
  background-color: darken(@base-color, 10%);
  li{
    margin: 0;
  }
}

.app-layout {
  //width: 1440px;
  //min-width: 1440px;
  //max-width: 1440px;
  min-height: 100vh;
}


.header-trigger {
    cursor: pointer;
    transition: all .3s,padding 0s;
    color: white;
}

.badge { // WCAG 1.4.4 – relative sizing so badge text is readable at 200 % zoom
  position: absolute;
  right: 0.625rem; // 10px → 0.625rem

  min-width: 1.375rem; // 22px → 1.375rem
  min-height: 1.375rem; // 22px → 1.375rem
  line-height: 1.375rem; // 22px → 1.375rem
  font-size: 0.75rem; // 12px → 0.75rem

  border-radius: 50%;
  border: 1px solid white;
  text-align: center;

  /* WCAG 1.4.3: switched to high-contrast red */ // Assumption
  background: #D40D00;
}


.sidebar-logo { // WCAG 1.4.4 – convert pixel spacing to rem
  min-height: 3rem; // 48px → 3rem
  margin-left: 2rem; // 32px → 2rem
  line-height: 3rem; // 48px → 3rem
}

.sidebar-logo i { // WCAG 1.4.4 – relative icon size
  font-size: 1.25rem; // 20px → 1.25rem
  vertical-align: middle;
  color: white;
  &:hover{
    color: #017BC6;
  }
}

.logo{ // WCAG 1.4.4 – responsive logo dimensions
  width: 9rem; // 144px → 9rem
  height: 3.875rem; // 62px → 3.875rem
  margin-left: 1.5rem; // 24px → 1.5rem
  margin-right: 1.5rem; // 24px → 1.5rem
}

/* Homepage logo link styling */
#homepage-logo {
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
  
  &:focus-visible {
    outline: 3px solid #2D323D;
    outline-offset: 2px;
  }
  
  &:hover {
    opacity: 0.8;
  }
}
.menu-sidebar-mobile{ // WCAG 1.4.4 – relative sizing for mobile sidebar toggle
  margin-left: 0.3125rem; // 5px
  margin-right: 0.3125rem; // 5px
  padding: 0.625rem; // 10px
  font-size: 1.375rem; // 22px
  /* WCAG 1.4.3: High-contrast blue */ // Assumption
  color: #055485;
  cursor: pointer;
}
.logo{
  width: 144px;
  height: 62px;
  margin-left: 24px;
  margin-right: 24px;
}


.sidebar-item a{ // WCAG 1.4.4 – convert font-size to rem
  color: white;
  font-weight: 400;
  font-size: 0.875rem; // 14px
  i {
    margin-right: 1.5rem; // 24px → 1.5rem
  }
}

.hover-selected a{
  &:hover{
  font-weight:bold;
    color: white;
  }
}

.hover-unselected a{
  &:hover{
  font-weight:bold;
    /* WCAG 1.4.3: High-contrast blue */ // Assumption
    color: #055485;
  }
}

.hover-report{
  color: white;
  &:hover{
  font-weight:bold;
  }
  padding-right: 100px;
  padding-top: 10px;
  padding-bottom: 10px;
  z-index:10;
}
// ::ng-deep .ant-menu-submenu-arrow::before, .ant-menu-submenu-arrow::after{
//        color: greenyellow;
//
//}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
  position: fixed;
}

.app-header {
  display: flex;
  align-items: center;
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  font-weight: 300;
  border-bottom: 1px solid #676767;
}
.app-header2 {
  display: block;
  align-items: center;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  font-weight: 300;
  width: 100%;
  width: -moz-available;
  width: -webkit-fill-available;
  width: fill-available;
}

.sandbox-switch { // WCAG 1.4.4 – relative font-size
    font-weight: 300;
    font-size: 1.125rem; // 18px
}



.header-sandbox {
    background: #2D323D;
    color:#fff;
}

@media (min-width:641px) {
.right-layout-collapsed { // WCAG 1.4.4 – sidebar offset in rem
    margin-left: 5rem; // 80px
  }
.right-layout-extended {
    margin-left: 5rem; // 80px
  }
}

@media (min-width:640px) {

.right-layout-extended {
    margin-left: 16rem; // 256px
  }
  nz-content {
    margin-left: 1.5rem; // 24px
    margin-right: 1.5rem; // 24px
  }
   .warningMessage {
    margin-left: 1.5rem;
    margin-right: 1.5rem;
  }


}
@media (max-width:640px) {
  .logo{ // already converted above but mobile overrides
    margin-left: 0.625rem; // 10px
    margin-right: 0.625rem; // 10px
  }
  .card_size {
    min-height: 11.5625rem !important; // 185px
  }
}

.main-layout {
    margin-top: 4rem; // 64px
    height: 100%;
}

.inner-content {
  padding: 1.5rem; // 24px
  background: #fbfbfb;
  height: 100%;
}

// Updated colour to meet ≥ 3 : 1 contrast (WCAG 1.4.11). Original light grey (#bfbfbf) failed on white.
.vertical-line {
  display: flex;
  align-items: center;
  border-left: 1px solid #757575; // Assumption: #757575 ≈ 4.5 : 1 against white
  line-height: 1.5rem; // 24px
  min-height: 3rem; // 48px
  width: auto;
  padding-left: 1.5rem;

  margin-top: 0.46875rem; // 7.5px
  margin-bottom: 0.53125rem; // 8.5px

  font-size: 1.5rem; // 24px
  font-weight: 24px; // untouched value semantics
  color: #2D323D;
}
// WCAG 1.4.11 – darken divider line inside mobile header likewise.
.vertical-line2 {
  border-left: 1px solid #757575; // Assumption: same as above
  line-height: 3rem; // 48px
  width: auto;

  margin-top: 7.5px;
  margin-bottom: 8.5px;

  font-size: 1.5rem;
  font-weight: 24px;
  color: #2D323D;
}

.right {
  margin-left: auto;
  display: flex;
  align-items: center;
}

::ng-deep .ant-switch-checked i {
    background: #017BC6 !important;
}

::ng-deep .ant-switch {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25)), linear-gradient(to right, #fff, #fff);
}

#new_template01.TemplatesContainer {
	display: flex;
	flex-direction: row;
}

#new_template01.TemplatesContainer > .TemplateItem {
	flex: 1;
}

.TemplateItem p{
  font-size: 14px;
    color: #2D323D;
    margin: 0 0 10px;
}

.template-icons { // WCAG 1.4.4 – relative icon container sizing
  min-height: 6.25rem; // 100px
  color: rgb(1, 131, 211);
  font-size: 4.375rem; // 70px
	line-height: 6.25rem; // 100px
}

.widget{
  margin-bottom: 24px;
    border: 1px solid #e6e6e6;
  text-align: center;
}

.mb-50 {
  margin-bottom: 3.125rem !important; // 50px
}

.card_size {
  min-height: 14.6875rem; // 235px
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.card_size:hover{
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    -webkit-box-shadow: 0px 0px 5px 1px rgba(1, 131, 211, 0.67);
    -moz-box-shadow:    0px 0px 5px 1px rgba(1, 131, 211, 0.67);
    box-shadow:         0px 0px 5px 1px rgba(1, 131, 211, 0.67);

}

.pt-30 {
  padding-top: 1.875rem !important; // 30px
}


// modal close button icon
::ng-deep .ant-modal-close-x{
  //width: 20px;
  min-height: 2.8125rem; // 45px
  line-height: 2.8125rem; // 45px
  font-size: 0.75rem; // 12px
  color: #2D323D;
  &:hover{
    color: black;
  }
}

.fontstyle{
    font-weight: 600;
    color: #000;
    margin-top: 0;
  font-size: 1.125rem;
}

.maintenance-msg-header {
  background-color: #fffbe6;
  border-width: 2px;
  border-color: #fff0ba;
  border-style: solid;
  width:100%;
  min-height: 3.75rem; // 60px
  padding: 0.3125rem; // 5px
  padding-left: 0.625rem; // 10px
  position: fixed;
  z-index: 2;
}

.disabled-template-card {
  background-color: #f3f3f3;
  color: #808080;
  cursor:not-allowed;
  .template-icons{
    color: lightgrey;
  }
}

::ng-deep nz-table-inner-default {
  overflow: scroll;
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;
}
::ng-deep nz-table-inner-default::-webkit-scrollbar  {
  display: none;  /* Safari and Chrome */

}

.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #0078c2;

  /* WCAG 2.1 – 1.4.1 Use of Colour
     Previously, the active sidebar item was indicated solely by a blue background.
     Users who cannot perceive colour (e.g. in high-contrast or greyscale modes)
     would lose this context.  The update adds a left-hand indicator bar so the
     current location is communicated via shape as well as colour. */ // Assumption: 4 px bar accepted by design
  position: relative; // ensure ::before positions correctly
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor; // uses text colour, ensuring sufficient contrast
  }
}

.-ml-1\.5 {
  margin-left: 0 !important;
}

.quick-links {
  display: none !important;
}