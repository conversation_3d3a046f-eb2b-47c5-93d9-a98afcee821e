import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from "@shared/shared.module";

import { HomePageRoutingModule } from './home-page-routing.module';

import {DashboardModule} from "./dashboard/dashboard.module";
import { UsersAndTeamsModule } from './users-and-teams/users-and-teams.module';
import { SecurityComponent } from './reports/security/security.component';
import { CancelComponent } from './reports/cancel/cancel.component';
import { UsageComponent } from './reports/usage/usage.component';
import { ContactsComponent } from './contacts/contacts.component';

import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BulkExportComponent } from './bulk-export/bulk-export.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { DocumentTableComponent } from './documents/document-table/document-table.component';
import { InboxComponent } from './documents/inbox/inbox.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { DocumentListComponent } from './documents/document-list/document-list.component';
import { DocumentActionsComponent } from './documents/document-actions/document-actions.component';
import { TemplateListComponent } from './documents/template-list/template-list.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ManagePlanComponent } from './plan/manage-plan/manage-plan.component';
import { PaymentMethodComponent } from './plan/payment-method/payment-method.component';
import { InvoiceComponent } from './plan/invoice/invoice.component';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { SealsComponent } from './seals/seals.component';
import { ApplicationsIframeComponent } from './applications-iframe/applications-iframe.component';
import { ManageDocumentsComponent } from './documents/manage-documents/manage-documents.component';
import { PaymentComponent } from './plan/payment/payment.component';
import { ChangePlanComponent } from './plan/change-plan/change-plan.component';



@NgModule({
    declarations: [
    SecurityComponent,
    CancelComponent,
    UsageComponent,
    ContactsComponent,
    BulkExportComponent,
    DocumentTableComponent,
    InboxComponent,
    DocumentListComponent,
    DocumentActionsComponent,
    TemplateListComponent,
    ManagePlanComponent,
    PaymentMethodComponent,
    InvoiceComponent,
    SealsComponent,
    ApplicationsIframeComponent,
    ManageDocumentsComponent,
    PaymentComponent,
    ChangePlanComponent
    ],

    imports: [
        CommonModule,
        SharedModule,
        HomePageRoutingModule,
        DashboardModule,
        UsersAndTeamsModule,
        NzTableModule,
        NzButtonModule,
        FormsModule,
        ReactiveFormsModule,
        NzModalModule,
        NzInputModule,
        NzIconModule,
        NzAlertModule,
        NzSpinModule,
        NzToolTipModule,
        NzDropDownModule,
        DragDropModule,
        NzUploadModule    ]
})
export class HomePageModule { }
