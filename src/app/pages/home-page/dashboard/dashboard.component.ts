// Added "class" and "subClass" support to Card interface to allow styling updates that increase non-text contrast (WCAG 1.4.11)
import {Role, ConsoleType, Roles, Plan, Plans} from '@core/models' ;
// Added Angular Title service to dynamically set <title> for WCAG 2.4.2 compliance.
import { Title } from '@angular/platform-browser';
import { AfterViewInit, Component, OnInit, Optional } from '@angular/core';
import {ResolveEnd, Router} from "@angular/router";
import {UserService} from "@core/services";
import { MediaService } from '@core/services';
import { NzModalService } from 'ng-zorro-antd/modal';

type CardName = "Service Period" | 'Users' |  'Teams' | 'Billing' |
    'Branding' | "Templates" |  'Support Channels' | 'Document Report' |
    "Add shortcut" | "Recent Activities" | "Plan & Usage"

interface Card{
    icon:string,
    width:number,
    title:string,
    class?:string // Allow us to specify classes for the component.
    subClass?:string
}


@Component({
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.less']
})
export class DashboardComponent implements OnInit,AfterViewInit {
    roleID:number = -1
    pageName:ConsoleType = "user"
    isTeamPlan: boolean = false

    readonly roles = Roles
    readonly plans = Plans
    readonly allCards: Record<CardName, Card> ={
        "Service Period":   { icon: 'money-check-dollar',  width: 12, title:$localize `Service Period`},
        "Users":            { icon: 'user',  width: 12, title:$localize `Users`, class:'min300 '},
        "Teams":            { icon: 'users',  width: 12, title:$localize `Teams`, class:'min300'},
        "Billing":          { icon: 'money-check-dollar',  width: 12, title:$localize `Billing`, class:'min300'},
        "Branding":         { icon: 'star',  width: 12, title:$localize `Branding`, class:'min300'},
        "Templates":        { icon: 'file-lines', width: 12 , title:$localize `Templates`, class:'min300'},
        "Support Channels": { icon: 'circle-question', width: 12, title:$localize `Support Channels`, class:'min300'},
        "Document Report":  { icon: 'file',  width: 24, title:$localize `Document Report`, class:'min300 '},
        "Add shortcut":     { icon: '',  width: 12, title:$localize `Add shortcut`},
        "Recent Activities":{ icon: 'clock-rotate-left',  width: 24, title:$localize `Recent Activities`},
        "Plan & Usage":     { icon: 'money-check-dollar', width: 24, title:$localize `Plan & Usage`, subClass:"center-mobile"}
    }

    // Holds the current visual order of dashboard cards.  This list is the
    // single source of truth for the template so both drag-and-drop *and* the
    // new pointer-accessible “Move earlier / later” buttons operate on the
    // same data structure – WCAG 2.5.1.
    cards: CardName[] = []

    public currentMouseOver: number = -1;

    constructor(
        private router: Router,
        private userService:UserService,
        public mediaService: MediaService,
        // @Optional() prevents DI errors in unit tests that omit a Title provider. – WCAG 2.4.2
        @Optional() private titleService?: Title // Assumption: Provided in app module for production builds
    ) { }

     ngOnInit() {

        this.setHeaderPageName(this.router.url)
        this.router.events.subscribe(
            event => (event instanceof ResolveEnd) && this.setHeaderPageName(event.url)
        )
        this.userService.getRoleID().subscribe(val => {
            this.roleID = val;
            this.computeCards();
        })
        this.userService.isTeamPlan().subscribe(val => {
            this.isTeamPlan = val;
            this.computeCards();
        })

        // Initial population (values may still be defaults but ensures array is never empty)
        this.computeCards();
    }
    setHeaderPageName(url:string) : void{
        switch (url.split('/')[1]){
            case 'admin-console':
                this.pageName = 'admin-console'
                break
            case 'user':
                this.pageName = 'user'
                break
        }

        // Only set the title if we're actually on the dashboard route
        if (url.includes('/dashboard') || url.endsWith('/')) {
            // Keep the document <title> in sync with the visible heading so that the
            // topic of the page is conveyed to all users, including those relying on
            // assistive technologies or browser UI such as tabs and bookmarks.
            // Complies with WCAG 2.4.2 (Page Titled – Level A, required for Level AA).
            this.updateDocumentTitle();
        }

        // Re-evaluate card list when context changes so pointer move controls
        // stay in sync. – WCAG 2.5.1
        this.computeCards();
    }

    /**
     * Updates the document <title> so it remains descriptive of the current
     * dashboard context (Admin console vs. User), thereby satisfying WCAG 2.4.2.
     * Pattern used: "<Context> Dashboard" to keep titles unique yet concise.
     */
    private updateDocumentTitle(): void {
        if (!this.titleService) {
            return; // Title service not provided (e.g. isolated unit test)
        }

        const context = this.pageName === 'admin-console' ? 'Admin Dashboard' : 'Dashboard';
        this.titleService.setTitle(context);
    }

    ngAfterViewInit(): void {
        let me = this;
        // The template now prefixes each card’s `id` with "dashboard-card-"
        // (see HTML change) to avoid cross-document ID collisions.  We retain
        // the numeric index for internal use by shifting the value into the
        // `data-index` attribute, which we parse here instead of `elem.id`.
        document.querySelectorAll(".editable-card").forEach(elem => {
            elem.addEventListener("mouseover", function () {
                const idx = (elem as HTMLElement).getAttribute('data-index');
                me.currentMouseOver = idx ? parseInt(idx, 10) : -1;
            }, false);
        });

        // WCAG 2.4.4 & 2.5.3 – ensure interactive elements inside the
        // dashboard respect "Label in Name" by synchronising their accessible
        // names (`aria-label`) with the exact visible text users see on
        // screen.  We perform the patch once after initial view init to avoid
        // deep template changes across many nested components. // Assumption:
        // dashboard card content is static after first render.
        this.patchInteractiveLabels();

        // Resolve duplicate ID collisions introduced by the ApexCharts
        // library so the document meets WCAG 4.1.1 (Parsing).
        this.deDuplicateApexChartIds();

        // Reset focus to the beginning of the page to ensure natural tab order
        this.resetFocusToPageStart();
    }

    /**
     * Adds an `aria-label` to all anchor elements within the dashboard that
     * lack one, using their full text content as the label.  This low-impact
     * strategy avoids modifying child components’ templates (which are out of
     * scope for this task) while still satisfying WCAG 2.4.4 at Level AA.
     */
    /**
     * Synchronises `aria-label` attributes for anchors, native buttons and any
     * element explicitly marked `role="button"` so their accessible names
     * contain the exact same string users see visually.  This fulfils WCAG
     * 2.5.3 (Label in Name – Level AA) and also guarantees link purpose per
     * WCAG 2.4.4 when no label is present.
     */
    private patchInteractiveLabels(): void {
        // Defer execution until content projected by descendant components has
        // rendered.
        setTimeout(() => {
            const dashboardRoot: HTMLElement | null = document.getElementById('dashboard');
            if (!dashboardRoot) {
                return;
            }

            // Helper that aligns `aria-label` with the element's visible text
            // when that text is non-empty.
            const syncLabel = (el: HTMLElement): void => {
                const visibleText = (el.textContent || '').trim();
                if (!visibleText) {
                    return; // Icon-only control – leave author-provided aria-label intact.
                }

                // If the element already has an aria-label that contains the
                // visible text, no action is needed. The comparison is
                // case-sensitive because speech-recognition software matches
                // exact phrases.
                const existing = el.getAttribute('aria-label');
                if (existing && existing.includes(visibleText)) {
                    return;
                }

                el.setAttribute('aria-label', visibleText);
            };

            // 1. Standard links
            dashboardRoot.querySelectorAll('a').forEach(anchor => syncLabel(anchor as HTMLElement));

            // 2. Native buttons
            dashboardRoot.querySelectorAll('button').forEach(btn => syncLabel(btn as HTMLElement));

            // 3. Custom role="button" elements (e.g. divs, spans)
            dashboardRoot.querySelectorAll('[role="button"]').forEach(el => syncLabel(el as HTMLElement));
        });
    }

    /**
     * WCAG 4.1.1 (Parsing – duplicate IDs):
     * --------------------------------------------------------------
     * The 3rd-party ApexCharts library used by some dashboard cards
     * (e.g. "Plan & Usage") renders <svg> fragments that contain the
     * hard-coded IDs `apexcharts-radialbarTrack-0`,
     * `apexcharts-radialbarTrack-1`, etc.  Because each chart instance
     * generates the *same* ID values, the final DOM ends up with
     * duplicates, violating the uniqueness requirement of HTML IDs.
     * Assistive technologies rely on unique IDs to create an
     * unambiguous accessibility tree; duplicates can lead to
     * unexpected navigation jumps or the wrong element receiving
     * focus/description.
     *
     * We cannot modify the chart component directly (out of scope) so
     * we post-process the DOM after initial rendering:
     *   1.  Enumerate every SVG that belongs to an ApexCharts instance
     *       under the dashboard region.
     *   2.  For each hard-coded ID that would otherwise collide, append
     *       a unique suffix derived from the chart index so the ID
     *       becomes, for example, `apexcharts-radialbarTrack-0--c1`.
     *   3.  Update all child attributes that reference the original
     *       value via URL/hash syntax (e.g. `fill="url(#…)",
     *       clip-path="url(#…)"`) so visual appearance remains intact.
     *
     * Assumption: Charts are rendered once on component init and are
     * not dynamically added/removed afterwards.  This allows us to run
     * the fix a single time without observing future mutations.
     */
    private deDuplicateApexChartIds(): void {
        setTimeout(() => {
            const dashboardRoot = document.getElementById('dashboard');
            if (!dashboardRoot) {
                return;
            }

            const svgCharts = dashboardRoot.querySelectorAll('svg');
            let chartCounter = 0;

            svgCharts.forEach(svgEl => {
                const idMap: Record<string, string> = {};

                // List of ID values generated by ApexCharts that are
                // known to collide. If future versions introduce new
                // patterns they can be added here.
                const troublesomeIds = [
                    'apexcharts-radialbarTrack-0',
                    'apexcharts-radialbarTrack-1',
                    'apexcharts-radialbarTrack-2',
                    'apexcharts-radialbarTrack-3',
                ];

                troublesomeIds.forEach(idValue => {
                    const elementWithId = svgEl.querySelector(`#${idValue}`);
                    if (elementWithId) {
                        // Only rename if the same ID already exists elsewhere
                        // in the document (i.e. duplicate).  This avoids
                        // needlessly touching the first occurrence which is
                        // already unique at this point in the loop.
                        const isDuplicate = document.querySelectorAll(`#${idValue}`).length > 1;
                        if (isDuplicate) {
                            const newId = `${idValue}--c${chartCounter}`;
                            idMap[idValue] = newId;
                            elementWithId.setAttribute('id', newId);
                        }
                    }
                });

                // Update referencing attributes inside the same SVG.
                if (Object.keys(idMap).length) {
                    const updateAttrs = ['fill', 'stroke', 'filter', 'clip-path', 'mask'];
                    const elements = svgEl.querySelectorAll('*');
                    elements.forEach(el => {
                        updateAttrs.forEach(attrName => {
                            const attrVal = (el as HTMLElement).getAttribute(attrName);
                            if (!attrVal) { return; }
                            Object.keys(idMap).forEach(oldId => {
                                const newId = idMap[oldId];
                                const pattern = new RegExp(`#${oldId}(?![\w-])`, 'g');
                                if (pattern.test(attrVal)) {
                                    (el as HTMLElement).setAttribute(attrName, attrVal.replace(pattern, `#${newId}`));
                                }
                            });
                        });
                    });
                }

                chartCounter++;
            });
        });
    }


    /**
     * Resets focus to the beginning of the page to ensure natural tab order
     * when the dashboard loads, preventing focus from jumping to card elements.
     */
    private resetFocusToPageStart(): void {
        // Use setTimeout to ensure this runs after all child components have initialized
        setTimeout(() => {
            // Focus the body element to reset to the natural tab order starting point
            document.body.focus();
            // Remove focus to ensure the next tab starts from the beginning
            document.body.blur();
        }, 100);
    }

    /**
     * Returns the current list used by the template.
     * Replaces the previous on-the-fly calculation with the pre-computed
     * `this.cards` array so re-ordering actions can mutate a single canonical
     * source – required for the new pointer-based move buttons. – WCAG 2.5.1
     */
    get_cards(): CardName[] {
        return this.cards;
    }
    
    /**
     * Checks if a dashboard card is non-clickable (informational only).
     * These cards should be removed from the tab order.
     * Different cards are clickable depending on user view vs admin view.
     * @param cardName The name of the card to check
     * @returns true if the card is non-clickable, false if it's clickable
     */
    isNonClickableCard(cardName: CardName): boolean {
        if (this.pageName === 'admin-console') {
            // Admin view: different cards are clickable
            const adminNonClickableCards: CardName[] = [
                'Document Report', 
                'Templates',
                'Support Channels'
            ];
            return adminNonClickableCards.includes(cardName);
        } else {
            // User view: different set of non-clickable cards
            const userNonClickableCards: CardName[] = [
                'Plan & Usage'
            ];
            return userNonClickableCards.includes(cardName);
        }
    }

    /**
     * Populates `this.cards` according to the user role, plan and current
     * console context.  The logic mirrors the original `get_cards` method but
     * stores the result so it can later be mutated by drag-and-drop or the
     * new move-controls that provide a single-pointer alternative to the
     * path-based drag gesture. – WCAG 2.5.1
     */
    private computeCards(): void {
        let result: CardName[] = [];

        if (this.pageName === 'admin-console') {
            switch (this.roleID) {
                case Roles.SUPER_ADMIN:
                    result = ["Plan & Usage", "Document Report", "Users", "Teams", "Branding", "Support Channels"];
                    break;
                case Roles.TEAM_ADMIN:
                    result = ["Plan & Usage", "Document Report", "Users", "Support Channels"];
                    break;
                case Roles.BILLING_ADMIN:
                    result = ["Plan & Usage", "Document Report", "Billing", "Branding", "Support Channels"];
                    break;
                case Roles.DOC_ADMIN:
                    result = ["Plan & Usage", "Document Report", "Templates", "Support Channels"];
                    break;
                default:
                    result = [];
            }
        } else {
            if (this.isTeamPlan) {
                if (this.roleID !== Roles.REGULAR_USER) {
                    result = ["Plan & Usage", "Document Report", "Templates", "Support Channels", "Recent Activities"];
                } else {
                    result = ["Document Report", "Templates", "Support Channels", "Recent Activities"];
                }
            } else {
                result = ["Plan & Usage", "Document Report", "Billing", "Templates", "Support Channels", "Recent Activities"];
            }
        }

        // Overwrite existing array content without replacing reference, so
        // Angular change detection recognises the update even inside an async
        // callback.
        this.cards.length = 0;
        this.cards.push(...result);
    }
}
