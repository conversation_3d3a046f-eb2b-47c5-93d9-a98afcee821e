.cardcontent--content {
    width: 100%;
}

// WCAG 2.1 – 1.4.1 (Use of Colour): Add underline and strong focus outline to
// template links so their interactive nature is perceivable without colour.
.list a {
  text-decoration: underline; // Assumption: underline does not conflict with truncation.
  /* WCAG 1.4.12 – Allow text to wrap and expand when users increase
     letter, word or line spacing. Tailwind's `truncate` utility previously
     enforced `nowrap`/`hidden` which could clip content. By explicitly
     setting these properties to their defaults, we ensure no loss of
     information or functionality under custom spacing styles. */ // Assumption: wrapping acceptable within card layout.
  white-space: normal;
  overflow: visible;
  line-height: normal; // ensures ≥1.5× can be honoured
}

.list a:focus,
.list a:focus-visible {
  // WCAG 2.4.7 – Provide outline for :focus-visible as well to ensure
  // reliable focus indication across modern browsers.
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

.cardcontent--actions a {
  text-decoration: underline;
}

.cardcontent--actions a:focus,
.cardcontent--actions a:focus-visible {
  // WCAG 2.4.7 – Extend indicator to :focus-visible.
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 – styles for skip link
   ------------------------------------------------------------------------ */
.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: 2px solid #1a73e8;
}