// CHANGE (2025-08-07): WCAG 2.4.2 – add Title provider to testing module.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing'; // WCAG 2.4.5 – routerLink used in action link

import { SharedTemplatesComponent } from './shared-templates.component';

describe('SharedTemplatesComponent', () => {
    let component: SharedTemplatesComponent;
    let fixture: ComponentFixture<SharedTemplatesComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ SharedTemplatesComponent ],
            imports: [RouterTestingModule],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(SharedTemplatesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
