<!-- WCAG 2.4.6 – added sr-only heading for descriptive label and linked via aria-labelledby -->
<main id="sharedTemplatesMain" class="cardcontent" aria-labelledby="sharedTemplatesHeading">
    <h2 id="sharedTemplatesHeading" class="sr-only" i18n>Shared templates</h2>
    <div class="cardcontent--content flex-col xs:text-left xs:!items-start">
        <ul class="list">
            <li class="text-lg font-bold" *ngFor="let template of templates">
                <!-- WCAG 1.4.12 fix: removed Tailwind `truncate` utility that forces
                     `white-space: nowrap; overflow: hidden; text-overflow: ellipsis;`
                     resulting in loss of content when users increase letter-/word-spacing. -->
                <!-- WCAG 1.4.13 – removed native browser tooltip generated by
                     the `title` attribute because it appears on hover only,
                     is not keyboard-accessible and cannot be dismissed or kept
                     open, violating the dismissible/hoverable/persistent
                     requirements for additional content.  The visible link
                     text already conveys the same information, so the
                     attribute is unnecessary. -->
                <a *ngIf="isLoaded" [href]="template.link" (keydown.space)="onSpaceKey($event)">{{template.title}}</a>
            </li>
            <!-- Decorative loading skeleton hidden from assistive tech (WCAG 1.1.1) -->
            <nz-skeleton *ngIf="!isLoaded"  [nzActive]="true" [nzTitle]="false" [nzParagraph]="{ rows: 5 }" aria-hidden="true"></nz-skeleton>

        </ul>
    </div>
    <div class="cardcontent--actions">
        <a routerLink="/templates" (keydown.space)="onSpaceKey($event)" i18n>View All Templates</a>
    </div>
</main>
