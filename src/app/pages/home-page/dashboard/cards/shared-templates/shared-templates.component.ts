// CHANGE (2025-08-07): WCAG 2.4.1 – AfterViewInit for focus handling.

// CHANGE (2025-08-07): WCAG 2.4.2 – import Title to set document title.
import { Component, OnInit, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';

declare const $localize: any; // Assumption
import {DocumentService, Template} from "@core/services";

@Component({
    selector: 'app-shared-templates',
    templateUrl: './shared-templates.component.html',
    styleUrls: ['./shared-templates.component.less']
})
export class SharedTemplatesComponent implements OnInit, AfterViewInit {

    templates:Template[] = []
    isLoaded:boolean = false;
    constructor(
        private documentService: DocumentService,
        private elementRef: ElementRef, // Assumption
        private titleService: Title // Assumption
    ) {
        documentService.getTemplates().subscribe(v=> {
            this.templates = v;
        this.isLoaded = true;
        })

        // Title management is now handled by the parent dashboard component
    }

    ngOnInit(): void {
    }

    // ------------------------------------------------------------------
    // WCAG 2.4.1 – focus management
    // ------------------------------------------------------------------
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#sharedTemplatesMain');
            if (main) {
                main.focus();
            }
        });
    }

    onSpaceKey(event: Event): void {
        event.preventDefault();
        (event.target as HTMLElement).click();
    }

}
