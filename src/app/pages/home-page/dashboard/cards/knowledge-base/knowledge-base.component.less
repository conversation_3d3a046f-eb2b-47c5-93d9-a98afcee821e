// WCAG 2.4.6 – utility class to visually hide elements while keeping them accessible to assistive technology
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

// WCAG 2.1 – 1.4.1 (Use of Colour): provide non-colour visual cue (underline)
// for knowledge base links so users who cannot perceive colour can still
// identify them as interactive elements.
.list a {
  text-decoration: underline; // Assumption: design allows underline.
}

.list a:focus,
.list a:focus-visible {
  // WCAG 2.4.7 – Add :focus-visible support so the indicator appears whenever
  // the element receives keyboard focus, even if the browser suppresses the
  // generic outline after pointer interaction.
  outline: 3px solid #2D323D; // Provides visible focus indicator beyond colour.
  outline-offset: 2px;
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 – styles for skip link
   ------------------------------------------------------------------------ */
.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: 2px solid #1a73e8;
}
