// CHANGE (2025-08-07): WCAG 2.4.2 – add Title provider.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';

import { KnowledgeBaseComponent } from './knowledge-base.component';

describe('KnowledgeBaseComponent', () => {
    let component: KnowledgeBaseComponent;
    let fixture: ComponentFixture<KnowledgeBaseComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ KnowledgeBaseComponent ],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(KnowledgeBaseComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
