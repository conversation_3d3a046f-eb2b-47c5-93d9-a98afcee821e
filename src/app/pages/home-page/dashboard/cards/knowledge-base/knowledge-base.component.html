<!-- WCAG 2.4.6 – provide sr-only heading and connect via aria-labelledby for descriptive label. -->
<main id="knowledgeBaseMain" class="cardcontent xs:text-left" aria-labelledby="knowledgeBaseHeading">
    <h2 id="knowledgeBaseHeading" class="sr-only" i18n>Knowledge base resources</h2>
    <ul class="list">
        <!-- WCAG 2.4.6 – inform users that links open in a new tab; provide aria-label and hidden text -->
        <li class="text-lg font-bold" *ngFor="let feature of features">
            <a
                [href]="feature.link"
                target="_blank"
                rel="noopener"
                (keydown.space)="onSpaceKey($event)"
                [attr.aria-label]="feature.title + ' (opens in new tab)'"
            >
                {{feature.title}}
                <!-- Assumption: visually hidden text is best for screen-reader users while keeping the visual layout unchanged -->
                <span class="sr-only" i18n>(opens in new tab)</span>
            </a>
        </li>
    </ul>
</main>
