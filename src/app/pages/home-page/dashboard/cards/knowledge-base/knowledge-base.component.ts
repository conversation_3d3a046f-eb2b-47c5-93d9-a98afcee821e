// CHANGE (2025-08-07): WCAG 2.4.1 – add AfterViewInit for focus handling.

// CHANGE (2025-08-07): WCAG 2.4.2 – import Title for setting document title.
import { Component, OnInit, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';

declare const $localize: any; // Assumption: provided by @angular/localize


interface item{
    link:string
    title:string
}

@Component({
    selector: 'app-knowledge-base',
    templateUrl: './knowledge-base.component.html',
    styleUrls: ['./knowledge-base.component.less']
})
export class KnowledgeBaseComponent implements OnInit, AfterViewInit {

    features:item[] =[
        {
            title: $localize `Knowledge Base`,
            link:  "https://signority.com/help"
        },
        // {
        //     title: $localize `Chat`,
        //      link: "https://signority.com/help/#hs-chat-open"
        // },
        {
            title: $localize `Log a Support Ticket`,
            link:  "https://www.signority.com/support-center/"
        },
        {
            title: $localize `Email Support`,
            link:  "mailto:<EMAIL>"
        }
        // {
        //     title: $localize `Phone Support`,
        //     link: "https://www.signority.com/contact-us"
        // }
    ]
    constructor(
        private elementRef: ElementRef,
        private titleService: Title // Assumption: available globally
    ) {
        // Title management is now handled by the parent dashboard component
    }

    // Assumption: $localize exists globally via Angular i18n polyfill.
    

    ngOnInit(): void {
    }

    // ------------------------------------------------------------------
    // WCAG 2.4.1 – focus management
    // ------------------------------------------------------------------
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#knowledgeBaseMain');
            if (main) {
                main.focus();
            }
        });
    }

    onSpaceKey(event: Event): void {
        event.preventDefault();
        (event.target as HTMLElement).click();
    }

}
