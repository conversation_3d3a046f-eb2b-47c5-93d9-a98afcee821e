import { ComponentFixture, TestBed } from '@angular/core/testing';
// CHANGE (2025-08-06): WCAG 2.4.2 – provide Title injection for component
// after adding dynamic document title support.
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing'; // WCAG 2.4.5 – breadcrumb navigation uses anchor, but paginator or other parts might reference routerLink in future; include stub now

import { RecentActivitiesComponent } from './recent-activities.component';

describe('RecentActivitiesComponent', () => {
    let component: RecentActivitiesComponent;
    let fixture: ComponentFixture<RecentActivitiesComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RouterTestingModule],
            declarations: [ RecentActivitiesComponent ],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(RecentActivitiesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
