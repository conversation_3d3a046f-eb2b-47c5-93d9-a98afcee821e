.list {
    margin-right: auto;
    margin-top: 1.25rem; // WCAG 1.4.4 – convert margin-top from 20px to 1.25rem so spacing scales with root font size
    padding-left: 1.875rem !important; // WCAG 1.4.4 – convert padding-left from 30px to 1.875rem for scalable indent
}

/* WCAG 1.4.12 (Text Spacing): ensure timeline items can grow when users
   increase line-height, letter- or word-spacing via custom style sheets.
   Reset potentially restrictive values imposed by the ng-zorro component so
   content is not clipped or overlapped. */ // Assumption: override is scoped
   // to this component thanks to ViewEncapsulation.Emulated.
.list ::ng-deep .ant-timeline-item-content {
  line-height: normal;   /* allow ≥1.5× font-size line height */
  white-space: normal;   /* permit wrapping when spacing expands */
  overflow: visible;     /* prevent clipping of enlarged text */
}

/* WCAG 1.4.12: ensure bullet & tail also accommodate additional spacing so
   they remain vertically aligned with the content block. */
.list ::ng-deep .ant-timeline-item {
  padding-bottom: 1.5em; /* provide enough gap for increased paragraph spacing */
}

.recent-activities--pagniation {
  margin-left: auto; // Aligns pagination to right in a flexbox
  /* WCAG 1.4.10 – ensure paginator never forces the entire page to scroll
     horizontally on very small viewports (≤ 320 px).  The Material paginator
     has an internal `min-width: 400px`; overriding the constraint keeps the
     control functional while letting it shrink or wrap as needed. */ // Assumption: override is safe for this isolated card
  max-width: 100%;
  overflow-x: auto; // Single-axis scroll container if internal elements exceed 100 %

  .mat-paginator {
    min-width: 0; // WCAG 1.4.10 – remove hard pixel min-width
    width: 100%;  // Allow paginator to shrink within the wrapper
  }
}

// WCAG 2.1 – SC 1.4.11 (Non-text Contrast):
// The default ng-zorro timeline renders the connector line using a very light
// grey (#f0f0f0 ≈ 1.2 : 1 against white), which fails the minimum 3 : 1
// contrast ratio for non-textual visual components.  Darken the colour so low-
// vision users can clearly perceive the chronological relationship between
// items.  Also increase focus visibility for the paginator’s interactive
// controls.

// Assumption: Angular’s Emulated view encapsulation restricts these styles to
// this component, so overriding the library selectors will not leak globally.

// Improve contrast of the vertical timeline connector line.
.list ::ng-deep .ant-timeline-item-tail {
  background-color: #757575; /* ≥ 4.5 : 1 contrast on white */
}

// Improve contrast of the default timeline bullet outline so it is
// distinguishable when printed in greyscale.
.list ::ng-deep .ant-timeline-item-head {
  border-color: #0078c2; /* Custom blue color for timeline icons */
  background-color: #0078c2; /* Custom blue background for timeline icons */
}

/* High-contrast keyboard focus indicator for paginator buttons */
.recent-activities--pagniation button:focus,
.recent-activities--pagniation button:focus-visible {
  outline: #2D323D 3px solid; /* Brand focus blue – ≥ 3 : 1 on white */
  outline-offset: 2px;
}

// WCAG 2.4.7 (Focus Visible) – provide an explicit outline for the <main>
// region that receives programmatic focus via the component’s skip link so
// users have a clear visual cue of where keyboard navigation has landed.
#recent-activities:focus,
#recent-activities:focus-visible {
  outline: #2D323D 3px solid; // Brand focus blue – ≥ 3 : 1 contrast
  outline-offset: 2px;
}

// WCAG 2.1 – visually hidden utility class for screen-reader only content
.sr-only {
  position: absolute; /* Assumption: remove from visual flow */
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

@media (max-width: 768px) {
  .mat-paginator-range-label {
    margin: 0px !important;
  }
}