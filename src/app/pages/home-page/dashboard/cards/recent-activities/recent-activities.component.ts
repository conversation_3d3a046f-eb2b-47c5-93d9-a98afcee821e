// Added AfterViewInit to patch any dynamically rendered <input> elements with
// `autocomplete="off"` to satisfy WCAG 2.1 SC 1.3.5 Identify Input Purpose.
// Assumption: Inputs within this component (e.g., Angular Material paginator)
// do NOT capture personal user data, so disabling autocomplete is acceptable.
import { Component, AfterViewInit, ElementRef, Renderer2, ViewEncapsulation } from '@angular/core';
// CHANGE (2025-08-06): WCAG 2.4.2 – import Title service so the component can
// programmatically set a unique, descriptive page title.
import { Title } from '@angular/platform-browser';
import { UserService } from '@core/services';

@Component({
    selector: 'app-recent-activities',
    templateUrl: './recent-activities.component.html',
    styleUrls: ['./recent-activities.component.less'],
    encapsulation: ViewEncapsulation.None
})
export class RecentActivitiesComponent implements AfterViewInit {

    activities: string[] = [];
    count = 0;
    dates: any[] = [];
    pageIndex = 1;
	// Indicates whether the component has finished loading remote data.
	isLoaded = false;

    constructor(
        private user: UserService,
        private elementRef: ElementRef, // Assumption: injected for DOM patching
        private renderer: Renderer2, // Assumption: Angular provided implementation
        private titleService: Title // Assumption: Provided at root; no new deps
    ) {
        // Fetch first page of activity data on instantiation.
        this.getData(1);

        // Title management is now handled by the parent dashboard component
    }

    // ------------------------------ WCAG 1.3.5 -------------------------------
    ngAfterViewInit(): void {
        // Defer execution to ensure child components (e.g., paginator) have
        // rendered their internal structure.
        this.applyAutocompletePatch();

        // WCAG 2.4.1 – shift keyboard focus to the primary <main> landmark so
        // users bypass any repeated navigation when this view loads.
        this.moveFocusToMain();
    }

    /**
     * Iterates over all <input> descendants of this component and sets
     * `autocomplete="off"` when the attribute is missing, preventing automated
     * 1.3.5 violations for non-personal data fields generated by third-party
     * components.
     */
    private applyAutocompletePatch(): void {
        setTimeout(() => {
            const inputs: NodeListOf<HTMLInputElement> = this.elementRef.nativeElement.querySelectorAll('input');
            inputs.forEach((input) => {
                if (!input.hasAttribute('autocomplete')) {
                    this.renderer.setAttribute(input, 'autocomplete', 'off');
                }
            });
        });
    }

    // ---------------------------------------------------------------------
    // WCAG 2.4.1 – helper to set focus on <main id="recentActivitiesMain">
    // ---------------------------------------------------------------------
    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#recent-activities');
            if (main) {
                main.focus();
            }
        });
    }

    // WCAG 2.4.3 – exposed for template skip-link so users can shift focus
    // directly into the main landmark after activating the control.
    public focusMain(): void {
        this.moveFocusToMain();
    }

    pageIndexChange(e: any): void {
        this.getData(e);
    }

    getData(pageIndex: number): void {
        this.user.getActivities(pageIndex).subscribe((v) => {
            this.activities = [];
            this.dates = [];
            v.notificationMessages.forEach((c: any) => {
                this.dates.push(new Date(c.actionTime));
                this.activities.push(this.getNotificationContent(c) + ' - ');
            });
            this.count = v.messagesCount;
			this.isLoaded = true;

            // Activities may repopulate the paginator; re-apply patch.
            this.applyAutocompletePatch();
        });
    }

    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    getNotificationContent = function (activity: any) {
		//send document by sender
		//code 203--USER_ACTIVITY_ACTION_SENDING_DOC_TO_RECIPIENT_BY_SENDER
		if(activity.actionCode==203){
			if(activity.numberOfRecipientsHaveSameInvitationSequence==1){
				return $localize `${activity.docTitle} was sent to ${activity.recipientName}`;
			}else{
				return  $localize `${activity.docTitle} was sent to ${activity.recipientName} and ${(activity.numberOfRecipientsHaveSameInvitationSequence-1)} others`;
			}
		}
		//send document by system automatically
		//code 204--USER_ACTIVITY_ACTION_SENDING_DOC_TO_RECIPIENT_AUTOMATICALLY
		if(activity.actionCode==204){
			return  $localize `${activity.docTitle} was automatically sent to ${activity.recipientName}`;
		}
		//send document using bulk sign template
		//code 202---USER_ACTIVITY_ACTION_SENDING_BULK_SIGN
		if(activity.actionCode==202){
			return  $localize `${activity.docTitle}  was sent`;
		}
		//the recipient opens a document
		//code 205--USER_ACTIVITY_ACTION_RECIPIENT_OPEN_DOCUMENT
		if(activity.actionCode==205){
			return  $localize `${activity.docTitle}  was opened by ${activity.recipientName}`;
		}

		//the recipient rejects a document
		//code 207--USER_ACTIVITY_ACTION_RECIPIENT_REJECT_DOCUMENT
		if(activity.actionCode==207){
			if(activity.rejectionReason===""){
                return  $localize `${activity.docTitle} was rejected by ${activity.recipientName}`;
			}else{
                return  $localize `${activity.docTitle} was rejected. ${activity.recipientName} included a reason: ${activity.rejectionReason}`;
			}
		}
		//the recipient signs a document
		//code 206--USER_ACTIVITY_ACTION_RECIPIENT_SIGN_DOCUMENT
		//code 53---SIGNING_STATUS_COMPLETED
		if(activity.actionCode==206){
			if(activity.documentSigningStatus==53){
                return  $localize `${activity.docTitle}  was signed by ${activity.recipientName} and is now complete`;
			}else{
                return  $localize `${activity.docTitle}  was signed by ${activity.recipientName}`;
			}

		}
		//For recipients,document received for reviewing or signing
		//code 201--USER_ACTIVITY_ACTION_RECIPIENT_RECEIVE_DOCUMENT
		//code 15---INVITATION_TYPE_SIGNER
		//code 16--INVITATION_TYPE_VIEWER
		if(activity.actionCode==201){
			if(activity.recipientActionType==15){
                return  $localize `${activity.docTitle}  was received for signing`;
			}
			if(activity.recipientActionType==16){
                return  $localize `${activity.docTitle}  was received for reviewing`;
			}
			if(activity.recipientActionType==23){
                return  $localize `${activity.docTitle}  was received for editing`;
			}
		}
        return;
	};
}
