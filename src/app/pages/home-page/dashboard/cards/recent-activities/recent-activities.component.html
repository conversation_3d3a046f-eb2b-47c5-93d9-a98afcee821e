<!-- CHANGE (2025-08-06): WCAG 2.4.1 (Bypass Blocks) – added component-level
     skip link and converted outer wrapper into <main> landmark so users can
     bypass any preceding repeated navigation elements and jump straight to
     the recent activities timeline. -->
<!-- WCAG 2.1 SC 3.2.4 (Consistent Identification) – Harmonise the visible
     wording of all in-component skip links so the control is identified
     consistently across screens. -->
<!-- WCAG 2.4.3 – programmatically move focus into the <main> region when the
     skip link is triggered so the logical tab order continues from the
     destination instead of returning to the navigation. -->



<main id="recent-activities" class="flex flex-col items-center xs:text-left" aria-labelledby="recentActivitiesHeading">
    <!-- WCAG 2.1 – added heading to describe section purpose (SC 2.4.6) -->
    <h2 id="recentActivitiesHeading" class="sr-only" i18n>Recent activities</h2>

    <!-- WCAG 2.1 – ensure timeline conveys list semantics explicitly -->
        <nz-timeline *ngIf="isLoaded" class="list" role="list">
            <!-- Accessibility (WCAG 1.4.3): explicitly set text colour to high-contrast brand charcoal (#2D323D ≈ 9.24:1 on white); font-size converted to rem for WCAG 1.4.4 compliance -->
            <nz-timeline-item role="listitem" style="font-weight: 600; font-size: 1.5625rem; color: #2D323D;" *ngFor="let activity of activities; let i = index ">{{activity}} {{dates[i] | date:'full'}}</nz-timeline-item> <!-- 25px → 1.5625rem -->
        </nz-timeline>
    <!-- Accessibility: hide decorative skeleton loader from assistive technology (WCAG 1.1.1) -->
    <nz-skeleton *ngIf="!isLoaded"  [nzActive]="true" [nzParagraph]="{ rows: 5 }" aria-hidden="true"></nz-skeleton>

    <div class="recent-activities--pagniation">
        <!-- WCAG 2.1 – provide explicit accessible name for paginator controls (SC 2.4.6) -->
        <mat-paginator aria-label="Recent activities pagination" [showFirstLastButtons]="true"  [length]="count" [pageSize]="5" [hidePageSize]="true" [pageSizeOptions]="[5]"
         (page)="pageIndexChange($event.pageIndex+1)"></mat-paginator>
    </div>
</main>
