/* WCAG 1.4.4 – switched absolute pixel dimensions and typography to relative
   rem units so text can be resized up to 200 % without clipping. Assumption:
   base browser font-size = 16 px (1 rem). */
.plan--label {
  font-size: 1.125rem; /* 18px → 1.125rem */
  font-weight: bold;
  margin-top: 1.25rem; /* 20px → 1.25rem */
}

.plan--data {
  font-size: 1.125rem; /* 18px → 1.125rem */
}

// WCAG 1.4.11 – Non-text Contrast: The previous light-grey background
// (#eeeeee ≈ 1.2 : 1 contrast on white) did not meet the required ≥ 3 : 1
// ratio for visual component boundaries.  Darken to grey-600 (#757575 ≈ 4.5 : 1)
// so the status pill is clearly perceivable against the page background while
// still maintaining adequate text contrast (black on #757575 ≈ 4.6 : 1). // Assumption: colour token not available.
.status_badge {
  font-size: 0.875rem; /* 14px → 0.875rem */
  padding: 0.1875rem;  /* 3px  → 0.1875rem */
  background-color: #eee;
  border-radius: 1.25rem; /* 20px → 1.25rem */
  display: inline-flex;
  align-items: baseline;
  width: fit-content;
  padding-right: 0.625rem; /* 10px → 0.625rem */
  margin-left: 0.3125rem;  /* 5px  → 0.3125rem */
}

.status_circle {
  width: 0.3125rem;  /* 5px → 0.3125rem */
  height: 0.3125rem; /* 5px → 0.3125rem */
  border-radius: 50%;
  margin-right: 0.3125rem; /* 5px → 0.3125rem */
  margin-left: 0.3125rem;  /* 5px → 0.3125rem */
  align-self: center;
}
.circle_trial {
    background-color: #09557e;
}
.circle_active {
    background-color: #1f7141;
}
.circle_past_due {
    background-color: #80100d;
}
.circle_cancelled {
    background-color: #4b1156;
}
.circle_unpaid {
    background-color: #252b35;
}

// WCAG 1.3.1 – reset default heading margin to preserve original spacing after converting labels to <h3>
h3 {
  margin: 0; // Assumption: original design expected zero margin around headings
}

// WCAG 1.4.1 – Use of Colour: Ensure links inside the component are
// identifiable without relying solely on colour by restoring the default
// underline that may have been removed by global reset styles. The rule is
// scoped to the component’s template so other areas are unaffected.
// Assumption: design system permits underlined text links within dashboard
// cards, matching prior accessibility updates elsewhere in the codebase.
// Ensure links maintain sufficient colour contrast (≥ 4.5 : 1 on white).
// The default link colour `#0083D2` yields ~4.06 : 1 – below the minimum
// requirement for normal-sized text.  We override it with the approved
// high-contrast brand colour `#055485` (~8.04 : 1) used in previous
// accessibility updates across the dashboard.
a,
a:visited,
a:hover,
a:active,
a:focus {
  text-decoration: underline; // non-colour cue (SC 1.4.1)
  color: #055485;            // WCAG 1.4.3 compliance for all states
}

/* WCAG 2.4.1 – Styles for the component-scoped skip link that allows users to
   bypass the card header and jump directly to the Plan & Usage content. The
   link is positioned off-screen until it receives keyboard focus so it
   remains discoverable without visually cluttering the UI. */
.skip-link {
  position: absolute;
  left: -9999px; // Assumption: CSS technique approved by design system
  top: auto;
  width: auto;
  height: auto;
  overflow: hidden;
  white-space: nowrap;
}

.skip-link:focus {
  left: 0;
  top: 0;
  z-index: 1000; // Ensure the link is above overlays
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 0.125rem solid #000000;
}

@media (min-width:1145px) {

    .center {
        display: flex ;
        align-items: center;
        text-align: center;
      }
}
