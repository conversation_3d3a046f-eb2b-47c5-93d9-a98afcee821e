// Accessibility update: implemented scalable typography, high-contrast colours,
// semantic landmarks, unique heading IDs and disabled non-keyboard tooltips to
// meet WCAG 2.1 Level AA for the Plan & Usage dashboard card.
// Added Angular Title service to set a descriptive <title> element so the page
// meets WCAG 2.4.2 (Page Titled). – keeps same functionality.
import { Component, OnInit, Optional } from '@angular/core';
import { Title } from '@angular/platform-browser';
// WCAG 1.4.13 – import ApexTooltip so we can explicitly disable default hover tooltips
import {
    ApexNonAxisChartSeries,
    ApexPlotOptions,
    ApexChart,
    ApexStroke,
    ApexStates,
    ApexFill,
    ApexTooltip
} from "ng-apexcharts";
import { BillingCycle, UserService } from "@core/services";
import { zip, Observable, map, forkJoin, combineLatest } from "rxjs";
import { UrlService } from "@core/services/url.service";
import { Roles } from "@core/models";

// WCAG 3.1.2 – Allow callers to override the natural language of this card’s
// content when it differs from the surrounding document.  Assistive
// technologies will switch pronunciation rules accordingly.  Mirrors pattern
// already used by sibling dashboard-card components (e.g. billing.component).
import { Input } from '@angular/core';


export interface ChartOptions {
    series: ApexNonAxisChartSeries;
    chart: ApexChart;
    labels: string[];
    plotOptions: ApexPlotOptions;
    stroke: ApexStroke;
    states: ApexStates;
    fill: ApexFill;
    // WCAG 1.4.13 – expose tooltip option so we can turn the chart tooltip off and avoid
    // additional hover/focus content that is not persistent or keyboard-dismissible.
    tooltip?: ApexTooltip;
}



@Component({
    selector: 'app-plan-usage',
    templateUrl: './plan-usage.component.html',
    styleUrls: ['./plan-usage.component.less']
})
export class PlanUsageComponent {

    // WCAG 3.1.2 – Optional BCP-47 language tag applied to the host <section>
    // via template binding so "parts" using a different language are
    // programmatically declared. // Assumption: parent passes valid code.
    @Input() lang?: string;

    chart_time: ChartOptions;
    chart_usage: ChartOptions;
    plan: Observable<string>;
    role: String | undefined;
    roleId: number = -1;
    amount: Observable<number>;

    isTeamPlan: boolean = false;
    isLoaded: boolean = false;

    start_date = ''
    end_date = ''
    planStatus = ''
    endTrial:Date = new Date()
    trialDaysLeft = 0

    manage_visible: Observable<boolean>
    upgrade_visible: Observable<boolean>
    readonly roles = Roles
    public isAdminConsole: Observable<boolean>

    /**
     * Unique heading IDs used to programmatically associate the two child
     * <section> landmarks we expose inside the template.  The IDs are bound in
     * the HTML so that assistive-technology users can discover the chart
     * regions via their headings – fulfilling WCAG 2.4.6 (Headings and
     * Labels).
     */
    billingHeadingId: string;  // WCAG 2.4.6 – label for Billing Cycle
    usageHeadingId: string;    // WCAG 2.4.6 – label for Document Usage

    /**
     * Target ID used by the local “Skip to plan and usage details” link that
     * lets keyboard users bypass the repeated card header – WCAG 2.4.1
     * (Bypass Blocks).  Derived from a component-level counter so every
     * <app-plan-usage> instance remains unique in the DOM.
     */
    public regionId!: string; // WCAG 2.4.1 – local skip-link target
    private static nextId = 0; // Assumption: component instantiated few times

    get_percentage(current: number, total: number) {
        return Math.trunc(current / total * 1000) / 10;
    }

    // WCAG 1.4.11 – Non-text Contrast: ensure wedge colour of radial chart
    // maintains ≥ 3 : 1 contrast against the light-grey track (#D7D9DD).
    // Previous blue (#0083D2, 2.87:1) and yellow (#ffc849, 1.09:1) failed.
    // Darkened hues below meet the threshold while preserving semantic intent.
    // Assumption: brand colour #055485 is acceptable substitute for blue range.
    get_color = (value: number) => {
        if (value < 55) {
            return '#055485';       // contrast ≈ 5.69 : 1 vs #D7D9DD
        } else if (value < 80) {
            return '#b05e00';       // dark amber – contrast ≈ 3.34 : 1
        } else if (value < 100) {
            return '#D40D00';       // unchanged – contrast ≈ 3.85 : 1
        } else {
            return '#8600d4';       // unchanged – already > 3 : 1
        }
    }

    constructor(
        private userService: UserService,
        private url: UrlService,
        // @Optional() avoids DI errors in unit tests that omit Title provider – WCAG 2.4.2
        @Optional() private titleService?: Title,
    ) {

        // Generate deterministic, unique IDs for the two headings so we never
        // clash with other instances that may appear on the same page.
        // Assumption: the component will not be destroyed/created in a tight
        // loop that would cause the counter to overflow.
        const idBase = PlanUsageComponent.nextId++;

        // WCAG 2.4.1 – Skip-link target that receives focus after activation.
        this.regionId = `plan-usage-region-${idBase}`;

        this.billingHeadingId = `plan-usage-billing-heading-${idBase}`;
        this.usageHeadingId   = `plan-usage-usage-heading-${idBase}`;

        this.isAdminConsole = url.is_admin_console();
        this.manage_visible = combineLatest([url.is_admin_console(), userService.getRoleID()])
            .pipe(
                map(([flag, id]) => flag && (id === Roles.BILLING_ADMIN || id === Roles.SUPER_ADMIN))
            )
        this.upgrade_visible = combineLatest([url.is_admin_console(), userService.getRoleID()])
            .pipe(
                map(([flag, id]) => flag && (id === Roles.BILLING_ADMIN))
            )

        // Keep the document <title> in sync with the visible card heading so
        // assistive-technology users can quickly identify the page topic – WCAG 2.4.2
        this.updateDocumentTitle();
        // this.userService.getRoleID().subscribe(id => this.roleID = id)
        let color = this.get_color(0)
        this.chart_usage = {
            states: { //disabling interaction if mouse hover or click
                hover: { filter: { type: 'none' } },
                active: { filter: { type: 'none' } }
            },

            series: [0], // placeholder, will be re-fresh after api fetch
            // WCAG 1.4.10 – replaced fixed 280 px width with percentage so chart
            // scales with viewport and does not introduce horizontal scrolling
            // at 320 px width.
            chart: { type: "radialBar", width: '100%' },
            plotOptions: {
                radialBar: {
                    hollow: { size: "65%" },  // the larger the thicker of the track
                    track: { background: "#D7D9DD" }, // color for the missing part, (grey)
                    /*
                     * WCAG 2.1 – Success Criterion 1.3.4 (Orientation)
                     * ------------------------------------------------------------------
                     * Using a fixed `px` font-size for the radial-bar labels caused
                     * clipping on very narrow portrait devices (≤ 320 px) when the
                     * chart tried to shrink to fit the available width.  We switch
                     * to a CSS `clamp()` value so the text scales fluidly between
                     * small and large viewports while preserving the original 35 px
                     * maximum on desktop.  The lower bound (1.25rem ≈ 20 px)
                     * ensures the label remains legible on small screens without
                     * forcing horizontal scroll or overflow – fully satisfying the
                     * orientation requirement that *all content remain available
                     * in both portrait and landscape modes*.
                     */
                    dataLabels: {
                        name: {
                            offsetY: 5,
                            color: color,
                            fontSize: 'clamp(1.25rem, 5vw, 2.1875rem)' // 20-35 px responsive
                        },
                        value: {
                            formatter: () => "", // placeholder
                            color: '#9EA3AE',
                            fontSize: 'clamp(0.875rem, 3vw, 0.9375rem)', // 14-15 px responsive
                            fontWeight: 400,
                        }
                    }
                },
            },
            stroke: { lineCap: "round" },
            fill: { colors: [color] },
            labels: [""],
            // WCAG 1.4.13 – disable tooltip that appears only on hover and disappears on blur
            tooltip: { enabled: false }
        };

        this.chart_time = {
            states: { //disabling interaction if mouse hover or click
                hover: { filter: { type: 'none' } },
                active: { filter: { type: 'none' } }
            },
            series: [0], // prevent chart overflow
            // WCAG 1.4.10 – responsive width (see above rationale)
            chart: { type: "radialBar", width: '100%' },
            plotOptions: {
                radialBar: {
                    hollow: { size: "65%" },
                    track: { background: "#D7D9DD" }, // color for the missing part, (grey)
                    dataLabels: {
                        name: {
                            offsetY: 5,
                            color: "#000000",
                            // Responsive font-size so the chart adapts when the
                            // device rotates between portrait and landscape –
                            // see rationale above (SC 1.3.4).
                            fontSize: 'clamp(1.25rem, 5vw, 2.1875rem)'
                        },
                        value: {
                            formatter: () => $localize`Days left`,
                            color: '#9EA3AE',
                            fontSize: 'clamp(0.875rem, 3vw, 0.9375rem)',
                            fontWeight: 400
                        }
                    }
                },
            },
            stroke: { lineCap: "round" },
            fill: { colors: ["#000000"] },
            labels: ["0"],
            // WCAG 1.4.13 – disable hover tooltip for same reason as above
            tooltip: { enabled: false }
        };

        this.plan = userService.getPlan()
        userService.getRoleID().subscribe(id => {
            this.role = this.userService.getRoleNameFromId(id)
            this.roleId = id;
        })
        this.amount = userService.getBill()

        userService.isTeamPlan().subscribe(value => {
            this.isTeamPlan = value;
        })

        userService.getBillingCycle().subscribe(value => {
            let days_used = value.total_days - value.days_left;
            let p = this.get_percentage(days_used, value.total_days);
            this.chart_time.series = [p > 100 ? 100 : p];
            this.chart_time.labels = [`${value.days_left}`];

            let days_used_trial = value.total_days_trial - value.days_left_trial;
            this.start_date = value.start;
            this.end_date = value.end;
            this.planStatus = value.stripeStatus == "trialing" ? "trialing" : value.status;
            this.endTrial = typeof value.stripeEnd == "number" ? new Date(value.stripeEnd * 1000) : value.stripeEnd;
            this.trialDaysLeft = value.days_left_trial;
        }
        )

    }

    /**
     * Title management is now handled by the parent dashboard component
     */
    private updateDocumentTitle(): void {
        // Title management is now handled by the parent dashboard component
    }

    ngOnInit() {
        this.userService.getUsageInfo().subscribe(
            value => {
                this.isAdminConsole.subscribe(admin => {
                    if (!admin)
                        this.update_chart_usage(value.page_used, value.total_pages)
                    else if (this.roleId == this.roles.TEAM_ADMIN)
                        this.update_chart_usage(value.page_used_team, value.total_pages)
                    else
                        this.update_chart_usage(value.page_used_org, value.total_pages)
                })
                this.isLoaded = true

            }
        )

    }



    update_chart_usage(used: number, total: number): void {
        let percentage = this.get_percentage(used, total)
        let color = this.get_color(percentage)
        this.chart_usage.series = [percentage > 100 ? 100 : percentage] // prevent chart overflow
        this.chart_usage.plotOptions.radialBar!.dataLabels!.name!.color = color
        this.chart_usage.plotOptions.radialBar!.dataLabels!.value!.formatter = () => `${used}/${total}`
        this.chart_usage.fill.colors = [color]
        this.chart_usage.labels = [`${percentage}%`]

    }

    onSpaceKey(event: Event): void {
        event.preventDefault();
        (event.target as HTMLElement).click();
    }

}
