<!-- WCAG 2.4.1 – Added component-scoped skip link so keyboard users can bypass
     the repeated card header and move focus directly to the primary Plan &
     Usage details region. The link is visually hidden until focused — see
     component stylesheet. -->

<!-- accessibility update: added semantic headings, aria labels, and hidden skeleton placeholders; component contains no personal data inputs so WCAG 1.3.5 is satisfied -->
<!-- WCAG 1.4.4 – min-width 450px → 28.125rem so grid scales with text size -->

<!-- WCAG 3.1.2 – expose optional `lang` input on root <section> so language changes are conveyed -->
<section [attr.id]="regionId" tabindex="-1" class="grid grid-cols-3 xs:divide-y med:divide-y !med:divide-x med:min-w-[28.125rem]" [attr.lang]="lang ? lang : null">

      <div class=" xs:col-span-3 med:col-span-3 col-span-1  pr-[1rem]" *ngIf="isLoaded">
        <div class="mt-4">
            <span class="plan--label" i18n>Your Plan: </span>
            <span class="plan--data">
                {{plan | async}}
                <span class="status_badge" *ngIf="planStatus=='trialing'" i18n>
                    <span class="status_circle circle_trial"></span>
                    Trial
                </span>
                <span class="status_badge" *ngIf="planStatus=='Active'" i18n>
                    <span class="status_circle circle_active"></span>
                    Active
                </span>
                <span class="status_badge" *ngIf="planStatus=='Cancelled' || planStatus=='Terminated'" i18n>
                    <span class="status_circle circle_cancelled"></span>
                    Cancelled
                </span>
                <span class="status_badge" *ngIf="planStatus=='Suspended'" i18n>
                    <span class="status_circle circle_unpaid"></span>
                    Suspended
                </span>
            </span>

        </div>

            <ng-container >
                <ng-container *ngIf="isTeamPlan ">
                    <div class="mt-4">
                        <span class="plan--label" i18n>Your Role: </span>
                        <span class="plan--data">
                            {{role }}
                        </span>
                    </div>
                </ng-container>
                <ng-container *ngIf="!isTeamPlan">
                    <div class="mt-4">
                        <span class="plan--label" i18n>Estimated upcoming bill:</span>
                        <span class="plan--data">
                            ${{amount | async}}
                        </span>
                    </div>
                </ng-container>
            </ng-container>
            <div class="mt-4" *ngIf="planStatus != 'Cancelled'">
                <span class="plan--label" i18n>Billing Cycle Renewal Date: </span>
                <span class="plan--data">
                    {{end_date | date}}
                </span>
            </div>
            <div class="mt-4" *ngIf="planStatus=='trialing'">
                <span class="plan--label" i18n>Trial Period Ends: </span>
                <span class="plan--data">
                    {{endTrial | date}}
                </span>
            </div>


            <div class="text-center mt-10" *ngIf="(manage_visible | async)">
                <!-- WCAG 2.4.4 – make link purpose explicit even when removed from context -->
                <!-- CHANGE (2025-08-06): Align accessible name with visible link text to satisfy
                     WCAG 2.5.3 (Label in Name). The aria-label now starts with the exact
                     string users see (“Request Additional Packages”). -->
                <a *ngIf="roleId == roles.BILLING_ADMIN" class="text-xl"
                   href="https://www.signority.com/document-package-add-on-submission-form/"
                   target="_blank"
                   aria-label="Request Additional Packages (opens in new tab)"
                   i18n-aria-label i18n>Request Additional Packages</a>
                <br>
                <!-- WCAG 2.4.4 – clarify link purpose via explicit aria-label. Accessible name begins with visible label “Manage Plan” to comply with WCAG 2.5.3. -->
                <a class="text-xl" routerLink="/plan" (keydown.space)="onSpaceKey($event)" aria-label="Manage Plan" i18n-aria-label i18n>
                    Manage Plan
                </a>
            </div>
            
        </div>
        <!-- Accessibility: hide decorative loading skeleton while content loads (WCAG 1.1.1 compliance) -->
        <nz-skeleton class=" xs:col-span-3 med:col-span-3 col-span-1  pr-[1rem] pb-3"
        *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 6 }" aria-hidden="true"></nz-skeleton>

      
        <div class="xs:col-span-3 med:col-span-3 col-span-2" >
            <div class="flex justify-around xs:justify-items-center center grid grid-cols-2">

                <!--
                    WCAG 2.4.6 – Wrap each chart in a <section> that is explicitly
                    associated with its visible heading via aria-labelledby.
                    This creates a named landmark so screen-reader users can jump
                    directly to the “Billing Cycle Period” or “Document Usage”
                    regions from their rotor/landmark list.
                -->
                <section class="xs:col-span-2 col-span-1" *ngIf="isLoaded" role="region" [attr.aria-labelledby]="billingHeadingId">

                    <!-- WCAG 1.3.1 – promote visual label to semantic heading for proper information hierarchy -->
                    <!-- Assumption: h4 follows the h3 used by parent card title, maintaining logical hierarchy -->
                    <h4 class="text-center text-base my-2" [attr.id]="billingHeadingId" i18n>Billing Cycle Period</h4>

                    <!-- Accessibility: provide text alternative for radial chart (WCAG 1.1.1). 
                         Added i18n-aria-label for localisation and explicit role="img" -->
                    <apx-chart role="img" [series]="chart_time.series" [chart]="chart_time.chart"
                        [plotOptions]="chart_time.plotOptions" [labels]="chart_time.labels" [stroke]="chart_time.stroke"
                        [states]="chart_time.states" [fill]="chart_time.fill"
                        i18n-aria-label aria-label="Billing cycle progress radial chart"></apx-chart>

                </section>

                <!-- WCAG 1.4.4 – converted fixed container height 250px to 15.625rem so it scales with root font size; switched to inline-flex for modern browsers. -->
                <div class="xs:col-span-2 col-span-1"
                    style="min-height:15.625rem; display:flex; flex-direction:column; justify-content:center; align-items:center" *ngIf="!isLoaded">
                    <!-- Accessibility: decorative placeholders hidden from assistive tech -->
                    <!-- WCAG 1.4.4 – width 200px → 12.5rem so placeholder scales with text zoom -->
                    <nz-skeleton-element class="mb-3" nzType="input" nzSize="small" width="12.5rem" [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
                    <nz-skeleton-element nzType="image"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
                </div>

                <section class="xs:col-span-2 col-span-1" *ngIf="isLoaded" role="region" [attr.aria-labelledby]="usageHeadingId">
                    <!-- WCAG 1.3.1 – convert styled <div>+<strong> blocks into semantic headings -->
                    <!-- Use h4 to keep heading hierarchy consistent (parent card provides an h3) -->
                    <h4 *ngIf="!(isAdminConsole | async)"
                        class="text-center text-base my-2 flex items-end justify-center"
                        [attr.id]="usageHeadingId" i18n>
                        Document Usage
                    </h4>
                    <h4 *ngIf="(roleId == roles.SUPER_ADMIN || roleId == roles.BILLING_ADMIN) && (isAdminConsole | async)"
                        class="text-center text-base my-2"
                        [attr.id]="usageHeadingId" i18n>
                        Organization’s Document Usage
                    </h4>
                    <h4 *ngIf="(roleId == roles.TEAM_ADMIN ) && (isAdminConsole | async)"
                        class="text-center text-base my-2"
                        [attr.id]="usageHeadingId" i18n>
                        Team's Document Usage
                    </h4>
                    <!-- Accessibility: provide text alternative for radial chart (WCAG 1.1.1).
                         Added i18n-aria-label for localisation and explicit role="img" -->
                    <apx-chart role="img" [series]="chart_usage.series" [chart]="chart_usage.chart"
                        [plotOptions]="chart_usage.plotOptions" [labels]="chart_usage.labels"
                        [stroke]="chart_usage.stroke" [states]="chart_usage.states"
                        [fill]="chart_usage.fill"
                        i18n-aria-label aria-label="Document usage radial chart"></apx-chart>

                </section>

                <!-- Duplicate loading placeholder container – apply same scalable dimensions -->
                <div class="xs:col-span-2 col-span-1"
                    style="min-height:15.625rem; display:flex; flex-direction:column; justify-content:center; align-items:center" *ngIf="!isLoaded">
                    <!-- Accessibility: decorative placeholders hidden from assistive tech -->
                    <nz-skeleton-element class="mb-3" nzType="input" nzSize="small" width="12.5rem" [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
                    <nz-skeleton-element nzType="image"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
                </div>
            </div>

            <div class="text-center" *ngIf="(upgrade_visible | async)">
                <!-- WCAG 2.4.4 – replicate visible label in aria-label so purpose is conveyed in isolation -->
                <button class="ant-btn ant-btn-primary" onclick="location.href = '/UI/changePlan2.html';"
                        aria-label="Upgrade subscription plan" i18n-aria-label i18n>
                    Upgrade
                </button>
            </div>
        </div>
        
</section>
