// Unit-test adjustments for WCAG 3.3.3 update – provide mock NzModalService so
// the component’s new error-handling path compiles without pulling the real
// UI dependency during tests.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomizeComponent } from './customize.component';
import { NzModalService } from 'ng-zorro-antd/modal';

describe('CustomizeComponent', () => {
    let component: CustomizeComponent;
    let fixture: ComponentFixture<CustomizeComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ CustomizeComponent ],
            imports: [],
            providers: [
              { provide: NzModalService, useValue: { error: () => {} } }
            ]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(CustomizeComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
