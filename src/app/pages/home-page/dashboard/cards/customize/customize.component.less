::ng-deep .cdk-global-scrollblock {
    position: static;
    overflow: hidden !important;
}


// WCAG 2.1 – Success Criterion 1.4.3 (Contrast – Minimum)
// The previous background colour `#45B1E8` combined with white foreground
// text yielded a contrast ratio of ≈ 2.41 : 1 – below the required 3 : 1 for
// large text (24 px) and the 4.5 : 1 ratio for normal-sized text.  We darken
// the background to the high-contrast brand colour `#055485` (≈ 8.04 : 1 on
// white) to meet the guideline while staying inside the approved palette.
/* WCAG 1.4.4 – replace fixed pixel units with rem so shortcut cards scale with
   user text size up to 200 %. Assumption: 1rem = 16 px. */
.shortcuts-card { // Assumption: visual design permits slightly darker brand blue
  border-radius: 0.5rem; /* 8px → 0.5rem */
  background: #055485; // WCAG 1.4.3 – ensure ≥ 3:1 contrast with white text
  color: #ffffff;
  //width: 488px; /* kept commented as before */
  min-height: 3rem; /* 48px → 3rem – use min-height to allow growth */
  font-weight: 400;
  font-size: 1.5rem; /* 24px → 1.5rem */
  line-height: 3rem; /* 48px → 3rem */
}

.shortcuts-card-selected {
  border-radius: 0.5rem; /* 8px → 0.5rem */
  background: white;
  // WCAG 1.4.3 – Minimum contrast: change foreground colour to high-contrast
  // brand blue `#055485` (~7.4 : 1 on white) so 24 px label text inside the
  // "selected" shortcut card meets the ≥ 3 : 1 ratio required for large text.
  // Assumption: design palette already uses this tone elsewhere (see
  // .shortcuts-card) so the visual intent is preserved.
  color: #055485;
  //width: 488px; /* kept commented */
  min-height: 3rem; /* 48px → 3rem */
  font-weight: 400;
  font-size: 1.5rem; /* 24px → 1.5rem */
  line-height: 3rem; /* 48px → 3rem */
  border: 0.125rem solid #055485; // 2px → 0.125rem (WCAG 1.4.4 – scalable border)

  // WCAG 1.4.1 – Use of Colour: add non-colour visual cue (checkmark icon)
  // so the "selected" state remains perceivable when colour information is
  // lost (e.g. greyscale or high-contrast modes). The pseudo-element is purely
  // decorative for sighted users and is ignored by assistive technologies via
  // `aria-hidden` on the parent label in the template.
  // Assumption: left padding added to accommodate the icon without layout shift.
  position: relative;
  padding-left: 2rem; /* 32px → 2rem */
}

.shortcuts-card-selected::before {
  content: "\2713"; /* Unicode checkmark */
  position: absolute;
  left: 0.5rem; /* 8px → 0.5rem */
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.25rem; /* 20px → 1.25rem */
  line-height: 1;
  color: currentColor;
}

// Removed stray closing brace that broke LESS parsing – restores compilation (no visual change)

::ng-deep .ant-modal-content {
  border-radius: 0.625rem; /* 10px → 0.625rem */
}

// WCAG 1.3.1 – reset heading margin in modal title to preserve design
h2 {
  margin: 0; // Assumption: original layout expected tight spacing
}

// WCAG 1.3.1 – remove default bullets for dynamic checkbox list added as semantic <ul>
ul {
  list-style: none; // Assumption: visual design previously had no bullets
  margin: 0;
  padding: 0;
}

/* ------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   Default Ant Design checkbox outlines use a faint #d9d9d9 border (~1.2 : 1
   contrast on white) and provide no high-contrast keyboard focus indicator.
   The following scoped overrides ensure checkbox boundaries and focus states
   reach ≥ 3 : 1 contrast and are perceivable by low-vision users without
   altering functionality outside this component. */

:host ::ng-deep .ant-checkbox-inner {
  border: 1px solid #757575; /* WCAG 1.4.11 – darken checkbox outline */
}

/* Visible keyboard focus indicator for checkboxes inside the modal */
:host ::ng-deep .ant-checkbox-wrapper input:focus + .ant-checkbox-inner,
:host ::ng-deep .ant-checkbox-input:focus + .ant-checkbox-inner {
  outline: 3px solid #1a73e8; // WCAG 1.4.11 – high-contrast focus ring
  outline-offset: 2px; // Assumption: matches global focus styling
}

/* Ensure the anchor rendered as a button inside the dashboard card also
   exhibits a visible focus indicator when operated via keyboard. The generic
   dashboard card rule already covers many cases, but Safari/WebKit sometimes
   suppresses outlines on anchors with role="button". Reinstate here. */
a[role="button"]:focus,
a[role="button"]:focus-visible {
  outline: 3px solid #1a73e8; // WCAG 1.4.11 – consistent focus colour
  outline-offset: 2px;
}

/* WCAG 2.4.1 – Off-screen until focused skip-link styling so it remains
   discoverable via keyboard but does not visually clutter the interface. */
.skip-link {
  position: absolute;
  left: -9999px; // Hide off-screen while remaining focusable
  top: auto;
  width: auto;
  height: auto;
  overflow: hidden;
  white-space: nowrap;
}

.skip-link:focus {
  left: 0;
  top: 0;
  z-index: 1000;
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 0.125rem solid #000000;
}
