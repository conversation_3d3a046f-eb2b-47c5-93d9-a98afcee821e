<!-- WCAG 1.3.1 – anchor used purely for interaction without navigation; expose as button for semantics -->
<!-- WCAG 2.4.4 – provide explicit aria-label so the link-turned-button is understandable in isolation -->
<!-- Added keyboard handler for <Space> key to satisfy WCAG 2.1 – Success Criterion 2.1.1
     (Keyboard).  While the anchor already reacts to <Enter> via the native
     click event, browsers do not dispatch `click` on <Space> for links.  We
     therefore listen to the `keydown.space` gesture and invoke the same
     action, preventing the default page-scroll behaviour so keyboard users
     can activate the dialog using either key.  -->
<!-- CHANGE (2025-08-06): Accessible name now begins with the visible label text “Customize Shortcuts”
     to fulfil WCAG 2.5.3 (Label in Name). -->
<!-- WCAG 3.1.2 – Bind optional `lang` input so language switches are announced -->
<section [attr.id]="regionId" tabindex="-1" [attr.lang]="lang ? lang : null">

<a class="flex flex-col items-center" role="button" aria-haspopup="dialog"
   (click)="showModal(); $event.preventDefault()"
   (keydown.space)="showModal(); $event.preventDefault()"
   href="#"
   aria-label="Customize Shortcuts – open dialog" i18n-aria-label>

    <div class="mt-24">
        <!-- Accessibility: decorative icon marked hidden for screen readers (WCAG 1.1.1) -->
        <img src="assets/icon/pen.svg" height="88" width="88" alt="" aria-hidden="true" />
    </div>
          <!-- Fixed typo and added i18n for label to maintain clear meaning (WCAG 1.3.2) -->
          <!-- WCAG 1.4.3 – Increased colour contrast for label text -->
          <!-- WCAG 1.4.4 – font-size 24px → 1.5rem so label scales with text resize -->
          <div style="font-weight:300; font-size:1.5rem; color:#055485" i18n>
              Customize Shortcuts
          </div>
</a>

</section>


<!-- WCAG 1.4.4 – modal width 1200px → 75rem so dialog adapts at enlarged text sizes -->
<!-- WCAG 1.4.13 – make modal dismissible via ESC key and click outside while keeping design (no close icon) -->
<!-- WCAG 4.1.2 – provide programmatic name for the dialog via aria-labelledby -->
<nz-modal [(nzVisible)]="isVisible" (nzOnCancel)="handleCancel()" (nzOnOk)="handleOk()"
          [nzZIndex]="3" nzClosable="false" [nzWidth]="'90vw'"
          [nzMaskClosable]="true" [nzKeyboard]="true"
          [attr.aria-labelledby]="modalHeadingId">
      <ng-container *nzModalContent>
          <!-- WCAG 1.3.1 – promote modal title to semantic heading -->
          <!-- Added i18n to modal title for localisation consistency (WCAG 1.3.2) -->
          <!-- WCAG 1.4.3 – Increased colour contrast for modal heading -->
          <!-- WCAG 1.4.4 – convert 40px/24px to relative units so heading scales -->
          <!-- WCAG 1.4.12 fix: removed compressed line-height to allow ≥1.5× spacing under custom text-spacing
               preferences. Using 'normal' lets browsers calculate appropriate value and ensures content isn’t
               clipped when users apply a user style sheet. // Assumption: visual rhythm acceptable without 1.1 -->
          <!-- WCAG 2.4.6 – provide a programmatic identifier for the dialog heading so the
               modal can be referenced via `aria-labelledby` if required by the
               underlying component library in future.  A static ID is sufficient
               because only one CustomizeComponent dialog may be open at a time. -->
          <h2 [attr.id]="modalHeadingId" class="text-center py-5" style="font-weight:400; font-size:2.5rem; line-height:normal; color:#055485;" i18n>
               Customize Shortcuts
          </h2>
          <!-- WCAG 1.4.10 – allow blocks to wrap when viewport is narrow so the
               dialog does not overflow horizontally on small screens -->
          <div class="flex flex-wrap justify-evenly">

              <div class="flex flex-col items-center">
                  <!-- WCAG 1.4.4 – width 560px→35rem, height 384px→24rem -->
                  <div style="max-width:35rem; min-height:24rem; border:0.125rem solid #737373;">
                      <div nz-row [nzGutter]="32" nzJustify="center" class="mt-6">
                    <div nz-col nzSpan="22" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <!-- Accessibility: icon is decorative, hide from assistive tech -->
                           <i class="fa-solid fa-money-check-dollar" aria-hidden="true"></i>
                           Plan and Usage
                       </div>
                    </div>


                    <div nz-col nzSpan="22" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-solid fa-file" aria-hidden="true"></i>
                           Document Report
                       </div>
                    </div>


                   <div nz-col nzSpan="11" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-solid fa-user" aria-hidden="true"></i>
                            User
                       </div>
                    </div>

                    <div nz-col nzSpan="11" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-solid fa-users" aria-hidden="true"></i>
                            Teams
                       </div>
                    </div>


                    <div nz-col nzSpan="11" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-regular fa-star" aria-hidden="true"></i>
                            Branding
                       </div>
                    </div>

                    <div nz-col nzSpan="11" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-regular fa-circle-question" aria-hidden="true"></i>
                            Knowledge Base
                       </div>
                    </div>

                    <div nz-col nzSpan="22" class="py-2">
                       <div  class="box-border text-center shortcuts-card-selected">
                           <i class="fa-solid fa-clock-rotate-left" aria-hidden="true"></i>
                           Recent Activities
                       </div>
                    </div>

                </div>
                  </div>
              </div>





              <div class="flex flex-col items-center">
                  <!-- WCAG 1.4.4 – width 200px→12.5rem, height 384px→24rem; border 2px→0.125rem -->
                  <div style="max-width:12.5rem; min-height:24rem; border:0.125rem solid #737373;">
                      <!-- WCAG 1.3.1 – ensure list items are children of a semantic list container -->
                      <!-- Added accessible label to list for screen readers (WCAG 1.3.2) -->
                      <!-- WCAG 3.3.2 – added visible group label so users who rely on visual
                           cues (including those with assistive technologies that render the
                           visual interface) can identify the purpose of this set of checkboxes.
                           The list is now programmatically associated with the heading via
                           `aria-labelledby`, ensuring screen-reader users hear the same text
                           that sighted users see. -->
                      <h3 [attr.id]="selectHeadingId" class="text-center mb-2" i18n>
                          Select shortcuts to display
                      </h3>
                      <ul role="list" class="m-0 p-0 list-none" [attr.aria-labelledby]="selectHeadingId">
                          <li *ngFor="let card of cards">
                              <label nz-checkbox [(ngModel)]="card.selected">{{card.name}}</label>
                          </li>
                      </ul>
                  </div>
              </div>


          </div>



      </ng-container>
    </nz-modal>
<!--
<div cdkDropList [cdkDropListSortingDisabled]='true' (cdkDropListDropped)="drop($event)"  nz-row [nzGutter]="[32, 32]">
    <div nz-col [nzSpan]="24">
        <app-card title="Usage Report" icon="chart-column">
            <app-usage-report></app-usage-report>
        </app-card>
    </div>
    <div  [id]=i class="editable-card" nz-col *ngFor="let card of cards; let i = index" [nzSpan]="card.width">
        <app-card cdkDrag cdkDragPreview [title]="card.title" [icon]="card.icon" >
            <i class="fa-solid fa-x float-right close-card" (click)="cards.splice(i, 1)"></i>
            <div [ngSwitch]="card.type">
                <app-service-period *ngSwitchCase="'service-period'"></app-service-period>
                <app-billing *ngSwitchCase="'billing'"></app-billing>
                <app-teams *ngSwitchCase="'teams'"></app-teams>
                <app-users *ngSwitchCase="'users'"></app-users>
                <app-branding *ngSwitchCase="'branding'"></app-branding>
                <app-shared-templates *ngSwitchCase="'shared-templates'"></app-shared-templates>
                <app-knowledge-base *ngSwitchCase="'knowledge-base'"></app-knowledge-base>
                <app-document-report *ngSwitchCase="'document-report'"></app-document-report>
            </div>
        </app-card>
    </div>
    <div nz-col [nzSpan]="12">
        <app-card>
            <app-customize></app-customize>
        </app-card>
    </div>
    <div nz-col [nzSpan]="24">
        <app-card title="Recent Activities" icon="clock-rotate-left">
            <app-recent-activities></app-recent-activities>
        </app-card>
    </div>
</div>

-->
