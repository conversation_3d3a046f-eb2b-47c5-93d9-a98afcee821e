import { Component, OnInit, AfterViewInit, ElementRef, HostListener } from '@angular/core';
// CHANGE (2025-08-06): WCAG 2.4.2 – import Angular Title service to set
// a descriptive document <title> when the route loads.
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Code } from "@core/models";
import { DocumentInfo, DocumentService, UserService } from "@core/services";
import { UrlService } from "@core/services/url.service";
import { Observable } from "rxjs";

@Component({
    selector: 'app-document-report',
    templateUrl: './document-report.component.html',
    styleUrls: ['./document-report.component.less']
})
export class DocumentReportComponent implements OnInit, AfterViewInit {
    info:any;
    userRole:number = -1;
    isAdminConsole:boolean = false;
    isLoaded:boolean = false;

    constructor(
        doc: DocumentService,
        private userService: UserService,
        private urlService: UrlService,
        private elementRef: ElementRef, // Assumption: needed for focus management
        private titleService: Title, // Assumption: Angular Title service available at root
        private router: Router
    ) {
        // Get user's role
        userService.getRoleID().subscribe(id => {
            this.userRole = id;
        });

        // Check if admin console
        urlService.is_admin_console().subscribe(admin => {
            this.isAdminConsole = admin;
        });

        // Display usage based on dashboard and user's role
        if (this.isAdminConsole) {
            switch (this.userRole) {
                // Show organization usage for super and billing admins
                case Code.roles.TEAM_SUPERADMIN:
                case Code.roles.TEAM_BILLING_ADMIN:
                    doc.getDocumentInfo(Code.report_type.org).subscribe(data=>{
                        this.info = data;
                        this.isLoaded = true;
                    });
                    break;
                // Show team usage for team admins
                case Code.roles.TEAM_ADMIN:
                case Code.roles.TEAM_DOCADMIN:
                    doc.getDocumentInfo(Code.report_type.team).subscribe(data=>{
                        this.info = data;
                        this.isLoaded = true;
                    });
                    break;
                // Failsafe in case new roles are introduced are above is not considered,
                // default to user's own usage
                default:
                    doc.getDocumentInfo(Code.report_type.user).subscribe(data=>{
                        this.info = data;
                        this.isLoaded = true;
                    });
            }
        }
        else {
            // Show User's usage report
            doc.getDocumentInfo(Code.report_type.user).subscribe(data=>{
                this.info = data;
                this.isLoaded = true;
            });
        }

        //console.log(">>> DocumentReportComponent", this.info, this.userRole);

        // Title management is now handled by the parent dashboard component
    }

    ngOnInit(): void {}

    // ---------------------------------------------------------------------
    // WCAG 2.4.1 (Bypass Blocks)
    // ---------------------------------------------------------------------
    /**
     * After the component view has been initialised, move programmatic focus
     * to the <main id="documentReportMain"> landmark so that screen-reader
     * users start reading at the primary content instead of traversing any
     * repeated navigation that may precede this card when rendered as a
     * dedicated route.
     */
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        // setTimeout 0 ensures execution after the browser paints the view
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#documentReportMain');
            if (main) {
                main.focus();
            }
        });
    }

    // WCAG 2.4.3 – public wrapper invoked by the template skip link so that
    // focus shifts into the main content when users activate the control
    // via keyboard or pointer.  Delegates to the same helper used on initial
    // view load to avoid duplication.
    public focusMain(): void {
        this.moveFocusToMain();
    }

    // Handle space bar key press on status links
    @HostListener('keydown.space', ['$event'])
    onSpaceKeyPress(event: KeyboardEvent): void {
        const target = event.target as HTMLElement;
        if (target.tagName === 'A' && target.classList.contains('data')) {
            event.preventDefault();
            const status = target.getAttribute('data-status');
            if (status) {
                this.router.navigate(['/documents'], { queryParams: { status } });
            }
        }
    }
}
