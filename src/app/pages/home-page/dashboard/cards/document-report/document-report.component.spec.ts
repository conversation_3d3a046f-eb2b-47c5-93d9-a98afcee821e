import { ComponentFixture, TestBed } from '@angular/core/testing';
// CHANGE (2025-08-06): WCAG 2.4.2 – add Title provider so dependency
// injection succeeds after introducing page title logic.
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing'; // WCAG 2.4.5 – breadcrumb uses <a href>, but grid links rely on routerLink; include testing module for directive stubs

import { DocumentReportComponent } from './document-report.component';

describe('DocumentReportComponent', () => {
    let component: DocumentReportComponent;
    let fixture: ComponentFixture<DocumentReportComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RouterTestingModule],
            declarations: [ DocumentReportComponent ],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DocumentReportComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
