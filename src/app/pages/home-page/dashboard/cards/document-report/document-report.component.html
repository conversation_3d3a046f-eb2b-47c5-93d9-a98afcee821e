<!-- CHANGE (2025-08-06): WCAG 2.4.1 (Bypass Blocks) – added in-component skip
     link and converted wrapper <div> into the primary <main> landmark so
     keyboard and assistive-technology users can jump directly to the document
     statistics, bypassing any preceding repeated navigation or banner
     elements when this card is rendered as a stand-alone route.  The anchor is
     visually hidden until it receives focus. -->
<!-- Skip link comes first in DOM order so it is the initial Tab stop -->
<!-- WCAG 2.1 SC 3.2.4 (Consistent Identification) – Standardise the visible
     label used for skip links throughout the application so keyboard users
     encounter the same wording whenever the control performs the same
     function (bypass repeated blocks).  "Skip to main content" is now used
     everywhere, ensuring uniform identification irrespective of the specific
     section the internal anchor targets. -->
<!-- WCAG 4.1.3 (Status Messages) – Live region for loading and error states -->
<span *ngIf="!isLoaded" class="sr-only" aria-live="polite" i18n>Loading document statistics</span>
<span *ngIf="isLoaded" class="sr-only" aria-live="polite" i18n>Document statistics loaded</span>

<main id="documentReportMain" class="cardcontent xs:!items-stretch" aria-labelledby="documentReportHeading">
    <!-- WCAG 2.4.6 – invisible heading gives the card a programmatic name; now referenced by aria-labelledby on the container -->
    <h2 id="documentReportHeading" class="sr-only" i18n>Document report statistics</h2>
        <ng-template [ngIf]="!isAdminConsole" [ngIfElse]="adminView">
            <!-- Reordered grid items so DOM sequence matches visual presentation (WCAG 1.3.2 Meaningful Sequence) -->
            <div class="grid grid-cols-6 h-full w-full gap-y-8">
                <!-- All items in one row -->
                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 2.4.4 – add explicit aria-label so link purpose is clear when announced out of context -->
                    <a *ngIf="isLoaded"
                       class="data"
                       routerLink="/documents"
                       [queryParams]="{status: 'draft'}"
                       data-status="draft"
                       [attr.aria-label]="'Draft – view ' + (info?.draft ?? 0) + ' documents'"> <!-- Assumption: accessible name starts with visible label (WCAG 2.5.3) -->
                        <div class="data--info">{{info?.draft}}</div>
                        <div class="data--label" i18n>Draft</div>
                    </a>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 2.4.4 – clarify link purpose via descriptive aria-label -->
                    <a *ngIf="isLoaded"
                       class="data"
                       routerLink="/documents"
                       [queryParams]="{status: 'in_progress'}"
                       data-status="in_progress"
                       [attr.aria-label]="'In Progress – view ' + (info?.in_progress ?? 0) + ' documents'"> <!-- WCAG 2.5.3 -->
                        <div class="data--info">{{info?.in_progress}}</div>
                        <div class="data--label" i18n>In Progress</div>
                    </a>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 2.4.4 – clarify link purpose via descriptive aria-label -->
                    <a *ngIf="isLoaded"
                       class="data"
                       routerLink="/documents"
                       [queryParams]="{status: 'completed'}"
                       data-status="completed"
                       [attr.aria-label]="'Finalized – view ' + (info?.finalized ?? 0) + ' documents'"> <!-- WCAG 2.5.3 -->
                        <div class="data--info">{{info?.finalized}}</div>
                        <div class="data--label" i18n>Finalized</div>
                    </a>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 2.4.4 – clarify link purpose via descriptive aria-label -->
                    <a *ngIf="isLoaded"
                       class="data"
                       routerLink="/documents"
                       [queryParams]="{status: 'canceled'}"
                       data-status="canceled"
                       [attr.aria-label]="'Canceled – view ' + (info?.canceled ?? 0) + ' documents'"> <!-- WCAG 2.5.3 -->
                        <div class="data--info">{{info?.canceled}}</div>
                        <div class="data--label" i18n>Canceled</div>
                    </a>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent !med:border-r xs:border-b med:border-b" style="border-color: #e5e7eb;">
                    <!-- WCAG 2.4.4 – clarify link purpose via descriptive aria-label -->
                    <a *ngIf="isLoaded"
                       class="data"
                       routerLink="/documents"
                       [queryParams]="{status: 'expired'}"
                       data-status="expired"
                       [attr.aria-label]="'Expired – view ' + (info?.expired ?? 0) + ' documents'"> <!-- WCAG 2.5.3 -->
                        <div class="data--info">{{info?.expired}}</div>
                        <div class="data--label" i18n>Expired</div>
                    </a>
                    <nz-skeleton *ngIf="!isLoaded" class="xs:w-1/2 med:w-1/2 xs:pb-10 med:pb-10" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.avg_time_to_sign}}</div>
                        <div class="data--label text-center" i18n>Average time to sign in last 60 days</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" class="ml-10 xs:w-1/2 med:w-1/2" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>
            </div>
        </ng-template>
        <ng-template #adminView>
            <!-- Reordered grid items for admin view so DOM order reflects visual arrangement (WCAG 1.3.2) -->
            <div class="grid grid-cols-6 h-full w-full gap-y-8">
                <!-- All items in one row -->
                <!-- WCAG 4.1.1 – switch wrapper from <span> (inline) to <div>
                     (block) so that nested <div> elements are semantically
                     valid; prevents parser errors due to disallowed nesting
                     of block elements within phrasing content. -->
                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.draft}}</div>
                        <div class="data--label" i18n>Draft</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 4.1.1 fix -->
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.in_progress}}</div>
                        <div class="data--label" i18n>In Progress</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 4.1.1 – replaced <span> with <div> to correct block-in-inline nesting -->
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.finalized}}</div>
                        <div class="data--label" i18n>Finalized</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <!-- WCAG 4.1.1 fix -->
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.canceled}}</div>
                        <div class="data--label" i18n>Canceled</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent !med:border-r xs:border-b med:border-b">
                    <!-- WCAG 4.1.1 – corrected invalid <div> inside <span> nesting -->
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.expired}}</div>
                        <div class="data--label" i18n>Expired</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" class="xs:w-1/2 med:w-1/2 xs:pb-10 med:pb-10" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>

                <div class="col-span-1 xs:col-span-6 med:col-span-6 cardcontent--content datacontent">
                    <div *ngIf="isLoaded" class="data">
                        <div class="data--info">{{info?.avg_time_to_sign}}</div>
                        <div class="data--label text-center" i18n>Average time to sign in last 60 days</div>
                    </div>
                    <nz-skeleton *ngIf="!isLoaded" class="ml-10 xs:w-1/2 med:w-1/2" [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
                </div>
            </div>
        </ng-template>
</main>
