a {
  color: inherit;

  &:hover {
    /* Accessibility (WCAG 1.4.3): switch to design-system high-contrast blue (#055485 – 6.15:1 against white)
       to ensure link hover state meets minimum colour-contrast requirements */
    color: #055485; // Assumption: hover colour aligns with brand token and improves contrast

    .data--info {
      font-size: 3.75rem; // WCAG 1.4.4 – convert fixed 60px font-size to relative rem so text scales with user preference (60px ÷ 16px = 3.75rem)
      line-height: normal; // WCAG 1.4.12 – ensure text can grow vertically without clipping on hover
    }
  }

  // WCAG 2.1 – 1.4.1 (Use of Colour): ensure link purpose remains identifiable
  // without relying on colour alone by underlining label text and adding a
  // focus outline that is perceivable in high-contrast or grayscale modes.
  &:focus {
    outline: 3px solid #2D323D; // Brand focus blue – ≥ 3 : 1 on white
    outline-offset: 2px;
  }

  // WCAG 2.4.7 (Focus Visible) – replicate the same visually
  // distinctive indicator on :focus-visible to guarantee it appears for
  // keyboard-only users when browsers apply the new heuristics that suppress
  // the generic :focus ring after pointer interaction.
  &:focus-visible {
    outline: 3px solid #2D323D; // Brand focus blue – ≥ 3 : 1 on white
    outline-offset: 2px;
  }

  .data--label {
    text-decoration: underline; // Assumption: underline provides non-colour cue for link
  }
}

// WCAG 2.4.7 (Focus Visible) – ensure programmatic focus moved onto the
// <main> landmark via the skip link receives a highly visible outline even in
// environments where a global reset removes default focus styles.
#documentReportMain:focus,
#documentReportMain:focus-visible {
  outline: #2D323D 3px solid; /* Brand focus blue – meets 3:1 contrast */
  outline-offset: 2px;
}

// WCAG 2.1 – utility class to visually hide elements while keeping them accessible to screen readers (reused pattern)
.sr-only {
  position: absolute; /* Assumption: remove from visual flow */
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 (Bypass Blocks)
   ------------------------------------------------------------------------
   Styles for the in-component skip link that allows keyboard users to jump
   directly to the document statistics grid.  The link is positioned off-
   screen until it receives focus, at which point it is moved into view with
   sufficient colour contrast and a clear focus outline.
*/

.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: #2D323D 3px solid; /* Brand focus blue – ≥ 3 : 1 on white */
}

/* -------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   -------------------------------------------------------------------------
   The grid cell separators on medium & extra-small breakpoints are added via
   Tailwind utility classes (e.g. `!med:border-r`, `xs:border-b`).  Tailwind
   sets only the border-width, leaving the colour to inherit from the
   `currentColor`, which in this component defaults to a light grey that can
   drop below the required 3 : 1 contrast ratio against the white card
   background.  Explicitly set a darker border colour so the divisions remain
   perceivable for low-vision users across all themes/prints.

   Assumption: These styles are scoped to this component (ViewEncapsulation
   Emulated) therefore they will not affect other cards that rely on the
   inherited colour.
*/

.datacontent {
  border-color: #757575; /* Dark grey (≈ 4.5 : 1 vs white) */

  /* WCAG 1.4.10 – allow large numbers in the statistic blocks to wrap if
     necessary so the card does not introduce horizontal scroll on narrow
     screens (e.g., 320 px). `overflow-wrap:anywhere` permits breaking long
     digit sequences that lack natural breakpoints. */ // Assumption: numbers
     // need not stay on one line for comprehension.
  .data--info {
    overflow-wrap: anywhere;
  }
}

#documentReportMain {
  padding-top: 60px !important;
}

@media (min-width: 1388px) {
  #documentReportMain {
    padding-right: 50px !important;
  }
}