<!-- Unique heading ID added to satisfy WCAG 4.1.1 (no duplicate IDs). -->
<!-- WCAG 4.1.2 – expose loading state to assistive technologies via aria-busy. -->
<section class="cardcontent" [attr.aria-labelledby]="headingId" [attr.aria-busy]="!isLoaded">
     <div *ngIf="isLoaded" class="cardcontent--content flex-col justify-center">
         <h3 class="data--label" i18n>Estimated upcoming bill</h3>
         <!-- WCAG 4.1.2 – announce dynamically loaded amount once data arrives. -->
         <div class="data--info" aria-live="polite" i18n>$ {{amount | currency:'':''}}</div>
     </div>
     <!-- Accessibility: hide decorative loading skeleton from assistive technology (WCAG 1.1.1 compliance) -->
     <nz-skeleton *ngIf="!isLoaded" class="cardcontent--content flex-col justify-center"  [nzActive]="true" [nzParagraph]="{ rows: 1 }" aria-hidden="true"></nz-skeleton>
 
     <div class="cardcontent--actions">
         <a routerLink="/payment" (keydown.space)="onSpaceKey($event)" i18n>Payment Method</a>
         <a routerLink="/invoice/list" (keydown.space)="onSpaceKey($event)" i18n>Invoices</a>
     </div>
</section>
