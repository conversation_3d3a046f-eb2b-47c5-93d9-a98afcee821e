// Ensures the heading ID is unique to prevent duplicate IDs (WCAG 4.1.1 – Parsing).
// Added Angular Title service so the component can set a descriptive <title>
// element that reflects the visible page heading – WCAG 2.4.2 (Page Titled).
// Assumption: There is no global title-management strategy; therefore this
// component takes responsibility for its own view.
// WCAG 3.1.2 – Allow authors to specify the language of the billing card when
// it differs from the surrounding page.  Assistive technologies will then
// switch pronunciation rules appropriately.  Mirrors the pattern used in
// card.component to keep the API consistent across dashboard cards.
// Assumption: callers supply a valid BCP 47 language tag (e.g. "es", "fr-CA").
import { Component, Optional, Input } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { UserService } from '@core/services';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  styleUrls: ['./billing.component.less']
})
export class BillingComponent {
  amount: any;
  isLoaded = false;

  // WCAG 3.1.2 – Input allowing consumers to override the language of this
  // component’s content when it contains a different natural language than the
  // surrounding document.  The value is applied to the main <section> element
  // via attribute binding in the template.
  @Input() lang?: string; // Assumption: optional; falls back to document language

  headingId: string;

  /**
   * Target ID for the local “Skip to main content” link that lets
   * keyboard users bypass the navigation links inside the card – WCAG 2.4.1
   * (Bypass Blocks). // Assumption: `headingId` is already unique per
   * component instance, therefore we derive the region ID from it to guarantee
   * uniqueness while keeping the DOM easy to debug.
   */
  // Assumption: The region ID only needs to be unique within the document, which
  // is satisfied by basing it on the already-unique headingId.
  public regionId: string;

  private static nextId = 0;

  constructor(
    private userService: UserService,
    // @Optional() prevents DI errors in unit tests that omit a Title provider.
    // This mirrors patterns used in other components of the code-base – WCAG 2.4.2
    @Optional() private titleService?: Title
  ) {
    this.headingId = `billing-heading-${BillingComponent.nextId++}`;
    this.regionId = `${this.headingId}-region`; // WCAG 2.4.1 – local bypass target
    userService.getBill().subscribe(data => {
      this.amount = data;
      this.isLoaded = true;
    });

    // Keep the document <title> in sync with the visible heading so that the
    // topic of the page is conveyed to all users, including those relying on
    // assistive technologies or browser UI such as tabs and bookmarks.
    // Complies with WCAG 2.4.2 (Page Titled – Level A, required for Level AA).
    this.updateDocumentTitle();
  }

  /**
   * Title management is now handled by the parent dashboard component
   */
  private updateDocumentTitle(): void {
    // Title management is now handled by the parent dashboard component
  }

  onSpaceKey(event: Event): void {
    event.preventDefault();
    (event.target as HTMLElement).click();
  }

}
