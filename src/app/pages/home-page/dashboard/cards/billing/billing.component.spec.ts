// Tests updated headings ID uniqueness fix (WCAG 4.1.1 – Parsing).
// Injected Title stub so the component can set the document title during the
// test without depending on the real browser API – WCAG 2.4.2.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';


import { BillingComponent } from './billing.component';

describe('BillingComponent', () => {
    let component: BillingComponent;
    let fixture: ComponentFixture<BillingComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [],
            declarations: [ BillingComponent ],
            providers: [ { provide: Title, useValue: { setTitle: (_t: string) => {} } } ]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(BillingComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

});
