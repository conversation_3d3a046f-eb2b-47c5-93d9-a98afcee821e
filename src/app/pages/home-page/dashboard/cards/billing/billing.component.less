// Minor layout tweaks retained – original visual design preserved after HTML updates.
.cardcontent--actions {
  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    display: flex;
    gap: 0.5rem;


    flex-wrap: wrap;
  }

  li {
    white-space: normal;
  }



  a,
  a:visited,
  a:hover,
  a:active,
  a:focus {
    text-decoration: underline;
    color: #055485;
  }


  a:focus,
  a:focus-visible {
    outline: #2D323D 3px solid;
    outline-offset: 2px;
  }

}



/* WCAG 2.4.1 – Styles for the component-scoped skip link that lets users
   bypass the repeated navigation links and move focus directly to the main
   billing information region.  The link is positioned off-screen until it
   receives keyboard focus so it remains discoverable but does not clutter
   the UI. */
.skip-link {
  position: absolute;
  left: -9999px; // Assumption: off-screen technique is acceptable in current design system.
  top: auto;
  width: auto;
  height: auto;
  overflow: hidden;
  white-space: nowrap;
}

.skip-link:focus {
  left: 0; // Slide into viewport when focused
  top: 0;
  z-index: 1000; // Ensure the link is above overlays
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 2px solid #000000; // Visible focus indicator (≥3:1 contrast)
}

/* -------------------------------------------------------------------------
   WCAG 1.4.10 – Reflow (Level AA)
   ---------------------------------------------------------------------- */
// Ensure the billing card never introduces horizontal scrolling at a 320 px
// viewport (or 400 % zoom).
// 1. The root section (.cardcontent) is allowed to grow/shrink fluidly.
// 2. If exceptionally long, unbroken strings are rendered (e.g. translated
//    breadcrumb labels, invoice numbers) we confine the overflow to this
//    component by enabling single-axis scrolling. This prevents the page
//    itself from requiring two-direction scrolling, thereby meeting the
//    Reflow criterion.

.cardcontent {
  max-width: 100%; // WCAG 1.4.10 – guarantee the card never exceeds viewport width
  overflow-x: auto; // WCAG 1.4.10 – contains any unavoidable horizontal overflow
  word-break: break-word; // Allows long tokens to wrap within the card
}

// Breadcrumb: allow long translated labels or dynamic tenant names to wrap so
// they do not push the layout wider than the viewport.
.breadcrumb a {
  word-break: break-word; // WCAG 1.4.10 – prevents horizontal scroll on narrow screens
}


.data--label {
  margin: 0;
}
