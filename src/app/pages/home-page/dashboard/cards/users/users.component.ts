// CHANGE (2025-08-07): WCAG 2.4.1 – implement AfterViewInit for focus shift.

// CHANGE (2025-08-07): WCAG 2.4.2 – import Title service.
import { Component, OnInit, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';

declare const $localize: any; // Assumption: provided globally
import {UserCount, UserService} from "@core/services";
import {Observable} from "rxjs";

@Component({
    selector: 'app-users',
    templateUrl: './users.component.html',
    styleUrls: ['./users.component.less']
})
export class UsersComponent implements OnInit, AfterViewInit {

    counts:any;
    isLoaded:boolean = false;

    constructor(
        private user: UserService,
        private elementRef: ElementRef, // Assumption: Angular provides ElementRef
        private titleService: Title // Assumption: Title service at root
    ) {
        user.getUserCount().subscribe(data=>{
            this.counts = data;
            this.isLoaded = true;
        })

        // Title management is now handled by the parent dashboard component
    }

    ngOnInit(): void {
    }

    // ------------------------------------------------------------------
    // WCAG 2.4.1 – focus management
    // ------------------------------------------------------------------
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#usersMain');
            if (main) {
                main.focus();
            }
        });
    }

    onSpaceKey(event: Event): void {
        event.preventDefault();
        (event.target as HTMLElement).click();
    }

}
