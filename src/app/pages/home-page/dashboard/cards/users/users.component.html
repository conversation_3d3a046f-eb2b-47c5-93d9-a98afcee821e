<!-- WCAG 2.4.6 – added invisible heading and aria-labelledby to supply a descriptive label for the card. -->
<main id="usersMain" class="cardcontent" aria-labelledby="usersCardHeading">
    <h2 id="usersCardHeading" class="sr-only" i18n>User statistics</h2>
    <!-- Replaced invalid <span> with <div> to ensure block-level semantics and maintain
         meaningful sequence for assistive technologies (WCAG 1.3.2). -->
    <div class="cardcontent--content datacontent" role="group" aria-label="User counts" i18n-aria-label>
        <div class="data">
            <div *ngIf="isLoaded" class="data--info">{{counts?.activeCount}}</div>
            <!-- Decorative loading skeleton hidden from assistive tech (WCAG 1.1.1) -->
            <nz-skeleton-element *ngIf="!isLoaded"  nzType="input"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
            <div class="data--label" i18n>Active</div>
        </div>
        <div class="data">
            <div *ngIf="isLoaded" class="data--info">{{counts?.pendingCount}}</div>
            <!-- Decorative loading skeleton hidden from assistive tech (WCAG 1.1.1) -->
            <nz-skeleton-element *ngIf="!isLoaded"  nzType="input"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
            <div class="data--label" i18n>Pending</div>
        </div>
    </div>

    <div class="cardcontent--actions">
        <!-- WCAG 2.4.4 – provide descriptive link purpose so "Manage" is no
             longer ambiguous when announced out of context. Visible text kept
             to maintain existing visual design; additional context is supplied
             via aria-label and an off-screen span. -->
        <!-- Assumption: link opens the section where users can be managed. Comment moved outside tag to avoid breaking Angular template parsing. (Validation fix) -->
        <!-- WCAG 2.1 – Replace bound aria-label with static attribute so
             `i18n-aria-label` can be processed by Angular without conflict. -->
        <a
            routerLink="/teams"
            (keydown.space)="onSpaceKey($event)"
            aria-label="Manage users" i18n-aria-label
            i18n
        >
            Manage <!-- Removed nested i18n to fix compilation error -->
            <span class="sr-only">(users)</span>
        </a>
    </div>
</main>
