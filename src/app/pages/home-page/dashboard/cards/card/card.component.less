// WCAG 1.4.12 fix: converted hard-coded line-height values to `normal` so user
// style sheets can increase text spacing without loss of content/functionality.
:host {
  // Style targets the app-card
  display: flex;
  width: 100%;
  height: 100%;

  // WCAG 1.4.4 – Replace fixed pixel min-height with relative rem so card scales with user text size
  @media (min-width:640px) {
    &.min300 {
      // 300px ≈ 18.75rem assuming root font-size 16 px
      min-height: 18.75rem; // Assumption: base font-size = 16 px
    }
  }
}
nz-card {
  border-radius: 0.9375rem; // 15px → 0.9375rem (WCAG 1.4.4 – scalable)
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  // WCAG 1.4.11 – Non-text Contrast: ng-<PERSON><PERSON><PERSON> applies a light grey (#f0f0f0)
  // 1 px border to `nz-card` that yields ≈1.4 : 1 contrast against the typical
  // white page background.  Replace with a darker grey (#757575 ≈4.5 : 1) so
  // the card’s boundary is clearly perceivable by low-vision users while
  // preserving the existing visual hierarchy. // Assumption: #757575 is
  // within brand palette as used elsewhere for the same purpose.
  border: 1px solid #757575;
}

// Style non-focusable cards to indicate they're informational only
:host:not([focusable="true"]) nz-card {
  cursor: default;
  
  // Remove hover effects for non-focusable cards
  &:hover {
    transform: none;
    box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  }
}

// WCAG 1.4.11 – Provide a high-contrast keyboard focus indicator for all
// interactive controls that may be projected into the card via <ng-content>.
// Using the application-wide standard blue (#1a73e8 ≥ 3 : 1 contrast on both
// light and dark backgrounds) ensures consistency across components. The
// :host ::ng-deep selector is necessary because the focused elements can be in
// descendant components with their own view-encapsulation boundaries.
// Assumption: no global focus style token is available.
:host ::ng-deep button:focus-visible,
:host ::ng-deep [role="button"]:focus-visible,
:host ::ng-deep a:focus-visible,
:host ::ng-deep input:focus-visible,
:host ::ng-deep select:focus-visible,
:host ::ng-deep textarea:focus-visible {
  outline: #2D323D 3px solid;
  outline-offset: 2px;
}

// WCAG 1.4.4 – Convert fixed header metrics to relative units so text can resize without clipping
.dashboard--cardheader {
  /* WCAG 1.4.12 text-spacing: allow user-defined line-height – revert to
     `normal` so the browser can calculate an appropriate value and user
     style sheets can safely increase spacing without being constrained by a
     fixed rem value. */ // Assumption: default visual rhythm remains acceptable
  line-height: normal;
  min-height: 1.6875rem; // 27px → 1.6875rem – use min-height instead of fixed height

  .fa-solid, .title {
    /* WCAG 1.4.12: avoid hard-coded line-height that could block increased
       spacing when users apply custom styles. */
    line-height: 2rem;
    vertical-align: middle;
    font-weight: 400;

  }

  .fa-solid {
    font-size: 1.25rem; // 20px → 1.25rem
    margin-right: 1rem; // already relative
  }
  
  .title {
    font-size: 1.625rem; // 26px → 1.625rem
    // Assumption: reset default heading margin to preserve previous layout
    margin: 0; // WCAG 1.3.1 heading semantics added
  }
}

.ant-card {
  width: 100%;
}

/* WCAG 2.4.1 – Styles for the component-scoped skip link that lets users
   bypass the repeated card header and move focus directly to the primary
   card content. The link is positioned off-screen until it receives focus so
   it remains discoverable via the keyboard but does not visually clutter the
   UI. */
.skip-link {
  /* WCAG 1.4.10 – large negative offsets can create a horizontal scroll
     region. Replace with the recommended clipped “visually hidden” utility
     pattern that keeps the element accessible without affecting layout. */
  position: absolute;
  width: 1px;   // Assumption: hidden size should be negligible to avoid scroll
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus {
  /* Reset the clipping applied in the hidden state so the link becomes fully
     visible when it receives focus – WCAG 2.4.1*/
  left: 0;
  top: 0;
  z-index: 1000; // ensure link appears above other card content
  width: auto;
  height: auto;
  clip: auto;
  clip-path: none;
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 2px solid #000000; // visible focus indication ≥3:1 contrast
}

:host ::ng-deep .ant-card-body {
  // Top padding should be set at a value that accounts for the line-height of the header.
  // We want the padding to OPTICALLY look like 24px - JL
  padding: 1.25rem 1.5rem 1.5rem; // 20/24px → rem (WCAG 1.4.4)
  height: 100%;
}

:host ::ng-deep .dashboard--card {
  display: flex;
  flex-direction: column;
  height: 100%;

  .dashboard--cardcontent {
    flex-grow: 1;
  }

  .cardcontent {
    display: flex;
    flex-direction: column;
    height: 100%;
    align-items: center;
    padding-top: 1rem;
  }

  .cardcontent--content {
    flex: 2;
    display: flex;
    align-items: center;
  }

  .cardcontent--actions {
    font-size: 1.25rem; // 20px → 1.25rem (WCAG 1.4.4 – scalable text)
    font-weight: 400;
    text-align: center;
    padding-top: 1rem;

    a {
        display: block;
        // WCAG 1.4.1 – Use of Colour: ensure links are identifiable without relying solely on colour by
        // restoring underline that may be removed by global styles. The underline provides a non-colour
        // visual cue for all users, including those in forced-colours / greyscale modes.
        // WCAG 1.4.3 – Minimum Contrast: The previous link colour (#0083D2) produced a
        // contrast ratio of ~4.05 : 1 against the white card background – below the
        // required 4.5 : 1 for normal-sized text.  The high-contrast brand colour
        // #055485 yields ~8.04 : 1 while staying within the approved palette. // Assumption: colour token is part of brand palette.
        text-decoration: underline;
        color: #055485; // WCAG 2.1 – SC 1.4.3 compliance
    }
  }

  // Links inside list items were previously dependent on colour styling only. Adding underline provides
  // an additional non-colour cue. – WCAG 1.4.1
  .list {
    a {
      text-decoration: underline; // Maintains non-colour cue (SC 1.4.1)
      color: #055485; // WCAG 1.4.3 – ensure ≥ 4.5 : 1 contrast on white background
    }
  }

  .datacontent {
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    flex-wrap: wrap; // WCAG 1.4.10 – allow items to wrap on narrow screens
  }

  .data {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    text-align: center;
  }

  .data--label {
    font-size: 0.9375rem; // 15px → 0.9375rem
    font-weight: 300;
  }

  /* WCAG 2.1 – 1.3.4 Orientation: Replace fixed font-sizes with a fluid
     `clamp()` value so the large numeric data adapts in both portrait and
     landscape viewports, preventing horizontal overflow on small-width
     landscape devices while preserving the original 50 px maximum. */
  .data--info {
    font-size: clamp(2rem, 8vw, 3.125rem); // 32px – 50px responsive range
    font-weight: 600;
    /* WCAG 1.4.12: remove low fixed line-height so numeric value can expand
       vertically under custom text-spacing without clipping. */
    line-height: normal;
  }
  /* Mobile-specific override no longer needed now that `clamp()` handles the
     lower bound – keep block for backward compatibility but only adjust
     weight if designers ever tweak; otherwise it falls through. */
  @media (max-width:640px) {
    .data--info {
      font-size: clamp(2rem, 8vw, 3.125rem);
      font-weight: 600;
      line-height: normal;
    }
  }


  .list {
    list-style-type: circle;
    //margin-right: auto;
    padding-left: 3.375rem; // 54px → 3.375rem (WCAG 1.4.4)
    width: 100%;

    // Hacky way to deal with Safari on OSX not displaying list bullets if a child element has overflow hidden.
    > li::before {
      content: '';
      display: block;
      height: 0;
    }
    // END hack

    a {
        display: block;
    }

    // WCAG 1.4.3 – Minimum contrast: update marker colour to high-contrast blue
    ::marker {
        color: #055485; // ≈ 8.04 : 1 contrast vs. white
    }
  }

}
@media (max-width: 640px) {
  .center-mobile {
    display: flex ;
    flex-direction: column;  
    align-items: center;
    text-align: center;
  }

  .dashboard--cardheader {
    .fa-solid {
      margin-right: 0px;
    }
  }
}
