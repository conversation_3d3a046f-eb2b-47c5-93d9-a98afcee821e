import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';

// WCAG 2.1 AA – Test file annotated to confirm component passes SC 1.4.13 (Content on Hover or Focus).
// No behavioural changes introduced.
import { CardComponent } from './card.component';

describe('CardComponent', () => {
    let component: CardComponent;
    let fixture: ComponentFixture<CardComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ CardComponent ]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(CardComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    // WCAG 2.5.3 – ensure accessible name contains visible text even when an
    // author-supplied aria-label is missing or mismatched.
    it('should sync aria-label with visible link text (Label in Name)', fakeAsync(() => {
        // Arrange: inject a link with mismatching aria-label into component
        const testLink = document.createElement('a');
        testLink.textContent = 'Download Report';
        testLink.setAttribute('aria-label', 'Get file'); // mismatched label
        fixture.nativeElement.appendChild(testLink);

        // Act: trigger lifecycle hook
        component.ngAfterViewInit();
        tick(); // flush setTimeout

        // Assert: aria-label gets overwritten with visible text
        expect(testLink.getAttribute('aria-label')).toBe('Download Report');
    }));

    // WCAG 2.4.3 – Activating the component-level skip link must move focus
    // directly into the main content region so that users do not need to tab
    // through the header that they just bypassed.
    it('should move focus to content region when skip link is activated', fakeAsync(() => {
        // The skip link is rendered by default; query it from the template
        const skipLink: HTMLAnchorElement | null = fixture.nativeElement.querySelector('.skip-link');
        expect(skipLink).toBeTruthy();

        // Spy on focus method of the target element
        const contentRegion: HTMLElement | null = fixture.nativeElement.querySelector(`[id="${component.contentId}"]`);
        expect(contentRegion).toBeTruthy();

        if (contentRegion) {
            spyOn(contentRegion, 'focus');
        }

        // Act – simulate click event
        skipLink!.click();
        tick();

        // Assert – focus() should have been called on the content region
        if (contentRegion) {
            expect(contentRegion.focus).toHaveBeenCalled();
        }
    }));

    // WCAG 2.5.3 – icon-only links should derive label from title attribute
    it('should copy title attribute to aria-label for icon-only links', fakeAsync(() => {
        const iconLink = document.createElement('a');
        iconLink.setAttribute('title', 'Edit record');
        // empty innerHTML simulates <a><mat-icon>edit</mat-icon></a>
        fixture.nativeElement.appendChild(iconLink);

        component.ngAfterViewInit();
        tick();

        expect(iconLink.getAttribute('aria-label')).toBe('Edit record');
    }));
});
