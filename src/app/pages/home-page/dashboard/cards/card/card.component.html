<!-- WCAG 2.4.6 – Expose each card as an ARIA region labelled by its heading
     so assistive-technology users can quickly navigate between dashboard
     sections.  The role and aria-labelledby attributes are bound only when a
     title is provided to prevent empty labels. -->
<!-- WCAG 3.1.2 – Pass through optional `lang` input so authors can identify
     language changes at the component level.  Binding to null avoids adding
     an empty attribute when no override is necessary. -->
<nz-card [attr.role]="title ? 'region' : null" [attr.aria-labelledby]="title ? headingId : null" [attr.lang]="lang ? lang : null" [tabindex]="focusable ? 0 : -1">
    <div class="dashboard--card">
        <!-- WCAG 2.4.1 – Add a component-scoped skip link so keyboard users can
             bypass the card header (icon + title) and move focus directly to
             the primary card content.  The link is visually hidden until it
             receives focus – styling lives in the component stylesheet. -->
        <!-- WCAG 3.2.4 – Standardise wording of the local bypass link to match
             the site-wide convention (“Skip to main content”) so users hear
             the exact same label whenever the action is to bypass a repeated
             block, regardless of scope (page-level or component-level). This
             ensures consistent identification across the application. -->
        <div class="dashboard--cardheader center-mobile flex">
            <!-- Assumption: Icon is purely decorative; hide from assistive technology for WCAG 1.1.1 compliance -->
            <i *ngIf="icon" [ngClass]="'fa-' + icon" class="fa-solid" aria-hidden="true"></i>
            <!-- WCAG 1.3.1 – converted visually styled span to semantic heading for proper information hierarchy -->
            <!-- Bind unique ID for aria-labelledby relationship (generated in TS). -->
            <h3 *ngIf="title" class="title" [attr.id]="headingId">{{title}}</h3>
        </div>
        <!-- Wrap projected content in a focusable region that serves as the
             destination for the local skip link.  `tabindex="-1"` allows the
             link activation to programmatically move focus here so screen
             readers announce the landmark without altering the natural tab
             order once inside the region. – WCAG 2.4.1. -->
        <div [attr.id]="contentId" tabindex="-1">
            <ng-content></ng-content>
        </div>
    </div>
</nz-card>
