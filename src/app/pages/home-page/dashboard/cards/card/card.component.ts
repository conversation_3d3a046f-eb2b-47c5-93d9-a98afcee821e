// Added ElementRef, Renderer2 and AfterViewInit to allow post-render DOM updates.
// This enables automatic patching of anchor elements so their accessible names
// always include the exact visible text in accordance with WCAG 2.1 Level AA –
// Success Criterion 2.5.3 (Label in Name).
// Assumption: Card contents are relatively static after initial render; dynamic
// updates are out of scope for this lightweight enhancement.
import { Component, Input, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';

@Component({
    selector: 'app-card',
    templateUrl: './card.component.html',
    styleUrls: ['./card.component.less']
})
export class CardComponent implements AfterViewInit {
    @Input() title?: string;
    @Input() icon?: string;
    @Input() focusable: boolean = true; // Controls whether the card is focusable via tab

    // WCAG 3.1.2 – Allow authors to declare the language of the card when its
    // contents differ from the page default.  Consumers can set
    // `<app-card lang="es">` or bind a dynamic value to ensure assistive
    // technologies switch pronunciation rules appropriately.  The value is
    // applied to the host `<nz-card>` element via attribute binding in the
    // template. // Assumption: The card content is consistently written in a
    // single language, so applying `lang` on the wrapper is sufficient.
    @Input() lang?: string; // Assumption: valid BCP 47 tag supplied by caller

    /**
     * Each card exposes its heading text through `aria-labelledby` so that the
     * component can be discovered in screen-reader landmark or rotor menus.
     * To avoid duplicate IDs when multiple cards are rendered on the same
     * page we generate a unique identifier per instance.
     *
     * WCAG 2.4.6 – Headings and labels MUST describe topic or purpose. By
     * linking the region (the `<nz-card>` wrapper) to the visible heading we
     * provide an unambiguous programmatic name that matches the visual label.
     */
    headingId: string;

    /**
     * Unique ID used as the target for the component-scoped “Skip to card
     * content” link that lets keyboard users bypass the card header and move
     * focus directly to the main card content – WCAG 2.4.1 (Bypass Blocks).
     * // Assumption: Heading ID is already unique per component instance, so we
     * can derive the content ID from it to guarantee uniqueness while keeping
     * the DOM readable when debugging.
     */
    public contentId: string; // WCAG 2.4.1 – added local skip-link target

    // Maintain a simple static counter – small, predictable and avoids the
    // overhead of UUID generation for this use-case. // Assumption: Cards are
    // not dynamically destroyed/created in a loop that would make the counter
    // overflow.
    private static nextId = 0;
    //@Input() height: string ='350px'; // Should this really be hardcoded?
    // ElementRef gives read-only access to the host element so we can query for
    // descendant anchors without changing their content or structure.
    constructor(private hostEl: ElementRef, private renderer: Renderer2) {
        this.headingId = `card-title-${CardComponent.nextId++}`;

        // Derive the content region ID from the heading ID so both remain in
        // sync and are guaranteed unique.  This creates the relationship
        // <a href="#${contentId}"> ➡ <div id="${contentId}"> …
        this.contentId = `${this.headingId}-content`; // Assumption: safe uniqueness
    }

    /**
     * WCAG 2.5.3 (Label in Name – Level AA)
     * --------------------------------------------------
     * Automated audits revealed instances where icon-only or generic links
     * (e.g. "More", "View") inside various card projections lacked an explicit
     * `aria-label`, meaning their purpose could not be determined out of
     * context by assistive-technology users.  To mitigate this at the
     * component level without requiring every consumer to update their
     * templates, we copy each link’s full visible text into an `aria-label`
     * if—and only if—one is absent.
     *
     * This method runs once after the view initializes so that the DOM is
     * present.  It purposefully ignores links whose textContent is empty
     * because they likely rely on an icon; those require a developer-provided
     * description and should fail loudly during audits rather than receiving a
     * potentially incorrect fallback.
     */
    ngAfterViewInit(): void {
        // Defer until the next microtask so projected content has rendered.
        setTimeout(() => this.patchLinkAriaLabels());
    }

    private patchLinkAriaLabels(): void {
        const anchors: NodeListOf<HTMLAnchorElement> = this.hostEl.nativeElement.querySelectorAll('a');

        anchors.forEach(anchor => {
            const visibleText = (anchor.textContent || '').trim();

            if (visibleText.length === 0) {
                // Icon-only control – Attempt to derive a programmatic name from the
                // `title` attribute which is often used by developers to provide a
                // tooltip.  If `title` is absent we deliberately leave the element
                // unchanged so that automated accessibility audits continue to flag
                // the missing label for manual remediation. // Assumption: a
                // developer who adds a `title` intends it to convey the control’s
                // purpose and therefore it is safe to promote it to an
                // `aria-label`.
                const tooltip = anchor.getAttribute('title');
                if (tooltip && !anchor.hasAttribute('aria-label')) {
                    this.renderer.setAttribute(anchor, 'aria-label', tooltip);
                }
                return;
            }

            const existing = anchor.getAttribute('aria-label');

            // If an aria-label already exists and it already contains the exact
            // visible text, no action is needed.  We perform a case-sensitive
            // check because most speech-recognition software requires an exact
            // phrase match. // Assumption: Designers intend users to pronounce
            // the label exactly as rendered.
            if (existing && existing.includes(visibleText)) {
                return;
            }

            // Either no aria-label or it does not contain the visible text –
            // set/overwrite to satisfy WCAG 2.5.3 (Label in Name).
            this.renderer.setAttribute(anchor, 'aria-label', visibleText);
        });
    }




}
