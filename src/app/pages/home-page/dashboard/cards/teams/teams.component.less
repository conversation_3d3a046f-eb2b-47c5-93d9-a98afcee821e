// WCAG 2.1 – 1.4.1 (Use of Colour): underline the "Manage" link and provide a
// high-contrast focus outline so meaning and interaction are not conveyed by
// colour alone.

.cardcontent--actions a {
  text-decoration: underline; // Assumption: acceptable given current design language.
}

.cardcontent--actions a:focus,
.cardcontent--actions a:focus-visible {
  // WCAG 2.4.7 – Expose same strong outline under :focus-visible so the cue
  // persists across input modality changes.
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 – styles for skip link
   ------------------------------------------------------------------------ */
.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: 2px solid #1a73e8;
}
