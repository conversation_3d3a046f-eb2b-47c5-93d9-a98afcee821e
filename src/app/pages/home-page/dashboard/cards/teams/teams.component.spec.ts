// CHANGE (2025-08-07): WCAG 2.4.2 – include Title provider.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing'; // WCAG 2.4.5 – routerLink present in template

import { TeamsComponent } from './teams.component';

describe('TeamsComponent', () => {
    let component: TeamsComponent;
    let fixture: ComponentFixture<TeamsComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ TeamsComponent ],
            imports: [RouterTestingModule],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(TeamsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
