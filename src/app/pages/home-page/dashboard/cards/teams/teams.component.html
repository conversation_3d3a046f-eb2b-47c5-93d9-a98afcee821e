<!-- WCAG 2.4.6 – supply descriptive programmatic name via hidden heading and aria-labelledby. -->
<main id="teamsMain" class="cardcontent" aria-labelledby="teamsCardHeading">
    <h2 id="teamsCardHeading" class="sr-only" i18n>Team statistics</h2>
    <!-- Replaced invalid <span> with <div> to keep block-level children inside a block
         container, ensuring assistive technologies interpret the structure logically (WCAG 1.3.2). -->
    <div class="cardcontent--content datacontent" role="group" aria-label="Team counts" i18n-aria-label>
        <div class="data">
            <div *ngIf="isLoaded" class="data--info">{{counts?.teamsCount}}</div>
            <!-- Decorative loading skeleton hidden from assistive tech (WCAG 1.1.1) -->
            <nz-skeleton-element *ngIf="!isLoaded"  nzType="input"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>
            <div class="data--label" i18n>Teams</div>
        </div>
        <!-- <div class="flex flex-col items-center">
            <div class="data--info">{{(counts|async)?.settungsCount}}</div>
            <div class="data--label" i18n>Settings groups</div>
        </div> -->
    </div>

    <div class="cardcontent--actions">
        <!-- WCAG 2.4.4 – add specific accessible name to clarify link purpose
             for assistive technology users. Visible label preserved. -->
        <!-- Assumption: link navigates to teams management area. Comment moved outside tag to avoid breaking Angular template parsing. (Validation fix) -->
        <!-- WCAG 2.1 – Resolve Angular i18n binding conflict as above. -->
        <a
            routerLink="/teams"
            (keydown.space)="onSpaceKey($event)"
            aria-label="Manage teams" i18n-aria-label
            i18n
        >
            Manage <!-- Removed nested i18n to fix compilation error -->
            <span class="sr-only">(teams)</span>
        </a>
    </div>
</main>
