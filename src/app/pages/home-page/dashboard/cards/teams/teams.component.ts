// CHANGE (2025-08-07): WCAG 2.4.1 – add AfterViewInit to shift focus to main.

// CHANGE (2025-08-07): WCAG 2.4.2 – import Title to set page title.
import { Component, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';

declare const $localize: any; // Assumption
import {UserCount, UserService} from "@core/services";
import {Observable} from "rxjs";

@Component({
    selector: 'app-teams',
    templateUrl: './teams.component.html',
    styleUrls: ['./teams.component.less']
})
export class TeamsComponent implements AfterViewInit {
    counts:any
    isLoaded:boolean = false;
    constructor(
        private user: UserService,
        private elementRef: ElementRef, // Assumption
        private titleService: Title // Assumption
    ) {
        user.getUserCount().subscribe(data=>{
            this.counts = data;
        this.isLoaded = true;
        })

        // Title management is now handled by the parent dashboard component
    }

    // ------------------------------------------------------------------
    // WCAG 2.4.1 – focus management
    // ------------------------------------------------------------------
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#teamsMain');
            if (main) {
                main.focus();
            }
        });
    }

    onSpaceKey(event: Event): void {
        event.preventDefault();
        (event.target as HTMLElement).click();
    }


}
