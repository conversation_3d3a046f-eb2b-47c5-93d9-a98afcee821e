// CHANGE (2025-08-07): WCAG 2.4.1 – Implement AfterViewInit to move focus to
// <main> landmark once the view initialises.

// CHANGE (2025-08-07): WCAG 2.4.2 – import Title service to set descriptive
// document title.
import { Component, ViewChild, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';

// Assumption: $localize global provided by @angular/localize at runtime.
declare const $localize: any;
import {BrandingService} from "@core/services";
import {Observable} from "rxjs";

@Component({
    selector: 'app-branding',
    templateUrl: './branding.component.html',
    styleUrls: ['./branding.component.less']
})
export class BrandingComponent implements AfterViewInit  {
    @ViewChild('branding') brandingModal:any;
    public brandingDialogType = false;

    logo:any
    isLoaded:boolean = false;

    constructor(
        private branding: BrandingService,
        private elementRef: ElementRef, // Assumption: provided by Angular DI
        private titleService: Title // Assumption: Title service provided at root
    ) {
        branding.get_logo().subscribe(data=>{
            this.logo = data;
            this.isLoaded = true;
        })

        // Title management is now handled by the parent dashboard component
    }

    showBrandingModal(): void {
        this.brandingModal.showModal();
      }

    // ------------------------------------------------------------------
    // WCAG 2.4.1 – focus management
    // ------------------------------------------------------------------
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#brandingMain');
            if (main) {
                main.focus();
            }
        });
    }


}
