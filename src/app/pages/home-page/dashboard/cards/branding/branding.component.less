/* WCAG 1.4.4 – changed fixed pixel dimensions to rem units so logo container scales with text-only zoom */
.add-logo {
    background-color: #FBFBFB;
    width: 9.375rem; // 150px → 9.375rem  // Assumption: 1rem = 16px
    border-radius: 0.9375rem; // 15px → 0.9375rem

    // WCAG 1.4.11 – Non-text Contrast: #FBFBFB background against the white
    // card offers only ~1.1 : 1 contrast, making the logo container boundary
    // hard to perceive for low-vision users.  Add a 1 px dark grey border
    // (#757575 ≈ 4.5 : 1 vs white) so the component outline is clearly
    // distinguishable without altering the existing colour scheme.
    border: 1px solid #757575; // Assumption: brand guidelines allow neutral grey borders.
}

// WCAG 2.1 – 1.4.1 (Use of Colour): ensure the actionable "Edit" link remains
// distinguishable without relying on colour alone by underlining it and adding
// a visible focus outline that works in high-contrast/forced-colours modes.
.cardcontent--actions a {
  text-decoration: underline; // Assumption: underline acceptable for design.
}

// WCAG 2.4.7 – Ensure focus is visible for both legacy :focus and the newer
// :focus-visible pseudo-class so keyboard users always receive a cue even when
// browsers suppress the generic outline after pointer interaction.
.cardcontent--actions a:focus,
.cardcontent--actions a:focus-visible {
  outline: 3px solid #2D323D; // Assumption: dashed style provides clear non-colour cue.
  outline-offset: 2px;
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 (Bypass Blocks) – styles for skip link
   ------------------------------------------------------------------------ */
.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: 2px solid #1a73e8;
}
