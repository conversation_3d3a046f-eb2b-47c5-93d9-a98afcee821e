<!-- WCAG 2.4.6 – add invisible heading so the card has a descriptive programmatic name and link the container via aria-labelledby. -->
<!-- WCAG 4.1.2 – ensure custom "Edit" control is fully keyboard-accessible by handling Space key in addition to Enter. -->
<main id="brandingMain" class="cardcontent" aria-labelledby="brandingHeading">
    <h2 id="brandingHeading" class="sr-only" i18n>Email notification branding</h2>
    <div class="data--label" i18n>Email notification logo</div>
    <div>
        <nz-card class="add-logo flex flex-col items-center pt-2">
            <ng-container *ngIf="logo  != '' && isLoaded ">
                <!-- Removed duplicate alt attribute and provided meaningful, translatable alternative text (WCAG 1.1.1) -->
                <img  i18n-alt alt="Email notification logo" [src]="logo" />
            </ng-container>
            <!-- Decorative loading placeholder hidden from assistive technology (WCAG 1.1.1) -->
            <nz-skeleton-element *ngIf="!isLoaded" nzType="image"  [nzActive]="true" aria-hidden="true"></nz-skeleton-element>


            <ng-template *ngIf="!logo" #addLogo>
                <!-- Decorative fallback company logo with descriptive alternative text (WCAG 1.1.1) -->
                <img i18n-alt alt="Signority logo" src="assets/images/BasicSiteCompanyLogoRegular.png?version=3" class="logo">
            </ng-template>
        </nz-card>
    </div>

    <div class="cardcontent--actions">
        <!-- WCAG 4.1.2 & 2.1.1 – added role="button" and keydown handler so the custom link behaves like a button for assistive tech -->
        <!-- WCAG 2.4.4 – clarify purpose: replace generic "Edit" link text with
             a descriptive accessible name so its purpose is clear when
             announced out of context (e.g. in a screen-reader links list).  The
             visible text remains "Edit" to preserve the existing UI while an
             explicit aria-label exposes additional context. -->
        <!-- Assumption: fixed English string is acceptable because surrounding text is already hard-coded.  Comment moved outside tag to avoid breaking Angular template parsing. (Validation fix) -->
        <!-- WCAG 2.1 – Angular i18n does not allow combining a bound
             `[attr.aria-label]` with the `i18n-aria-label` marker. Replaced the
             bound attribute with a static `aria-label` so the template
             compiles while still exposing a translatable string. -->
        <a
            tabindex="0"
            role="button"
            (click)="showBrandingModal()"
            (keydown.enter)="showBrandingModal()"
            (keydown.space)="showBrandingModal()"
            aria-label="Edit email notification logo" i18n-aria-label
            i18n
        >
            Edit
            <!-- Removed nested i18n to prevent Angular compilation error. Screen-reader only text remains static English. (Validation fix) -->
            <span class="sr-only">(email notification logo)</span>
        </a>
    </div>
    <branding [dialogType]="brandingDialogType" #branding></branding>
</main>
