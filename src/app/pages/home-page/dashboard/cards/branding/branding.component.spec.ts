// CHANGE (2025-08-07): WCAG 2.4.2 – provide Title in testing module.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';

import { BrandingComponent } from './branding.component';

describe('BrandingComponent', () => {
    let component: BrandingComponent;
    let fixture: ComponentFixture<BrandingComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ BrandingComponent ],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(BrandingComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
