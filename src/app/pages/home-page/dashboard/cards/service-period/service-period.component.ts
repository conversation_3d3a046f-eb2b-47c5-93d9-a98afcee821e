/* WCAG 2.1 – Guideline 1.3.3 (Sensory Characteristics):
   Added programmatic description (chartAriaLabel) so the radial chart's
   meaning is conveyed to users who cannot perceive the colour-coded graphic. */

// CHANGE (2025-08-07): WCAG 2.4.1 (Bypass Blocks) – implemented AfterViewInit
// so programmatic focus is moved to the <main> landmark after the component
// loads, enabling keyboard users to bypass repeated navigation automatically.

// CHANGE (2025-08-07): WCAG 2.4.2 (Page Titled) – import Angular Title service
// so the component can set a unique, descriptive document <title> when it is
// displayed as a stand-alone route.
import { Component, AfterViewInit, ElementRef } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {
    ApexNonAxisChartSeries,
    ApexPlotOptions,
    ApexChart,
    ApexStroke,
    ApexStates,
    ApexFill
} from "ng-apexcharts";

// Assumption: $localize global is available via @angular/localize polyfill; declare
// to satisfy TypeScript without adding new dependencies.
declare const $localize: any;

export interface ChartOptions {
    series: ApexNonAxisChartSeries;
    chart: ApexChart;
    labels: string[];
    plotOptions: ApexPlotOptions;
    stroke: ApexStroke;
    states:ApexStates;
    fill:ApexFill;
}

@Component({
    selector: 'app-service-period',
    templateUrl: './service-period.component.html',
    styleUrls: ['./service-period.component.less']
})
export class ServicePeriodComponent implements AfterViewInit {

    public chartOptions: ChartOptions;
    // Assumption: These values will be replaced by real data via inputs/service. Kept
    // as internal state for demo purposes.
    used_page =  3000;
    total_page = 5000;

    /**
     * Computed, localised description of the radial chart so that users who
     * cannot perceive colour or the visual shape still understand the meaning
     * conveyed by the component (WCAG 2.1 – Guideline 1.3.3 Sensory
     * Characteristics).
     */
    public chartAriaLabel: string;

    get_percentage(){
        return Math.trunc(this.used_page/this.total_page * 1000) / 10;
    }

    get_color = (value : number) => {
        // if (value < 55) {
        //     return '#0083D2'
        // } else if (value < 80) {
        //     return '#ffc849'
        // } else if (value< 100){
        //     return '#D40D00'
        // } else{
        //     return '#8600d4'
        // }
        return '#000000'
    }

    constructor(
        // Assumption: ElementRef is provided by Angular DI.
        private elementRef: ElementRef,
        private titleService: Title // Assumption: Title service available at root
    ) {
        let percentage = this.get_percentage()
        let color = this.get_color(percentage)
        // Build aria-label once we have the numeric values.
        const daysLeft = Math.max(this.total_page - this.used_page, 0);
        // $localize enables extraction for translation according to existing i18n pipeline.
        this.chartAriaLabel = $localize`${daysLeft} days left out of ${this.total_page} in the current service period`;

        this.chartOptions = {
            states:{ //disabling interaction if mouse hover or click
                hover:{filter:{type:'none'}},
                active:{filter:{type:'none'}}
            },
            series: [ percentage>100 ? 100 : percentage ], // prevent chart overflow
            // WCAG 1.4.10 Reflow – use relative width so the chart never exceeds
            // the available column space on very small viewports (e.g. 320 px).
            // ApexCharts accepts percentage strings; 100 % lets it shrink and
            // grow fluidly with its container. A max-width is applied via the
            // parent utility classes to retain the previous visual sizing on
            // larger screens.
            chart: { type: "radialBar", width: '100%' },
            plotOptions: {
                radialBar: {
                    hollow: { size: "60%" },  // the smaller the thicker of the track
                    // WCAG 1.4.11 – Non-text Contrast: the default light-grey
                    // #D7D9DD track provided ~1.3 : 1 contrast against the white
                    // card background, failing the required ≥ 3 : 1 ratio for
                    // graphical components that convey meaning (the remaining
                    // service period).  Darken to #757575 (≈ 4.5 : 1 on white)
                    // so low-vision users can perceive the unused segment.
                    track:{ background: "#757575"},
                    dataLabels:{
                        name: { // the name in label
                            offsetY: 10,
                            color: color,
                            // WCAG 1.4.4 – convert fixed font-size 35px to relative rem so it scales with user preference
                            fontSize: '2.1875rem', // 35px → 2.1875rem
                        },
                        value: { show:false }
                    }
                },
            },
            stroke: { lineCap: "round"},
            fill:{ colors: [color] },
            labels: [`XX`]
        };

        // ------------------------------------------------------------------
        // WCAG 2.4.2 – Page Titled
        // ------------------------------------------------------------------
        // Provide a descriptive document title so screen-reader users can
        // immediately identify the page context from the browser tab.
        // Pattern: "<Section> – Dashboard" to stay consistent across routes.
        // this.titleService.setTitle($localize`Service period – Dashboard`);
    }

    // ---------------------------------------------------------------------
    // WCAG 2.4.1 (Bypass Blocks)
    // ---------------------------------------------------------------------
    /**
     * Shift keyboard focus to the <main id="servicePeriodMain"> landmark once
     * the view is initialised so screen-reader users start reading the primary
     * content without tabbing through preceding navigation.
     */
    ngAfterViewInit(): void {
        this.moveFocusToMain();
    }

    private moveFocusToMain(): void {
        // setTimeout ensures focus is moved after the browser paints.
        setTimeout(() => {
            const main: HTMLElement | null = this.elementRef.nativeElement.querySelector('#servicePeriodMain');
            if (main) {
                main.focus();
            }
        });
    }


}
