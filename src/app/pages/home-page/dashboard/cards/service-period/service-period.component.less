// WCAG 1.4.10 – make chart responsive to prevent horizontal scroll on narrow screens.

// Assumption: `apx-chart` is a direct child, so `::ng-deep` is not needed.
:host apx-chart {
  max-width: 12.5rem; // 200px → 12.5rem
  width: 100%; // allows shrinking with container
}

/* ------------------------------------------------------------------------
   WCAG 2.4.1 (Bypass Blocks)
   ------------------------------------------------------------------------
   Styles for the in-component skip link that allows keyboard users to jump
   directly to the primary <main> landmark.  The link is visually hidden until
   it receives focus, at which point it is placed in the viewport with a clear
   focus outline and sufficient colour contrast. */

.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus,
.skip-link:focus-visible {
  position: static;
  width: auto;
  height: auto;
  margin: 1rem;
  padding: 0.5rem 1rem;
  background: #ffffff;
  color: #000000;
  font-weight: bold;
  text-decoration: underline;
  z-index: 1000;
  outline: 2px solid #1a73e8; // Assumption: brand focus colour.
}
