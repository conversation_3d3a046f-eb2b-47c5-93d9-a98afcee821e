// CHANGE (2025-08-07): WCAG 2.4.2 – add Title provider so DI resolves after
// introducing page-title logic in the component.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Title } from '@angular/platform-browser';

import { ServicePeriodComponent } from './service-period.component';

describe('ServicePeriodComponent', () => {
    let component: ServicePeriodComponent;
    let fixture: ComponentFixture<ServicePeriodComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ ServicePeriodComponent ],
            providers: [Title]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ServicePeriodComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
