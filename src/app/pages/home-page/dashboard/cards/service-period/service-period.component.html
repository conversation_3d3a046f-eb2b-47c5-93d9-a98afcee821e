
<!-- CHAN<PERSON> (2025-08-07): WCAG 2.4.1 (Bypass Blocks) – Added component-level
     skip link and wrapped the primary content inside a <main> landmark so
     keyboard and assistive-technology users can bypass repeated navigation
     when this card is rendered as a stand-alone route.  The anchor is
     visually hidden until focused. -->
<!-- Skip link must appear first in DOM order within the component -->
<!-- WCAG 2.1 SC 3.2.4 – Standardise skip-link wording so the identical
     "bypass repeated blocks" function is identified consistently across the
     application. -->
<main id="servicePeriodMain">
    <!-- WCAG 2.1 – Guideline 1.3.3: Added dynamic aria-label on radial chart so
         meaning is not conveyed through colour/shape alone. -->
<!-- -mb-10 is to align the bottom anchor 'Manage' to align other cards -->
<!-- WCAG 2.4.6 – added sr-only heading to give the card a programmatic name
     and referenced it from the container with aria-labelledby so assistive
     technology users receive a meaningful label. -->
<!-- WCAG 1.4.10 Reflow – switch to a single-column layout on narrow (≤640 px) viewports
     to avoid horizontal scroll caused by the fixed-width radial chart exceeding
     half of the card width (≈160 px at 320 px viewport). Added responsive
     Tailwind utilities: xs:grid-cols-1 xs:divide-x-0 xs:divide-y. -->
    <div class="grid grid-cols-2 xs:grid-cols-1 divide-x xs:divide-x-0 xs:divide-y mt-10 -mb-10" aria-labelledby="servicePeriodHeading">
    <h2 id="servicePeriodHeading" class="sr-only" i18n>Service period information</h2>
    <div class="flex justify-center items-center">
        <!-- WCAG 1.4.4 – switched fixed pixel font-size to relative rem so text scales with user zoom -->
        <div style="font-weight: 300; font-size: 0.9375rem;">
            ABC XX XXXX ~ ABC xx xxxx
        </div>
    </div>
    <div class="flex flex-col items-center">
        <!-- Replaced generic container with semantic heading so the descriptive label
             is announced at the correct point in the reading order (WCAG 1.3.2). // Assumption: h3 keeps visual hierarchy -->
        <!-- WCAG 1.4.4 – use relative unit (1.25rem) instead of 20px so heading enlarges with root font size -->
        <h3
            style="font-weight: 275; font-size: 1.25rem;"
            i18n
        >
            Days left
        </h3>
        <!-- Assumption: chart is the visual representation of the information shown in text (Days left). Provide an accessible name so screen-reader users understand the purpose (WCAG 1.1.1). -->
        <apx-chart
            class="-mt-3"
            [series]="chartOptions.series"
            [chart]="chartOptions.chart"
            [plotOptions]="chartOptions.plotOptions"
            [labels]="chartOptions.labels"
            [stroke]="chartOptions.stroke"
            [states]="chartOptions.states"
            [fill]="chartOptions.fill"
            role="img"
            [attr.aria-label]="chartAriaLabel"
        ></apx-chart>
        <!-- WCAG 1.3.3: Provided dynamic aria-label so information is not conveyed by colour/shape alone. -->
    </div>
    </div>

    <!-- WCAG 1.4.4 – convert margin-top 110px to 6.875rem so spacing adapts to text resizing -->
    <div class="text-center" style="margin-top: 6.875rem">
    <!-- WCAG 2.4.4 – replaced non-functional anchor with a span to avoid misleading users with an inactive link -->
    <!-- Accessibility (WCAG 1.4.3): Switched to design-system high-contrast blue
         (#055485 – 6.15:1 contrast ratio against white) to meet minimum text
         colour-contrast requirements. -->
    <!-- WCAG 1.4.4 – font-size converted from 20px to 1.25rem for scalable text -->
    <span style="font-weight: 400; font-size: 1.25rem; color:#055485;">Manage Plan</span>
    </div>
</main>
