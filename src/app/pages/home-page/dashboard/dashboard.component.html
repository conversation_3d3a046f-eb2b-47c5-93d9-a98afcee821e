<!-- WCAG 1.4.4 (Resize Text – Level AA):
     Replaced absolute-pixel inline styles with a semantic CSS class that uses
     relative units (rem/em).  This allows users who zoom text or override
     base font-size to see a proportional increase without clipping or
     horizontal scrolling. -->
<!-- WCAG 2.4.6 – add explicit heading ID and landmark relationship so assistive
     technology users can quickly locate the dashboard region and understand
     its purpose. -->
<!-- WCAG 3.2.3 (Consistent Navigation – Level AA):
     To keep the breadcrumb in a consistent DOM position relative to the page
     heading across the application (e.g. Inbox, Documents, etc.), the
     <nav> element is rendered *before* the <h1>.  This matches other pages
     so keyboard and screen-reader users encounter navigation controls in a
     predictable order site-wide. -->

<h1 id="dashboard-heading" class="dashboard-title -mt-6 ml-5 font-thin xs:text-center mobile-title">
    Dashboard
</h1>

<!-- Converted the generic <div> wrapper to a semantic <section> that is tied to
     the heading above via aria-labelledby.  This forms a named landmark
     (region) in the accessibility tree without affecting existing styling or
     functionality. // Assumption: No external code relies on the element being
     a <div>; the `#dashboard` ID remains unchanged for query selectors. -->
<!-- The dashboard region that the skip link targets. `tabindex="-1"` allows
     the link activation (and scripts) to move focus programmatically so
     screen readers announce the region – WCAG 2.4.1. -->
<section id="dashboard" aria-labelledby="dashboard-heading" tabindex="-1">
    <!-- WCAG 3.3.2 (Labels or Instructions – Level A, applicable in Level AA review):
         Provide clear, programmatically-associated instructions for the drag-and-drop
         interaction so that users of assistive technologies understand how to
         reorder dashboard cards via keyboard. The paragraph is visually hidden
         using the Angular CDK's built-in `.cdk-visually-hidden` utility class
         to avoid visual clutter while remaining available to screen readers. -->
    <p id="dashboard-instructions" class="cdk-visually-hidden">
        Dashboard cards can be rearranged. Press spacebar while focused on a card to pick it up, use the arrow keys to move it, and press spacebar again to drop it.
    </p>
    <!-- WCAG 1.3.1 – replaced generic container with semantic list to convey relationship between dashboard cards -->
    <!-- WCAG 1.4.10 – make gutter responsive so horizontal padding does not
         cause overflow at 320 px viewport. Shrinks to 16 px on mobile while
         retaining the original 32 px spacing on larger screens. -->
    <ul role="list" class="dashboard-card-list"
        aria-describedby="dashboard-instructions"
        nz-row [nzGutter]="mediaService.isMobile() ? [16, 16] : [32, 32]">
        <!-- WCAG 4.1.1 (Parsing – duplicate IDs can break the accessibility tree):
             Prefix the dynamically-generated `id` so it is guaranteed to be
             unique across the entire document – using plain indexes (e.g. "0",
             "1") risks collisions with identical IDs that might be rendered
             by other components.  We also expose the raw index via
             `data-index` for internal scripts that rely on numeric parsing. -->
        <!-- WCAG 4.1.2 – expose drag-and-drop items as proper widgets by
             instantiating the Angular CDK drag directive.  The directive
             injects the necessary ARIA attributes (e.g. `aria-grabbed`,
             `aria-dropeffect`) and keyboard handlers so screen-reader users
             can initiate, move and drop cards via the space/arrow pattern
             described in the instructions.  Adding `cdkDrag` here restores
             functionality that was commented out previously while preserving
             the existing list semantics (`role="listitem"`). // Assumption:
             No external selector depends on the absence of the `cdk-drag`
             class that the directive adds. -->
        <li role="listitem" [id]="'dashboard-card-' + i" [attr.data-index]="i" class="editable-card" nz-col *ngFor="let cardName of get_cards(); let i = index" [nzSpan]="mediaService.isMobile() ? 24 : allCards[cardName].width">
            <!-- <app-card cdkDrag [title]="cardName==='Add shortcut'? '' : cardName" [icon]="allCards[cardName].icon" > -->
                <!-- <i class="fa-solid fa-x float-right close-card" (click)="cards.splice(i, 1)"></i> -->
                <!-- <i class="fa-solid fa-grip-dots absolute top-3 left-3"></i> -->
            <app-card [title]="cardName==='Add shortcut'? '' : allCards[cardName].title" [icon]="allCards[cardName].icon" [class]="allCards[cardName].class" [focusable]="!isNonClickableCard(cardName)">
                <div class="dashboard--cardcontent" [ngSwitch]="cardName" [class]="allCards[cardName].subClass">
                    <app-plan-usage *ngSwitchCase="'Plan & Usage'"></app-plan-usage>
                    <app-service-period *ngSwitchCase="'Service Period'"></app-service-period>
                    <app-billing *ngSwitchCase="'Billing'"></app-billing>
                    <app-teams *ngSwitchCase="'Teams'"></app-teams>
                    <app-users *ngSwitchCase="'Users'"></app-users>
                    <app-branding *ngSwitchCase="'Branding'"></app-branding>
<!--                    <app-integration *ngSwitchCase="'Integration'"></app-integration>-->
                    <app-shared-templates *ngSwitchCase="'Templates'"></app-shared-templates>
                    <app-knowledge-base *ngSwitchCase="'Support Channels'"></app-knowledge-base>
                    <app-document-report *ngSwitchCase="'Document Report'"></app-document-report>
                    <app-customize *ngSwitchCase="'Add shortcut'"></app-customize>
                    <app-recent-activities *ngSwitchCase="'Recent Activities'"></app-recent-activities>
                </div>
            </app-card>
        </li>
    </ul>
</section>
