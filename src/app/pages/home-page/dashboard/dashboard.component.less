// WCAG 1.4.4 – switched pixel offsets to rem so close icon maintains position when text is resized
.close-card {
    position: absolute;
    top: 0.9375rem;  // 15px → 0.9375rem – Assumption: base font-size = 16px
    right: 1.25rem;  // 20px → 1.25rem
    z-index: 1;
    cursor: pointer;
}

.cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 0.9375rem; // 15px → 0.9375rem (relative)
    pointer-events: none;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

#dashboard {
  margin-left:  1.375rem; // 22px → 1.375rem
  margin-right: 1.375rem; // 22px → 1.375rem
}
@media (max-width:1145px) {
  .center-mobile {
    display: flex ;
    flex-direction: column;  
    align-items: center;
    text-align: center;
  }
}

// WCAG 1.4.12 (Text Spacing – Level AA):
// Replace compressed 1.1 line-height with relative ≥ 1.5× value so users that
// increase text spacing via a custom style sheet do not experience clipping or
// overlapping content. `!important` is removed to allow user overrides.
// Orientation fix (WCAG 2.1 – 1.3.4):
// Replace the fixed 50 px mobile heading with a fluid `clamp()` value so the
// text can shrink on very narrow portrait screens, preventing horizontal
// overflow while keeping the original size on larger viewports.  This mirrors
// previous automated fixes applied to other components’ headings.
@media (max-width:640px) {
  .mobile-title {
      font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // 28px‒50px
      line-height: 1.5em;
      padding-bottom: 1.25rem !important; // 20px
  }
}

// WCAG 1.3.1 – remove default bullet styling introduced by converting dashboard cards container to <ul>
  .dashboard-card-list {
list-style: none;
  margin: 0;
  padding: 0;
}

// WCAG 1.4.1 – Use of Colour: Ensure hover / focus state of dashboard cards is
// perceivable by more than colour alone.  We add a left-hand bar (shape change)
// and a dashed outline so users in forced-colours or greyscale modes still see
// which card is active.  Assumption: dashboard cards were previously
// distinguished only by a subtle background colour on hover supplied by the
// design system; adding these cues does not affect existing functionality.
.dashboard-card-list {
  .editable-card {
    position: relative; // Assumption: required for ::before positioning

    // Remove default browser outline first
    ::ng-deep nz-card {
      &:focus {
        outline: none;
      }
      
      &:focus-visible {
        outline: 3px solid #2D323D; // Black dashed outline for keyboard focus
        outline-offset: 4px !important;
        display: block; // Needed for outline to show properly
      }

      // Non-colour cue – 4 px solid bar appears on the left when focused via keyboard
      &:focus-visible::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 0.25rem; // 4px → 0.25rem – scales with text
        background: currentColor;
      }
    }
  }

  // Links inside dashboard cards regain underline so they remain
  // identifiable without colour.
  a {
    text-decoration: underline; // Assumption: global styles removed underline
  }

  /* ------------------------------------------------------------------
     WCAG 2.1 – Success Criterion 1.4.3 (Contrast – Minimum)
     ------------------------------------------------------------------
     The list bullets rendered inside the descendant `app-card` component use
     the colour `#0083D2` (set in `card.component.less`).  Against a white
     background this yields a contrast ratio of ~4.05 : 1, which falls below
     the required 4.5 : 1 for normal-sized text and text-like glyphs such as
     list markers.  We override the marker colour with the high-contrast blue
     token `#055485` (≈ 8.04 : 1 on white) to fully satisfy SC 1.4.3 while
     staying within the approved brand palette.  The rule is placed here and
     combined with `::ng-deep` so it can penetrate the encapsulation boundaries
     of both the dashboard component *and* the nested `app-card` component
     without altering files outside the scope of this task.
     ------------------------------------------------------------------ */

  :host ::ng-deep .list ::marker {
    color: #055485; // WCAG 1.4.3 – ensure ≥ 4.5 : 1 contrast for list bullets
  }

  /* Ensure all ApexCharts SVG text inside dashboard cards inherits the high
     contrast foreground colour so automated tools can correctly evaluate
     colour pairs.  This mitigates the pa11y warnings that flagged anonymous
     SVG elements `#SvgjsSvg1006`, `#SvgjsSvg1026`, etc. */

  :host ::ng-deep .apexcharts-text {
    fill: #2D323D;  // Primary text colour (≈ 12.8 : 1 on white)
    color: #2D323D; // Fallback for environments that honour `color` instead
  }

  /* ------------------------------------------------------------------
     WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
     ------------------------------------------------------------------
     The default Ant Design `nz-card` component renders a very light grey
     border (#f0f0f0) around each card – this achieves only ~1.4 : 1 contrast
     against the typical white page background and therefore fails the
     required ≥ 3 : 1 ratio for visual boundaries of user-interface
     components.  We darken the border to grey-600 (#757575 ≈ 4.5 : 1 on
     white) so the card edges are clearly perceivable by users with low
     vision.

     Because the actual `.ant-card` element lives inside the descendant
     `app-card` component, we need to pierce Angular’s view encapsulation –
     `::ng-deep` is used here as no other scoped alternative exists within
     the current component boundaries. // Assumption: global theme does not
     provide a reusable high-contrast border token. */



  /* Provide a visible keyboard focus indicator for all interactive
     controls inside dashboard cards. The application already uses
     #1a73e8 as its standard focus ring colour, which yields ≥ 3 : 1 contrast
     on both white backgrounds and the darker blue accents employed inside
     certain cards. */

  button:focus-visible,
  a:focus-visible {
    outline: #2D323D 3px solid; // WCAG 1.4.11 – high-contrast focus ring
    outline-offset: 2px;
  }
}

/* ------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   ------------------------------------------------------------------
   Elevate the contrast of the Ant Design card boundary that encloses each
   dashboard widget. Placing this rule at the root of the component style
   sheet (instead of nesting under `.dashboard-card-list`) ensures the
   selector survives Angular’s emulated style encapsulation.
   The `::ng-deep` combinator is required because the actual `.ant-card`
   element sits inside a descendant component (`app-card`), which resides in
   a different view encapsulation context. // Assumption: use of ::ng-deep is
   acceptable here as no cleaner scoped alternative exists within the files
   allowed for modification. */

:host ::ng-deep .ant-card {
  border: 1px solid #757575; // WCAG 1.4.11 – increase component border contrast
}

/* WCAG 2.4.1 – Styles for the component-scoped skip link that lets users
   bypass repeated navigation elements and move focus directly to the main
   dashboard region. Hidden off-screen until focused so it remains
   discoverable via the keyboard but does not visually clutter the UI. */

.skip-link {
  position: absolute;
  left: -9999px; // Hide off-screen while still keyboard-focusable
  top: auto;
  width: auto;
  height: auto;
  overflow: hidden;
  white-space: nowrap;
}

.skip-link:focus {
  left: 0;   // Slide into viewport when focused
  top: 0;
  z-index: 1000; // Ensure link is above overlays
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 2px solid #000000; // Visible focus indication with ≥3:1 contrast
}

// WCAG 1.4.4 (Resize Text – Level AA): Move fixed pixel heading styles into
// a reusable class that employs relative units, so text scales with the
// user's base font-size settings.
.dashboard-title {
  /* WCAG 1.4.10 – use fluid clamp() so large heading scales down on very
     narrow viewports and avoids horizontal scrolling while preserving the
     original 70 px size on desktop. The lower bound (2 rem) ensures the
     heading remains legible when users zoom out or increase base font size. */
  font-size: clamp(2rem, 10vw, 4.375rem); // 32 px–70 px responsive range
  // WCAG 1.4.12 – ensure heading defaults to ≥ 1.5 × line spacing so content
  // is not truncated when users apply custom text-spacing settings.
  line-height: 1.5em;  // Assumption: visually acceptable across breakpoints
  font-weight: 275;
  color: #2D323D;
  padding-bottom: 5rem; // 80px ⇒ 5rem

  // WCAG 1.4.12 – neutralise negative top margin from Tailwind `-mt-6` class
  // present in the template so expanded line-height does not cause overlap.
  margin-top: 0 !important; // Assumption: safe reset across breakpoints

  // Maintain previous mobile override but now using rem as well
  @media (max-width: 640px) {
    /* WCAG 1.4.10 – ensure fluid scaling continues inside mobile breakpoint */
    font-size: clamp(2rem, 10vw, 3.125rem); // max 50 px, scales down further
    // WCAG 1.4.12 – propagate ≥ 1.5× spacing to mobile override as well.
    line-height: 1.5em;

    // Neutralise negative Tailwind margin class (-mt-6) applied in template to
    // prevent potential overlap once line-height increases.
    margin-top: 0 !important; // Assumption: safe reset – heading retains spacing
    padding-bottom: 1.25rem; // 20px
  }
}

// Deprecated .mobile-title kept for backward compatibility—redirects to the
// new class.  Assumption: other templates might still reference it.
.mobile-title:extend(.dashboard-title) {}
