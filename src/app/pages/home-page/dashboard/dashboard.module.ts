// No functional changes – updated declarations list to expose new dashboard card components used for WCAG 1.4.11 styling adjustments.
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {SharedModule} from "@shared/shared.module";
import { CardComponent } from './cards/card/card.component';
import { TeamsComponent } from './cards/teams/teams.component';
import { BillingComponent } from './cards/billing/billing.component';
import { BrandingComponent } from './cards/branding/branding.component';
import { UsersComponent } from './cards/users/users.component';
import { SharedTemplatesComponent } from './cards/shared-templates/shared-templates.component';
import { KnowledgeBaseComponent } from './cards/knowledge-base/knowledge-base.component';
import { DocumentReportComponent } from './cards/document-report/document-report.component';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';

import {ServicePeriodComponent} from "./cards/service-period/service-period.component";


import {FormsModule} from "@angular/forms";
import { RecentActivitiesComponent } from './cards/recent-activities/recent-activities.component';
import { CustomizeComponent } from './cards/customize/customize.component';
import { DashboardComponent } from './dashboard.component';
import { PlanUsageComponent } from './cards/plan-usage/plan-usage.component';
import {NzModalModule} from "ng-zorro-antd/modal";
import {NzButtonModule} from "ng-zorro-antd/button";
import {NzCheckboxModule} from "ng-zorro-antd/checkbox";
import { UsersAndTeamsModule } from "../users-and-teams/users-and-teams.module";
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';


@NgModule({
    declarations: [
        CardComponent,
        TeamsComponent,
        BillingComponent,
        BrandingComponent,
        UsersComponent,
        SharedTemplatesComponent,
        KnowledgeBaseComponent,
        DocumentReportComponent,
        RecentActivitiesComponent,
        CustomizeComponent,
        ServicePeriodComponent,
        CustomizeComponent,
        DashboardComponent,
        PlanUsageComponent
    ],
    exports: [
        DashboardComponent
    ],
    imports: [
        CommonModule,
        SharedModule,
        DragDropModule,
        FormsModule,
        NzTimelineModule,
        NzModalModule,
        NzButtonModule,
        NzCheckboxModule,
        UsersAndTeamsModule,
        NzSkeletonModule    ]  
})
export class DashboardModule { }
