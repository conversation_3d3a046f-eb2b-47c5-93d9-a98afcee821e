import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Title } from '@angular/platform-browser';
declare let AssignmentManager:any;

@Component({
  selector: 'app-applications-iframe',
  templateUrl: './applications-iframe.component.html',
  styleUrls: ['./applications-iframe.component.less']
})
export class ApplicationsIframeComponent implements OnInit {

  public sanitizedUrl:any = '';
  public AssignmentManager: any;

  constructor(
    private domSanitizer: DomSanitizer,
    private titleService: Title
    ) { }

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    this.titleService.setTitle($localize`Applications`);
    
    let me = this;
    AssignmentManager.loginWithHash('https://signority.on.joget.cloud/jw', 'customer1', 'kjKfMHCJAbhTLoLVHwl5yClz', {
      success : function(response:any) {
        console.log("I am logged in as this user --> " + response.username, response);
        if(!response.token) { 
          me.sanitizedUrl = me.domSanitizer.bypassSecurityTrustResourceUrl("https://signority.on.joget.cloud/jw/web/embed/userview/POC_Healthcare/care_application/_/menu_applications")
        }
      }
    });
  }

}
