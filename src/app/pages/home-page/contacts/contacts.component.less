/* WCAG 1.4.12 fixes: switched fixed line-heights to relative 1.5em and removed
   rigid 30 px select height to allow text to grow without clipping. */

@media (max-width:640px) {
    .mobile-title {
        font-size: 3.125rem !important; // 50px → 3.125rem — WCAG 1.4.4: use relative units so heading scales with text-only zoom
    /* WCAG 1.4.12 fix: use relative ≥1.5× line-height so heading text is not
       clipped when users apply custom text-spacing styles. */ // Assumption: 1.5em meets visual design intent
    line-height: 1.5em !important;
        padding-bottom: 1.25rem !important; // 20px → 1.25rem
    }
  }

.title {
    font-size: 4.375rem; // 70px → 4.375rem — WCAG 1.4.4: allow scalable typography
    font-weight: 275;
    padding-bottom: 3.4375rem; // 55px → 3.4375rem
  }

.top-text {
    vertical-align: middle;
    font-weight: bold;
    margin-right: 1.25rem; // 20px → 1.25rem – WCAG 1.4.4
}

.top-icon {
    font-size: 1.125rem; // 18px → 1.125rem – WCAG 1.4.4
    margin-right: 0.3125rem; // 5px → 0.3125rem
}

.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
}

/* WCAG 1.4.10 – ensure data tables can scroll horizontally within their own
   container at 320 px viewport without forcing the entire page to scroll. */
.table-responsive {
  width: 100%;
  max-width: 100%; /* prevents container from exceeding viewport width */
  overflow-x: auto; /* single-axis scroll; vertical scroll remains on page */
}

// WCAG 1.3.1 – visually hidden utility class reused for off-screen captions/labels
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 0.625rem; // 10px → 0.625rem – WCAG 1.4.4
}

/* WCAG 2.1 – 1.3.3 (Sensory Characteristics):
   Provide a non-colour cue (increased font-weight) for highlighted search
   terms so that meaning is not conveyed by colour alone. */ // Assumption:
// Using the same colour and weight as elsewhere in the application keeps UI
// consistent.
.search-highlight {
  font-weight: 600; /* semi-bold for additional emphasis */
  color: #b91c1c;   /* matches Tailwind's text-red-700 */
  text-decoration: underline; /* Assumption: underline provides non-colour cue for users who cannot perceive colour */
}

/* WCAG 2.1 – 1.4.3 (Contrast): The default browser placeholder colour often
   falls below the minimum 4.5:1 contrast ratio on a white background. Force a
   higher-contrast colour taken from the design token list so the instructional
   text is readable for users with low vision. */
.contact-search-input::placeholder { // Assumption: class is only applied to the search box in this component
  color: #545A61; // Medium grey (≈ 6.2:1 contrast against #ffffff)
  opacity: 1;     // Ensure the colour is not further faded by the browser
}

// Cross-browser placeholder selectors for older Blink/WebKit and Microsoft engines
.contact-search-input::-webkit-input-placeholder {
  color: #545A61;
  opacity: 1;
}
.contact-search-input::-moz-placeholder {
  color: #545A61;
  opacity: 1;
}
.contact-search-input:-ms-input-placeholder {
  color: #545A61;
}
.contact-search-input:-moz-placeholder { /* Firefox 4-18 */
  color: #545A61;
  opacity: 1;
}

/* ------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   ------------------------------------------------------------------------
   The default styles provided by Ant Design / ng-zorro as well as Angular
   Material rely on very light grey (#d9d9d9 / rgba(0,0,0,.12)) borders for
   inputs, checkboxes and secondary buttons.  These fall well below the
   required 3 : 1 contrast ratio against the typical white page background
   and make it difficult for low-vision users to perceive component
   boundaries and keyboard focus.  The following overrides are scoped to the
   Contacts component only (thanks to the style encapsulation attribute that
   Angular adds) so they do not affect the rest of the application.  They
   introduce:

   1. A consistent 3 px blue focus ring (#1a73e8 ≥ 3 : 1 on both white and the
      light-grey backgrounds used here) for every interactive element –
      buttons, inputs, selects, table checkboxes, etc.
   2. Darker borders/underlines (#757575 ≈ 4.5 : 1 on white) for Ant Design
      inputs, unchecked checkboxes and default (non-primary) buttons so
      component boundaries meet the 3 : 1 threshold.

   // Assumption: #1a73e8 and #757575 are already used elsewhere in the code
   // base for the same purpose (see previous automated fixes) therefore no
   // new brand colours are introduced.
------------------------------------------------------------------------ */

/* Generic focus indicator for all buttons, inputs, checkboxes, table rows etc. */
/* CHANGE (2025-07-30): Included anchor elements so keyboard focus is visible on
   links rendered by Ant Design pagination, satisfying WCAG 2.4.7 */





/* Darken low-contrast secondary Ant Design button borders (default: #d9d9d9). */
button.ant-btn:not(.ant-btn-primary) {
  border: 2px solid #4c4c4c; // ≈ 8 : 1 against white
}

/* Ensure input component boundaries meet ≥ 3 : 1 contrast on white. */
input[nz-input], textarea[nz-input] {
  border: 1px solid #757575; // Darker grey vs default #d9d9d9
}

/* CHANGE (2025-07-30): Additional focus style for breadcrumb links so they
   receive the same high-visibility indicator as other interactive elements
   in this component, ensuring keyboard users can perceive focus (WCAG 2.4.7). */

nav[aria-label='Breadcrumb'] a:focus,
nav[aria-label='Breadcrumb'] a:focus-visible,
.ml-3 a:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Increase Angular Material form-field underline contrast inside modals. */
:host ::ng-deep .mat-form-field-underline, /* host selector keeps scope */
:host ::ng-deep .mat-form-field-underline::after {
  background-color: #757575 !important; /* Override faint rgba(0,0,0,.12) */
}

// WCAG 3.3.2 – style for the visual required asterisk; avoids reliance on colour alone because symbol * itself communicates requirement.
.required {
  color: #c0392b; // dark red ensures ≥ 4.5:1 contrast against white.
  font-weight: 700; // additional non-colour emphasis
}

/* Darken unchecked Ant Design checkbox outline so its boundary is perceivable. */
:host ::ng-deep .ant-checkbox-inner {
  border: 1px solid #757575 !important; // ≥ 3 : 1 contrast vs white
}

/* Sortable table headers - make them visually interactive */
th[role="button"] {
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

th[role="button"]:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

th[role="button"]:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: -4px;
}

/* Ensure sortable headers are properly styled in both tables */
.table-responsive th[role="button"] {
  user-select: none; /* Prevent text selection on click */
}

/* Sort indicators for active sorting */
th[role="button"].sort-active {
  background-color: rgba(26, 115, 232, 0.1);
  font-weight: 600;
}

/* Pagination keyboard accessibility */
.ant-pagination a:focus,
.ant-pagination button:focus,
.ant-pagination a:focus-visible,
.ant-pagination button:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Ensure pagination controls are keyboard accessible */
.ant-pagination li {
  position: relative;
}

.ant-pagination a,
.ant-pagination button {
  cursor: pointer;
  transition: all 0.2s ease;
}

.ant-pagination a:hover,
.ant-pagination button:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Override Ant Design's border-based focus with our outline-based focus */
.ant-pagination-item:focus,
.ant-pagination-item:focus-visible,
.ant-pagination-prev:focus,
.ant-pagination-prev:focus-visible,
.ant-pagination-next:focus,
.ant-pagination-next:focus-visible,
.ant-pagination-jump-prev:focus,
.ant-pagination-jump-prev:focus-visible,
.ant-pagination-jump-next:focus,
.ant-pagination-jump-next:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px;
  border-radius: 2px;
  /* Override Ant Design's border-based focus styles */
  border-color: transparent !important;
  box-shadow: none !important;
}

/* Override the specific Ant Design focus styles shown in the dev tools */
.ant-pagination-item-active:focus-visible,
.ant-pagination-item-active:focus {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px;
  border-radius: 2px;
  border-color: transparent !important;
  color: inherit !important;
}

.ant-pagination-item:focus-visible,
.ant-pagination-item:focus {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px;
  border-radius: 2px;
  border-color: transparent !important;
  transition: none !important;
}

/* Sample CSV link focus styling */
.ml-3 a:focus,
.ml-3 a:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Main landmark focus styling - ensure black outline */
#contacts-main:focus,
#contacts-main:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Improve visibility of disabled table row checkbox when it receives focus */
.ant-checkbox-disabled .ant-checkbox-input:focus + .ant-checkbox-inner {
  outline: 3px solid #2D323D; // Still provide visible focus even if disabled state lightens colour
  outline-offset: 2px;
}

.ant-table-thead th.ant-table-column-has-sorters:focus-visible {
  color: #2D323D !important;
}

/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.12 (Text Spacing)
   ---------------------------------------------------------------------------
   Global styles elsewhere in the code base apply a fixed 30 px height on
   elements with the `.material-select-height` utility class.  When users
   enable custom text-spacing (e.g. by injecting a user style sheet that sets
   `line-height: 1.5` or increases `letter-spacing`), the rigid height can
   clip the Angular Material `mat-select` label/value text inside our modal
   forms, resulting in loss of content – a direct failure of SC 1.4.12.

   We locally override the rule so the control can grow vertically while still
   retaining its original minimum visual footprint (≈ 30 px). */ // Assumption: root font-size ≈ 16 px ⇒ 1.875 rem ≈ 30 px

.material-select-height {
  height: auto !important;  /* allow vertical expansion under increased spacing */
  min-height: 1.875rem;     /* preserve baseline size */
  overflow: visible;        /* prevent clipping of expanded content */
  white-space: normal;      /* permit wrapping when letter/word spacing increases */
  line-height: normal;      /* defer to user/UA stylesheet */
}

/* WCAG 2.1 – Success Criterion 1.3.4 (Orientation):
   -------------------------------------------------
   Ensure that modal dialogs displayed by the Contacts component remain fully
   visible in both portrait and landscape orientations on small devices.
   Ant Design’s default modal can exceed the viewport height when rotated to
   landscape on phones (≈ 320 px tall), causing the bottom action buttons to
   fall off-screen and making the interface unusable with touch or keyboard.

   The rule below sets an adaptive `max-height` on the modal body and enables
   internal scrolling so all content and controls stay available without
   locking the screen orientation – satisfying WCAG 2.1 Level AA guideline
   1.3.4 that forbids restricting content to a single orientation. */ // Assumption: 60 vh leaves comfortable margin on desktop and prevents overflow on mobile

:host ::ng-deep .ant-modal-body {
  max-height: 60vh; /* shrinks in landscape on phone */
  overflow: auto;   /* allows internal scroll if content taller than 60 vh */
}
