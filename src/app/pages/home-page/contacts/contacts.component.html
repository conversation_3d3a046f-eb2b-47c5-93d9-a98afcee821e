

<!-- Existing note: WCAG 4.1.2 (Name, Role, Value) – replaced non-semantic
     <a> elements with proper <button> controls, added missing aria-labels, and
     labelled hidden file input to satisfy automated audits. -->
<h1 class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    Contacts
</h1>

<div class="top-bar">
    <!-- WCAG 2.1 – SC 3.2.3 (Consistent Navigation):
         Replaced the generic <div> container with a semantic <nav> landmark so
         assistive technology can identify the action toolbar as navigation.
         The DOM structure and visual layout remain unchanged, thereby
         preserving the relative order of each action button across every
         instance of the Contacts page.  This ensures the repeated
        navigational mechanism (Add New, Import from CSV, Edit, Delete) is
        programmatically exposed in a consistent manner on every visit. -->
        <!-- FIX (2025-07-30): Properly closed HTML comment to prevent Angular template
             parser errors (Unexpected closing tag for <span> / <nav>). -->
    <nav class="m-3 inline-block" aria-label="Contact actions">
        <span>
            <!-- Replaced invalid nested interactive elements (button inside anchor) with a single button element
                 for correct semantic structure and meaningful reading order (WCAG 1.3.2). // Assumption: styling
                 is preserved via existing utility classes. -->
            <!-- WCAG 2.4.6 – provide a descriptive, programmatic name so the button’s purpose is clear to assistive technologies -->
            <button type="button" (click)="addContactModal = true" class="bg-transparent border-0 cursor-pointer inline-flex items-center" i18n-aria-label aria-label="Add new contact">
                <!-- Decorative icon hidden from assistive tech (WCAG 1.1.1) -->
                <span class="top-icon" nz-icon nzType="plus" nzTheme="outline" aria-hidden="true"></span>
                <!-- Consistent visible label with modal title (SC 3.2.4) -->
                <span class="top-text" i18n>Add New Contact</span>
            </button>
        </span>
        <span>
            <!-- WCAG 2.4.6 – clarify that this control imports contacts rather than an arbitrary file -->
            <!-- Adjusted aria-label so it begins with the exact visible text string (WCAG 2.5.3 – Label in Name) -->
            <!-- WCAG 2.1 SC 3.2.4 – Consistent Identification: align the accessible
                 name used for every control that imports contacts from a CSV so
                 they are phrased identically across the application.  The exact
                 string “Import contacts from CSV file” is now applied both to
                 the visible trigger button and to the visually-hidden file
                 input, ensuring a uniform, programmatic label wherever the same
                 functionality is exposed. -->
            <button type="button" (click)="uploader.click()" class="bg-transparent border-0 cursor-pointer inline-flex items-center" i18n-aria-label aria-label="Import from CSV">
                <!-- Decorative icon hidden from assistive tech (WCAG 1.1.1) -->
                <span class="top-icon" nz-icon nzType="download" nzTheme="outline" aria-hidden="true"></span>
                <span class="top-text" i18n>Import from CSV</span>
            </button>
        </span>
        <span aria-live="polite">
            <span *ngIf="setOfCheckedId.size == 1"> 
                <!-- WCAG 2.4.6 – explicit aria-label communicates that the selected contact will be edited -->
                <button type="button" (click)="selectContactToModify()" class="bg-transparent border-0 cursor-pointer inline-flex items-center" i18n-aria-label aria-label="Edit selected contact">
                    <!-- Decorative icon hidden from assistive tech (WCAG 1.1.1) -->
                    <span class="top-icon" nz-icon nzType="edit" nzTheme="outline" aria-hidden="true"></span>
                    <span class="top-text" i18n>Edit</span>
                </button>
            </span>
        </span>

        <span aria-live="polite">
            <span  *ngIf="setOfCheckedId.size > 0">
                <!-- WCAG 2.4.6 – descriptive aria-label specifies action and target -->
                <button type="button" (click)="deleteConfirmationModal = true" class="bg-transparent border-0 cursor-pointer inline-flex items-center" i18n-aria-label aria-label="Confirm Deletion">
                    <!-- Decorative icon hidden from assistive tech (WCAG 1.1.1) -->
                    <span class="top-icon" nz-icon nzType="delete" nzTheme="outline" aria-hidden="true"></span>
                    <span class="top-text" i18n>Delete</span>
                </button>
            </span>
        </span>
    </nav>
    <div>
        <nz-input-group nzSearch (keyup.enter)="search()" [nzAddOnAfter]="suffixIconButton">
            <!-- WCAG 1.3.1 – added programmatic label via aria-label and associated id for search input to satisfy F68 -->
            <!-- Added autocomplete="off" because this is a generic search box that does not collect
                 personal user data (WCAG 2.1 SC 1.3.5 Identify Input Purpose). -->
            <!-- Added `contact-search-input` class so we can explicitly style the placeholder
                 colour in the component stylesheet to meet the 4.5:1 contrast ratio
                 required by WCAG 2.1 SC 1.4.3. -->
            <input id="contactSearch" class="contact-search-input" [(ngModel)]="searchValue" type="text" nz-input autocomplete="off" i18n-placeholder placeholder="Name or Email" i18n-aria-label aria-label="Search contacts by name or email" />
        </nz-input-group>
        <ng-template #suffixIconButton>
            <!-- WCAG 2.4.6 – refine label to describe what will be searched -->
            <button class="flex items-center" nz-button nzType="primary" (click)="search()" nzSearch i18n-aria-label aria-label="Search contacts">
                <!-- Decorative search icon hidden from assistive tech; button already labelled (WCAG 1.1.1) -->
                <span nz-icon nzType="search" aria-hidden="true"></span>
            </button>
        </ng-template>
    </div>
</div>
<div class="ml-3" i18n>To import your contact list, use this <a href=".\assets\files\importSample.csv"><u>sample .csv file</u></a></div>

<!-- MAIN LANDMARK: all primary page content is contained here so that the skip
     link target is meaningful and the application can set focus to it after
     route navigation. -->
<main id="contacts-main" tabindex="-1">
<!-- WCAG 1.4.10 – wrapped data table in single-axis scroll region so page never requires horizontal scrolling on small viewports -->
<div class="table-responsive" role="region" aria-labelledby="contactsTableCaption">
    <!-- <nz-table #basicTable [nzData]="contactsFiltered" [nzShowPagination]="false"> -->
    <nz-table [nzPageIndex]="pageIndex"  (nzPageIndexChange)="changePageIndex($event)" 
    nzShowSizeChanger="true" [nzPageSizeOptions]="[10,20,30]" #basicTable [nzData]="contactsFiltered">
        <!-- Keeping caption visually hidden preserves current visual layout while providing an accessible name for the region -->
        <caption id="contactsTableCaption" class="sr-only" i18n>Contacts – list of personal contacts</caption>
    <thead>
        <tr>
            <th [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)" 
                appCheckboxKeyboard
                [isChecked]="checked"
                [isDisabled]="false"
                (appCheckboxKeyboard)="onAllChecked($event)"
                tabindex="0"
                role="checkbox"
                [attr.aria-checked]="checked"
                [attr.aria-label]="'Select all contacts'"
                i18n-aria-label aria-label="Select all contacts">
                <span class="sr-only" i18n>Select all</span>
            </th>
            <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.firstname" 
                tabindex="0" role="button" 
                (click)="sortByColumn('firstname')" 
                (keydown.enter)="sortByColumn('firstname')" 
                (keydown.space)="sortByColumn('firstname')"
                i18n-aria-label aria-label="Sort by first name"
                [class.sort-active]="currentSortColumn === 'firstname'"
                i18n>First Name</th>
            <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.lastname" 
                tabindex="0" role="button"
                (click)="sortByColumn('lastname')" 
                (keydown.enter)="sortByColumn('lastname')" 
                (keydown.space)="sortByColumn('lastname')"
                i18n-aria-label aria-label="Sort by last name"
                [class.sort-active]="currentSortColumn === 'lastname'"
                i18n>Last Name</th>
            <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.email" 
                tabindex="0" role="button"
                (click)="sortByColumn('email')" 
                (keydown.enter)="sortByColumn('email')" 
                (keydown.space)="sortByColumn('email')"
                i18n-aria-label aria-label="Sort by email"
                [class.sort-active]="currentSortColumn === 'email'"
                i18n>Email</th>
            <th i18n>Country Code</th>
            <th i18n>Phone Number</th>
        </tr>
    </thead>
    <tbody>
        <tr *ngFor="let data of basicTable.data">
            <td [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
                        (nzCheckedChange)="onItemChecked(data.id, $event)"
                        appCheckboxKeyboard
                        [isChecked]="setOfCheckedId.has(data.id)"
                        [isDisabled]="data.disabled"
                        (appCheckboxKeyboard)="onItemChecked(data.id, $event)"
                        role="checkbox"
                        [attr.aria-checked]="setOfCheckedId.has(data.id)"
                        [attr.aria-disabled]="data.disabled"
                        [attr.aria-label]="'Select contact ' + (data.firstname || '') + ' ' + (data.lastname || '')"></td>
            <td [innerHTML]="data.firstname"></td>
            <td [innerHTML]="data.lastname"></td>
            <td [innerHTML]="data.email"></td>
            <td [innerHTML]="data.phoneCountryCode"></td>
            <td [innerHTML]="data.phonenumber"></td>
        </tr>
    </tbody>
    </nz-table>
</div>
<!-- <mat-paginator [showFirstLastButtons]="true" [length]="totalContacts" [pageSize]="10" [pageSizeOptions]="[10,20,30]" [hidePageSize]="true"
(page)="changePageIndex($event.pageIndex+1)"></mat-paginator>     -->

<div *ngIf="teamContacts">
    <!-- WCAG 2.4.6 – use semantic heading for section to improve document outline -->
    <h2 class="mt-3 mb-2 text-2xl" i18n>Contacts Shared By Team Members</h2>
    <!-- WCAG 1.4.10 – wrapped secondary data table in scroll container -->
    <div class="table-responsive" role="region" aria-labelledby="teamContactsTableCaption" tabindex="0">
        <!-- <nz-table #basicTeamTable [nzData]="teamContactsFiltered"  [nzShowPagination]="false"> -->
        <nz-table nzShowSizeChanger="true" [nzPageSizeOptions]="[10,20,30]" #basicTeamTable [nzData]="teamContacts">
            <caption id="teamContactsTableCaption" class="sr-only" i18n>Contacts shared by team members</caption>

        <thead>
            <tr>
                <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.firstname" 
                    tabindex="0" role="button"
                    (click)="sortByColumn('firstname')" 
                    (keydown.enter)="sortByColumn('firstname')" 
                    (keydown.space)="sortByColumn('firstname')"
                    i18n-aria-label aria-label="Sort by first name"
                    [class.sort-active]="currentSortColumn === 'firstname'"
                    i18n>First Name</th>
                <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.lastname" 
                    tabindex="0" role="button"
                    (click)="sortByColumn('lastname')" 
                    (keydown.enter)="sortByColumn('lastname')" 
                    (keydown.space)="sortByColumn('lastname')"
                    i18n-aria-label aria-label="Sort by last name"
                    [class.sort-active]="currentSortColumn === 'lastname'"
                    i18n>Last Name</th>
                <th [nzShowSort]="'true'" [nzSortFn]="sortFunctions.email" 
                    tabindex="0" role="button"
                    (click)="sortByColumn('email')" 
                    (keydown.enter)="sortByColumn('email')" 
                    (keydown.space)="sortByColumn('email')"
                    i18n-aria-label aria-label="Sort by email"
                    [class.sort-active]="currentSortColumn === 'email'"
                    i18n>Email</th>
                <th i18n>Country Code</th>
                <th i18n>Phone Number</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTeamTable.data">
                <td>{{data.firstname}}</td>
                <td>{{data.lastname}}</td>
                <td>{{data.email}}</td>
                <td>{{data.phoneCountryCode}}</td>
                <td>{{data.phonenumber}}</td>
            </tr>
        </tbody>
        </nz-table>
    </div>
    <!-- <mat-paginator [showFirstLastButtons]="true" [length]="teamContactsFiltered.length" [pageSize]="10" [pageSizeOptions]="[10,20,30]" [hidePageSize]="true"
(page)="changeTeamPageIndex($event.pageIndex+1)"></mat-paginator> -->
</div>
<!-- Close main landmark before modal overlays so dialogs are not nested
     inside <main>. -->
</main>

<!-- WCAG 1.4.13 – enable dismissal of Add Contact dialog via ESC key and click outside -->
<!-- WCAG 2.1 SC 3.2.4 – Rename modal title so the same contact-creation
     functionality is identified consistently throughout the page and across
     the wider application (buttons, dialog titles, documentation). -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="addContactModal" nzTitle="Add New Contact"
    (nzOnOk)="addContact()" (nzOnCancel)="addContactModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- WCAG 1.4.10 – responsive modal width so dialog fits within 320 px viewport -->
    <ng-container *nzModalContent>
        <form [formGroup]="addContactForm">
            <table class="modal-table"><!-- Layout table: removed role="presentation" to avoid F92 per WCAG 1.3.1 -->
                <tr>
                    <!-- WCAG 3.3.2 – added visual asterisk to communicate the field is required.
                         Using <span class="required">* </span> ensures symbol is conveyed even
                         when colour alone cannot be perceived. -->
                    <td i18n>First Name <span class="required" aria-hidden="true">*</span></td>
                    <td aria-live="polite">
                        <!-- Added semantic purpose tokens per WCAG 2.1 SC 1.3.5 -->
                        <!-- WCAG 3.3.1 (Error Identification):
                             1. Expose validation state via aria-invalid.
                             2. Programmatically associate error message with the field via aria-describedby
                             so assistive technologies announce the error when it appears. -->
                        <!-- WCAG 3.3.2 – provide programmatic indication that the field is mandatory -->
                        <!-- WCAG 2.4.3 – Move initial keyboard focus to the first form field when the
                             modal opens so users do not have to Tab through the dialog chrome (close
                             button, header) before reaching the content.  The cdkFocusInitial
                             directive is provided by Angular CDK (already a transitive dependency
                             via Angular Material) and therefore introduces no new package. -->
                        <input cdkFocusInitial i18n-aria-label aria-label="First name" autocomplete="given-name" required aria-required="true"
                            [style.borderColor]="addContactModalSubmitted && addContactForm.controls['firstName'].errors ? 'red':''"
                            [attr.aria-invalid]="addContactModalSubmitted && addContactForm.controls['firstName'].invalid"
                            [attr.aria-describedby]="'add-firstname-error'"
                            formControlName="firstName" nz-input />
                            <!-- Use [hidden] instead of *ngIf so the element always exists in the
                                 accessibility tree, avoiding broken IDREF when aria-describedby is evaluated. -->
                            <div id="add-firstname-error" role="alert" class="text-red-500"
                                [hidden]="!(addContactModalSubmitted && addContactForm.controls['firstName'].errors)" i18n>
                                <!-- WCAG 2.1 – SC 3.3.3: actionable guidance -->
                                Enter the first name
                            </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Last Name</td>
                    <td>
                        <input formControlName="lastName" nz-input i18n-aria-label aria-label="Last name" autocomplete="family-name" />
                    </td>
                </tr>
                <tr>
                    <!-- WCAG 3.3.2 – include required indicator -->
                    <td i18n>Email <span class="required" aria-hidden="true">*</span></td>
                    <td aria-live="polite">
                        <input i18n-aria-label aria-label="Email" type="email" autocomplete="email" required aria-required="true"
                            [style.borderColor]="addContactModalSubmitted && addContactForm.controls['email'].errors ? 'red':''"
                            [attr.aria-invalid]="addContactModalSubmitted && addContactForm.controls['email'].invalid"
                            [attr.aria-describedby]="'add-email-error'"
                            formControlName="email" nz-input />
                        <div id="add-email-error" role="alert" class="text-red-500"
                            [hidden]="!(addContactModalSubmitted && addContactForm.controls['email'].errors)">
                            <span *ngIf="addContactForm.controls['email'].errors && addContactForm.controls['email'].errors['required']" i18n>Email is required</span>
                            <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion): Provide an example so users understand how to correct the error. -->
                            <span *ngIf="addContactForm.controls['email'].errors && addContactForm.controls['email'].errors['email']" i18n>Enter a valid email such as name&#64;example.com</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Country Code</td>
                    <td>
                        <mat-form-field class="material-select-height w-full" >
                            <mat-select name="Country code" formControlName="countryCode" i18n-aria-label aria-label="Country code">
                                <mat-option *ngFor="let country of countryCodes" [value]="country[2]">
                                    {{country[0]+' (+'+country[2]+')'}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </td>
                </tr>
                <tr>
                    <td i18n>Phone Number</td>
                    <td aria-live="polite">
                        <!-- WCAG 3.3.2 – add instructional hint for expected digit-only phone number input length -->
                        <input formControlName="phoneNumber" nz-input type="tel" autocomplete="tel" i18n-aria-label aria-label="Phone number"
                            title="Enter 3–16 digits (numbers only)" aria-describedby="add-phone-hint add-phone-error"
                            [attr.aria-invalid]="addContactModalSubmitted && addContactForm.controls['phoneNumber'].invalid" />
                        <div id="add-phone-hint" class="text-gray-600 text-sm" i18n>Enter 3–16 digits, numbers only</div>
                        <div id="add-phone-error" role="alert" class="text-red-500"
                            [hidden]="!(addContactModalSubmitted && addContactForm.controls['phoneNumber'].errors)" i18n>
                            Phone number should have more than 2 and less than 17 digits
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Edit Contact dialog via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="editContactModal" nzTitle="Edit Contact"
    (nzOnOk)="editContact()" (nzOnCancel)="editContactModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
        <form [formGroup]="editContactForm">
            <table class="modal-table"><!-- Layout table: removed role="presentation" to avoid F92 per WCAG 1.3.1 -->
                <tr>
                    <!-- WCAG 3.3.2 – added required marker -->
                    <td i18n>First Name <span class="required" aria-hidden="true">*</span></td>
                    <td aria-live="polite">
                        <!-- WCAG 2.4.3 – Ensure focus starts inside Edit Contact dialog as well. -->
                        <input cdkFocusInitial i18n-aria-label aria-label="First name" autocomplete="given-name" required aria-required="true"
                            [style.borderColor]="editContactModalSubmitted && editContactForm.controls['firstName'].errors ? 'red':''"
                            [attr.aria-invalid]="editContactModalSubmitted && editContactForm.controls['firstName'].invalid"
                            [attr.aria-describedby]="'edit-firstname-error'"
                            formControlName="firstName" nz-input />
                        <div id="edit-firstname-error" role="alert" class="text-red-500"
                            [hidden]="!(editContactModalSubmitted && editContactForm.controls['firstName'].errors)" i18n>
                            <!-- WCAG 2.1 – SC 3.3.3 -->
                            Enter the first name
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Last Name</td>
                    <td>
                        <input formControlName="lastName" nz-input i18n-aria-label aria-label="Last name" autocomplete="family-name" />
                    </td>
                </tr>
                <tr>
                    <td i18n>Email <span class="required" aria-hidden="true">*</span></td>
                    <td aria-live="polite">
                        <input i18n-aria-label aria-label="Email" type="email" autocomplete="email" required aria-required="true"
                            [style.borderColor]="editContactModalSubmitted && editContactForm.controls['email'].errors ? 'red':''"
                            [attr.aria-invalid]="editContactModalSubmitted && editContactForm.controls['email'].invalid"
                            [attr.aria-describedby]="'edit-email-error'"
                            formControlName="email" nz-input />
                        <div id="edit-email-error" role="alert" class="text-red-500"
                            [hidden]="!(editContactModalSubmitted && editContactForm.controls['email'].errors)">
                            <span *ngIf="editContactForm.controls['email'].errors && editContactForm.controls['email'].errors['required']" i18n>Email is required</span>
                            <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion) -->
                            <span *ngIf="editContactForm.controls['email'].errors && editContactForm.controls['email'].errors['email']" i18n>Enter a valid email such as name&#64;example.com</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td i18n>Country Code</td>
                    <td>
                    <mat-form-field class="material-select-height w-full" >
                        <mat-select name="Country code" formControlName="countryCode" i18n-aria-label aria-label="Country code">
                            <mat-option *ngFor="let country of countryCodes" [value]="country[2]">
                                {{country[0]+' (+'+country[2]+')'}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    </td>
                </tr>
                <tr>
                    <td i18n>Phone Number</td>
                    <td aria-live="polite">
                        <input formControlName="phoneNumber" nz-input type="tel" autocomplete="tel" i18n-aria-label aria-label="Phone number"
                            title="Enter 3–16 digits (numbers only)" aria-describedby="edit-phone-hint edit-phone-error"
                            [attr.aria-invalid]="editContactModalSubmitted && editContactForm.controls['phoneNumber'].invalid" />
                        <div id="edit-phone-hint" class="text-gray-600 text-sm" i18n>Enter 3–16 digits, numbers only</div>
                        <div id="edit-phone-error" role="alert" class="text-red-500"
                            [hidden]="!(editContactModalSubmitted && editContactForm.controls['phoneNumber'].errors)" i18n>
                            Phone number should have more than 2 and less than 17 digits
                        </div>
                    </td>
                </tr>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of Delete Confirmation dialog via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteConfirmationModal" nzTitle="Confirm Deletion"
    (nzOnOk)="deleteContacts()" (nzOnCancel)="deleteConfirmationModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"> <!-- WCAG 1.4.10 – responsive modal width -->
    <ng-container *nzModalContent>
            <table class="modal-table"><!-- Layout table: removed role="presentation" to avoid F92 per WCAG 1.3.1 -->
            <tr>
                <td i18n>Delete the selected contact(s) from the system?</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 4.1.2 – provide accessible name for hidden file input so automated
     tools do not flag a missing label. The input is visually hidden and only
     triggered programmatically, therefore Name is supplied but also removed
     from the accessibility tree with aria-hidden. -->
<!-- Assumption: The file input remains visually hidden; however it still
     requires a programmatic name so that assistive technologies announce a
     consistent description if the element ever receives focus.  The label
     now matches the trigger button text exactly (SC 2.5.3 Label in Name) and
     therefore also meets SC 3.2.4 Consistent Identification. -->
<input hidden type="file" accept=".csv" (change)="importContacts($event)" #uploader aria-label="Import from CSV" aria-hidden="true" />
