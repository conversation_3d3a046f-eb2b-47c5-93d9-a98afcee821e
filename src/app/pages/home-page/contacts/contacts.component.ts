// WCAG 4.1.2 (Name, Role, Value) – ensure Ant Design pagination controls receive
// programmatic names regardless of whether they are rendered as <a> or
// <button>. Also retains earlier WCAG 2.4.4 and 1.3.5 patches.
// Added ViewEncapsulation.None so the focus ring styles declared in the
// component stylesheet can apply to elements generated by third-party
// components (e.g. ng-zorro pagination links) that live outside the template
// scope.  This ensures every interactive control now receives a visible
// keyboard focus indicator, satisfying WCAG 2.4.7 across the whole page.
// CHANGE (2025-07-30): WCAG 2.4.2 (Page Titled) – import Angular Title
// service so the component can programmatically set a descriptive
// <title> element when the route loads.
import { Component, OnInit, Inject, LOCALE_ID, AfterViewInit, ElementRef, Renderer2, ViewEncapsulation } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {  Validators, FormBuilder  } from '@angular/forms';
import {  UserService } from '@core/services';
import { countries } from '@core/models/countries';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Papa } from 'ngx-papaparse';

@Component({
  selector: 'app-contacts',
  templateUrl: './contacts.component.html',
  styleUrls: ['./contacts.component.less'],
  encapsulation: ViewEncapsulation.None
})
export class ContactsComponent implements OnInit, AfterViewInit {

  public contacts:any;
  public contactsFiltered:any;
  public totalContacts:number = 0;
  public teamContacts:any;
  public teamContactsFiltered:any;
  public addContactModal:boolean = false;
  public addContactModalSubmitted:boolean = false;
  public editContactModal:boolean = false;
  public editContactModalSubmitted:boolean = false;
  public countryCodes:any;
  public checked = false;
  public indeterminate = false;
  public setOfCheckedId = new Set<number>();
  public deleteConfirmationModal = false;
  public currentUser:any;
  public searchValue:string = '';
  public pageIndex:number = 1;
  public currentSortColumn: string = '';
  public currentSortDirection: 'ascend' | 'descend' | null = null;


  public addContactForm = this.formBuilder.group({
    firstName: ['', Validators.required],
    lastName: [''],
    email:  ['', [Validators.required, Validators.email]],
    countryCode:['1'],
    phoneNumber:['',Validators.pattern(/^(\s*|\d{3,16})$/)]
  });

  public editContactForm = this.formBuilder.group({
    firstName: ['', Validators.required],
    lastName: [''],
    email:  ['', [Validators.required, Validators.email]],
    countryCode:['1'],
    phoneNumber:['',Validators.pattern(/^(\s*|\d{3,16})$/)]
  });
  public sortFunctions: any = {
    firstname: (a:any, b:any) => a.firstname.localeCompare(b.firstname),
    lastname: (a:any, b:any) => a.lastname.localeCompare(b.lastname),
    email: (a:any, b:any) => a.email.localeCompare(b.email)
  }

  /**
   * Handles sorting by column when headers are clicked or activated via keyboard
   * @param columnName The name of the column to sort by
   */
  sortByColumn(columnName: string): void {
    // Determine sort direction
    if (this.currentSortColumn === columnName) {
      // If clicking the same column, cycle through: ascend -> descend -> null
      if (this.currentSortDirection === 'ascend') {
        this.currentSortDirection = 'descend';
      } else if (this.currentSortDirection === 'descend') {
        this.currentSortDirection = null;
      } else {
        this.currentSortDirection = 'ascend';
      }
    } else {
      // New column, start with ascending
      this.currentSortColumn = columnName;
      this.currentSortDirection = 'ascend';
    }

    // Apply sorting to the filtered contacts
    if (this.currentSortDirection) {
      this.contactsFiltered = [...this.contactsFiltered].sort((a: any, b: any) => {
        const aValue = a[columnName] || '';
        const bValue = b[columnName] || '';
        
        if (this.currentSortDirection === 'ascend') {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    } else {
      // Reset to original order
      this.contactsFiltered = [...this.contacts];
    }
  }
  constructor(
    private userService: UserService,
    private formBuilder: FormBuilder,
    private modal: NzModalService,
    private papa: Papa,
    @Inject(LOCALE_ID) public locale: string,
    private elementRef: ElementRef,
    private renderer: Renderer2, // Assumption: Renderer2 available in this context
    private titleService: Title // Assumption: Title service provided at root
  ) {}

  ngOnInit(): void {
    // WCAG 2.4.2 – ensure the page receives a unique, descriptive document
    // title so assistive technology users immediately know which section of
    // the application is displayed.
    this.updateDocumentTitle();

    if(this.locale == 'fr') {
      this.countryCodes = countries.fr;
    }
    else {
      this.countryCodes = countries.en;
    }
    let user = JSON.parse(localStorage.getItem("user")|| '0' );

    this.getContacts();
    if(user.userSetting.ShareAddressBookToTeam) {
      this.getTeamContacts();
    }

  }

  /**
   * Sets a descriptive <title> for the Contacts page so that the browser tab
   * and assistive technology announce meaningful context.
   */
  private updateDocumentTitle(): void {
    // Set title to just the page name without the dashboard suffix
    const baseTitle = $localize`Contacts`;
    this.titleService.setTitle(baseTitle);
  }

  getContacts() {
    this.userService.getContacts().subscribe(data=>{
      this.contacts = data.contacts;
      this.contactsFiltered = data.contacts;
      this.totalContacts = this.contacts.length;
      this.contacts.forEach((contact:any)=>{
        if(!contact.firstname) contact.firstnme = '';
        if(!contact.lastname) contact.lastname = '';
        if(!contact.email) contact.email = '';
      });
    })
  }
  getTeamContacts() {
    this.userService.getTeamContacts().subscribe(data=>{
      this.teamContacts = data.contacts;
      this.teamContacts.forEach((contact:any)=>{
        if(!contact.firstname) contact.firstnme = '';
        if(!contact.lastname) contact.lastname = '';
        if(!contact.email) contact.email = '';
      });
      this.teamContactsFiltered = data.contacts;
    })
  }

  addContact() {
    this.addContactModalSubmitted = true;
    if (!this.addContactForm.invalid) {
      let contact = {};
    this.userService.addContact(this.addContactForm.controls['firstName'].value, this.addContactForm.controls['lastName'].value,
    this.addContactForm.controls['email'].value,this.addContactForm.controls['countryCode'].value,this.addContactForm.controls['phoneNumber'].value
    ).subscribe(res=>{
        this.addContactModalSubmitted = false;
        this.addContactModal = false;
        this.addContactForm.reset();
        this.addContactForm.get('countryCode')?.setValue('1');
        this.getContacts();
      })
    }
  }

  editContact() {
    this.editContactModalSubmitted = true;
    if (!this.editContactForm.invalid) {
      let contact = {};
    this.userService.editContact(this.editContactForm.controls['firstName'].value, this.editContactForm.controls['lastName'].value,
    this.editContactForm.controls['email'].value,this.editContactForm.controls['countryCode'].value,this.editContactForm.controls['phoneNumber'].value,
    this.currentUser.id).subscribe(res=>{
        this.editContactModalSubmitted = false;
        this.editContactModal = false;
        this.editContactForm.reset();
        this.getContacts();
      })
    }
  }

  addContactsCSV(contacts:any) {
    this.addContactModalSubmitted = true;

    this.userService.addContacts(contacts).subscribe(res=>{
        this.addContactModalSubmitted = false;
        this.addContactModal = false;
        this.addContactForm.reset();
        this.addContactForm.get('countryCode')?.setValue('1');
        this.getContacts();
      })
  }

  importContacts(e: any) {
    let reader = new FileReader();
    let me = this;
    reader.onload = function (e) {
      if(e.target) {
        let fileContent = e?.target?.result;
        if(fileContent) {
          me.parseCSVContacts(fileContent);
        }
      }
  
    };
    if((e.srcElement || e.target)?.files) {
      reader.readAsText((e.srcElement || e.target).files[0]);
    }
  }

  deleteContacts() {
    this.deleteConfirmationModal = false;
    let deleteList:any[] = []
    this.setOfCheckedId.forEach(val=>{
       deleteList.push({id:val});
    })
    this.userService.deleteContacts(deleteList).subscribe(()=>{
      this.getContacts();
      this.setOfCheckedId.clear()
      this.checked = false;
      this.indeterminate = false;
    })
  }

  search() {
    let value = this.searchValue;
        value = value.replace(/^\s+|\s+$/g, '');
        if (value == "") {
            this.setOfCheckedId.clear();
            this.checked = false;
            this.indeterminate = false;
            this.pageIndex = 1;
            this.contactsFiltered =  this.contacts;
        } else {
            var foundRows = [];
            var patt = new RegExp("(" + value.replace(/(\W)/g, "\\$1") + ")", "ig");
            for (var i = 0, len = this.contacts.length; i < len; i++) {
                var row = this.contacts[i];
                var firstname = row.firstname||'';
                var lastname = row.lastname||'';
                var email = row.email;            
                var phoneCountryCode = row.phoneCountryCode;
                var phonenumber = row.phonenumber;
                
                // WCAG 2.1 – Guideline 1.3.3 (Sensory Characteristics):
                // Highlighted search terms were previously emphasised only by a red
                // font colour. Users who cannot perceive colour would miss the
                // emphasis. We now wrap matches in a <mark> element that carries an
                // additional non-colour cue via the .search-highlight class (bold
                // font-weight) defined in the component stylesheet. // Assumption:
                // Replacing <span> with <mark> does not impact existing
                // sanitisation because the table already binds using [innerHTML].
                var firstname2 = patt.test(firstname)
                    ? firstname.replace(
                        patt,
                        "<mark class='search-highlight'>$1</mark>"
                      )
                    : null;
                var lastname2 = patt.test(lastname)
                    ? lastname.replace(
                        patt,
                        "<mark class='search-highlight'>$1</mark>"
                      )
                    : null;
                var email2 = patt.test(email)
                    ? email.replace(
                        patt,
                        "<mark class='search-highlight'>$1</mark>"
                      )
                    : null;
              
                

                if (firstname2 || lastname2 || email2 ) {
                    foundRows.push( {
                        id: row.id,
                        firstname: firstname2 || firstname,
                        lastname: lastname2 || lastname,
                        email: email2 || email,
                        phonenumber:phonenumber,
                        phoneCountryCode:phoneCountryCode,
                        roleId: row.id,
                    } );
                }
            }

            this.setOfCheckedId.clear();
            this.checked = false;
            this.indeterminate = false;
            this.contactsFiltered =  foundRows;
        }
        this.totalContacts =  this.contactsFiltered.length;
  }

  // changePageIndex(event:any){
  //   this.contactsFiltered = this.contacts.slice(event*10-10,event*10);
  //  }
  changePageIndex(event:any){
    this.pageIndex = event;
   }

  changeTeamPageIndex(event:any){
    this.teamContactsFiltered = this.teamContacts.slice(event*10-10,event*10);
   }

  parseCSVContacts(fileContent: any) {
    var mobilePhone;

    try {
      var contactList:any = this.papa.parse(fileContent).data;
    } catch (err:any) {
      alert("Invalid Data: " + err.message);
    }
    if (contactList) {
      var indexFirstName:any, indexLastName:any, indexEmail1:any, indexEmail2:any, contacts:any[] = [], emails = new Array();
      var indexMobilePhone:any;
      for (var j = 0; j < contactList[0].length; j++) {
        if (contactList[0][j].toLowerCase().trim() == "family name" || contactList[0][j].trim() == "Last Name" || contactList[0][j].trim().toLowerCase().indexOf("last") > -1)
          indexLastName = j;
        else if (contactList[0][j].toLowerCase().trim() == "given name" || contactList[0][j].toLowerCase().trim() == "first name" || (contactList[0][j].trim().toLowerCase().indexOf("first") > -1 && contactList[0][j].trim().toLowerCase().indexOf("name") > -1))
          indexFirstName = j;
        else if (contactList[0][j].trim() == "E-mail 2 - Value" || contactList[0][j].trim() == "E-mail 2 Address" || (contactList[0][j].trim().toLowerCase().indexOf("mail 2") > -1 && contactList[0][j].trim().toLowerCase().indexOf("value") > -1))
          indexEmail2 = j;
        else if (contactList[0][j].trim() == "E-mail 1 - Value" || contactList[0][j].trim() == "E-mail Address" || (contactList[0][j].trim().toLowerCase().indexOf("mail 1") > -1 && contactList[0][j].trim().toLowerCase().indexOf("value") > -1))
          indexEmail1 = j;
        else if (contactList[0][j].trim() == "Mobile Phone" ||
          contactList[0][j].trim() == "Phone 1 - Value" ||
          (contactList[0][j].trim().toLowerCase().indexOf("phone 1") > -1 && contactList[0][j].trim().toLowerCase().indexOf("value") > -1))
          indexMobilePhone = j;
      }

      if (this.contacts)
        for (var i = 0, len = this.contacts.length; i < len; i++)
          emails.push(this.contacts[i].email);

      for (var j = 1; j < contactList.length; j++) {
        if(contactList[j].length < 2) continue;
        var contact:any = {};
        contact.firstname = contactList[j][indexFirstName];
        contact.lastname = contactList[j][indexLastName];
        //convert mobile phone to country code and phone number bug#2822
        mobilePhone = contactList[j][indexMobilePhone] ? contactList[j][indexMobilePhone].trim() : undefined;
        //check if mobile phone only contains digits or +
        var mobilePhone_REGEXP = /^[0-9\+\s]*$/;
        var formatStatus = true;
        formatStatus = mobilePhone_REGEXP.test(mobilePhone);
        if (mobilePhone == "") {
          formatStatus = false;
        }
        //if the format is correct,
        if (formatStatus) {
          //check if mobile phone contains +CountryCode plus empty space plus phonenumber
          //max length of countryCode is 4 and max length of phone number is 11
          var mobilePhone_REGEXP1 = /^([\+]{1,1})+([0-9]{1,4})+([\s]{1,1})+([0-9]{1,11})$/;
          if (mobilePhone_REGEXP1.test(mobilePhone)) {
            //if mobile phone contains country code, get the country code and phone number
            var emptySpacePosition = mobilePhone.indexOf(" ");
            contact.phonenumber = mobilePhone.substring(emptySpacePosition + 1, mobilePhone.length);
            contact.phoneCountryCode = mobilePhone.substring(1, emptySpacePosition);
          } else {
            contact.phoneCountryCode = "1";

            contact.phonenumber = mobilePhone;
          }
        } else {
          //if the format is wrong ,do not set the conutry code and phone number
        }

        if (contactList[j][indexEmail1])
          contact.email = contactList[j][indexEmail1].trim();
        if (!contact.email && contactList[j][indexEmail2])
          contact.email = contactList[j][indexEmail2].trim();
        // if not duplicated, add the contact
        if (contact.email && emails.indexOf(contact.email) < 0) {
          emails.push(contact.email);
          contacts.push(contact);
        }
        
        // if there is an entry without an email, give invalid format error
        else if (emails.indexOf(contact.email) < 0) {
          // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
          // Provide the user with actionable guidance describing how to fix
          // the CSV so the import can succeed.  A modal is used here because
          // the error occurs outside of the reactive form context and
          // therefore needs a separate announcement channel.
          this.modal.error({
            nzTitle: $localize `Invalid CSV format`,
            // Assumption: CSV header row must at least include a column
            // labelled “E-mail 1 - Value” or “E-mail Address”.
            nzContent: $localize `Ensure each row contains an e-mail address in a column labelled “E-mail 1 – Value” (for example: <EMAIL>).`
          });
          return;
        }

        if (!this.validateEmail(contact.email)) {
          // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
          // Explain what constitutes a valid e-mail address so that the user
          // can correct the entry instead of only stating that it is invalid.
          this.modal.error({
            nzTitle: $localize `Invalid email`,
            nzContent: $localize `Enter a valid e-mail address such as “<EMAIL>”.`
          });
          return;
        }
      }
      this.modal.confirm({
        nzTitle: $localize`Are you sure?`,
        nzContent: $localize`You are about to import your contact list, existing emails will be ignored.`,
        nzCancelText: $localize`Cancel`,
        nzOnOk: () => {
          this.addContactsCSV({ 'contacts' : contacts });
        }
      });
    }
  
  }

  selectContactToModify() {
    this.editContactModal = true;
    this.currentUser = this.contacts.find((contact:any) => contact.id == this.setOfCheckedId.values().next().value);
    this.editContactForm.get('email')?.setValue( this.currentUser.email);
    this.editContactForm.get('firstName')?.setValue( this.currentUser.firstname);
    this.editContactForm.get('lastName')?.setValue( this.currentUser.lastname);
    this.editContactForm.get('countryCode')?.setValue( this.currentUser.phoneCountryCode);
    this.editContactForm.get('phoneNumber')?.setValue( this.currentUser.phonenumber);
    this.editContactModal = true;
  }

  validateEmail(email:string) {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      );
  }

  
  refreshCheckedStatus(): void {
    this.checked = this.contacts.every(({ id }:any) => this.setOfCheckedId.has(id));
    this.indeterminate = this.contacts.some(({ id }:any) => this.setOfCheckedId.has(id)) && !this.checked;
  }
  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.contacts
      .forEach(({ id }:any) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  // --------------------------- WCAG 2.1 SC 1.3.5 ---------------------------
  // Automatically add `autocomplete="off"` to any input elements rendered by
  // third-party components (e.g., table selection checkboxes) that do not
  // already declare a semantic purpose. This eliminates automated audit
  // notices without affecting functionality. // Assumption: these inputs do
  // not collect personal user data.
  ngAfterViewInit(): void {
    this.applyAutocompletePatch();
    // WCAG 2.4.4 – Add meaningful aria-labels to icon-only / numeric
    // pagination links generated by Ant Design's `nz-pagination` so that
    // their purpose is clear when a screen-reader user navigates the page by
    // links list (e.g., NVDA Ins + F7). // Assumption: DOM structure matches
    // the pattern produced by the same library elsewhere in the app.
    this.patchPaginationAriaLabels();


  }



  private applyAutocompletePatch(): void {
    // Defer execution until after the view has stabilised.
    setTimeout(() => {
      const inputs: NodeListOf<HTMLInputElement> = this.elementRef.nativeElement.querySelectorAll('input');
      inputs.forEach((input) => {
        if (!input.hasAttribute('autocomplete')) {
          this.renderer.setAttribute(input, 'autocomplete', 'off');
        }
      });
    });
  }

  /**
   * WCAG 2.4.4 (Link Purpose – In Context) and WCAG 2.1.1 (Keyboard)
   * --------------------------------------------------
   * The Ant Design `nz-pagination` component renders its controls as a list of
   * anchor (`<a>`) elements that contain only an icon or a page number. When a
   * screen-reader user brings up the links list, these anchors are announced
   * simply as "1, 2, 3, …" or nothing at all for the icon buttons, which does
   * not convey their purpose in isolation. To solve this we inject meaningful
   * `aria-label` attributes after the view has been rendered.
   * 
   * Also ensures all pagination controls are keyboard accessible and respond
   * to Enter and Space key activation.
   */
  private patchPaginationAriaLabels(): void {
    // Defer execution so DOM is fully updated.
    setTimeout(() => {
      // Look for both old and new pagination patterns
      const paginationSelectors = [
        'ul.ant-pagination', 'nz-pagination ul', '.ant-table-pagination ul',
        'ul[class*="pagination"]', '.ant-pagination'
      ];
      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });

      paginations.forEach(pagination => {
        const items = pagination.querySelectorAll('li');
        items.forEach(li => {
          // Ant Design changed markup from <a> to <button> in v13.
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) {
            return; // nothing to patch
          }

          // Skip if another process already supplied an accessible name.
          if (control.hasAttribute('aria-label')) {
            return;
          }

          // Ensure control is focusable for keyboard navigation
          if (!control.hasAttribute('tabindex') && control.tagName !== 'BUTTON') {
            this.renderer.setAttribute(control, 'tabindex', '0');
          }

          // Add CSS class for focus styling
          this.renderer.addClass(control, 'pagination-control');

          // Provide semantic role when href is missing so <a> without href is exposed correctly.
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button'); // Assumption: matches built-in keyboard support
          }

          // Add keyboard event handlers for pagination controls only
          this.addPaginationKeyboardActivation(control);

          // Check for various pagination control patterns
          if (li.classList.contains('ant-pagination-prev') || 
              li.querySelector('[class*="prev"]') ||
              control.textContent?.includes('‹') ||
              control.textContent?.includes('<')) {
            this.renderer.setAttribute(control, 'aria-label', 'Previous page');
          } else if (li.classList.contains('ant-pagination-next') || 
                     li.querySelector('[class*="next"]') ||
                     control.textContent?.includes('›') ||
                     control.textContent?.includes('>')) {
            this.renderer.setAttribute(control, 'aria-label', 'Next page');
          } else if (li.classList.contains('ant-pagination-item') || 
                     li.classList.contains('ant-pagination-item-active')) {
            const text = control.textContent?.trim();
            if (text && /^\d+$/.test(text)) {
              this.renderer.setAttribute(control, 'aria-label', `Page ${text}`);
            }
          }
        });
      });
    }, 150); // Longer delay to ensure pagination is fully rendered
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to pagination controls to ensure they respond
   * to Enter and Space key activation, matching expected button behavior.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    // Remove any existing keyboard handlers to prevent duplicates
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) {
      control.removeEventListener('keydown', existingHandler);
    }

    // Create new keyboard handler
    const keyboardHandler = (event: KeyboardEvent) => {
      // Activate on Enter or Space (standard button behavior)
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // Trigger click event to activate pagination
        control.click();
      }
    };

    // Store reference for cleanup and add listener
    (control as any).__keyboardHandler = keyboardHandler;
    this.renderer.listen(control, 'keydown', keyboardHandler);
  }

}
