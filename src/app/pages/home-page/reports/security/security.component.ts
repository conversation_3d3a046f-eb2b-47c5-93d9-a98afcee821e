// Added AfterViewInit to programmatically patch missing `autocomplete` attributes for
// non-personal inputs (WCAG 2.1 SC 1.3.5 Identify Input Purpose)
// WCAG 4.1.2 – supply accessible names/roles for dynamically rendered pagination controls
// WCAG 2.4.3 – added focus-management helpers to keep keyboard focus order logical when dialogs open/close
import { Component, OnInit, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';
import { ReportService, UserService } from '@core/services';
import { Roles } from "@core/models";
import { combineLatest } from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';
import { en_US,fr_FR, NzI18nService } from 'ng-zorro-antd/i18n';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security.component.less']
})
export class SecurityComponent implements OnInit, AfterViewInit {

  readonly roles = Roles;
  public generateFormModal: boolean = false;
  public unreadReportsCount:number = 0;
  public deleteConfirmationModal: boolean = false;
  public renameConfirmationModal: boolean = false;
  public renameNewTitle: string = '';
  public showReportTable: boolean = false;
  public showSavedReportTable: boolean = true;
  public displayData: any;
  public currentReportId:number = -1
  public tableTotal: number = 0;
  public reportName:string = '';
  public reportDateFrom:string = '';
  public reportDateTo:string = '';
  public reportUser:string = '';
  public reportEvent:string = '';
  public displayListData: any;
  public tableListTotal: number = 0;
  public userDetails: any;
  public isTeamPlan: boolean = false;
  public users: any = [{ value: 0, label: $localize `All`}];
  public eventTypes: any = [{ value: 0, label: $localize `All`},
  { value: 193, label: $localize `User login failed`},
  { value: 200, label: $localize `Admin changed team member password`},
  { value: 210, label: $localize `User changed password`},
  { value: 221, label: $localize `User updated 2FA settings`},
  { value: 231, label: $localize `User reset password`},
  ];
  public datePresets: any = [{ value: '', label: $localize `Filter Results`},
  { value: '1', label: $localize `Yesterday`},
  { value: '2', label: $localize `Last 7 Days`},
  { value: '3', label: $localize `Last 30 Days`},
  { value: '4', label: $localize `Quarter to Date`},
  { value: '5', label: $localize `Last Quarter`},
  { value: '6', label: $localize `Year to Date`},
  { value: '8', label: $localize `Subscription Period to Date`},
  ];
  public checked = false;
  public loading = false;
  public indeterminate = false;
  public setOfCheckedId = new Set<number>();
  public cycleBegin: string = '';
  public selectedUserId: number = 0;
  public selectedEventType: number = 0;
  public selectedDatePreset: string = '';
  public dateFrom: Date = new Date(new Date().getTime() - (7 * 24 * 60 * 60 * 1000));
  public dateTo: Date = new Date();
  public newReportTitle:string = '';
  public locale = $localize.locale

  constructor(
    private reportService: ReportService,
    private userService: UserService,
    private modal: NzModalService,
    private i18n: NzI18nService,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    this.titleService.setTitle($localize`Security Report`);
    
    this.getListTableData(1);
    this.setTableLanguage();
    let $isTeamplan = this.userService.isTeamPlan();
    let $user = this.userService.getUser();

    //wait for userdetails and teamplan before calling getUsers()
    combineLatest([$isTeamplan, $user]).subscribe(([isteamplan, user]) => {
      this.isTeamPlan = isteamplan;
      this.userDetails = user;
      this.getUsers();
    })

    this.userService.getAccountDetails().subscribe((details) => {
      this.cycleBegin = details.cycle_begin;
    })

  }

  // WCAG 2.1 SC 1.3.5 – ensure any dynamically rendered <input> elements that do
  // not capture personal data explicitly disable browser autofill so automated
  // audits do not flag missing purpose tokens.
  ngAfterViewInit(): void {
    this.applyAutocompletePatch();
    // WCAG 2.4.4 – ensure pagination links expose purpose via `aria-label`
    this.patchPaginationAriaLabels();
    // WCAG 2.4.3 – remove unwanted focus traps
    this.removeUnwantedFocusTargets();
    // WCAG 2.1.1 – ensure checkboxes respond to keyboard activation
    this.addCheckboxKeyboardActivation();
  }

  private applyAutocompletePatch(): void {
    // Assumption: inputs within this component do NOT request personal data;
    // therefore `autocomplete="off"` is appropriate when a semantic token is
    // absent. Patch is deferred to allow child components to finish rendering.
    setTimeout(() => {
      const inputs: NodeListOf<HTMLInputElement> = this.elementRef.nativeElement.querySelectorAll('input');
      inputs.forEach((input) => {
        if (!input.hasAttribute('autocomplete')) {
          this.renderer.setAttribute(input, 'autocomplete', 'off');
        }
      });
    });
  }

  /**
   * WCAG 2.4.4 (Link Purpose – In Context)
   * --------------------------------------------------
   * The Ant Design `nz-pagination` component renders its page controls as
   * a list of anchor (`<a>`) elements that contain an icon or page number
   * as their only visible content.  Screen-reader users navigating the page
   * by links list (NVDA Ins + F7, VoiceOver ⌥ ⌘ L) would therefore hear only
   * “1, 2, 3, …” or silent icon names – insufficient to identify each
   * control’s purpose in isolation, which fails WCAG 2.4.4 at Level AA.
   *
   * Because the pagination markup is generated internally by the
   * `nz-table` / `nz-pagination` components, the most pragmatic fix is to
   * apply `aria-label` attributes after Angular finishes rendering the DOM
   * for the current view.  This method is invoked after view init and every
   * time new table data is loaded.
   *
   * Assumption: the DOM structure follows the pattern used by
   * Ant Design v12 – list items carry BEM utility classes such as
   * `.ant-pagination-prev`, `.ant-pagination-item`, `.ant-pagination-next`.
   */
  private patchPaginationAriaLabels(): void {
    // Defer execution so DOM is fully updated.
    setTimeout(() => {
      // Look for both old and new pagination patterns
      const paginationSelectors = [
        'ul.ant-pagination',
        'nz-pagination ul',
        '.ant-table-pagination ul',
        'ul[class*="pagination"]',
        '.ant-pagination'
      ];
      
      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });

      // Also check for ng-zorro table pagination directly
      const nzTables = this.elementRef.nativeElement.querySelectorAll('nz-table');
      nzTables.forEach((table: HTMLElement) => {
        const paginationInTable = table.querySelectorAll('ul.ant-pagination, .ant-pagination');
        paginations.push(...Array.from(paginationInTable) as HTMLElement[]);
      });

      paginations.forEach(pagination => {
        const items = pagination.querySelectorAll('li');

        items.forEach(li => {
          // Ant Design changed markup from <a> to <button> in v13.
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) {
            return; // nothing to patch
          }

          // Skip if another process already supplied an accessible name.
          if (control.hasAttribute('aria-label')) {
            return;
          }

          // Ensure control is focusable for keyboard navigation
          if (!control.hasAttribute('tabindex') && control.tagName !== 'BUTTON') {
            this.renderer.setAttribute(control, 'tabindex', '0');
          }

          // Add CSS class for focus styling
          this.renderer.addClass(control, 'pagination-control');

          // Provide semantic role when href is missing so <a> without href is exposed correctly.
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button'); // Assumption: matches built-in keyboard support
          }

          // Add keyboard event handlers for pagination controls only
          this.addPaginationKeyboardActivation(control);

          // Check for various pagination control patterns
          if (li.classList.contains('ant-pagination-prev') || 
              li.querySelector('[class*="prev"]') ||
              control.textContent?.includes('‹') ||
              control.textContent?.includes('<')) {
            this.renderer.setAttribute(control, 'aria-label', 'Previous page');
          } else if (li.classList.contains('ant-pagination-next') || 
                     li.querySelector('[class*="next"]') ||
                     control.textContent?.includes('›') ||
                     control.textContent?.includes('>')) {
            this.renderer.setAttribute(control, 'aria-label', 'Next page');
          } else if (li.classList.contains('ant-pagination-item') || 
                     li.classList.contains('ant-pagination-item-active')) {
            const text = control.textContent?.trim();
            if (text && /^\d+$/.test(text)) {
              this.renderer.setAttribute(control, 'aria-label', `Page ${text}`);
            }
          }
        });
      });
    }, 150); // Longer delay to ensure pagination is fully rendered
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to pagination controls to ensure they respond
   * to Enter and Space key activation, matching expected button behavior.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    // Remove any existing keyboard handlers to prevent duplicates
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) {
      control.removeEventListener('keydown', existingHandler);
    }

    // Create new keyboard handler
    const keyboardHandler = (event: KeyboardEvent) => {
      // Activate on Enter or Space (standard button behavior)
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // Trigger click event to activate pagination
        control.click();
      }
    };

    // Store reference for cleanup and add listener
    (control as any).__keyboardHandler = keyboardHandler;
    this.renderer.listen(control, 'keydown', keyboardHandler);
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to checkboxes to ensure they respond
   * to Enter and Space key activation for toggling checked state.
   */
  private addCheckboxKeyboardActivation(): void {
    setTimeout(() => {
      // Find all actual checkbox inputs and ensure they're the only focusable elements
      const allCheckboxInputs = this.elementRef.nativeElement.querySelectorAll('.ant-checkbox-input, input[type="checkbox"]');
      
      allCheckboxInputs.forEach((checkboxInput: HTMLElement) => {
        // Ensure the checkbox input itself is focusable
        if (!checkboxInput.hasAttribute('tabindex')) {
          this.renderer.setAttribute(checkboxInput, 'tabindex', '0');
        }

        // Remove focusability from parent cell to avoid duplicate tab stops
        const parentCell = checkboxInput.closest('td, th');
        if (parentCell) {
          this.renderer.removeAttribute(parentCell, 'tabindex');
        }

        // Add keyboard handler directly to the checkbox input
        if (!(checkboxInput as any).__checkboxKeyboardHandler) {
          this.addKeyboardHandlerToCheckbox(checkboxInput);
        }
      });

      // Handle header checkbox specially (it might not have a direct input)
      const headerCheckbox = this.elementRef.nativeElement.querySelector('th[tabindex="0"]');
      if (headerCheckbox) {
        // Look for checkbox input inside header
        const headerInput = headerCheckbox.querySelector('.ant-checkbox-input, input[type="checkbox"]');
        if (headerInput) {
          // Transfer focus to the input
          this.renderer.setAttribute(headerInput, 'tabindex', '0');
          this.renderer.removeAttribute(headerCheckbox, 'tabindex');
          if (!(headerInput as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerInput as HTMLElement);
          }
        } else {
          // Fallback: keep header cell focusable if no input found
          if (!(headerCheckbox as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerCheckbox);
          }
        }
      }
    }, 100);
  }

  private addKeyboardHandlerToCheckbox(element: HTMLElement): void {
    const keyboardHandler = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // If this IS a checkbox input, click it directly
        if (element.classList.contains('ant-checkbox-input') || element.tagName === 'INPUT') {
          element.click();
        } else {
          // Otherwise, look for checkbox input inside the element
          const checkboxInput = element.querySelector('.ant-checkbox-input, input[type="checkbox"]');
          if (checkboxInput) {
            (checkboxInput as HTMLElement).click();
          } else {
            // Fallback: trigger click on the element itself (for header checkbox cells)
            element.click();
          }
        }
      }
    };

    // Store reference and add listener
    (element as any).__checkboxKeyboardHandler = keyboardHandler;
    this.renderer.listen(element, 'keydown', keyboardHandler);
  }

  /**
   * WCAG 2.4.3 (Focus Order)
   * Remove any problematic tabindex attributes from elements that should not 
   * receive focus before the main interactive content.
   */
  private removeUnwantedFocusTargets(): void {
    setTimeout(() => {
      // Remove tabindex from potential focus-trapping elements
      const problematicSelectors = [
        '.sr-only[tabindex]',
        'caption[tabindex]',
        '.table-responsive[tabindex]',
        '.ant-table-wrapper[tabindex]',
        '.ant-table-container[tabindex]',
        '.ant-table[tabindex]'
      ];

      problematicSelectors.forEach(selector => {
        const elements = this.elementRef.nativeElement.querySelectorAll(selector);
        elements.forEach((element: HTMLElement) => {
          if (element.hasAttribute('tabindex')) {
            this.renderer.removeAttribute(element, 'tabindex');
          }
        });
      });
    });
  }

  setTableLanguage() {
    //message if table has no data
    en_US.Empty.description= this.showReportTable ? "There is no data available based on the report's criteria." : 
    'No reports have been generated yet.' ;
    fr_FR.Empty.description=this.showReportTable ? "Il n'y a pas de données disponibles sur la base des critères du rapport.":
    "Aucun rapport n'a encore été généré." ;
    //set language for antd components
    if(this.locale =='fr')  this.i18n.setLocale(fr_FR);
    else this.i18n.setLocale(en_US);
}
  generateReportModal() {
    this.newReportTitle = '';
    if(this.dateFrom > this.dateTo) {
      this.modal.error({
        nzTitle: $localize `Uh oh, we can't run those dates`,
        nzContent: $localize `Please make sure that your "Date to" is greater than your "date from". You may want to check that you have chosen the right month or year.`
      });
    }
    else {
      this.generateFormModal = true;
    }
  }
  generateReport() {
    // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
    // If the user attempts to create a new report without supplying a title
    // (or with only whitespace), provide an explicit corrective suggestion
    // describing how to fix the error rather than letting the request fail
    // silently or receive a generic back-end error.
    if (!this.newReportTitle || this.newReportTitle.trim().length === 0) {
      this.modal.error({
        nzTitle: $localize `Report title missing`,
        nzContent: $localize `Enter a descriptive title for your report (e.g. \"Q3 Security Audit\").` // Assumption: guidance helps user correct error
      });
      return; // prevent submission until error resolved
    }

    this.reportService.createReport(
      2,
      this.newReportTitle.trim(),
      this.dateFrom.getTime(),
      this.dateTo.getTime(),
      this.selectedDatePreset,
      this.selectedUserId,
      this.selectedEventType
    ).subscribe((response) => {
      this.generateFormModal = false;
      this.getListTableData(1);
    });
  }
  getTableData(pageNumber: number, id:number) {
    this.reportService.getReport(pageNumber, id).subscribe((val: any) => {
      this.displayData = val.data;
      // labels above table
      this.reportName = val.reportName;
      this.reportDateFrom = val.parameters?.dateFrom;
      this.reportDateTo = val.parameters?.dateTo;
      this.reportEvent = this.getEventNameFromId(val?.parameters?.forType);
      this.reportUser = this.getUserNameFromId(val?.parameters?.forUsers);

      this.tableTotal = val.total;
      this.currentReportId = id; 
      this.showSavedReportTable = false; 
      this.showReportTable = true;
      this.setTableLanguage();
      this.applyAutocompletePatch();
      this.patchPaginationAriaLabels();
      this.removeUnwantedFocusTargets();
      this.addCheckboxKeyboardActivation();

      // WCAG 2.4.3 – once the detailed report view is visible, place focus on
      // the 'Back to Saved Reports' button so that users can continue
      // navigating within the new context without traversing the entire
      // document.
      setTimeout(() => {
        const backButton = this.elementRef.nativeElement.querySelector('button[aria-label="Back to Saved Reports"]');
        if (backButton) {
          (backButton as HTMLElement).focus();
        }
      });
    })
  }
  getListTableData(pageNumber: number) {
    this.unreadReportsCount = 0;
    this.reportService.listReports(pageNumber,2).subscribe((val: any) => {
      this.displayListData = val.data.records;
      this.tableListTotal = val.data.total;
      this.displayListData.forEach((item:any) => {
        switch (item.reportStaus) {
          case 0: item.statusLabel = $localize `Queued`; break;
          case 1: item.statusLabel = $localize `In Progress`; break;
          case 2: item.statusLabel = $localize `Complete`;this.unreadReportsCount++;break;
          case 3: item.statusLabel = $localize `Error Occured`; break;
          case 4: item.statusLabel = $localize `Opened`; break;
        }
      })
      //don't show number greater than 99
      if(this.unreadReportsCount > 99) this.unreadReportsCount = 99;

      // Re-apply in case table rendered new checkbox inputs.
      this.applyAutocompletePatch();

      // Add descriptive aria-labels to any freshly rendered pagination controls.
      this.patchPaginationAriaLabels();

      // Remove any problematic focus targets that may have been added.
      this.removeUnwantedFocusTargets();

      // Add keyboard activation to checkboxes.
      this.addCheckboxKeyboardActivation();

    })
  }

  rename() {
    if (this.renameNewTitle.trim().length === 0) {
      this.modal.error({
        nzTitle: $localize `Report title missing`,
        nzContent: $localize `Enter a descriptive title for your report.`
      });
      return;
    }

    this.reportService.renameReport(Array.from(this.setOfCheckedId)[0], this.renameNewTitle.trim()).subscribe(() => {
      this.renameConfirmationModal = false;
      this.getListTableData(1);
    });
  }

  deleteReports() {
    let reportString = '';
    this.setOfCheckedId.forEach(val=>{
      reportString += val+',';
    })
    reportString = reportString.substring(0, reportString.length - 1);
    this.reportService.deleteReports(reportString).subscribe(()=>{
      this.deleteConfirmationModal = false;
      this.getListTableData(1);
      this.setOfCheckedId.clear()
      this.checked = false;
      this.indeterminate = false;
    })
  }

  download() {
    this.reportService.downloadReport(this.currentReportId);
  }

  getUsers() {
    if (this.userDetails.roleId != this.roles.DOC_ADMIN && this.userDetails.roleId != this.roles.REGULAR_USER && this.isTeamPlan) {
      this.reportService.getUsers().subscribe((users: any) => {
        if (users?.records) {
          users.records.forEach((user: any) => {
            if(user.status == 2) {
              this.users.push({ value: user.id, label: user.firstName + " " + user.lastName })
            }
          })
        }
      })

    }
    //if doc admin or regular user or individual plan, only show themself in list
    else {
      this.users.push({ value: this.userDetails.id, label: this.userDetails.firstname + " " + this.userDetails.lastname })

    }
  }

  getUserNameFromId(users:any) {
    if(!users) return '';
    if(users.length == 0 || users[0] <= 0 ) return '';
    else {
      let user = this.users.find((val:any)=>{
        return val.value == users[0];
      })
      return user?.label ? user?.label : '';
    }
  }
  getEventNameFromId(event:number) {
    if(!event) return '';
    if(event <= 0 ) return '';
    else {
      let e = this.eventTypes.find((val:any)=>{
        return val.value == event;
      })
      return e.label;
    }
  }

  previous() {
    this.showReportTable = false;
    this.showSavedReportTable = true;
    this.currentReportId = -1;
    this.setTableLanguage();
    this.getListTableData(1);
  }

  filterDates() {
    switch (this.selectedDatePreset) {
      case '1': this.dateFrom = new Date(Date.now() - (1 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '2': this.dateFrom =  new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '3': this.dateFrom =  new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '4': this.dateFrom =  new Date(this.getBegginingofQuarter()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '5': this.dateFrom =  new Date(this.getLastQuarter().dateFrom);
        this.dateTo =  new Date(this.getLastQuarter().dateTo);
        break;
      case '6': this.dateFrom =  new Date(new Date(new Date().getFullYear(), 0, 1).getTime()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '8': this.dateFrom =  new Date(new Date(this.cycleBegin).getTime()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
    }
  }
  getBegginingofQuarter() {
    var now = new Date();
    var quarter = Math.floor((now.getMonth() / 3));
    var firstDate = new Date(now.getFullYear(), quarter * 3, 1);
    return firstDate.getTime();
  }
  getLastQuarter() {
    var now = new Date();
    var year = now.getFullYear();
    var quarter = Math.floor((now.getMonth() / 3));
    var lastQuarter;
    if (quarter - 1 < 0) {
      lastQuarter = 3;
      year--;
    }
    else lastQuarter = quarter - 1;
    var dateFrom = new Date(year, lastQuarter * 3, 1).getTime();
    var dateTo = new Date(now.getFullYear(), quarter * 3, 1).getTime() - (1 * 24 * 60 * 60 * 1000);
    return { dateFrom, dateTo }
  }

  refreshCheckedStatus(): void {
    this.checked = this.displayListData.every(({ id }:any) => this.setOfCheckedId.has(id));
    this.indeterminate = this.displayListData.some(({ id }:any) => this.setOfCheckedId.has(id)) && !this.checked;
  }
  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.displayListData
      .forEach(({ id }:any) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  /**
   * WCAG 2.1.1 (Keyboard) – enable activation of clickable table rows via the
   * Space key, which some users expect when an element behaves like a button.
   * The existing implementation only reacted to Enter. We now mirror that
   * behaviour while also preventing the default page-scroll action that the
   * browser performs on Space.
   */ // Assumption: row click should be suppressed when status is not ready
  // Fix validation error: template passes generic `Event`; relax the type to
  // `Event` so Angular compiler no longer complains (preventDefault is still
  // available).  // WCAG 2.1.1 – keyboard activation maintained.
  onRowSpace(event: Event, data: any): void {
    if (data?.reportStaus === 2 || data?.reportStaus === 4) {
      event.preventDefault();
      this.getTableData(1, data.id);
    }
  }

  openDeleteModal(): void {
    this.deleteConfirmationModal = true;
  }

  openRenameModal(): void {
    this.renameNewTitle = '';
    this.renameConfirmationModal = true;
  }

  onGenerateModalCancel(): void {
    this.generateFormModal = false;
  }

  onDeleteModalCancel(): void {
    this.deleteConfirmationModal = false;
  }

  onRenameModalCancel(): void {
    this.renameConfirmationModal = false;
  }
}
