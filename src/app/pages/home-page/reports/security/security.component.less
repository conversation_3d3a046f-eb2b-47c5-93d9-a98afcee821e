// Added sr-only utility class for visually hidden caption and headers – WCAG 1.3.1
.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
}

// WCAG 1.3.1 – utility class to visually hide content while keeping it accessible to screen readers
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

// WCAG 1.4.10 – ensure data tables can scroll horizontally within their own
// container at 320 px viewport without forcing the entire page to scroll.
.table-responsive {
  width: 100%;
  max-width: 100%; // Assumption: container should never exceed viewport width
  overflow-x: auto; // single-axis scroll; vertical scroll remains on page
}

/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.12 (Text Spacing)
   ---------------------------------------------------------------------------
   Global stylesheet forces a rigid 30 px height on elements with the
   `.material-select-height` utility class.  When users apply custom text
   spacing (e.g. increase line-height to 1.5 × font size or raise
   letter/word spacing), the fixed height can clip the select’s label and
   value text, resulting in loss of content – a direct failure of SC 1.4.12.

   Override the constraint within the Security Report component so the
   control can grow vertically while still retaining its original minimum
   visual footprint (≈30 px).  *Assumption:* root font size ≈16 px, therefore
   1.875 rem ≈ 30 px.
--------------------------------------------------------------------------- */

/* WCAG 1.4.12 fix: allow Material select controls to expand when users
   adjust text spacing settings */ // Assumption: scoped to this component
.material-select-height {
  height: auto !important;   // remove rigid vertical constraint
  min-height: 1.875rem;      // preserve baseline size (~30 px)
  overflow: visible;         // prevent clipping of expanded content
  white-space: normal;       // permit wrapping when spacing increases
  line-height: normal;       // defer to user / UA stylesheet
}

// WCAG 1.4.10 – limit Angular Material datepicker overlay width so it fits
// within small viewports and does not introduce horizontal scrolling.
:host ::ng-deep .cdk-overlay-pane .mat-datepicker-content {
  max-width: 90vw; // Assumption: 5 % side gutters on 320 px viewport
  width: auto !important; // allow intrinsic width up to the max constraint
}
// WCAG 1.4.3 (Contrast Minimum – Level AA):
// The previous colour combination of white text (#FFFFFF) on a bright red
// background (#ff4d4f) delivered a contrast ratio of roughly 3.27:1, which is
// below the required 4.5:1 for normal-sized text.  To meet the minimum
// contrast requirement while preserving the brand-red background, switch the
// foreground text to black (#000000).  This yields a contrast ratio of ≈6.43:1
// and therefore complies with WCAG 2.1 SC 1.4.3.
.notify-div {
    background-color: #D40D00; // existing brand colour retained
    color: #fff;               // improved contrast against red background
    border-radius: 2.5rem; // WCAG 1.4.4 – convert pixel value (40px) to rem so radius scales with root font size
    text-align: center;
    margin-left: 0.625rem; // 10px → 0.625rem
    width: 2.1875rem; // 35px → 2.1875rem
    display: inline-block;
    font-weight: 600;          // Assumption: heavier weight improves legibility
}
th {
    font-weight: bold !important;
}

::ng-deep nz-table-inner-default {
    overflow: auto;
}

/* Orientation fix (WCAG 2.1 – 1.3.4):
   Convert fixed pixel font-sizes/line-heights on headings to fluid
   `clamp()` values so text scales across portrait and landscape views
   without causing horizontal scroll. */
@media (max-width:640px) {
    .mobile-title {
        font-size: clamp(1.75rem, 9vw, 3.125rem) !important; /* Assumption: previously 28-50 px */
        /* WCAG 1.4.12 – increase default line-height so heading retains ≥1.5× spacing
           when users apply custom text-spacing. Dropping the !important flag allows
           user style sheets to override with even larger values if desired. */ // Assumption: 1.5em satisfies guideline while preserving layout rhythm
        line-height: 1.5em;
        /* WCAG 1.4.12 – neutralise Tailwind negative top margin class (-mt-6) that
           could cause content overlap once line-height increases. */
        margin-top: 0 !important;
        padding-bottom: 1.25rem !important; // 20px → 1.25rem (relative) – prevents clipped heading on text zoom
    }
}

/* Desktop / larger viewports – also switch to clamp so the heading can
   shrink when the viewport narrows (e.g., iPad landscape). */
@media (min-width:641px) {
    .desktop-title {
        font-size: clamp(2.5rem, 6vw, 4.375rem); /* Assumption: 40-70 px */
        /* WCAG 1.4.12 – raise default line-height to ≥1.5× font size to prevent
           clipping/overlap under custom text-spacing. */
        line-height: 1.5em;
        /* WCAG 1.4.12 – override negative Tailwind margin to avoid overlap when
           heading expands. */
        margin-top: 0 !important;
        font-weight: 275;
        color: #2D323D;
        padding-bottom: 3.4375rem; // 55px → 3.4375rem – scales with text size
    }
}

/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   ---------------------------------------------------------------------------
   Default styles provided by Angular Material and Ant Design use very light
   grey (#d9d9d9 / rgba(0,0,0,.12)) borders and underlines for form controls
   as well as faint or absent focus indicators.  These fall well below the
   required 3 : 1 contrast ratio against the typical white page background
   and make it difficult for low-vision users to perceive component
   boundaries and keyboard focus.

   The following overrides are scoped to the Security Report component only
   (style encapsulation attribute) so they do not affect the rest of the
   application.  They introduce:

   1. A consistent 3 px blue focus ring (#2D323D ≥ 3 : 1 on both white and the
      light-grey backgrounds used throughout this component) for every
      interactive element – buttons, inputs, selects, table rows, etc.
   2. Darker borders/underlines (#797E8A ≈ 4.5 : 1 on white) for Material form
      fields, unchecked Ant Design checkboxes and default (non-primary)
      buttons so component boundaries meet the 3 : 1 threshold.

   // Assumption: #2D323D and #797E8A are already used elsewhere in the code
   // base for the same purpose (see previous automated fixes) and therefore
   // do not introduce new brand colours.
--------------------------------------------------------------------------- */

/* High-contrast keyboard focus indicator */
button:focus-visible,
[tabindex="0"]:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
select:focus-visible,
.mat-select:focus-visible,
.mat-select-trigger:focus-visible {
  outline: 3px solid #2D323D; // Assumption: ≥ 3 : 1 contrast on light backgrounds
  outline-offset: 2px;
}

/* WCAG 2.4.7 – enhanced focus indicators for pagination controls */
:host ::ng-deep .ant-pagination li a:focus-visible,
:host ::ng-deep .ant-pagination li button:focus-visible,
:host ::ng-deep .ant-pagination-item:focus-visible,
:host ::ng-deep .ant-pagination-prev:focus-visible,
:host ::ng-deep .ant-pagination-next:focus-visible,
:host ::ng-deep .pagination-control:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
  z-index: 1; // ensure focus ring appears above other elements
}

/* Ensure pagination controls are properly styled and clickable */
:host ::ng-deep .pagination-control {
  cursor: pointer;
  display: inline-block;
  min-width: 32px;
  min-height: 32px;
  text-align: center;
  border-radius: 4px;
}

/* Make tabbable table rows visually identifiable when focused */
tr:focus,
tr:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

/* Row selection highlighting - blue outline for selected row */
.row-selected {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
  background-color: rgba(26, 115, 232, 0.05) !important;
  position: relative;
  z-index: 1;
}

/* Checkbox selection highlighting - blue outline for selected checkbox */
.checkbox-selected {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
  background-color: rgba(26, 115, 232, 0.05) !important;
  position: relative;
  z-index: 1;
}

/* Ensure selected row maintains focus outline when focused */
tr.row-selected:focus,
tr.row-selected:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
}

/* Ensure selected checkbox maintains focus outline when focused */
td.checkbox-selected:focus,
td.checkbox-selected:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
}

/* Darken Angular Material form-field underline */
:host ::ng-deep .mat-form-field-underline,
:host ::ng-deep .mat-form-field-underline::after {
  background-color: #797E8A !important; // ≥ 3 : 1 contrast on white
}

/* Increase checkbox stroke contrast (Ant Design) */
:host ::ng-deep .ant-checkbox-inner {
  border: 1px solid #797E8A !important; // replace faint default border
}

/* Darken border for default Ant Design buttons (non-primary) when used */
:host ::ng-deep .ant-btn:not(.ant-btn-primary) {
  border: 2px solid #4c4c4c; // ≈ 8 : 1 vs white – aligns with other modules
}

/* WCAG 1.4.11 – Non-text Contrast: Ant Design text inputs use a faint
   #d9d9d9 border (~1.2 : 1 on white) which fails the required ≥ 3 : 1 ratio.
   Darken the default border for nz-input (and textarea variant) so the
   component boundary is clearly perceivable for low-vision users. */
input[nz-input],
textarea[nz-input] {
  border: 1px solid #797E8A; // Assumption: #797E8A ≈ 4.5 : 1 contrast on white
}
@media (max-width:1145px) {
    .select {
        width: 100%;
        width: -moz-available;          
        width: -webkit-fill-available;  
        width: fill-available;
        max-width: 21.875rem; // 350px → 21.875rem – responsive select width
    }
}
@media (min-width:1146px) {
    .select {
        min-width: 12.5rem; // 200px → 12.5rem
        margin-top: 0.625rem; // 10px → 0.625rem
    }
}

/* Thicker blue borders for focus highlighting */
tr:focus,
tr:focus-visible {
  box-shadow: 0 0 0 1px #2D323D !important;
  position: relative;
  z-index: 1;
}

td:focus,
td:focus-visible {
  box-shadow: 0 0 0 1px #2D323D !important;
  position: relative;
  z-index: 1;
}

/* Ensure table container doesn't clip borders */
::ng-deep .ant-table-tbody {
  overflow: visible !important;
}

::ng-deep .ant-table-container {
  overflow: visible !important;
}

::ng-deep .ant-table {
  overflow: visible !important;
}
