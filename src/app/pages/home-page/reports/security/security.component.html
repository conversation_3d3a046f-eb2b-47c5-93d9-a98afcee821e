<!-- WCAG 2.4.3 – revised modal triggers & added autofocus so dialog focus order is logical -->
<!-- WCAG 1.3.1 – added captions for data tables, scope attributes for header cells, labelled filter select, and marked layout tables as presentational -->
<!-- CHAN<PERSON> (2025-07-17): Updated aria-label usage to ensure visible label text is included in the
     accessible name for interactive controls (WCAG 2.5.3 Label in Name). Removed conflicting
     aria-labels from date inputs and aligned button aria-labels with their visible text. -->
<!-- Converted fixed pixel heading to responsive clamp() values for WCAG 2.1 – 1.3.4 compliance -->
<!-- Validation fix: moved comment outside of <h1> start tag and closed tag properly to resolve Angular template parse error (NG5002) -->
<!-- WCAG 1.4.4 – convert padding-bottom 55px→3.4375rem for scalable spacing -->
<!-- WCAG 1.4.12 – increased line-height to 1.5em so text remains readable and
     functional when users apply custom text spacing. Also reset margin-top to
     0 to neutralise the Tailwind -mt-6 class that could cause overlap. -->
<h1 style="font-size: clamp(2.5rem, 6vw, 4.375rem); line-height: 1.5em; margin-top: 0; font-weight: 275; color: #2D323D; padding-bottom: 37.5px;margin-top:-41.5px !important;"
    class="-mt-6 ml-5 font-thin desktop-title mobile-title" i18n>
    <!-- Assumption: clamp maintains previous visual size on large screens -->
    Security Report
</h1>
<div *ngIf="!showReportTable ">
    <!-- Converted decorative <span> to semantic heading so section label is conveyed programmatically (WCAG 1.3.2) -->
    <div class="m-3">
        <!-- WCAG 1.4.4 – font-size 16px→1rem -->
        <h2 style="font-size: 1rem;" class="font-bold" i18n>
            Report Settings
        </h2>
    </div>
    <div class="!med:flex med:grid xs:grid grid-cols-2 gap-y-2">
        <div class="mx-3 col-span-2" i18n>
            User:
            <br>
            <mat-form-field class="material-select-height select">
                <mat-select name="User" [(ngModel)]="selectedUserId" i18n-aria-label aria-label="User">
                    <mat-option *ngFor="let user of users" [value]="user.value">{{user.label}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="mx-3 col-span-2" i18n>
            Event Type:
            <br>
            <mat-form-field class="material-select-height select">
                <mat-select name="Event type" [(ngModel)]="selectedEventType" i18n-aria-label aria-label="Event Type">
                    <mat-option *ngFor="let event of eventTypes" [value]="event.value">{{event.label}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
    </div>
    <div class="!med:flex med:grid xs:grid grid-cols-2 gap-y-2">
        <div class="mx-3 col-span-2" i18n>
            <!-- WCAG 3.3.2 – added visible label so users who rely on visual
                 cues (including those with assistive technologies that render
                 the visual interface) can identify the purpose of this
                 control. The programmatic name (aria-label) was already set
                 earlier; this change provides the corresponding on-screen
                 text label required by the success criterion. -->
            Filter Dates:
            <br>
            <mat-form-field class="material-select-height select">
                <!-- WCAG 1.3.1 – provide explicit accessible name for date preset filter -->
                <mat-select name="Filter dates" i18n-aria-label aria-label="Filter dates" [(ngModel)]="selectedDatePreset" (ngModelChange)="filterDates()">
                    <mat-option *ngFor="let date of datePresets" [value]="date.value">{{date.label}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex items-center flex-wrap mx-3">
            <div class="mr-2">
                <span i18n>From:</span>
                <br>
                <!-- WCAG 2.5.3 (Label in Name): ensure the visible text label
                     "From:" is included in the component’s accessible name so
                     speech-input users can activate the control using the same
                     words they see on screen. -->
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Assumption: date range does not capture personal data therefore disable browser autofill -->
                    <!-- Assumption: changing label text does not break existing
                         e2e tests because it remains semantically identical. -->
                    <input matInput [matDatepicker]="picker" [(ngModel)]="dateFrom" autocomplete="off" aria-label="Select date – start date"><!-- WCAG 2.5.3: accessible name now contains visible label text -->
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <!-- WCAG 2.4.6 – give the calendar toggle a descriptive name so screen-reader users know which date field it relates to -->
                    <mat-datepicker-toggle matSuffix [for]="picker" i18n-aria-label aria-label="Open calendar for start date"></mat-datepicker-toggle>
                    <mat-datepicker #picker ></mat-datepicker>
                </mat-form-field>
            </div>
            <div >
                <span i18n>To:</span>
                <br>
                <!-- WCAG 2.5.3 (Label in Name): include visible text in
                     programmatic name. -->
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Assumption: non-personal date input – disable autocomplete per WCAG 2.1 SC 1.3.5 -->
                    <input matInput [matDatepicker]="picker2" [(ngModel)]="dateTo" autocomplete="off" aria-label="Select date – end date"><!-- WCAG 2.5.3: accessible name equals visible label -->
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <!-- WCAG 2.4.6 – descriptive label for end-date calendar control -->
                    <mat-datepicker-toggle matSuffix [for]="picker2" i18n-aria-label aria-label="Open calendar for end date"></mat-datepicker-toggle>
                    <mat-datepicker #picker2 ></mat-datepicker>
                </mat-form-field>
            </div>
        </div>
        <div class="mx-3 col-span-2">
            <!-- WCAG 1.4.4 – margin-top 30px→1.875rem -->
            <!-- WCAG 2.4.6 – replace generic button label with a more descriptive one so users understand the action without additional context -->
            <button (click)="generateReportModal()" nz-button nzType="primary"
                style="margin-top:1.875rem;" i18n i18n-aria-label
                aria-label="Generate Report">Generate Report</button><!-- WCAG 2.5.3: aria-label matches visible text -->
        </div>
    </div>
</div>
<!-- WCAG 1.3.3 (Sensory Characteristics):
     Reworded instructional text so it no longer relies on spatial cues
     such as "above" and "below". Instead, references labelled sections
     ("Report Settings" and "Saved Reports") to provide equivalent
     meaning for all users, including those who cannot perceive layout.
     // Assumption: Section headings are unique and translated via existing
     // i18n extraction, so quoting them here is sufficient context. -->
<div *ngIf="!showReportTable" class="m-8 text-center" i18n>
    To create a report, choose your filter options in the "Report Settings" section and click "Generate".<br>
    Your report will be processed and added to the "Saved Reports" list when ready.
</div>
<!-- Converted "Saved Reports" label to semantic heading to ensure meaningful document outline (WCAG 1.3.2) -->
<div *ngIf="!showReportTable"  class=" ml-3">
    <!-- WCAG 1.4.4 – font-size 16px→1rem -->
    <h2 style="font-size: 1rem;" class="font-bold inline-block" i18n>Saved Reports</h2>   
    <span *ngIf="unreadReportsCount >0" class="notify-div">{{unreadReportsCount}}</span>
 </div>
<!-- WCAG 2.4.6 – clarify destination of the navigation control -->
<button *ngIf="showReportTable" (click)="previous()" class="underline mb-4 ml-3" i18n i18n-aria-label
        aria-label="Back to Saved Reports">Back to Saved Reports</button><!-- WCAG 2.5.3: align aria-label with visible text -->
<div *ngIf="showReportTable">
    <div style="display: flex; justify-content: space-between" class="ml-5 m-3 mb-4">
        <div >
            <!-- WCAG 1.4.4 – font-size 18px→1.125rem -->
            <span style="font-size: 1.125rem;" >
                <b i18n>Report Title:</b> {{reportName}}
            </span>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportDateFrom" style="font-size: 0.875rem;">
                <b i18n>From:</b> {{reportDateFrom |date}}
            </div>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportDateTo" style="font-size: 0.875rem;">
                <b i18n>To:</b> {{reportDateTo |date}}
            </div>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportUser" style="font-size: 0.875rem;">
                <b i18n>User:</b> {{reportUser}}
            </div>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportEvent" style="font-size: 0.875rem;">
                <b i18n>Event:</b> {{reportEvent}}
            </div>
        </div>
        <!-- WCAG 1.4.4 – margin-right 3px→0.1875rem -->
        <div style="margin-right: 0.1875rem; display: flex; flex-direction: column; justify-content: flex-end">
            <!-- WCAG 2.4.6 – label clearly states what will be downloaded -->
            <button (click)="download()" nz-button nzType="primary" i18n i18n-aria-label
                aria-label="Download Report">Download Report</button><!-- WCAG 2.5.3: aria-label matches visible text -->
        </div>
    </div>
    <!-- <nz-table class="mx-3" [nzShowPagination]="false" #basicTable [nzData]="displayData"> -->
    <!-- WCAG 1.3.1 – added caption for data table and explicit scope for header cells -->
    <!-- WCAG 1.4.10 – wrapped table in horizontally-scrollable region so page never requires two-axis scrolling on small viewports -->
    <!-- WCAG 2.4.3 – removed tabindex to prevent container from intercepting focus before table contents -->
    <div class="table-responsive" role="region" aria-labelledby="securityReportCaption">
    <!-- WCAG 1.4.4 – converted left/right margins 15px→0.9375rem -->
    <!-- WCAG 2.4.3 – prevent table container from receiving focus before interactive elements -->
    <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem;" #basicTable nzFrontPagination="false"
    [nzTotal]="tableTotal" [nzData]="displayData" (nzPageIndexChange)="getTableData($event, currentReportId)" tabindex="-1">
        <!-- Assumption: visually hidden caption gives the table an accessible name without affecting layout -->
        <caption id="securityReportCaption" class="sr-only" i18n>Security report results – list of security events</caption>
        <thead>
            <tr>
                <th scope="col" i18n>User</th>
                <th scope="col" i18n>Event</th>
                <th scope="col" i18n>Time</th>
                <th scope="col" i18n>IP Address</th>
                <th scope="col" i18n>Visiting OS</th>
                <th scope="col" i18n>Visiting Browser</th>
                <th scope="col" i18n>Note</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTable.data">
                <td>{{data.name}}</td>
                <td>{{data.type}}</td>
                <td>{{data.time}}</td>
                <td>{{data.ip}}</td>
                <td>{{data.os}}</td>
                <td>{{data.browser}}</td>
                <td>{{data.note}}</td>
            </tr>
        </tbody>
    </nz-table>
    </div>
    <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableTotal" [pageSize]="10" [hidePageSize]="true"
    (page)="getTableData($event.pageIndex+1, currentReportId)" ></mat-paginator>     -->
</div>

<div *ngIf="showSavedReportTable">
    <div class="m-3 mb-4" aria-live="polite">
        <!-- Accessibility: mark decorative icon as hidden from assistive tech (WCAG 1.1.1) -->
        <!-- WCAG 1.4.4 – margin-right 20px→1.25rem -->
        <!-- WCAG 2.4.6 – provide context in the label for users of assistive technology -->
        <button (click)="openRenameModal()" *ngIf="setOfCheckedId.size == 1" style="cursor:pointer; margin-right: 1.25rem;" i18n i18n-aria-label
            aria-label="Rename Selected Reports">
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="edit" nzTheme="outline" aria-hidden="true"></span> Rename Report
        </button>
        <!-- Accessibility: mark decorative icon as hidden from assistive tech (WCAG 1.1.1) -->
        <!-- WCAG 2.4.6 – descriptive label to avoid ambiguity -->
        <button (click)="openDeleteModal()" *ngIf="setOfCheckedId.size > 0" style="cursor:pointer;" i18n i18n-aria-label
            aria-label="Delete Selected Reports">
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="delete" nzTheme="outline" aria-hidden="true"></span> Delete Report(s)
        </button>
    </div>
    <!-- <nz-table class="mx-3" [nzShowPagination]="false" #rowSelectionTable [nzData]="displayListData"> -->
    <!-- WCAG 1.3.1 – added caption and scope/labels for header cells in saved reports table -->
    <!-- WCAG 1.4.10 – wrapped table in single-axis scroll region so page does not scroll horizontally -->
    <!-- WCAG 2.4.3 – removed tabindex to prevent container from intercepting focus before table contents -->
    <div class="table-responsive" role="region" aria-labelledby="savedReportsCaption">
    <!-- WCAG 1.4.4 – converted left/right margins 15px→0.9375rem -->
    <!-- WCAG 2.4.3 – prevent table container from receiving focus before interactive elements -->
    <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem;" #rowSelectionTable nzFrontPagination="false"
    [nzTotal]="tableListTotal" [nzData]="displayListData" (nzPageIndexChange)="getListTableData($event)" tabindex="-1">
        <caption id="savedReportsCaption" class="sr-only" i18n>Saved security reports</caption>
        <thead>
            <tr>
                <!-- Select all checkbox column -->
                <!-- WCAG 2.4.3 – explicit tabindex ensures header checkbox receives focus first in logical tab order -->
                <th scope="col" tabindex="0" aria-label="Select all saved reports"
                    [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)">
                </th>
                <th scope="col" i18n>Report Title</th>
                <th scope="col" i18n>Last Modified</th>
                <th scope="col" i18n>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr tabindex="0" [attr.aria-label]="'Open report ' + data.name"
            [class.cursor-pointer]="data.reportStaus == 2 || data.reportStaus == 4" *ngFor="let data of rowSelectionTable.data"
             (click)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''"
             (keydown.enter)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''">
                <td [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
                    (nzCheckedChange)="onItemChecked(data.id, $event)"></td>
                <td>{{data.name}}</td>
                <td>{{data.dateAdded}}</td>
                <td>{{data.statusLabel}}</td>
            </tr>
        </tbody>
    </nz-table>
    </div>
    <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableListTotal" [pageSize]="10" [hidePageSize]="true"
    (page)="getListTableData($event.pageIndex+1)" ></mat-paginator>     -->
</div>

<!-- Orientation fix (WCAG 2.1 – 1.3.4): use viewport-relative width so dialog fits within both portrait and landscape viewports. -->
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="generateFormModal" nzTitle="Security Report" [nzWidth]="'90vw'"
    (nzOnOk)="generateReport()" (nzOnCancel)="onGenerateModalCancel()" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- Assumption: 90vw leaves comfortable margin on desktop -->
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – mark layout table as presentational -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n><b>Report Type:</b> Security Report</td>
            </tr>
            <tr *ngIf="dateTo.getTime() > 0 && dateFrom.getTime() > 0">
                <td i18n><b >Date Range:</b> {{dateFrom | date}} to {{dateTo | date}}</td>
            </tr>
            <tr>
                <td><b i18n>Please Note:</b> </td>
            </tr>
            <tr>
                <td i18n>Reports are run each night and will be ready by 8am EST timethe next morning. Otherwise your report
                    may take up to an extra 24 hours.</td>
            </tr>
            <tr>
                <td><b i18n>Report Title:</b> </td>
            </tr>
            <tr>
                <!-- Assumption: report title is not personal data; explicitly disable autocomplete to avoid SC 1.3.5 notice -->
                <td><input [(ngModel)]="newReportTitle" nz-input i18n-aria-label aria-label="Report title" autocomplete="off" autofocus/></td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): responsive modal width -->
<!-- WCAG 1.4.13 – enable dismissal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteConfirmationModal" nzTitle="Confirm Deletion" [nzWidth]="'90vw'"
    (nzOnOk)="deleteReports()" (nzOnCancel)="onDeleteModalCancel()" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – mark layout table as presentational -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n>Delete the selected report(s) from the system?</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): ensure rename dialog adapts to viewport -->
<!-- WCAG 1.4.13 – enable dismissal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="renameConfirmationModal" nzTitle="Rename" [nzWidth]="'90vw'"
    (nzOnOk)="rename()" (nzOnCancel)="onRenameModalCancel()" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – mark layout table as presentational -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n>Report Title:</td>
                <!-- Assumption: rename title field – not personal data, disable autocomplete -->
                <!-- WCAG 3.3.2 – added programmatic label so screen-reader users
                     understand the purpose of the input even when table cell
                     association is lost. Accessible name reuses the visible
                     text “Report Title:” found in the adjacent header cell. -->
                <td><input [(ngModel)]="renameNewTitle" nz-input i18n-aria-label aria-label="Report title" autocomplete="off" autofocus/></td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
