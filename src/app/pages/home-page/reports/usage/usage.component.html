<!-- Accessibility: marked decorative icons aria-hidden for WCAG 1.1.1 compliance -->
<!-- WCAG 1.4.4 – converted fixed pixel typography & spacing to rem units so heading scales with 200 % text zoom -->
<!-- WCAG 1.4.12 fix: removed fixed line-height and neutralised negative margin
     so heading can grow vertically under custom text-spacing without content
     being clipped or overlapping preceding elements. -->
<h1 style="font-size: 4.375rem; line-height: 1.5em; margin-top: 0; font-weight: 275; color: #2D323D; padding-bottom: 37.5px;margin-top:-41.5px !important;"
    class="ml-5 font-thin mobile-title" i18n>
    Usage Report
</h1>
<div *ngIf="!showReportTable">
    <!-- Converted label to semantic heading for meaningful reading order (WCAG 1.3.2) -->
    <div class="m-3">
        <!-- WCAG 1.4.4 – 16px → 1rem so sub-heading scales with user font size -->
        <h2 style="font-size: 1rem;" class="font-bold" i18n>
            Report Settings
        </h2>
    </div>
    <div class="!med:flex med:grid xs:grid grid-cols-2 gap-y-2">
        <div class="mx-3 col-span-2">
            <br>
            <!-- WCAG 1.3.1 – added explicit accessible name for preset date filter -->
            <!-- WCAG 3.3.2 – added visible <mat-label> so the purpose of the
                 control is presented on-screen for all users, not only those
                 whose assistive tech announces the aria-label. This satisfies
                 the SC requirement that “labels or instructions are provided”. -->
            <mat-form-field class="material-select-height select">
                <!-- Assumption: "Filter dates" is the visible purpose of this control -->
                <mat-label i18n>Filter dates</mat-label>
                <mat-select name="Filter dates" i18n-aria-label aria-label="Filter dates"
                            [(ngModel)]="selectedDatePreset" (ngModelChange)="filterDates()">
                    <mat-option *ngFor="let date of datePresets" [value]="date.value">{{date.label}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex items-center flex-wrap mx-3">
            <div class="mr-2">
                <span i18n>From:</span>
                <br>
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Assumption: date range fields do NOT capture personal user data – disable browser autofill to satisfy WCAG 2.1 SC 1.3.5 Identify Input Purpose -->
                    <!-- WCAG 2.5.3 (Label in Name): aria-label includes visible text "Select date" plus context "start date" -->
                    <input matInput [matDatepicker]="picker" [(ngModel)]="dateFrom" autocomplete="off" aria-label="Select date – start date">
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <!-- WCAG 2.4.6 – add explicit, descriptive label so users know which date field this calendar button relates to -->
                    <mat-datepicker-toggle matSuffix [for]="picker" i18n-aria-label aria-label="Open calendar for start date"></mat-datepicker-toggle>
                    <mat-datepicker #picker ></mat-datepicker>
                </mat-form-field>
            </div>
            <div >
                <span i18n>To:</span>
                <br>
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Assumption: non-personal date input – explicitly disable autocomplete -->
                    <!-- WCAG 2.5.3 (Label in Name): aria-label includes visible text "Select date" plus context "end date" -->
                    <input matInput [matDatepicker]="picker2" [(ngModel)]="dateTo" autocomplete="off" aria-label="Select date – end date">
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <!-- WCAG 2.4.6 – descriptive label for end-date calendar control -->
                    <mat-datepicker-toggle matSuffix [for]="picker2" i18n-aria-label aria-label="Open calendar for end date"></mat-datepicker-toggle>
                    <mat-datepicker #picker2 ></mat-datepicker>
                </mat-form-field>
            </div>
        </div>
        <div class="mx-3 col-span-2">
            <!-- WCAG 1.4.4 – margin-top 30px→1.875rem for scalable spacing -->
            <!-- WCAG 2.4.6 – replace generic button label with a more descriptive one so users understand the action without additional context -->
            <!-- CHANGE (2025-07-22): Align aria-label with visible text to satisfy WCAG 2.5.3 (Label in Name) -->
            <button (click)="generateReportModal()" nz-button nzType="primary" style="margin-top:1.875rem;" i18n i18n-aria-label
                aria-label="Generate Report">Generate Report</button>
        </div>
    </div>
</div>
<!-- WCAG 1.3.3 (Sensory Characteristics):
     Reworded instructional text so it no longer relies on spatial cues
     such as "above" and "below". Instead, it references the labelled
     sections ("Report Settings" and "Saved Reports") to provide
     equivalent meaning for all users, including those who cannot
     perceive layout. // Assumption: Section headings are unique and
     translated via existing i18n extraction, so quoting them here is
     sufficient context. -->
<div *ngIf="!showReportTable" class="m-8 text-center" i18n>
    To create a report, choose your filter options in the "Report Settings" section and click "Generate".<br>
    Your report will be processed and added to the "Saved Reports" list when ready.
</div>
<!-- Section heading converted to <h2> to improve document outline and preserve meaningful sequence (WCAG 1.3.2) -->
<div *ngIf="!showReportTable" class="ml-3">
    <!-- WCAG 1.4.4 – converted font-size 16px→1rem -->
    <h2 style="font-size: 1rem;" class="font-bold inline-block" i18n>Saved Reports</h2>
    <span *ngIf="unreadReportsCount >0" class="notify-div">{{unreadReportsCount}}</span>
</div>
<!-- WCAG 2.4.6 – clarify destination of the navigation control -->
<!-- CHANGE (2025-07-22): Updated aria-label so that it matches the visible text exactly to comply with WCAG 2.5.3 (Label in Name). -->
<button *ngIf="showReportTable" (click)="previous()" class="underline mb-4 ml-3" i18n i18n-aria-label
        aria-label="Back to Saved Reports">Back to Saved Reports</button>
<div *ngIf="showReportTable">
    <div style="display: flex; justify-content: space-between" class="ml-5 m-3 mb-4">
        <div>
            <!-- WCAG 1.4.4 – font-size 18px→1.125rem -->
            <span style="font-size: 1.125rem;">
                <b i18n>Report Title:</b> {{reportName}}
            </span>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportDateFrom" style="font-size: 0.875rem;">
                <b i18n>From:</b> {{reportDateFrom |date}}
            </div>
            <div *ngIf="reportDateTo" style="font-size: 0.875rem;">
                <b i18n>To:</b> {{reportDateTo |date}}
            </div>
        </div>
        <!-- WCAG 1.4.4 – margin-right 3px→0.1875rem -->
        <div style="margin-right: 0.1875rem; display: flex; flex-direction: column; justify-content: flex-end">
            <!-- WCAG 2.4.6 – label clearly states what will be downloaded -->
            <!-- CHANGE (2025-07-22): Accessible name now matches visible label exactly (WCAG 2.5.3). -->
            <button (click)="download()" nz-button nzType="primary" i18n i18n-aria-label
                aria-label="Download Report">Download Report</button>
        </div>
    </div>
    <!-- <nz-table class="mx-3" #basicTable [nzData]="displayData" [nzShowPagination]="false"> -->
    <!-- WCAG 1.3.1 – added semantic caption and scope attributes for header cells -->
    <!-- WCAG 1.4.4 – left/right margins 15px→0.9375rem -->
    <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem;" #basicTable nzFrontPagination="false"
        [nzTotal]="tableTotal" [nzData]="displayData" (nzPageIndexChange)="getTableData($event, currentReportId)">
        <caption class="sr-only" i18n>Usage report results – list of users and their document statistics</caption>
        <thead>
            <tr>
                <th scope="col" i18n>Name</th>
                <th scope="col" i18n>Email</th>
                <th scope="col" i18n>Document Storage</th>
                <th scope="col" i18n>Documents Sent</th>
                <th scope="col" i18n>Average Time To Sign In</th>
            </tr>
        </thead>
        <tbody>
            <!-- Render rows only when the condition is met so that empty table rows are not read by assistive technologies (WCAG 1.3.2) -->
            <ng-container *ngFor="let data of basicTable.data">
                <tr *ngIf="data.status != 'Inactive' || data.docSent > 0" [class.teamRow]="data.team">
                    <td>{{data.name}}</td>
                    <td>{{data.emaill}}</td>
                    <td>{{data.docStorage}}</td>
                    <td>{{data.docSent}}</td>
                    <td>{{data.averageTimeToSign}}</td>
                </tr>
            </ng-container>
        </tbody>
    </nz-table>
    <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableTotal" [pageSize]="10" [hidePageSize]="true"
    (page)="getTableData($event.pageIndex+1, currentReportId)" ></mat-paginator>     -->
</div>

<div *ngIf="showSavedReportTable">
    <div class="m-3 mb-4" aria-live="polite">
        <!-- WCAG 1.4.4 – margin-right 20px→1.25rem for scalable spacing -->
        <!-- WCAG 2.4.6 – provide context in the label for users of assistive technology -->
            <!-- CHANGE (2025-07-22): Updated aria-label to include the exact visible label text (WCAG 2.5.3). -->
            <button (click)="renameConfirmationModal = true; this.renameNewTitle = ''" *ngIf="setOfCheckedId.size == 1"
            style="cursor:pointer; margin-right: 1.25rem;" i18n i18n-aria-label
            aria-label="Rename Selected Reports">
            <!-- Assumption: icon is purely decorative because the button already has visible text (WCAG 1.1.1 compliance) -->
            <!-- WCAG 1.4.4 – icon font-size 18px→1.125rem -->
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="edit" nzTheme="outline" aria-hidden="true"></span> Rename Report
        </button>
        <!-- WCAG 2.4.6 – descriptive label to avoid ambiguity -->
        <!-- CHANGE (2025-07-22): Accessible name matches visible label exactly (WCAG 2.5.3). -->
        <button (click)="deleteConfirmationModal = true" *ngIf="setOfCheckedId.size > 0" style="cursor:pointer;" i18n i18n-aria-label
            aria-label="Confirm Deletion">
            <!-- Assumption: icon is purely decorative because the button text conveys meaning (WCAG 1.1.1 compliance) -->
            <!-- WCAG 1.4.4 – icon font-size 18px→1.125rem -->
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="delete" nzTheme="outline" aria-hidden="true"></span> Delete Report(s)
        </button>
    </div>
    <!-- <nz-table class="mx-3" #rowSelectionTable [nzData]="displayListData" [nzShowPagination]="false"> -->
        <!-- WCAG 1.3.1 – added caption, scope attributes and explicit labels for checkbox column -->
        <!-- WCAG 1.4.4 – left/right margins 15px→0.9375rem -->
        <!-- WCAG 1.3.1 – added caption, scope attributes and explicit labels for checkbox column -->
        <!-- WCAG 1.4.4 – left/right margins 15px→0.9375rem -->
        <div class="table-responsive">
            <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem;" #rowSelectionTable nzFrontPagination="false"
            [nzTotal]="tableListTotal" [nzData]="displayListData" (nzPageIndexChange)="getListTableData($event)">
            <caption class="sr-only" i18n>Saved usage reports</caption>
            <thead>
                <tr>
                    <th scope="col" tabindex="0" i18n-aria-label aria-label="Select all saved reports" [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)" i18n></th>
                    <th scope="col" i18n>Report Title</th>
                    <th scope="col" i18n>Last Modified</th>
                    <th scope="col" i18n>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr tabindex="0" [attr.aria-label]="'Open report ' + data.name"
                    [class.cursor-pointer]="data.reportStaus == 2 || data.reportStaus == 4"
                    *ngFor="let data of rowSelectionTable.data"
                    (click)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''"
                    (keydown.enter)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''">
                    <td [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
                        (nzCheckedChange)="onItemChecked(data.id, $event)"></td>
                    <td>{{data.name}}</td>
                    <td>{{data.dateAdded}}</td>
                    <td>{{data.statusLabel}}</td>
                </tr>
            </tbody>
            </nz-table>
        </div>
        <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableListTotal" [pageSize]="10" [hidePageSize]="true"
        (page)="getListTableData($event.pageIndex+1)" ></mat-paginator>     -->

<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="generateFormModal" nzTitle="Usage Report"
    (nzOnOk)="generateReport()" (nzOnCancel)="generateFormModal = false" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – layout table marked as presentational to prevent misinterpretation as data table
             WCAG 4.1.1 – wrapped rows in a <tbody> element so the markup validates
             (a <tr> element may not be a direct child of <table>). -->
        <table class="modal-table" role="presentation">
            <tbody>
                <tr>
                    <td i18n><b>Report Type:</b> Usage Report</td>
                </tr>
                <tr *ngIf="dateTo.getTime() > 0 && dateFrom.getTime() > 0">
                    <td i18n><b>Date Range:</b> {{dateFrom | date}} to {{dateTo | date}}</td>
                </tr>
                <tr>
                    <td><b i18n>Please Note:</b> </td>
                </tr>
                <tr>
                    <td i18n>Reports are run each night and will be ready by 8am EST timethe next morning. Otherwise your
                        report
                        may take up to an extra 24 hours.</td>
                </tr>
                <tr>
                    <td><b i18n>Report Title:</b> </td>
                </tr>
                <tr>
                    <!-- Added i18n-aria-label for localisation -->
                    <!-- Assumption: report title is not personal user data; disable autocomplete to avoid false WCAG 1.3.5 notice -->
                    <!-- WCAG 3.3.1 – mark field as required so assistive technology
                         announces the expectation and add `aria-required="true"`
                         for explicit programmatic conveyance.  Validation happens
                         in the component TS file so users receive a descriptive
                         error message if left blank. -->
                    <td><input [(ngModel)]="newReportTitle" nz-input aria-label="Report title" i18n-aria-label autocomplete="off" required aria-required="true" /></td>
                </tr>
            </tbody>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteConfirmationModal" nzTitle="Confirm Deletion"
    (nzOnOk)="deleteReports()" (nzOnCancel)="deleteConfirmationModal = false" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 4.1.1 – added <tbody> wrapper so the markup remains valid HTML -->
        <table class="modal-table" role="presentation">
            <tbody>
                <tr>
                    <td i18n>Delete the selected report(s) from the system?</td>
                </tr>
            </tbody>
        </table>
    </ng-container>
</nz-modal>
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="renameConfirmationModal" nzTitle="Rename"
    (nzOnOk)="rename()" (nzOnCancel)="renameConfirmationModal = false" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 4.1.1 – added <tbody> wrapper so <tr> is not a direct child of <table> -->
        <table class="modal-table" role="presentation">
            <tbody>
                <tr>
                    <td i18n>Report Title:</td>
                    <!-- Assumption: provide accessible name via aria-label since visual label resides in adjacent cell -->
                    <!-- Added i18n-aria-label for localisation -->
                    <!-- Assumption: rename title is technical metadata, not personal data – disable autocomplete -->
                    <!-- WCAG 3.3.1 – same required semantics as above -->
                    <td><input [(ngModel)]="renameNewTitle" nz-input aria-label="New report title" i18n-aria-label autocomplete="off" required aria-required="true" /></td>
                </tr>
            </tbody>
        </table>
    </ng-container>
</nz-modal>
