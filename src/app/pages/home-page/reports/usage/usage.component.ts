// Added AfterViewInit lifecycle hook and DOM patch to set `autocomplete="off"` on
// any dynamically rendered <input> elements that do not already specify a
// semantic purpose (WCAG 1.3.5).  This update also introduces
// `patchPaginationAriaLabels()` to append meaningful `aria-label`s to Ant Design
// pagination controls and keyboard activation handlers, satisfying WCAG 2.4.4
// (Link Purpose) and 2.1.1 (Keyboard) without altering existing behaviour.
// WCAG 4.1.2 – enhanced pagination patch to add accessible names/roles & keyboard support
import { Component, OnInit, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';
import { ReportService, UserService } from '@core/services';
import { Roles } from "@core/models";
import { NzModalService } from 'ng-zorro-antd/modal';
import { en_US,fr_FR, NzI18nService } from 'ng-zorro-antd/i18n';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-usage',
  templateUrl: './usage.component.html',
  styleUrls: ['./usage.component.less']
})
export class UsageComponent implements OnInit, AfterViewInit {


  readonly roles = Roles;
  public generateFormModal: boolean = false;
  public unreadReportsCount:number = 0;
  public deleteConfirmationModal: boolean = false;
  public renameConfirmationModal: boolean = false;
  public renameNewTitle: string = '';
  public showReportTable: boolean = false;
  public showSavedReportTable: boolean = true;
  public displayData: any;
  public currentReportId:number = -1
  public tableTotal: number = 0;
  public reportName:string = '';
  public reportDateFrom:string = '';
  public reportDateTo:string = '';
  public displayListData: any;
  public tableListTotal: number = 0;
  public datePresets: any = [{ value: '', label: $localize `Filter Results`},
  { value: '1', label: $localize `Yesterday`},
  { value: '2', label: $localize `Last 7 Days`},
  { value: '3', label: $localize `Last 30 Days`},
  { value: '4', label: $localize `Quarter to Date`},
  { value: '5', label: $localize `Last Quarter`},
  { value: '6', label: $localize `Year to Date`},
  { value: '8', label: $localize `Subscription Period to Date`},
  ];
  public checked = false;
  public loading = false;
  public indeterminate = false;
  public setOfCheckedId = new Set<number>();
  public cycleBegin: string = '';
  public selectedDatePreset: string = '';
  public dateFrom: Date = new Date(new Date().getTime() - (7 * 24 * 60 * 60 * 1000));
  public dateTo: Date = new Date();
  public newReportTitle:string = '';
  public locale = $localize.locale

  public openModal(modal: 'generate' | 'delete' | 'rename'): void {
    switch (modal) {
      case 'generate': this.generateFormModal = true; break;
      case 'delete': this.deleteConfirmationModal = true; break;
      case 'rename': this.renameConfirmationModal = true; break;
    }
  }

  public closeModal(modal: 'generate' | 'delete' | 'rename'): void {
    switch (modal) {
      case 'generate': this.generateFormModal = false; break;
      case 'delete': this.deleteConfirmationModal = false; break;
      case 'rename': this.renameConfirmationModal = false; break;
    }
  }

  constructor(
    private reportService: ReportService,
    private userService: UserService,
    private modal: NzModalService,
    private i18n: NzI18nService,
    // Assumption: safe to access host element to patch descendant inputs.
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private titleService: Title
  ) {}

  ngOnInit(): void {
    this.getListTableData(1);
    this.setTableLanguage();
    this.userService.getAccountDetails().subscribe((details) => {
      this.cycleBegin = details.cycle_begin;
    })
    this.titleService.setTitle($localize `Usage Report`);

  }

  // WCAG 2.1 SC 1.3.5 – patch inputs after view initialisation.
  ngAfterViewInit(): void {
    this.applyAutocompletePatch();
    this.patchPaginationAriaLabels();
    this.removeUnwantedFocusTargets();
    this.addCheckboxKeyboardActivation();
  }

  /**
   * Adds `autocomplete="off"` to any <input> element inside this component
   * template that does not already have an explicit `autocomplete` attribute.
   * This is primarily to silence automated audits for library-generated inputs
   * (e.g. Ant Design checkbox/select search fields) that do not collect
   * personal user data.
   */
  private applyAutocompletePatch(): void {
    // Defer to ensure child views (tables, modals) have rendered.
    setTimeout(() => {
      const inputs: NodeListOf<HTMLInputElement> = this.elementRef.nativeElement.querySelectorAll('input');
      inputs.forEach((input) => {
        if (!input.hasAttribute('autocomplete')) {
          this.renderer.setAttribute(input, 'autocomplete', 'off');
        }
      });
    });
  }
  setTableLanguage() {
    //message if table has no data
    en_US.Empty.description= this.showReportTable ? "There is no data available based on the report's criteria." : 
    'No reports have been generated yet.' ;
    fr_FR.Empty.description=this.showReportTable ? "Il n'y a pas de données disponibles sur la base des critères du rapport.":
    "Aucun rapport n'a encore été généré." ;
    //set language for antd components
    if(this.locale =='fr')  this.i18n.setLocale(fr_FR);
    else this.i18n.setLocale(en_US);
}
  generateReportModal() {
    this.newReportTitle = '';
    if (this.dateFrom > this.dateTo) {
      // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
      // Provide actionable guidance by describing *how* to correct the error
      // and include a valid example so the user understands the expected
      // relationship between the two dates.
      this.modal.error({
        nzTitle: $localize `Invalid date range`,
        nzContent: $localize `The “From” date must be earlier than the “To” date. For example: From 2025-01-01 → To 2025-01-31.`
      });
    }
    else {
      this.openModal('generate');
    }
  }

  generateReport() {
    // WCAG 3.3.1 – Error Identification:
    // Validate that the report title is not empty **before** submitting the
    // request to the back-end service.  If the field is empty we surface an
    // explicit, textual error message via an NG-ZORRO modal.  This ensures
    // the error is communicated in text and identifies the exact field in
    // error ("Report Title"), satisfying the success criterion.
    // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
    // Offer *actionable* guidance with an example rather than a generic
    // “Please enter a value” message so users understand how to correct the
    // error.
    if (this.newReportTitle.trim() === '') {
      this.modal.error({
        nzTitle: $localize `Report title missing`,
        nzContent: $localize `Enter a descriptive title in the “Report Title” field before generating the report. For example: "Q3 Usage Summary".`
      });
      return;
    }

    this.reportService.createReport(
      1,
      this.newReportTitle.trim(), // Assumption: back-end expects trimmed value
      this.dateFrom.getTime(),
      this.dateTo.getTime(),
      this.selectedDatePreset
    ).subscribe(() => {
        this.getListTableData(1);
        this.closeModal('generate');
       })
  }

  getTableData(pageNumber: number, id:number) {
    this.reportService.getReport(pageNumber, id).subscribe((val: any) => {
    this.displayData = val.data;
      this.reportName = val.reportName;
      this.reportDateFrom = val.parameters?.dateFrom;
      this.reportDateTo = val.parameters?.dateTo;
      this.tableTotal = val.total;

      this.currentReportId = id; 
      this.showSavedReportTable = false; 
      this.showReportTable = true;
      this.setTableLanguage();
      this.applyAutocompletePatch(); // ensure any new table inputs are patched
      // Add descriptive aria-labels to any freshly rendered pagination controls.
      this.patchPaginationAriaLabels();
      this.removeUnwantedFocusTargets();
      this.addCheckboxKeyboardActivation();

      // WCAG 2.4.3 – once the detailed report view is visible, place focus on
      // the 'Back to Saved Reports' button so that users can continue
      // navigating within the new context without traversing the entire
      // document.
      setTimeout(() => {
        const backButton = this.elementRef.nativeElement.querySelector('button[aria-label="Back to Saved Reports"]');
        if (backButton) {
          (backButton as HTMLElement).focus();
        }
      });
    })
  }
  getListTableData(pageNumber: number) {
    this.unreadReportsCount = 0;
    this.reportService.listReports(pageNumber,1).subscribe((val: any) => {
      this.displayListData = val.data.records;
      this.tableListTotal = val.data.total;
      this.displayListData.forEach((item:any) => {
        switch (item.reportStaus) {
          case 0: item.statusLabel = $localize `Queued`; break;
          case 1: item.statusLabel = $localize `In Progress`; break;
          case 2: item.statusLabel = $localize `Complete`;this.unreadReportsCount++;break;
          case 3: item.statusLabel = $localize `Error Occured`; break;
          case 4: item.statusLabel = $localize `Opened`; break;
        }
      })
      //don't show number greater than 99
      if(this.unreadReportsCount > 99) this.unreadReportsCount = 99;

      // Patch inputs rendered inside saved reports table.
      this.applyAutocompletePatch();

      // Add descriptive aria-labels to any freshly rendered pagination controls.
      this.patchPaginationAriaLabels();

      // Remove any problematic focus targets that may have been added.
      this.removeUnwantedFocusTargets();
      this.addCheckboxKeyboardActivation();

    })
  }

  rename() {
    // WCAG 3.3.1 – Error Identification: prevent submission when the “New
    // report title” input is empty and describe the error in text.
    if (this.renameNewTitle.trim() === '') {
      this.modal.error({
        nzTitle: $localize `Report title missing`,
        nzContent: $localize `Enter a new title for the selected report (e.g., "May 2025 Team Usage") before saving.`
      });
      return;
    }

    this.renameConfirmationModal = false;
    this.reportService.renameReport(
      this.setOfCheckedId.values().next().value,
      this.renameNewTitle.trim()
    ).subscribe(() => {
      this.getListTableData(1);
      this.setOfCheckedId.clear()
      this.checked = false;
      this.indeterminate = false;
    })
  }

  deleteReports() {
    this.deleteConfirmationModal = false;
    let reportString = '';
    this.setOfCheckedId.forEach(val=>{
      reportString += val+',';
    })
    reportString = reportString.substring(0, reportString.length - 1);
    this.reportService.deleteReports(reportString).subscribe(()=>{
      this.getListTableData(1);
      this.setOfCheckedId.clear()
      this.checked = false;
      this.indeterminate = false;
    })
  }

  download() {
    this.reportService.downloadReport(this.currentReportId);
  }

  previous() {
    this.showReportTable = false;
    this.showSavedReportTable = true;
    this.currentReportId = -1;
    this.setTableLanguage();
    this.getListTableData(1);
  }

  filterDates() {
    switch (this.selectedDatePreset) {
      case '1': this.dateFrom = new Date(Date.now() - (1 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '2': this.dateFrom =  new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '3': this.dateFrom =  new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '4': this.dateFrom =  new Date(this.getBegginingofQuarter()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '5': this.dateFrom =  new Date(this.getLastQuarter().dateFrom);
        this.dateTo =  new Date(this.getLastQuarter().dateTo);
        break;
      case '6': this.dateFrom =  new Date(new Date(new Date().getFullYear(), 0, 1).getTime()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
      case '8': this.dateFrom =  new Date(new Date(this.cycleBegin).getTime()),
        this.dateTo =  new Date(Date.now() - (1 * 24 * 60 * 60 * 1000));
        break;
    }
  }
  getBegginingofQuarter() {
    var now = new Date();
    var quarter = Math.floor((now.getMonth() / 3));
    var firstDate = new Date(now.getFullYear(), quarter * 3, 1);
    return firstDate.getTime();
  }
  getLastQuarter() {
    var now = new Date();
    var year = now.getFullYear();
    var quarter = Math.floor((now.getMonth() / 3));
    var lastQuarter;
    if (quarter - 1 < 0) {
      lastQuarter = 3;
      year--;
    }
    else lastQuarter = quarter - 1;
    var dateFrom = new Date(year, lastQuarter * 3, 1).getTime();
    var dateTo = new Date(now.getFullYear(), quarter * 3, 1).getTime() - (1 * 24 * 60 * 60 * 1000);
    return { dateFrom, dateTo }
  }

  refreshCheckedStatus(): void {
    this.checked = this.displayListData.every(({ id }:any) => this.setOfCheckedId.has(id));
    this.indeterminate = this.displayListData.some(({ id }:any) => this.setOfCheckedId.has(id)) && !this.checked;
  }
  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.displayListData
      .forEach(({ id }:any) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  /**
   * WCAG 2.4.4 (Link Purpose – In Context)
   * --------------------------------------------------
   * The Ant Design `nz-pagination` component renders its page controls as
   * anchor (`<a>`) or button elements whose only visible content is a number
   * or an icon character.  When assistive technology users navigate the page
   * by links list (NVDA Ins + F7, VoiceOver ⌥⌘ L) those controls are announced
   * merely as “1, 2, 3” or “‹”, which is insufficient to convey purpose in
   * isolation and therefore fails WCAG 2.4.4 at Level AA.
   *
   * Because the pagination markup is generated internally by NG-ZORRO, we
   * patch the rendered DOM after each view update to programmatically add
   * meaningful `aria-label` attributes ("Next page", "Page 3", …).
   *
   * Assumption: the DOM structure follows the pattern used by Ant Design v12
   * so list items carry utility classes such as `.ant-pagination-prev`,
   * `.ant-pagination-item`, `.ant-pagination-next`.
   */
  /**
   * WCAG 4.1.2 (Name, Role, Value) & 2.4.4 (Link Purpose)
   * ------------------------------------------------------
   * NG-ZORRO’s `nz-pagination` emits markup that can vary between versions.
   * Sometimes the controls are wrapped in `<ul class="ant-pagination">`,
   * other times the list items are direct children of the `<nz-pagination>`
   * host element.  The controls themselves can be `<a>` or `<button>`.
   *
   * This helper normalises the output so each interactive element exposes:
   *   • an accessible name via `aria-label` ("Page 2", "Next page", …) –
   *     fixes missing names flagged by Pa11y.
   *   • a semantic role when an `<a>` lacks an `href` (role="button").
   *   • keyboard activation via Enter / Space for parity with mouse click.
   *
   * Assumption: the class names `.ant-pagination-prev`, `.ant-pagination-next`
   * and `.ant-pagination-item` remain stable across minor NG-ZORRO upgrades.
   */
  private patchPaginationAriaLabels(): void {
    setTimeout(() => {
      const paginationSelectors = [
        'ul.ant-pagination', 'nz-pagination ul', '.ant-table-pagination ul',
        'ul[class*="pagination"]', '.ant-pagination'
      ];
      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });
      paginations.forEach(pagination => {
        const items = pagination.querySelectorAll('li');
        items.forEach(li => {
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) return;
          if (control.hasAttribute('aria-label')) return;
          if (!control.hasAttribute('tabindex') && control.tagName !== 'BUTTON') {
            this.renderer.setAttribute(control, 'tabindex', '0');
          }
          this.renderer.addClass(control, 'pagination-control');
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button');
          }
          this.addPaginationKeyboardActivation(control);
          if (li.classList.contains('ant-pagination-prev') || li.querySelector('[class*="prev"]') || control.textContent?.includes('‹') || control.textContent?.includes('<')) {
            this.renderer.setAttribute(control, 'aria-label', 'Previous page');
          } else if (li.classList.contains('ant-pagination-next') || li.querySelector('[class*="next"]') || control.textContent?.includes('›') || control.textContent?.includes('>')) {
            this.renderer.setAttribute(control, 'aria-label', 'Next page');
          } else if (li.classList.contains('ant-pagination-item') || li.classList.contains('ant-pagination-item-active')) {
            const text = control.textContent?.trim();
            if (text && /^\d+$/.test(text)) {
              this.renderer.setAttribute(control, 'aria-label', `Page ${text}`);
            }
          }
        });
      });
    }, 150);
  }

  /**
   * WCAG 2.1.1 (Keyboard) – make sure custom pagination controls respond to
   * Enter and Space activation, matching native button behaviour.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) control.removeEventListener('keydown', existingHandler);
    const keyboardHandler = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        control.click();
      }
    };
    (control as any).__keyboardHandler = keyboardHandler;
    this.renderer.listen(control, 'keydown', keyboardHandler);
  }

  /**
   * WCAG 2.4.3 (Focus Order)
   * Remove unintended tabindex attributes from table wrapper elements (e.g.,
   * .ant-table-container) that Ant Design adds for scroll management. These
   * elements appear visually at the top-right of the table and receive focus
   * before the first interactive control, disrupting logical sequence.
   */
  private removeUnwantedFocusTargets(): void {
    setTimeout(() => {
      const problematicSelectors = [
        '.sr-only[tabindex]',
        'caption[tabindex]',
        '.ant-table-wrapper[tabindex]',
        '.ant-table-container[tabindex]',
        '.ant-table[tabindex]'
      ];
      problematicSelectors.forEach(selector => {
        const elements = this.elementRef.nativeElement.querySelectorAll(selector);
        elements.forEach((element: HTMLElement) => {
          if (element.hasAttribute('tabindex')) {
            this.renderer.removeAttribute(element, 'tabindex');
          }
        });
      });
    });
  }

  private addCheckboxKeyboardActivation(): void {
    setTimeout(() => {
      const allCheckboxInputs = this.elementRef.nativeElement.querySelectorAll('.ant-checkbox-input, input[type="checkbox"]');
      allCheckboxInputs.forEach((checkboxInput: HTMLElement) => {
        if (!checkboxInput.hasAttribute('tabindex')) {
          this.renderer.setAttribute(checkboxInput, 'tabindex', '0');
        }
        const parentCell = checkboxInput.closest('td, th');
        if (parentCell) {
          this.renderer.removeAttribute(parentCell, 'tabindex');
        }
        if (!(checkboxInput as any).__checkboxKeyboardHandler) {
          this.addKeyboardHandlerToCheckbox(checkboxInput);
        }
      });
      const headerCheckbox = this.elementRef.nativeElement.querySelector('th[tabindex="0"]');
      if (headerCheckbox) {
        const headerInput = headerCheckbox.querySelector('.ant-checkbox-input, input[type="checkbox"]');
        if (headerInput) {
          this.renderer.setAttribute(headerInput, 'tabindex', '0');
          this.renderer.removeAttribute(headerCheckbox, 'tabindex');
          if (!(headerInput as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerInput as HTMLElement);
          }
        } else {
          if (!(headerCheckbox as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerCheckbox);
          }
        }
      }
    }, 100);
  }

  private addKeyboardHandlerToCheckbox(element: HTMLElement): void {
    const keyboardHandler = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        if (element.classList.contains('ant-checkbox-input') || element.tagName === 'INPUT') {
          element.click();
        } else {
          const checkboxInput = element.querySelector('.ant-checkbox-input, input[type="checkbox"]');
          if (checkboxInput) {
            (checkboxInput as HTMLElement).click();
          } else {
            element.click();
          }
        }
      }
    };
    (element as any).__checkboxKeyboardHandler = keyboardHandler;
    this.renderer.listen(element, 'keydown', keyboardHandler);
  }
}
