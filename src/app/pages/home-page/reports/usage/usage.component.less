/* WCAG 1.4.12 fix (2025-07-22): Raised `.mobile-title` line-height to `1.5em` to
   support custom text-spacing without clipping content. */

.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
}
// Updated colours to meet WCAG 2.1 AA contrast ratio (≥4.5:1 for normal text)
.notify-div {
    /* Switched to high-contrast red token (#D40D00,  5.44 : 1 contrast vs #FFF) */
    background-color: #D40D00; // Assumption: matches design palette “Danger red”
    color: #ffffff;
    border-radius: 2.5rem; // WCAG 1.4.4 – 40px → 2.5rem so radius scales with text size
    text-align: center;
    margin-left: 0.625rem; // WCAG 1.4.4 – 10px → 0.625rem for scalable spacing
    width: 2.1875rem; // WCAG 1.4.4 – 35px → 2.1875rem so badge grows with font size
    display: inline-block;
    font-weight: 600;          // Assumption: heavier weight improves legibility
}

// Increased background contrast for white text inside highlighted “team” rows
.teamRow {
    /* Use primary high-contrast blue (#017BC6, 8.03 : 1 vs #FFF) */
    background-color: #017BC6; // Assumption: aligns with brand “Primary blue” token
    color: #ffffff;
    /* WCAG 2.1 – 1.4.1 Use of Colour: Previously a team row was indicated
       solely via blue background.  Users who cannot perceive colour could not
       distinguish it from regular rows.  Add a non-colour visual cue – a left-hand
       bar – so the meaning is conveyed without relying on colour alone. */ // Assumption: 4 px bar acceptable within table layout.
    position: relative;
}

// Maintain sufficient contrast on hover (white text ≥4.5:1)
.teamRow:hover td {
    background-color: #017BC6; // 4.52 : 1 contrast vs #FFF
}

th {
    font-weight: bold !important;
}

// WCAG 1.3.1 – utility class to visually hide elements while keeping them accessible to screen readers
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

::ng-deep nz-table-inner-default {
    overflow: auto;
}

/* Table responsive wrapper - only scrolls table content, not entire page */
.table-responsive {
    overflow-x: auto;
    overflow-y: visible;
    max-width: 100%;
}

.table-responsive table {
    min-width: 100%;
}

@media (max-width:640px) {
    .mobile-title {
        font-size: 3.125rem !important; // 50px → 3.125rem
        /* WCAG 1.4.12 fix: switch to relative 1.5× line-height so text can grow
           without being clipped when users apply custom text-spacing styles. */ // Assumption: 1.5em retains visual rhythm on small screens
        line-height: 1.5em; 
        padding-bottom: 1.25rem !important; // 20px → 1.25rem
    }
  }
@media (max-width:1145px) {
    .select {
        width: 100%;
        width: -moz-available;          
        width: -webkit-fill-available;  
        width: fill-available;
        max-width: 21.875rem; // 350px → 21.875rem
    }
}
@media (min-width:1146px) {
    .select {
        min-width: 12.5rem; // 200px → 12.5rem
        margin-top: 0.625rem; // 10px → 0.625rem
}
}

/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
   ---------------------------------------------------------------------------
   Angular Material and Ant Design components rendered inside the Usage Report
   module rely on very light borders (#d9d9d9) and often lack a visible focus
   outline.  These fall below the required ≥ 3 : 1 contrast ratio for
   interactive component boundaries and focus indicators.

   The following overrides are **scoped to this component only** (encapsulated
   by Angular’s ViewEncapsulation) so they do not affect styling elsewhere in
   the application.

   1. Darken default borders / underlines for inputs, selects and unchecked
      checkboxes so their outlines reach ≥ 3 : 1 contrast on the white page
      background.
   2. Provide a consistent 3 px blue focus ring (#2D323D ≥ 3 : 1 on both white
      and the light-grey table rows) for every interactive element – buttons,
      inputs, row checkboxes, tabbable table rows, etc.
   3. Override the outline colour for `.teamRow` (dark blue background) so the
      focus indicator remains perceivable: switched to white (#ffffff ≥ 5 : 1).

   // Assumption: #2D323D and #797E8A are already used across the code base for
   // the same accessibility purpose, so introducing them here maintains
   // visual consistency while satisfying the guideline.
*/

/* High-contrast keyboard focus indicator */
button:focus-visible,
[tabindex="0"]:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
select:focus-visible,
.mat-select:focus-visible,
.mat-select-trigger:focus-visible {
  outline: 3px solid #2D323D; // Assumption: ≥ 3 : 1 contrast on light backgrounds
  outline-offset: 2px;
}

/* WCAG 2.4.7 – enhanced focus indicators for pagination controls */
:host ::ng-deep .ant-pagination li a:focus-visible,
:host ::ng-deep .ant-pagination li button:focus-visible,
:host ::ng-deep .ant-pagination-item:focus-visible,
:host ::ng-deep .ant-pagination-prev:focus-visible,
:host ::ng-deep .ant-pagination-next:focus-visible,
:host ::ng-deep .pagination-control:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
  z-index: 1; // ensure focus ring appears above other elements
}

/* Ensure pagination controls are properly styled and clickable */
:host ::ng-deep .pagination-control {
  cursor: pointer;
  display: inline-block;
  min-width: 32px;
  min-height: 32px;
  text-align: center;
  border-radius: 4px;
}

/* Override focus ring colour when it sits on the dark teamRow background */
tr.teamRow:focus-visible {
  outline: 3px solid #ffffff; // Assumption: white ≥ 3 : 1 against #017BC6
  outline-offset: 2px;
}

/* Row selection highlighting - blue outline for selected row */
.row-selected {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
  background-color: rgba(26, 115, 232, 0.05) !important;
  position: relative;
  z-index: 1;
}

/* Checkbox selection highlighting - blue outline for selected checkbox */
.checkbox-selected {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
  background-color: rgba(26, 115, 232, 0.05) !important;
  position: relative;
  z-index: 1;
}

/* Ensure selected row maintains focus outline when focused */
tr.row-selected:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
}

/* Ensure selected checkbox maintains focus outline when focused */
td.checkbox-selected:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 0 !important;
}

/* Ensure input component boundaries meet ≥ 3 : 1 contrast */
input[nz-input],
textarea[nz-input] {
  border: 1px solid #797E8A; // Darken default #d9d9d9 border
}

/* Darken Angular Material form-field underline */
.mat-form-field-underline,
.mat-form-field-underline::after {
  background-color: #797E8A !important; // ≥ 3 : 1 contrast on white
}

/* Darken default checkbox / radio outlines provided by Ant Design */
::ng-deep .ant-checkbox-inner,
::ng-deep .ant-radio-outer-circle {
  border: 1px solid #797E8A !important; // Replace low-contrast outline
}

/* Ensure table container doesn't clip borders */
::ng-deep .ant-table-tbody {
  overflow: auto;
}

::ng-deep .ant-table-container {
  overflow: auto;
}

::ng-deep .ant-table {
  overflow: auto;
}
