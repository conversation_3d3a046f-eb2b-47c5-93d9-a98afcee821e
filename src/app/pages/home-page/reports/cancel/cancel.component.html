<!-- Accessibility: added aria-hidden to decorative icons for WCAG 1.1.1 compliance -->
<!-- Update: changed conditional binding for aria-invalid so the attribute is
     only present when an actual error exists (WCAG 2.1 SC 3.3.3 Error
     Suggestion). -->
<!-- Orientation fix (WCAG 2.1 – 1.3.4): removed fixed pixel font-size/line-height in favour of
     responsive clamp() defined in CSS. -->
<!-- WCAG 1.4.4 – padding-bottom converted from 55px to 3.4375rem so heading spacing scales with text size -->
<h1 style="font-weight: 275; color: #2D323D; padding-bottom: 37.5px;margin-top:-41.5px !important;"
    class="-mt-6 ml-5 font-thin mobile-title" i18n>
    Cancellation Report
</h1>
<div *ngIf="!showReportTable">
    <div class="m-3">
        <!-- Promoted label to semantic heading so assistive technologies receive proper structure // Assumption: visual styling retained via inline style -->
        <!-- WCAG 1.4.4 – font-size 16px→1rem for scalable heading -->
        <h2 style="font-size: 1rem" class="font-bold" i18n>
            Report Settings
        </h2>

    </div>
    <div class="!med:flex med:grid xs:grid grid-cols-2 gap-y-2">
        <div class="mx-3 col-span-2">
            <br>
            <!-- WCAG 1.3.1 – added programmatic label for mat-select -->
            <mat-form-field class="material-select-height select">
                <mat-label i18n>Filter dates</mat-label>
                <mat-select name="Filter dates" [(ngModel)]="selectedDatePreset" (ngModelChange)="filterDates()"
                    i18n-aria-label aria-label="Filter dates">
                    <mat-option *ngFor="let date of datePresets" [value]="date.value">{{date.label}}</mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex items-center flex-wrap mx-3">
            <div class="mr-2">
                <span i18n>From:</span>
                <br>
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Added autocomplete="off" as this field does not collect personal data (WCAG 2.1 SC 1.3.5) -->
                    <!-- WCAG 2.5.3 Label in Name – ensure accessible name contains the
                         visible label text ("Select date"). Include additional context
                         ("start date") so the control purpose remains clear. -->
                    <!-- WCAG 2.1 – 3.3.3 Error Suggestion: expose the invalid state
                         to assistive technologies only when an error is actually
                         present, so the control is not incorrectly announced as
                         invalid during normal use. // Validation fix: comment moved
                         outside of the <input> element to avoid breaking the HTML
                         tag (Angular compiler error). -->
                    <input matInput [matDatepicker]="picker" [(ngModel)]="dateFrom"
                           (ngModelChange)="onDateChange()"
                           i18n-aria-label aria-label="Select date – start date"
                           autocomplete="off"
                           [attr.aria-invalid]="dateRangeError ? 'true' : null"
                           [attr.aria-describedby]="dateRangeError ? 'date-range-error' : null">
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <mat-datepicker-toggle matSuffix [for]="picker" i18n-aria-label aria-label="Open calendar start date"></mat-datepicker-toggle>
                    <mat-datepicker #picker ></mat-datepicker>
                </mat-form-field>
            </div>
            <div >
                <span i18n>To:</span>
                <br>
                <mat-form-field appearance="standard" >
                    <mat-label i18n>Select date</mat-label>
                    <!-- Added autocomplete="off" for non-personal date field (WCAG 2.1 SC 1.3.5) -->
                    <!-- WCAG 2.5.3 Label in Name – accessible name now includes the
                         visible label text ("Select date") followed by contextual
                         information ("end date"). -->
                    <!-- WCAG 2.1 – 3.3.3 Error Suggestion: same logic as the
                         start-date field above. // Validation fix: comment relocated
                         outside of the <input> element to keep markup valid. -->
                    <input matInput [matDatepicker]="picker2" [(ngModel)]="dateTo"
                           (ngModelChange)="onDateChange()"
                           i18n-aria-label aria-label="Select date – end date"
                           autocomplete="off"
                           [attr.aria-invalid]="dateRangeError ? 'true' : null"
                           [attr.aria-describedby]="dateRangeError ? 'date-range-error' : null">
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <mat-datepicker-toggle matSuffix [for]="picker2" i18n-aria-label aria-label="Open calendar end date"></mat-datepicker-toggle>
                    <mat-datepicker #picker2 ></mat-datepicker>
                </mat-form-field>
            </div>
        </div>
        <div class="mx-3 col-span-2">
            <!-- WCAG 1.4.4 – margin-top 30px→1.875rem so button position scales with text -->
            <!-- WCAG 2.4.6 – provide a descriptive label so screen-reader users
                 understand the button purpose out of context. -->
            <button (click)="generateReportModal()" nz-button nzType="primary"
                style="margin-top: 1.875rem" i18n>Generate Report</button>
        </div>

        <!-- Inline error message for invalid date range. It occupies a full row
             in the grid so the message is visually aligned with other form
             elements. The container is referenced via `aria-describedby` from
             both date inputs so screen-reader users are informed of the problem
             (WCAG 2.1 SC 3.3.1). -->
        <div class="mx-3 col-span-2 text-red-700" *ngIf="dateRangeError" id="date-range-error" role="alert" i18n>
            The "To" date must be later than the "From" date.
        </div>
        </div>
    </div>
    <!-- WCAG 1.3.3 (Sensory Characteristics):
         Revised instructions so they do not rely on spatial cues ("above", "below").
         Instead we reference component section names so the guidance is equally
         understandable to users who may not perceive visual layout. // Assumption: Using
         quoted section titles matches the visible labels and therefore requires no extra
         translation context beyond existing i18n extraction. -->
<div *ngIf="!showReportTable" class="m-8 text-center" i18n>
    To create a report, choose your filter options in the "Report Settings" section and click "Generate".<br>
    Your report will be processed and added to the "Saved Reports" list when ready.
</div>
<div *ngIf="!showReportTable"  class=" ml-3" i18n>
   <!-- Converted label to heading for clearer document outline (WCAG 1.3.2) // Assumption: inline-block keeps heading aligned horizontally with notification badge -->
   <!-- WCAG 1.4.4 – font-size 16px→1rem for scalable sub-heading -->
   <h2 style="font-size: 1rem" class="font-bold inline-block">Saved Reports</h2>   
   <!-- WCAG 1.4.1 – ensure meaning (unread-count badge) is not conveyed by colour alone by adding
        an explicit aria-label for assistive tech users. Added programmatic label also helps when
        forced-colours mode is active and the red background is lost. // Assumption: inline binding
        does not affect translation context. -->
   <span *ngIf="unreadReportsCount >0" class="notify-div"
         [attr.aria-label]="unreadReportsCount + ' unread reports'">{{unreadReportsCount}}</span>
</div>
<!-- WCAG 2.4.4 – clarified button purpose so it is understandable out of context -->
<button *ngIf="showReportTable" (click)="previous()" class="underline mb-4 ml-3"
        aria-label="Back to saved reports list" i18n>Back to Saved Reports</button>
<div *ngIf="showReportTable">
    <div style="display: flex; justify-content: space-between" class="ml-5 m-3 mb-4">
        <div >
            <!-- WCAG 1.4.4 – font-size 18px→1.125rem so text resizes -->
            <span style="font-size: 1.125rem" >
                <b i18n>Report Title:</b> {{reportName}}
            </span>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportDateFrom" style="font-size: 0.875rem">
                <b i18n>From:</b> {{reportDateFrom |date}}
            </div>
            <!-- WCAG 1.4.4 – font-size 14px→0.875rem -->
            <div *ngIf="reportDateTo" style="font-size: 0.875rem">
                <b i18n>To:</b> {{reportDateTo |date}}
            </div>
        </div>
        <!-- WCAG 1.4.4 – margin-right 3px→0.1875rem for scalable spacing -->
        <div style="margin-right: 0.1875rem; display: flex; flex-direction: column; justify-content: flex-end">
            <!-- WCAG 2.4.6 – clarify action context for assistive technologies. -->
            <button (click)="download()" nz-button nzType="primary" i18n
                i18n-aria-label aria-label="Download cancellation report">Download</button>
        </div>
    </div>
    <!-- Wrapped in single-axis scroll container so page never requires horizontal
         scrolling on small viewports (WCAG 1.4.10 Reflow). -->
    <div class="table-responsive" role="region" aria-labelledby="cancellationReportResultsCaption">
        <!-- <nz-table [nzShowPagination]="false" class="mx-3" #basicTable  [nzData]="displayData"> -->
        <!-- WCAG 1.4.4 – margins 15px→0.9375rem so table paddings adapt -->
        <!-- WCAG 2.1 – 4.1.2 Name, Role, Value: forward our custom pagination
             template so that the automatically generated pagination controls
             expose an accessible name for assistive technologies. The default
             Ant Design markup renders <button> elements for «prev/next» without
             a label which fails the rule. -->
        <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem; " #basicTable nzFrontPagination="false"
        [nzTotal]="tableTotal" [nzData]="displayData" (nzPageIndexChange)="getTableData($event, currentReportId)">
            <caption id="cancellationReportResultsCaption" class="sr-only" i18n>Cancellation report results – list of cancelled documents and their details</caption>
        <thead>
            <tr>
                <th scope="col" i18n>Document Name</th>
                <th scope="col" i18n>Sender</th>
                <th scope="col" i18n>Recipients</th>
                <th scope="col" i18n>Last Modification Time</th>
                <th scope="col" i18n>Reason</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTable.data">
                <td>{{data.documentTitle}}</td>
                <td>{{data.sender}}</td>
                <td><span *ngFor="let rec of data.recipient">{{rec}}<br></span></td>
                <td>{{data.lastModifiedTime}}</td>
                <td>{{data.cancellationReason}}</td>
            </tr>
        </tbody>
        </nz-table>
    </div>
    <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableTotal" [pageSize]="10" [hidePageSize]="true"
    (page)="getTableData($event.pageIndex+1, currentReportId)" ></mat-paginator>     -->
</div>

<div *ngIf="showSavedReportTable">
    <div class="m-3 mb-4"aria-live="polite">
        <!-- WCAG 1.4.4 – margin-right 20px→1.25rem; icon font-size 18px→1.125rem -->
        <!-- WCAG 2.4.6 – descriptive labels for contextual actions on saved
             reports. -->
        <button (click)="openModal('rename'); this.renameNewTitle = ''" *ngIf="setOfCheckedId.size == 1"
            style="cursor:pointer; margin-right: 1.25rem;" i18n
            i18n-aria-label aria-label="Rename selected reports">
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="edit" nzTheme="outline" aria-hidden="true"></span > Rename Report
        </button>
        <!-- WCAG 1.4.4 – icon font-size 18px→1.125rem -->
        <button (click)="openModal('delete')" *ngIf="setOfCheckedId.size > 0" style="cursor:pointer;" i18n
            i18n-aria-label aria-label="Delete selected reports">
            <span style="vertical-align: middle; font-size: 1.125rem;" nz-icon nzType="delete" nzTheme="outline" aria-hidden="true"></span> Delete Report(s)
        </button>
    </div>
    <!-- Single-axis scroll wrapper for saved reports table (WCAG 1.4.10). -->
    <div class="table-responsive" role="region" aria-labelledby="savedCancellationReportsCaption">
        <!-- <nz-table class="mx-3" #rowSelectionTable [nzData]="displayListData" [nzShowPagination]="false"> -->
        <!-- WCAG 1.4.4 – margins 15px→0.9375rem -->
        <nz-table style="margin-left: 0.9375rem; margin-right: 0.9375rem; " #rowSelectionTable nzFrontPagination="false"
        [nzTotal]="tableListTotal" [nzData]="displayListData" (nzPageIndexChange)="getListTableData($event)">
            <caption id="savedCancellationReportsCaption" class="sr-only" i18n>Saved cancellation reports – list of generated reports and their status</caption>
        <thead>
            <tr>
                <th scope="col" tabindex="0" aria-label="Select all saved reports"
                    [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)">
                </th>
                <th scope="col" i18n>Report Title</th>
                <th scope="col" i18n>Last Modified</th>
                <th scope="col" i18n>Status</th>
            </tr>
        </thead>
        <tbody>
            <tr tabindex="0" [attr.aria-label]="'Open report ' + data.name"
                [class.cursor-pointer]="data.reportStaus == 2 || data.reportStaus == 4" *ngFor="let data of rowSelectionTable.data"
             (click)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''"
             (keydown.enter)="(data.reportStaus == 2 || data.reportStaus == 4) ? getTableData(1,data.id): ''">
                <td [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
                    (nzCheckedChange)="onItemChecked(data.id, $event)"></td>
                <td>{{data.name}}</td>
                <td>{{data.dateAdded}}</td>
                <td>{{data.statusLabel}}</td>
            </tr>
        </tbody>
        </nz-table>
    </div>
    <!-- <mat-paginator [showFirstLastButtons]="true" class="mx-3" [length]="tableListTotal" [pageSize]="10" [hidePageSize]="true"
    (page)="getListTableData($event.pageIndex+1)" ></mat-paginator>     -->
</div>

<!-- Orientation fix (WCAG 2.1 – 1.3.4): set modal width to 90vw so dialog fits within viewport in both portrait and landscape orientations. -->
<!-- WCAG 1.4.13 – enable dismissal of dialog via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="generateFormModal" nzTitle="Cancellation Report"
    [nzWidth]="'90vw'" (nzOnOk)="generateReport()" (nzOnCancel)="closeModal('generate')"
    [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – layout table marked as presentational to avoid confusion with data tables -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n><b>Report Type:</b> Cancellation Report</td>
            </tr>
            <tr *ngIf="dateTo.getTime() > 0 && dateFrom.getTime() > 0">
                <td i18n><b >Date Range:</b> {{dateFrom | date}} to {{dateTo | date}}</td>
            </tr>
            <tr>
                <td><b i18n>Please Note:</b> </td>
            </tr>
            <tr>
                <td i18n>Reports are run each night and will be ready by 8am EST timethe next morning. Otherwise your report
                    may take up to an extra 24 hours.</td>
            </tr>
            <tr>
                <td><b i18n>Report Title:</b> </td>
            </tr>
            <tr>
                <!-- Added autocomplete="off" – title is not personal user data (WCAG 2.1 SC 1.3.5) -->
                <td><input [(ngModel)]="newReportTitle" nz-input aria-label="Report title" autocomplete="off" autofocus/></td>
            </tr>
        </table>
    </ng-container>
</nz-modal>


<!-- Orientation fix (WCAG 2.1 – 1.3.4): responsive width for confirmation dialog. -->
<!-- WCAG 1.4.13 – make confirmation dialog dismissible via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteConfirmationModal" nzTitle="Confirm Deletion"
    [nzWidth]="'90vw'" (nzOnOk)="deleteReports()" (nzOnCancel)="closeModal('delete')"
    [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – layout table marked as presentational -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n>Delete the selected report(s) from the system?</td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): make rename dialog responsive. -->
<!-- WCAG 1.4.13 – make rename dialog dismissible via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="renameConfirmationModal" nzTitle="Rename"
    [nzWidth]="'90vw'" (nzOnOk)="rename()" (nzOnCancel)="closeModal('rename')"
    [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
        <!-- WCAG 1.3.1 – layout table marked as presentational -->
        <table class="modal-table" role="presentation">
            <tr>
                <td i18n>Report Title:</td>
                <!-- Added autocomplete="off" – title is not personal user data (WCAG 2.1 SC 1.3.5) -->
                <td><input [(ngModel)]="renameNewTitle" nz-input aria-label="New report title" autocomplete="off" autofocus/></td>
            </tr>
        </table>
    </ng-container>
</nz-modal>
