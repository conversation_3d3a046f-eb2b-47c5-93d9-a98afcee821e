
// Updated for WCAG 1.4.12: increased `.mobile-title` line-height and made `.notify-div` width flexible
.modal-table {
    width: 100%;
    border-collapse:separate; 
    border-spacing: 0 1em;
}
// Updated colour for WCAG 2.1 AA (1.4.3 Contrast) – replace low-contrast red
// (#ff4d4f, 3.26:1 vs white text) with high-contrast token #D40D00 (5.44:1)
// while preserving existing visual intent of a "danger/alert" badge.
.notify-div {
    background-color: #D40D00; // Assumption: design system danger red token
    color: #ffffff;
    border-radius: 2.5rem; // WCAG 1.4.4 – convert 40px to rem so badge corner radius scales consistently
    text-align: center;
    margin-left: 0.625rem; // WCAG 1.4.4 – convert fixed spacing to rem so badge shifts with text resize
    /* WCAG 1.4.12 fix: allow badge to grow with increased letter/word spacing */
    // Assumption: badge value rarely exceeds 3 characters – moved to its own line to avoid Less parser error
    min-width: 2.1875rem; // preserve minimum visual size from original design (≈35 px)
    width: auto; // let content expand horizontally instead of clipping
    display: inline-block;
    /* WCAG 1.4.1 – add non-colour visual cue (outline) so the badge remains
       perceivable when colour information is unavailable (e.g. greyscale or
       high-contrast modes).
       Assumption: 2 px outline is acceptable within existing design constraints. */
    font-weight: 600;          // Assumption: heavier weight improves legibility
}
th {
    font-weight: bold !important;
}

::ng-deep nz-table-inner-default {
    overflow: auto;
}

/* Orientation fix (WCAG 2.1 – 1.3.4):
   Converted fixed pixel font-sizes/line-heights to responsive clamp() values so
   the heading scales fluidly in both portrait and landscape orientations
   without causing horizontal scroll or clipping. */

.mobile-title {
    /* Assumption: original design intended ~70 px on large desktop and ~28 px on
       the smallest screens (similar to other pages). */
    font-size: clamp(1.75rem, 6vw, 4.375rem) !important; // 28 px–70 px
    /* WCAG 1.4.12 fix: increase default line-height to ≥1.5× and remove !important to enable user override */ // Assumption: 1.5em preserves intended visual rhythm
    line-height: 1.5em;
}

/* Previous media-query-specific font rules removed as they are now redundant
   thanks to fluid clamp() sizing. */
@media (max-width:1145px) {
    .select {
        width: 100%;
        width: -moz-available;          
        width: -webkit-fill-available;  
  width: fill-available;
  max-width: 21.875rem; // WCAG 1.4.4 – 350px→21.875rem so select scales with text
}
}
@media (min-width:1146px) {
    .select {
    min-width: 12.5rem; // WCAG 1.4.4 – 200px→12.5rem
  margin-top: 0.625rem; // WCAG 1.4.4 – 10px→0.625rem
}
}

// WCAG 1.3.1 – visually-hidden utility class for screen-reader only content (used to hide captions without impacting layout)
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

/* WCAG 1.4.10 – ensure data tables can scroll horizontally within their own
   region at small viewports (e.g. 320 px) so the overall page never requires
   two-direction scrolling. */
.table-responsive { // Assumption: reused utility class pattern from other components
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* WCAG 1.4.12 fix: allow Material select controls used in this component to
   expand vertically when users apply custom text-spacing (e.g. line-height ≥
   1.5×, word/letter spacing). The global stylesheet forces a rigid 30 px
   height via `.material-select-height`, which can clip the label text and
   make the control inaccessible. We reset the height to `auto` and keep the
   original measurement as a `min-height` so the design baseline is preserved
   while still accommodating larger user settings. */ // Assumption: root
   // Validation fix: make explanatory note a Less comment so it doesn't break parsing
   // font-size ≈ 16 px, therefore 1.875 rem ≈ 30 px.
.material-select-height {
  height: auto !important;   // permit vertical growth
  min-height: 1.875rem;      // maintain previous visual minimum (≈30 px)
  overflow: visible;         // prevent clipping of expanded text
  white-space: normal;       // allow label wrapping when spacing increases
  line-height: normal;       // honour user-defined line-height
}

// Added high-contrast borders and keyboard focus styles for ≥ 3 : 1 non-text contrast (WCAG 1.4.11)

// 1. Visible keyboard focus ring for all interactive controls inside the
//    component (buttons, links, table rows made focusable via tabindex,
//    date-picker toggles, etc.)
button:focus-visible,
[tabindex="0"]:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
select:focus-visible,
.ant-checkbox-inner:focus-visible,
mat-select:focus-visible {
    outline: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 against light backgrounds
    outline-offset: 2px;
}

// 2. Improve default Ant Design checkbox outline contrast (default #d9d9d9 <3:1)
::ng-deep .ant-checkbox-inner {
    border: 1px solid #797E8A; // Assumption: ≥ 3 : 1 contrast on white
}

// 3. Darken input/textarea borders (ng-zorro <input nz-input>) so control
//    boundary is perceivable.
input[nz-input],
textarea[nz-input] {
    border: 1px solid #797E8A;
}

// 4. Increase contrast of Angular Material form-field underline (opacity .42)
::ng-deep .mat-form-field-underline,
::ng-deep .mat-form-field-underline::after {
    background-color: #797E8A !important;
}

// 5. Force calendar icon inside mat-datepicker toggle to inherit current text
//    colour so developers only need to set foreground once.
::ng-deep .mat-datepicker-toggle-default-icon {
    color: currentColor;
}

// 6. Enable visible focus on clickable <tr> elements used as pseudo-links in the
//    "Saved Reports" table.
tr[tabindex="0"]:focus-visible,
.cursor-pointer:focus-visible {
    outline: 3px solid #2D323D;
    outline-offset: 2px;
}

// Row selection highlighting - blue outline for selected row
.row-selected {
    outline: 3px solid #2D323D !important;
    outline-offset: 0 !important;
    background-color: rgba(26, 115, 232, 0.05) !important;
    position: relative;
    z-index: 1;
}

// Checkbox selection highlighting - blue outline for selected checkbox
.checkbox-selected {
    outline: 3px solid #2D323D !important;
    outline-offset: 0 !important;
    background-color: rgba(26, 115, 232, 0.05) !important;
    position: relative;
    z-index: 1;
}

// Ensure selected row maintains focus outline when focused
tr.row-selected:focus-visible {
    outline: 3px solid #2D323D !important;
    outline-offset: 0 !important;
}

// Ensure selected checkbox maintains focus outline when focused
td.checkbox-selected:focus-visible {
    outline: 3px solid #2D323D !important;
    outline-offset: 0 !important;
}

// 7. WCAG 2.4.7 Focus Visible – High-contrast focus styles for pagination controls
//    to ensure keyboard navigation is clearly visible


// 8. Enhanced focus styles for NG-ZORRO pagination controls specifically
::ng-deep .ant-pagination {
    li {
        &:focus-within,
        button:focus-visible,
        a:focus-visible {
            outline: 3px solid #2D323D;
            outline-offset: 2px;
        }
    }
}

// 9. Focus styles for custom pagination template controls  
::ng-deep .ant-pagination-item-link:focus-visible {
    outline: 3px solid #2D323D;
    outline-offset: 2px;
}

// 10. Enhanced checkbox focus visibility for table header and row checkboxes
::ng-deep .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
    outline: 3px solid #2D323D;
    outline-offset: 2px;
    border-color: #2D323D;
}

// Thicker blue borders for focus highlighting
tr:focus-visible {
  box-shadow: 0 0 0 1px #2D323D !important;
  position: relative;
  z-index: 1;
}

td:focus-visible {
  box-shadow: 0 0 0 1px #2D323D !important;
  position: relative;
  z-index: 1;
}

// Ensure table container doesn't clip borders
::ng-deep .ant-table-tbody {
  overflow: visible !important;
}

::ng-deep .ant-table-container {
  overflow: visible !important;
}

::ng-deep .ant-table {
  overflow: visible !important;
}
