import { Component, OnInit } from '@angular/core';
import { PaymentService } from '@core/services/payment.service';
import { environment } from "@environment/environment";
import { Title } from '@angular/platform-browser';

declare let StripeCheckout:any;

@Component({
  selector: 'app-payment-method',
  templateUrl: './payment-method.component.html',
  styleUrls: ['./payment-method.component.less']
})
export class PaymentMethodComponent implements OnInit {

  public stripeHandler:any;
  public locale = $localize.locale
  public StripeCheckout: any;
  public isSpinning:boolean = false;
  public cards:any;
  public deleteCardModal:boolean = false;
  public selectedCard:number = -1;

  constructor(
    private paymentService: PaymentService,
    private titleService: Title,
  ) { }

  ngOnInit(): void {
    this.titleService.setTitle("Payment Methods");
    console.log(environment.stripeKey)
    this.stripeHandler = StripeCheckout.configure({
      key: environment.stripeKey,
      locale: this.locale,
      token: (token:any) => {
        this.isSpinning = true;
        this.paymentService.setPayment(token).subscribe(()=>{
          this.getPaymentSources();
          this.isSpinning = false;
        })
      }
    });
    this.getPaymentSources();
  }

  getPaymentSources() {
    this.isSpinning = true;
    this.paymentService.getPaymentSources().subscribe((result)=>{
      this.cards = result.paymentSources.data;
      this.isSpinning = false;
    })
  }

  addPaymentSource() {
    this.stripeHandler.open({
      name: 'SIGNORITY INC.',
      description: $localize `Add Payment Source`,
      panelLabel:  $localize `Add`,
      email: JSON.parse(localStorage.getItem("user")|| '0' ).email,
      allowRememberMe: false,
      zipCode: true,
      billingAddress: true
    });
  }

  deletePaymentMethod(card:number) {
    this.isSpinning = true;
    this.paymentService.deletePayment(card).subscribe(()=>{
      this.deleteCardModal = false;
      this.selectedCard = -1;
      this.getPaymentSources();
      this.isSpinning = false;

    })
  }

}
