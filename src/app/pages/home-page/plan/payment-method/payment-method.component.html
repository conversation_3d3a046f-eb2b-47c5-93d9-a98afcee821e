
<div class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    Payment Method
</div>
<nz-spin [nzSpinning]="isSpinning">
    <div class="card-row"  *ngFor="let card of cards;index as i" [class]="i == 0 ? 'top-border' : ''">
        <span>
            <span class="font-bold mr-2">{{card.brand}}</span>
            <span class="mr-1">....&nbsp;{{card.last4}}</span>
            <span>{{card.expMonth}}/{{card.expYear}}</span>
        </span>
        <button class="delete-card" *ngIf="cards.length > 1" nz-button nzType="default" nzDanger
            (click)="deleteCardModal=true; selectedCard = card.id" i18n>Delete</button>
    </div>
    <div class="flex justify-center mt-3">
        <button nz-button nzType="primary" (click)="addPaymentSource()" i18n>Add New Payment Method</button>
    </div>
</nz-spin>

<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteCardModal" nzTitle="Confirm" (nzOnOk)="deletePaymentMethod(selectedCard)"
(nzOnCancel)="deleteCardModal = false; selectedCard = -1">
<ng-container *nzModalContent>
    <table class="modal-table">
        <tr>
            <td i18n>Are you sure you would like to delete this payment method?</td>
        </tr>
    </table>
</ng-container>
</nz-modal>