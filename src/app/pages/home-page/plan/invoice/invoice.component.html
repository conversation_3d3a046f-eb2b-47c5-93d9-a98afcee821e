<h1 class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    My Invoices
</h1>

<nz-spin [nzSpinning]="isSpinning">
    <div *ngIf="!viewInvoice">
        <div class="m-3 inline-block" >
            <span class="top-menu">
                <button  [class.disabled]="setOfCheckedId.size < 1" [disabled]="setOfCheckedId.size < 1" 
                (click)="downloadInvoices()">
                    <span class="top-icon" nz-icon nzType="download" nzTheme="outline"></span>
                    <span class="top-text" i18n>Download</span>
                </button>
            </span>
        </div>
        <nz-table style="margin-left: 15px; margin-right: 15px; " #basicTable [nzShowTotal]="totalTemplate"
        [nzTotal]="tableTotal" [nzData]="invoices" [nzPageSize]="pageSize" [nzPageIndex]="pageIndex" (nzPageIndexChange)="changePageIndex($event)">
            <thead>
                <tr>
                    <th [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)">
                    </th>
                    <th i18n>Invoice #</th>
                    <th i18n>Invoice Date</th>
                    <th i18n>Due Date</th>
                    <th i18n>Total</th>
                    <th i18n>Currency</th>
                    <th i18n>Status</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let data of basicTable.data">
                    <td [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
                    (nzCheckedChange)="onItemChecked(data.id, $event)"></td>
                    <td>{{data.invoicenum}}</td>
                    <td>{{data.invoicedate.time | date:"yyyy-MM-dd"}}</td>
                    <td>{{data.duedate.time | date:"yyyy-MM-dd"}}</td>
                    <td>{{data.total}}</td>
                    <td>{{data.currency.code}}</td>
                    <td>{{data.status}}</td>
                    <td class="flex justify-center">
                        <button nz-button nzType="primary" [routerLink]="'/invoice/view/'+data.id" class="mr-1" i18n>View Invoice</button>
                        <button *ngIf="data.status == 'Unpaid' || data.status == 'Non payé'" 
                        (click)="payNow(data)" nz-button i18n>Pay Now</button>
                    </td>
                </tr>
            </tbody>
        </nz-table>
        <ng-template #totalTemplate let-total i18n>{{((pageIndex-1) * pageSize)+1}} - {{math.min((pageIndex-1) * pageSize + pageSize, total )}} of {{ total }}</ng-template>

    </div>
    <div *ngIf="viewInvoice">
        <div class="invoiceWrapper">
            <table class="w-full mb-5">
                <tr>
                    <td class="w-1/2">
                        <img i18n-alt alt="Signority logo" src="assets/images/BasicSiteCompanyLogoRegular.png?version=3" class="logo">
                    </td>
                    <td class="w-1/2 text-center">
                        <div *ngIf="currentInvoice.status=='Paid'" >
                            <div class="paid" i18n>
                                Paid
                            </div>
                            <div>
                                ( {{currentInvoice.datepaid }} )
                            </div>
                        </div>
                        <div *ngIf="currentInvoice.status=='Unpaid'" >
                            <div class="unpaid" i18n>
                                Unpaid
                            </div>
                            <button (click)="payNow(currentInvoice)" nz-button nzType="primary" i18n>Pay Now</button>
                        </div>
                    </td>
                </tr>
                <tr class="invoiceBox">
                    <td class="w-1/2 ">
                        <div class="addressBox">
                            <strong i18n>
                                Invoiced To
                            </strong>
                            <div *ngIf="user.companyname">{{user.companyname}}</div>
                            <div>
                                <span *ngIf="user.firstname">{{user.firstname}} </span>
                                <span *ngIf="user.lastname">{{user.lastname}}</span>
                            </div>
                            <div>
                                <span *ngIf="user.address1">{{user.address1}},</span>
                                <span *ngIf="user.address2">{{user.address2}}, </span>
                            </div>
                            <div>
                                <span *ngIf="user.city">{{user.city}},</span>
                                <span *ngIf="user.state">{{user.state}},</span>
                                <span *ngIf="user.postcode">{{user.postcode}},</span>
                            </div>
                            <div *ngIf="user.country">{{user.country}}</div>
                            <div *ngIf="user.email">{{user.email}}</div>


                        </div>
                    </td>
                    <td class="w-1/2">
                        <div class="addressBox">
                            <strong i18n>
                                Pay To
                            </strong>
                            <div [innerHTML]="currentInvoicePayTo">
                            </div>

                        </div>
                    </td> 
                </tr>
            </table>

            <div>
                <strong>B/N:843000514R001</strong>
            </div>
            <div>
                <strong i18n>Invoice # {{currentInvoice.invoicenum}}</strong>
            </div>
            <div i18n>
                Invoice Date: {{displayInvoiceDate}}
            </div>
            <div class="mb-5" i18n>
                Due Date: {{displayDueDate}}
            </div>
            <table class="w-full mb-5 amountTable">
                <tr class="amountTableGreyRow text-center">
                    <td class="width-70">
                        <strong i18n>Description</strong>
                    </td>
                    <td>
                        <strong i18n>Amount</strong>
                    </td>
                </tr>
                <tr *ngFor="let item of currentInvoice.invoiceitems" class="text-left">
                    <td>
                        {{invoiceLanguage(item.description)}}
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(item.amount, currentInvoice.currency)}}
                    </td>
                </tr>
                <tr class=" amountTableGreyRow">
                    <td class="text-right">
                        <strong i18n>Sub Total: </strong>
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(currentInvoice.subtotal, currentInvoice.currency)}}
                    </td>
                </tr>
                <tr *ngIf="currentInvoice.amountoff>0 || currentInvoice.percentoff>0" class="amountTableGreyRow">
                    <td class="text-right">
                        <strong i18n>Coupon: </strong>
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(currentInvoice.amountoff/100 + planAmount*currentInvoice.percentoff/100, currentInvoice.currency)}}
                    </td>
                </tr>
                <tr class=" amountTableGreyRow">
                    <td class="text-right">
                        <strong i18n>Tax ({{currentInvoice.taxrate}}%): </strong>
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(currentInvoice.tax, currentInvoice.currency)}}
                    </td>
                </tr>
                <tr class=" amountTableGreyRow">
                    <td class="text-right">
                        <strong i18n>Provincial({{currentInvoice.taxrate2}}%): </strong>
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(currentInvoice.tax2, currentInvoice.currency)}}
                    </td>
                </tr>
                <tr class=" amountTableGreyRow">
                    <td class="text-right">
                        <strong i18n>Total: </strong>
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(currentInvoice.total, currentInvoice.currency)}}
                    </td>
                </tr>
            </table>
            <div class="mb-5" >
               <strong i18n>Transactions</strong>
            </div>
            <table class="mb-5 amountTable">
                <tr class="amountTableGreyRow text-center">
                    <td class="width-15">
                        <strong i18n>Transaction Date</strong>
                    </td>
                    <td class="width-40">
                        <strong>Transaction ID</strong>
                    </td>
                    <td class="width-15">
                        <strong i18n>Type</strong>
                    </td>
                    <td class="width-15">
                        <strong i18n>Card Info</strong>
                    </td>
                    <td class="width-15">
                        <strong i18n>Amount</strong>
                    </td>
                </tr>
                <tr *ngFor="let transaction of currentInvoice.transactions" class="text-left">
                    <td class="font15">
                        {{transaction.date.time - timeOffset | date:"yyyy-MM-dd"}}
                    </td>
                    <td class="xs:break-all med:break-all">
                        {{transaction.transid}}
                    </td>
                    <td>
                        {{transaction.paymenttype}}
                    </td>
                    <td>
                        {{transaction.paymentmaskedinfo}}
                    </td>
                    <td class="text-center">
                        {{getCurrencyNumber(transaction.amountin, currentInvoice.currency)}}
                    </td>

                </tr>
                <tr *ngIf="!currentInvoice.transactions || currentInvoice.transactions.length == 0">
                    <td colspan="5" class="text-center">
                        <strong i18n>No transactions found</strong>
                </tr>
            </table>
            <div>
                <button (click)="downloadInvoice(currentInvoice.id)" nz-button nzType="primary" i18n>Download</button>
             </div>

        </div>
    </div>
</nz-spin>



<nz-modal i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="showPaymentModal" [nzOkText]="null" [nzWidth]="1200"
(nzOnCancel)="showPaymentModal = false; selectedInvoice = null">
<ng-container *nzModalContent>
    <app-payment [invoice]="selectedInvoice" (paymentDone)="refreshInvoices()"></app-payment>
</ng-container>
</nz-modal>
