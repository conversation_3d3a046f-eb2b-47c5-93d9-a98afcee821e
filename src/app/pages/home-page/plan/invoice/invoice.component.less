.top-menu {
    float: left;
    padding: 0 0 0 10px;
    color: #125b7e;
}

.top-text {
    vertical-align: middle;
    font-weight: bold;
    margin-right: 20px;
}

.top-icon {
    font-size: 18px;
    margin-right: 5px;
}

button.disabled {
    color: lightgrey;
    cursor: not-allowed;
    pointer-events: none;
}

.invoiceWrapper {
    background-color: white;
    margin: 0 auto;
    padding: 10px 40px 70px 40px;
    max-width: 620px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 16px;
}

.invoiceBox {
    border: 1px solid #ccc;
}

.paid {
    font-size: 18px;
    color: #779500;
    font-weight: bold;
}

.unpaid {
    font-size: 18px;
    color: #cc0000;
    font-weight: bold;
}

.addressBox {
    height: 100px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ccc;
    color: #000;
    overflow: hidden;
    min-height: 130px;
    line-height: 15px;
}

.amountTable, .amountTable td {
    border: 1px solid #ccc;
    border-collapse: collapse;
    line-height: 15px;
}
.amountTableGreyRow {
    border: 1px solid #ccc;
    background-color: #efefef;
}
.width-70 {
    width: 70%;
}
.width-40 {
    width: 40%;
}
.width-15 {
    width: 15%;
}
.font15 {
    font-size: 15px;
}