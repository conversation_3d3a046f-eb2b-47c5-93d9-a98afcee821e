import { Component, OnInit } from '@angular/core';
import { PaymentService } from '@core/services/payment.service';
import { environment } from '@environment/environment';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';


@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.less']
})
export class InvoiceComponent implements OnInit {
  public invoices: any[] = [];
  public currentInvoice: any;
  public currentInvoicePayTo: string = '';
  public timeOffset: number = 0;
  public displayInvoiceDate: string = '';
  public displayDueDate: string = '';
  public isSpinning: boolean = false;
  public tableTotal: number = 0;
  public user:any;
  public planAmount: number = 0;
  public pageIndex: number = 1;
  public pageSize: number = 10;
  public math = Math;


  public checked = false;
  public indeterminate = false;
  public setOfCheckedId = new Set<number>();
  public selectedInvoice: any;
  public showPaymentModal: boolean = false;
  public viewInvoice: boolean = false;

  constructor(
    private paymentService: PaymentService,
    private route: ActivatedRoute,
    private router: Router,
    private titleService: Title
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(localStorage.getItem("user") || '0');
    this.titleService.setTitle("Invoice");

    if( this.router.url.includes('/invoice/view/'))  {
      this.getInvoice();
    }
    else{
      this.getInvoices();
    }

    this.router.events.subscribe((val) => {
      //view invoice
      if (val instanceof NavigationEnd && this.router.url.includes('/invoice/view/')) {
        this.getInvoice();
      }
      //list invoices
      else{
        this.viewInvoice = false;
      }

    });
  }

  downloadInvoices(): void {
    let ids = "";
          
		this.setOfCheckedId.forEach((id,index) => {
      ids += id + ",";
    })
    ids = ids.slice(0, -1);
		window.open(environment.API_URL + 'v1/user/invoices/' + ids + '/content', '_blank');
  }
  downloadInvoice(id:number): void {
		window.open(environment.API_URL + 'v1/user/invoice/' + id + '/content', '_blank');
  }

  payNow(invoice: any): void {
    this.selectedInvoice = invoice;
    if(invoice) {
      this.showPaymentModal = true;
    }
  }

  refreshInvoices(): void {
    this.showPaymentModal = false;
    this.selectedInvoice = null;
    if( this.router.url.includes('/invoice/view/'))  {
      this.getInvoice();
    }
    else{
      this.getInvoices();
    }
  }

  invoiceLocalization(): void {
    if (this.invoices != null) {
        for(var i=0; i<this.invoices.length; i++){
            var inv = this.invoices[i];
            if (inv.status == "Paid") this.invoices[i].status = $localize `Paid`;
            else if(inv.status=="Cancelled") this.invoices[i].status = $localize `Cancelled`;
            else  if(inv.status=="Suspended") this.invoices[i].status = $localize `Suspended`;
            else  if(inv.status=="Unpaid") this.invoices[i].status = $localize `Unpaid`;
        }
    }
  }

  getInvoices(): void {
    this.isSpinning = true;
    this.paymentService.getInvoices().subscribe((result) => {
      //sort invoices by id
      this.invoices = result.invoices.sort((a:any, b:any) => {
        return b.id - a.id;
        } 
      );
      this.tableTotal = this.invoices.length;
      this.invoiceLocalization();
      this.isSpinning = false;
    })
  }
  getInvoice(): void {
    this.isSpinning = true;
    this.viewInvoice = true;

    this.paymentService.getInvoice( Number(this.route.snapshot.paramMap.get('id'))).subscribe((result) => {
      this.currentInvoice = result.invoice;
      this.currentInvoicePayTo = result.invoice.invoicepayto.value.replace(/\n/g, '<br/>');
      this.timeOffset = new Date().getTimezoneOffset() * 60 * 1000;
      this.displayInvoiceDate = new Date(result.invoice.invoicedate.time - this.timeOffset).toISOString().replace(/^(.+?)T(.+?).\d{3}Z$/, "$1");
      this.displayDueDate = new Date(result.invoice.duedate.time - this.timeOffset).toISOString().replace(/^(.+?)T(.+?).\d{3}Z$/, "$1");

      result.invoice.invoiceitems.forEach((item:any) => {
        this.planAmount += item.amount;
      })

      this.invoiceLocalization();
      this.isSpinning = false;
    })
  }

  refreshCheckedStatus(): void {
    this.checked = this.invoices.every(({ id }:any) => this.setOfCheckedId.has(id));
    this.indeterminate = this.invoices.some(({ id }:any) => this.setOfCheckedId.has(id)) && !this.checked;
  }
  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.invoices
      .forEach(({ id }:any) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }
  changePageIndex(index:number) {
    this.pageIndex = index;
  }

  invoiceLanguage(description: string): string {
		description=description.replace("Changing plan from", $localize `Changing plan from`);
		description=description.replace("to", $localize `to`);
		description=description.replace(/Monthly/g, $localize `Monthly`);
		description=description.replace(/Annually/g, $localize `Annually`);
		description=description.replace("Service between", $localize `Service between`);
		description=description.replace("and", $localize `and`);
		description=description.replace("Recurring invoice for the plan", $localize `Recurring invoice for the plan`);
		description=description.replace("between", $localize `between`);

		description=description.replace(/Jan/g, $localize `Jan`);
		description=description.replace(/Feb/g, $localize `Feb`);
		description=description.replace(/Mar/g, $localize `Mar`);
		description=description.replace(/Apr/g, $localize `Apr`);
		description=description.replace(/May/g, $localize `May`);
		description=description.replace(/June/g, $localize `June`);
		description=description.replace(/July/g, $localize `July`);
		description=description.replace(/Aug/g, $localize `Aug`);
		description=description.replace(/Sept/g, $localize `Sept`);
		description=description.replace(/Oct/g, $localize `Oct`);
		description=description.replace(/Nov/g, $localize `Nov`);
		description=description.replace(/Dec/g, $localize `Dec`);
		return description;
	}

  getCurrencyNumber(amount:any , currency:any):string {
		return parseFloat(amount).toFixed(2) + ' ' + currency.suffix;
	}

}
