<div class="p-4">
    <nz-spin [nzSpinning]="isSpinning">

    <div class="payment-container" >
        <div class="flex flex-col items-center justify-center">
            <h1 class="text-xl" i18n>Pay as your business grows</h1>
            <h2 class="text-lg" i18n>Your subscription can be scaled up or down anytime</h2>
        </div>
        <div *ngIf="plan" class="flex justify-between flex-wrap my-3 xs:justify-center">
            <div >
                <nz-button-group class="mb-4">
                    <button (click)="isMonthly = true; calculatePlanPrice()" class="filterBtn" nz-button 
                    [nzType]=" isMonthly ? 'primary': 'default'" i18n>Monthly</button>
                    <button (click)="isMonthly = false; calculatePlanPrice()" class="filterBtn" nz-button 
                    [nzType]=" isMonthly ? 'default': 'primary'" i18n>Yearly</button>
                </nz-button-group>
                <div class="planContainer text-center">
                    <div class="text-2xl mb-4 planTitle">
                        {{plan.productName}}
                    </div>
                    <div>
                        <span class="pricing mb-4">
                            {{user.currency.code}} $
                        </span>
                        <span class="pricing mb-3 text-3xl">
                            {{isMonthly ? plan.Monthly:plan.Annually/12}}
                        </span>
                    </div>

                    <div *ngIf="isMonthly" i18n>
                        month
                    </div>
                    <div *ngIf="!isMonthly" i18n>
                        month billed annually
                    </div>
                </div>
            </div>
            <div class="priceContainer">
                <div class="mb-6 text-lg planTitle" i18n>
                    Total:
                </div>
                <div class="mb-7 planTitle">
                    {{user.currency.code}} $ 
                    <span class="text-4xl">
                        {{isMonthly ? plan.Monthly:plan.Annually}}
                    </span>
                    <span *ngIf="isMonthly" i18n>
                        /month
                    </span>
                    <span *ngIf="!isMonthly" i18n>
                        /year
                    </span>
                </div>
                
            </div>
        </div>
        <div class="flex flex-row-reverse mb-5">  
            <img src="assets/images/powered_by_stripe.svg" alt="Powered by Stripe" />
        </div>
        <div class="flex justify-between flex-wrap mb-6">
            <div *ngIf="invoice">
                <span class="text-2xl" i18n>You Pay: </span>
                <span class="text-med">{{invoice.currency.code}}$ </span>
                <span class="text-lg"> {{invoice.total | number:'1.2-2'}}</span>
            </div>
            <div *ngIf="plan">
                <span class="text-2xl" i18n>You Pay: </span>
                <span class="text-med">{{user.currency.code}}$ </span>
                <span class="text-2xl">  {{planPrice | number:'1.2-2'}}</span>
            </div>
        
            <div class="flex flex-nowrap" *ngIf="plan">
                <input nz-input placeholder="Add Coupon Code" [(ngModel)]="couponCode"
                i18n-placeholder i18n-aria-label aria-label="Add Coupon Code" />
                <button nz-button nzType="primary" class="ml-2" (click)="getCoupon()" i18n>Apply Coupon</button>
            </div>
        </div>
        <div class="text-2xl mb-3" i18n>Your Credit Card </div>
        <div class="mb-6">
            <mat-radio-group [(ngModel)]="selectedPaymentSource" i18n-aria-label aria-label="Select a credit card" >
            <div *ngFor="let paymentSource of paymentSources" class="flex justify-between flex-wrap">
                <span>
                    <mat-radio-button class="w-full" [value]="paymentSource" i18n>
                        <i [ngClass]="getCreditCardIcon(paymentSource)" aria-hidden="true"></i>
                        {{paymentSource.brand}} ending in {{paymentSource.last4}}
                    </mat-radio-button>
                </span>
                <span>{{paymentSource.name}}</span>
                <span> {{paymentSource.expMonth}}/{{paymentSource.expYear}}</span>
                <span> 
                    <button i18n-aria-label aria-label="Delete credit card" *ngIf="paymentSources.length > 1"
                    (click)="deleteCardModal=true; selectedCardForDelete = paymentSource.id">    
                        <span class="delete-icon" nz-icon nzType="delete" nzTheme="outline"></span>
                    </button>
                </span>
            </div>
        </mat-radio-group>
        </div>
        <div class="flex justify-between items-center mb-3">   
            <div class="text-xl mb-3" i18n>Add a new payment method </div>
            <span class="flex items-center text-lg">
                    <i class="fa-brands fa-cc-visa mr-1" aria-hidden="true"></i>
                    <i class="fa-brands fa-cc-amex mr-1" aria-hidden="true"></i>
                    <i class="fa-brands fa-cc-mastercard" aria-hidden="true"></i> 
            </span>
        </div>
        <form  [formGroup]="addPaymentForm">

            <div class="flex justify-around  xs:justify-between med:justify-between flex-wrap mb-5">   
                <div class="flex items-baseline xs:mb-3 med:mb-3">
                    <span class="mr-1 xs:w-28 med:w-28" i18n>Card Holder</span>
                    <span>
                        <input nz-input class="w-auto" placeholder="John Smith" formControlName="cardHolder"
                        i18n-placeholder i18n-aria-label aria-label="Card Holder"
                        [style.borderColor]="addPaymentFormSubmitted && addPaymentForm.controls['cardHolder'].errors ? 'red':''"/>
                        <div aria-live="polite"><div *ngIf="addPaymentFormSubmitted && addPaymentForm.controls['cardHolder'].errors"
                            class="text-red-500" i18n>A card holder name is required</div></div>
                    </span>
                </div>
                <div>
                    <div class="flex items-baseline  mb-3">
                        <span class="mr-1 xs:w-28 med:w-28" i18n>Card Number</span>
                        <span>
                            <input nz-input class="w-auto" placeholder="1234123412341234" i18n-placeholder formControlName="cardNumber"
                            i18n-aria-label aria-label="Card Number" (keypress)="allowOnlyDigits($event)"
                            (paste)="blockNonDigitsPaste($event)" maxlength="16"
                            [style.borderColor]="addPaymentFormSubmitted && addPaymentForm.controls['cardNumber'].errors ? 'red':''"/>
                           <div aria-live="polite"><div *ngIf="addPaymentFormSubmitted && addPaymentForm.controls['cardNumber'].errors"
                               class="text-red-500" i18n>A valid card number is required</div></div>
                        </span>
                    </div>
                    <div class="flex justify-around  xs:justify-between flex-wrap">
                        <div class="flex items-baseline mr-5 xs:mb-3">
                            <span class="mr-1 xs:w-28 med:w-28" i18n>CVC</span>
                            <span>
                                <input nz-input class="w-16" placeholder="123" i18n-placeholder i18n-aria-label aria-label="CVC"
                                maxlength="4" (keypress)="allowOnlyDigits($event)" (paste)="blockNonDigitsPaste($event)"
                                formControlName="CVC" [style.borderColor]="addPaymentFormSubmitted && addPaymentForm.controls['CVC'].errors ? 'red':''"/>
                                <div aria-live="polite"><div *ngIf="addPaymentFormSubmitted && addPaymentForm.controls['CVC'].errors"
                                    class="text-red-500" i18n>A valid CVC is required</div></div>
                            </span>
                        </div>
                        <div class="flex items-baseline xs:mb-3">
                            <span class="mr-1 xs:w-28 med:w-28" i18n>Expiry Date</span>
                            <span>
                                <input nz-input class="w-24" placeholder="MM / YY" i18n-placeholder i18n-aria-label aria-label="Expiry Date"
                                [style.borderColor]="addPaymentFormSubmitted && addPaymentForm.controls['expiryDate'].errors ? 'red':''"
                                formControlName="expiryDate"/>
                                <div aria-live="polite"><div *ngIf="addPaymentFormSubmitted && addPaymentForm.controls['expiryDate'].errors"
                                    class="text-red-500" i18n>A valid expiry date is required</div></div>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <div class="flex flex-row-reverse mb-2">  
            <button nz-button nzType="primary" class="ml-2" (click)="addPaymentSource()" i18n>Add Payment Method</button>
        </div>
        <div class="text-xl mb-6" *ngIf="selectedPaymentSource && selectedPaymentSource.addressLine1">
            <div  i18n>Your Billing Addresses for your {{selectedPaymentSource.brand}} ending in {{selectedPaymentSource.last4}}  </div>
            <mat-radio-group i18n-aria-label aria-label="Select an address" class="text-med">
                <mat-radio-button class="w-full" [value]="1" checked="1">
                    <span> {{selectedPaymentSource.addressLine1}}</span>,
                    <span> {{selectedPaymentSource.addressState}}</span>,
                    <span> {{selectedPaymentSource.addressCity}}</span>,
                    <span> {{selectedPaymentSource.addressCountry}}</span>,
                    <span> {{selectedPaymentSource.addressZip}}</span>
                </mat-radio-button>
            </mat-radio-group>
        </div>
        <div *ngIf="selectedPaymentSource">
            <div class="text-xl mb-5" >
                <div  i18n>Add a new address for your {{selectedPaymentSource.brand}} ending in {{selectedPaymentSource.last4}}  </div>
            </div>
            <form  [formGroup]="updateAddressForm">
            <div class="flex mb-3">
                <span class="w-28" i18n>Country</span>
                <mat-form-field class="material-select-height select">
                    <mat-select i18n-aria-label aria-label="Country" formControlName="country">
                        <mat-option  *ngFor="let country of countryCodes" [value]="country[1]">{{country[0]}}</mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
            <div class="flex  flex-wrap">
                <div class="inline-flex items-center !xs:mr-7 mb-3">
                    <span class="w-28" i18n>Street Address</span>
                    <span>
                        <input nz-input i18n-placeholder formControlName="streetAddress" i18n-aria-label aria-label="Street Address"/>
                    </span>
                </div>
                <div class="inline-flex items-center mb-3">
                    <span class=" w-28" i18n>State/Province</span>
                    <span>
                        <input nz-input i18n-placeholder formControlName="stateProvince" i18n-aria-label aria-label="State/Province"/>
                    </span>
                </div>
            </div>
            <div class="flex  flex-wrap">
                <div class="inline-flex items-center !xs:mr-7 mb-3">
                    <span class="w-28" i18n>City</span>
                    <span>
                        <input nz-input i18n-placeholder formControlName="city" i18n-aria-label aria-label="City"/>
                    </span>
                </div>
                <div class="inline-flex items-center mb-3">
                    <span class=" w-28" i18n>Postal Code/Zip</span>
                    <span>
                        <input nz-input i18n-placeholder formControlName="postalZip" i18n-aria-label aria-label="State/Province"/>
                    </span>
                </div>
            </div>
            </form>
            <div class="flex flex-row-reverse mb-2">  
                <button nz-button nzType="primary" class="ml-2" (click)="updateAddress()" [disabled]="paymentSources.length < 1" i18n>Update Address</button>
            </div>
        </div>
        <div class="mb-5">
            
            <label nz-checkbox [(ngModel)]="agreeTos" i18n>I agree to Signority's</label>
            <a href="https://www.signority.com/signority-terms-service/" class="tos" target="_blank" i18n>
                Terms of Service
              </a>
              
              <div *ngIf="tosError" class="text-red-500" i18n>
                You must agree to the Terms of Service to proceed
              </div>
        </div>

        <div class="flex justify-center" *ngIf="invoice">  
            <button nz-button nzType="primary" [disabled]="paymentSources.length < 1" 
            (click)="payInvoice()" class="payNow" i18n>Pay Now</button>
        </div>
        <div class="flex justify-center" *ngIf="plan">  
            <button nz-button nzType="primary" [disabled]="paymentSources.length < 1" 
            (click)="changePlan()" class="payNow" i18n>Pay Now</button>
        </div>

    </div>
    </nz-spin>
</div>

<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteCardModal" nzTitle="Confirm" (nzOnOk)="deletePaymentMethod()"
(nzOnCancel)="deleteCardModal = false; selectedCardForDelete = -1">
<ng-container *nzModalContent>
    <table class="modal-table">
        <tr>
            <td i18n>Are you sure you would like to delete this payment method?</td>
        </tr>
    </table>
</ng-container>
</nz-modal>

