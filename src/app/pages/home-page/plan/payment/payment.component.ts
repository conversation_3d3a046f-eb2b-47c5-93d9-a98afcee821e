import { Component, Input, OnInit, LOCALE_ID, Inject, Output, EventEmitter } from '@angular/core';
import { PaymentService } from '@core/services/payment.service';
import { Validators, FormBuilder } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { countries } from '@core/models/countries';
import { environment } from "@environment/environment";
import {NzMessageService} from "ng-zorro-antd/message";
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';

import * as valid from 'card-validator';
declare let Stripe:any;


@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.less']
})
export class PaymentComponent implements OnInit {
    @Input() invoice: any;
    @Input() plan: any;
    @Input() isMonthly: boolean = true;
    @Output() paymentDone = new EventEmitter<boolean>();
    
    public paymentSources: any[] = [];
    public selectedPaymentSource:any;
    public selectedCardForDelete:number = -1;
    public addPaymentFormSubmitted: boolean = false;
    public Stripe: any = Stripe.setPublishableKey(environment.stripeKey);
    public isSpinning: boolean = false;
    public deleteCardModal:boolean = false;
    public countryCodes:any;
    public agreeTos: boolean = false;
    public tosError: boolean = false;
    public user:any;
    public couponCode: string = '';
    public couponId: number = -1;
    public couponPercentOff: number = 0;
    public planPrice: number = 0;

    public addPaymentForm = this.formBuilder.group({
      cardHolder: ['', Validators.required],
      cardNumber: ['', [Validators.required,this.validateCardNumber]],
      CVC: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(4)]],
      expiryDate: ['', [Validators.required, this.validateExpiryDate]],
    });

    public updateAddressForm = this.formBuilder.group({
      country: [''],
      streetAddress: [''],
      stateProvince: [''],
      city: [''],
      postalZip:['']
    });
    
  constructor(
    private paymentService: PaymentService,
    private formBuilder: FormBuilder,
    private modal: NzModalService,
    private message: NzMessageService,
    private router: Router,
    private title: Title,
    
    @Inject(LOCALE_ID) public locale: string
    

  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(localStorage.getItem("user") || '0');
    this.title.setTitle("Payment");
    if(this.plan) {
      this.calculatePlanPrice();
    }
    if(this.locale == 'fr') {
      this.countryCodes = countries.fr;
    }
    else {
      this.countryCodes = countries.en;
    }
    this.getPaymentSources();
  }

  getPaymentSources() {
    this.isSpinning = true;
    this.paymentService.getPaymentSources().subscribe((result) => {
      this.paymentSources = result.paymentSources.data;
      if(this.paymentSources.length > 0) {
        this.selectedPaymentSource = this.paymentSources[0];
      }
      this.isSpinning = false;
    })
  }

  addPaymentSource() {
    this.addPaymentFormSubmitted = true;

    if(this.addPaymentForm.valid) {
      this.isSpinning = true;
      const paymentSource = {
        name: this.addPaymentForm.get('cardHolder')?.value,
        number: this.addPaymentForm.get('cardNumber')?.value.trim(),
        cvc: this.addPaymentForm.get('CVC')?.value,
        exp_month: this.addPaymentForm.get('expiryDate')?.value.split('/')[0],
        exp_year: '20' +this.addPaymentForm.get('expiryDate')?.value.split('/')[1],
      };
      Stripe.card.createToken(paymentSource, (status:any, response:any) => {
        if (response.error) { 
          this.modal.error({
            nzTitle: response.error.message,
        });
          this.isSpinning = false;
        }
        else {  
          this.paymentService.addPaymentSource(response.id).subscribe((result:any) => {
            this.selectedPaymentSource = result;
            this.addPaymentForm.reset();
            this.addPaymentFormSubmitted = false;
            this.getPaymentSources();
        })
      }
    })
    }
  }

  deletePaymentMethod() {
    this.isSpinning = true;
    this.paymentService.deletePayment(this.selectedCardForDelete).subscribe(()=>{
      this.deleteCardModal = false;
      this.selectedCardForDelete = -1;
      this.getPaymentSources();
      this.isSpinning = false;
    })
  }

  updateAddress() {
    this.isSpinning = true; 
    if(this.updateAddressForm.get('country')?.value) {
      this.selectedPaymentSource.address_country =  this.updateAddressForm.get('country')?.value.toUpperCase();
    }
    if(this.updateAddressForm.get('streetAddress')?.value) {
      this.selectedPaymentSource.address_line1 = this.updateAddressForm.get('streetAddress')?.value;
    }
    if(this.updateAddressForm.get('stateProvince')?.value) {
      this.selectedPaymentSource.address_state = this.updateAddressForm.get('stateProvince')?.value;
    }
    if(this.updateAddressForm.get('city')?.value) { 
      this.selectedPaymentSource.address_city = this.updateAddressForm.get('city')?.value;
    }
    if(this.updateAddressForm.get('postalZip')?.value) {
      this.selectedPaymentSource.address_zip = this.updateAddressForm.get('postalZip')?.value;
    }

    this.paymentService.updatePaymentSource(this.selectedPaymentSource).subscribe((result:any) => {  
      this.updateAddressForm.reset();
      this.getPaymentSources()
    }
    ) 
  }

  payInvoice() {
    if(!this.agreeTos) { 
      this.tosError = true;
      return;
    }
    this.isSpinning = true; 
    this.paymentService.payInvoice(this.invoice.id, this.selectedPaymentSource.id).subscribe((result:any) => {
      if(result.success) {
        this.paymentDone.emit(true);
      }
      this.isSpinning = false;
    })
  }
  changePlan() {
    if(!this.agreeTos) { 
      this.tosError = true;
      return;
    }
    this.isSpinning = true; 
    this.paymentService.changePlan(this.plan.id, this.isMonthly? '0' : '1',
      this.couponId, this.selectedPaymentSource.id).subscribe((result: any) => {
        if (result.success) {
          let me = this;

          if (result.pricing.invoice) {
            this.message.success($localize`Your service plan has been changed successfully; please review the invoice in the next page.`);
            setTimeout(function () {
              me.router.navigate(['/invoice/view/' + result.pricing.invoice.id]);
            }, 5000);
          }
          else {
            this.message.success($localize`Your service plan has been changed successfully.`);

            window.setTimeout(function () {
              me.router.navigate(['/plan']);

            }, 5000);
          }
      }
      this.isSpinning = false;
    })
  }

  getCoupon() {
    if(this.couponCode.length >0) {
      this.isSpinning = true;
      this.paymentService.getCoupon(this.couponCode).subscribe((result:any) => {
        if(result.success) {
          this.couponId = result.coupon.id;
          if(result.coupon.percentOff>0){
            this.couponPercentOff = result.coupon.percentOff/100;
            this.message.success($localize`Coupon applied successfully`);
          }
          this.calculatePlanPrice();
        }
        else {
          if(result.code==5000) {
            this.message.error($localize`The coupon code is not available! Please try with other ones`);
          }
          else{
            this.message.error($localize`An erorr has occured`);
          }

        }
        this.isSpinning = false;
      })
    } 
    else{
      this.message.error($localize`The coupon code is not available! Please try with other ones`);

    }
  }

  calculatePlanPrice() {
    this.planPrice = this.isMonthly ? this.plan.Monthly : this.plan.Annually;
    if(this.couponPercentOff > 0) {
      this.planPrice = this.planPrice - (this.planPrice * this.couponPercentOff);
    }
  }

  getCreditCardIcon(paymentSource:any):String {
    switch (paymentSource.brand) {
    case "Visa":
        return "fa-brands fa-cc-visa";
    case "American Express":
        return "fa-brands fa-cc-amex";
    case "MasterCard":
        return "fa-brands fa-cc-mastercard";
    }
    return "";
}

allowOnlyDigits(event: KeyboardEvent): void {
  if (!/^\d$/.test(event.key)) {
    event.preventDefault();
  }
}

blockNonDigitsPaste(event: ClipboardEvent): void {
  const pastedInput: string = event.clipboardData?.getData('text') ?? '';
  if (!/^\d+$/.test(pastedInput.trim())) {
    console.log('Invalid paste input:', pastedInput);
    event.preventDefault();
  }
}

validateCardNumber(card: any): object | null {
  if(valid.number(card.value).isValid) {
    return null;
  }
  return { invalidCardNumber: true };
}
validateExpiryDate(card: any): object | null {
  const regex = /^(0[1-9]|1[0-2])\/\d{2}$/;

  // Check format
  if (!card.value || !regex.test(card.value)) {
    return { invalidDateFormat: true };
  }
  let [month, year] = card.value.split('/').map(Number);
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear() % 100; // Get last two digits of the year
  const currentMonth = currentDate.getMonth() + 1; // Months are 0-based in JavaScript  
  if (year < currentYear || (year === currentYear && month < currentMonth)) {
    return { expiredDate: true };
  }
  return null;
}

}
