import { Component, OnInit } from '@angular/core';
import { PaymentService } from '@core/services/payment.service';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-change-plan',
  templateUrl: './change-plan.component.html',
  styleUrls: ['./change-plan.component.less']
})
export class ChangePlanComponent implements OnInit {

  public  teamPlan:boolean = true;
  public  monthlyBilling:boolean = true;
  public  plans:any[] = [];   
  public  displayPlans:any[] = [];   
  public  selectedPlan:any = null;
  public  isSpinning: boolean = false;
  public  showPaymentModal: boolean = false;
  public  user:any;

  constructor(
    private paymentService: PaymentService,
    private titleService: Title,
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(localStorage.getItem("user") || '0');
    this.getPlans();
    this.titleService.setTitle("Change Plan");
  }

  getPlans() {  
    this.isSpinning = true;
    this.paymentService.getPlans().subscribe((res: any) => {
      // Filter plans to exclude groupId==50001 and isOldStripePlan==false and apiKeyEnabled == true
      this.plans = res.products.filter((plan: any) => plan.groupId !== 50001 &&  plan.isOldStripePlan === false && plan.apiKeyEnabled == true);
      this.toggleIndividualPlans();
      this.isSpinning = false;
    });
  }

  toggleIndividualPlans() {
    if(this.teamPlan) {
      this.displayPlans = this.plans.filter((plan: any) => plan.maxGroupMembers > 1);
    }
    else{
      this.displayPlans = this.plans.filter((plan: any) => plan.Monthly > 1 && plan.maxGroupMembers == 1);
    }
  }

  choosePlan(plan: any) {
    this.selectedPlan = plan;
    this.showPaymentModal = true;
  }

}
