.filterBtn {
    width:175px;
}

.planCard {
    border-radius: 10px;
    color: #FFF;
    padding: 15px;
    background:linear-gradient(to bottom right, #0183D3, #0fb8ad) !important;
    margin-right: 20px;
}

.planName {
    font-weight: 600;
    padding: 20px;
    text-align: center;
    position: relative;
    color:white
}

.planDescription {
    padding:15px;
    font-size: 16px;
}

.planName:after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 40%;
    height: 1px;
    width: 20%;
    background-color: #FFF
}

.white-link {
    color: white;
    text-decoration: underline;
}