<div class="text-center mb-8">
    <h1 class="text-3xl mb-3" i18n>
        Document Signing Made Easy
    </h1>
    <div class="text-2xl mb-3" i18n>
        Fair & transparent pricing. No surprise fees.
    </div>
    <div class="text-xl mb-4" *ngIf="teamPlan" i18n>
        All paid plans come with secure digital signatures, regular templates, email support and our core feature set.
    </div>
    <div class="text-xl mb-4" *ngIf="!teamPlan" i18n>
        All paid Individual plans come with regular templates, linksign templates, bulksign templates, email support and our core feature set.
    </div>
    <!-- <div class="text-2xl text-red-500 mb-4 mt-4"  i18n>
        You are currently on an annual plan. By changing to a monthly plan, you will start a new billing cycle and will not be refunded the remainder of your annual plan.
    </div> -->
    <div>
        <nz-button-group class="mb-4">
            <button (click)="teamPlan = false; toggleIndividualPlans()" class="filterBtn" nz-button 
            [nzType]=" teamPlan ? 'default': 'primary'" i18n>Individual Plans</button>
            <button (click)="teamPlan = true; toggleIndividualPlans()" class="filterBtn" nz-button 
            [nzType]=" teamPlan ? 'primary': 'default'" i18n>Team Plans</button>
        </nz-button-group>
    </div>
    <div>
        <nz-button-group class="mb-4">
            <button (click)="monthlyBilling = true" class="filterBtn" nz-button 
            [nzType]=" monthlyBilling ? 'primary': 'default'" i18n>Monthly Billing</button>
            <button (click)="monthlyBilling = false" class="filterBtn" nz-button 
            [nzType]=" monthlyBilling ? 'default': 'primary'" i18n>Annual Billing</button>
        </nz-button-group>
    </div>
</div>
<nz-spin [nzSpinning]="isSpinning">

    <div class="flex flex-wrap xs:justify-center">
        <div class="planCard w-80 mb-5" *ngFor="let plan of displayPlans">
            <h2 class="planName text-2xl mb-3">
                {{plan.productName}}
            </h2>
            <div class="flex justify-center mb-5">
                <span class="pt-1">{{user.currency.code }} $</span>
                <span class="text-4xl">{{monthlyBilling ? plan.Monthly : plan.Annually}}</span>
            </div>
            <div class="mb-5">
                <div *ngIf="monthlyBilling" class="text-center" i18n>per month</div>
                <div *ngIf="!monthlyBilling" class="text-center" i18n>billed anually</div>
            </div>
            <div class="flex justify-center mb-5">
                <button  class="filterBtn" nz-button nzType="default" (click)="choosePlan(plan)" i18n>Choose Plan</button>
            </div>
            <div class="planDescription">
                <div *ngFor="let feature of plan.description.split(',')">
                    {{feature}}
                </div>
            </div>
        </div>
        <div class="planCard w-80 mb-5">
            <h2 class="planName text-2xl mb-3" i18n>
                Custom
            </h2>
            <div class="flex justify-center mb-5">
                <span class="text-xl" i18n>Pricing available on request</span>
            </div>
            <div class="mb-5">
                <div class="text-center" i18n>Customize your plan to suit your business needs</div>
            </div>
            <div class="flex justify-center mb-5">
                <button  class="filterBtn" nz-button nzType="default" i18n>
                    <a href="https://www.signority.com/custom-pricing" target="_blank">
                        Contact Us
                    </a>
                </button>
            </div>
            <div class="planDescription">
                <div>
                    Enterprise level features available. 
                    To learn more please visit our <a class="white-link" target="_blank" href="https://www.signority.com/pricing/">pricing page</a> 
                    and contact sales!
                </div>
            </div>
        </div>
    </div>
</nz-spin>
<nz-modal i18n-nzCancelText nzCancelText="Cancel" [(nzVisible)]="showPaymentModal" [nzOkText]="null" [nzWidth]="1200"
(nzOnCancel)="showPaymentModal = false; selectedPlan = null">
<ng-container *nzModalContent>
    <app-payment [plan]="selectedPlan" [isMonthly]="monthlyBilling"></app-payment>
</ng-container>
</nz-modal>

<footer class="text-center flex flex-col">
    <a href="https://www.signority.com/pricing/" class="text-2xl" target="_blank" i18n>
        &lt; See all features &gt;
    </a>
    <a [routerLink]="'/plan'" class="text-lg" target="_blank" i18n>
        Have to terminate your plan? Click here
    </a>
    <a href="/UI/userSettings.html" class="text-lg" target="_blank" i18n>
        Generate API Key
    </a>
</footer>
