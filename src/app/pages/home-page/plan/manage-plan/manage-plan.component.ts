import { Component, OnInit } from '@angular/core';
import { UserService } from '@core/services';
import { Code } from '@core/models';
import {NzMessageService} from "ng-zorro-antd/message";
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-manage-plan',
  templateUrl: './manage-plan.component.html',
  styleUrls: ['./manage-plan.component.less']
})
export class ManagePlanComponent implements OnInit {

  public code:any = Code;
  public product:any;
  public subscription:any;
  public userInfo: any;
  public user: any;
  public discountAmmount:number = 0;
  public billAfterDiscount:number = 0;
  public showCancelModal:boolean = false;
  public isSpinning:boolean = false;

  constructor(
    private userService: UserService,
    private modal: NzMessageService,
    private titleService: Title

  ) { }

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    this.titleService.setTitle($localize`Manage Plan`);
    
   this.setUp();
  }

  setUp() {
    this.isSpinning = true;
    this.userInfo = JSON.parse(localStorage.getItem("userInfo")|| '0' );
    this.user = JSON.parse(localStorage.getItem("user")|| '0' );

    this.userService.getUserProduct().subscribe(data=>{
      this.product = data.product;
      this.subscription = data?.subscription;
      if(this.subscription && this.subscription.discount && this.subscription.discount.coupon.amountOff > 0) {
        this.discountAmmount = (this.subscription.discount.coupon.amountOff)/100;
        this.billAfterDiscount =  (this.subscription.plan.amount/100 * this.product.maxteammember) - this.discountAmmount;
      }
      else if(this.subscription && this.subscription.discount && this.subscription.discount.coupon.percentOff > 0) {
        this.discountAmmount = (this.subscription.discount.coupon.percentOff * this.subscription.plan.amount/10000) * this.product.maxteammember;
        this.billAfterDiscount =  (this.subscription.plan.amount/100 * this.product.maxteammember) - this.discountAmmount;
      }
      this.isSpinning = false;

    })
  }

  cancelPlan() {
    this.isSpinning = true;

    this.userService.cancelPlan().subscribe(()=>{
      this.showCancelModal = false;
      this.isSpinning = false;
      this.setUp();
      this.modal.success($localize`Your plan was successfully cancelled`);
    })
  }

}
