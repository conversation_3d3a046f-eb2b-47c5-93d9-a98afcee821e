@media (max-width:640px) {
    .mobile-title {
        font-size: 50px !important;
        line-height: 50px !important;
        padding-bottom: 20px !important;
    }
  }

  
.title {
    font-size: 70px;
    line-height: 70px;
    font-weight: 275;
    color: #2D323D;
    padding-bottom: 55px;
  }

.details-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.plan-details {
    background-color: white;
    width: 80%;
    border: 1px solid #efe7e7;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
}

.info-table {
    table-layout: fixed;
    width: 50%;
    border-collapse:separate; 
    border-spacing: 10px;
    width: 90%;
}

.plan-buttons {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}
