<h1 class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    Service Plan
</h1>
<nz-spin [nzSpinning]="isSpinning">
    <div class="details-container">
        <div class="plan-details">
            <div i18n><b>Your current plan</b></div>
            <table *ngIf="product" class="info-table">
                <tr>
                    <td class="text-right" i18n>Plan:</td><td>{{product.name}}</td>
                </tr>
                <tr>
                    <td class="text-right" i18n>Billing Cycle:</td><td>{{product.billingcycle}}</td>
                </tr>
                <!-- <tr>
                    <td class="text-right" i18n>Team Members:</td><td>{{product.maxteammember}}</td>
                </tr> -->
                <tr>
                    <td class="text-right" i18n>Plan Fee:</td><td>{{product.currency.prefix}}{{product.amount | number : '1.2-2'}} {{product.currency.suffix}}</td>
                </tr>
                <tr *ngIf="subscription && subscription.discount">
                    <td class="text-right"  i18n>Coupon Used:</td>
                    <td>{{subscription.discount.coupon.id + " " + subscription.discount.coupon.duration}}</td>
                </tr>
                <tr *ngIf="subscription && subscription.discount && subscription.discount.coupon.amountOff>0">
                    <td class="text-right"  i18n>Discount:</td>
                    <td>{{product.currency.prefix}}{{discountAmmount | number : '1.2-2'}} {{product.currency.suffix}}</td>
                </tr>
                <tr *ngIf="subscription && subscription.discount && subscription.discount.coupon.percentOff>0">
                    <td class="text-right"  i18n>Discount:</td>
                    <td>{{product.currency.prefix}}{{discountAmmount | number : '1.2-2'}} {{product.currency.suffix}}</td>
                </tr>
                <tr *ngIf="subscription && subscription.discount && discountAmmount > 0">
                    <td class="text-right"  i18n>Your Bill:</td>
                    <td>{{product.currency.prefix}}{{billAfterDiscount | number : '1.2-2'}} {{product.currency.suffix}}</td>
                </tr>
                <tr>
                    <td class="text-right" i18n>Status:</td><td>{{product.status}}</td>
                </tr>
                <tr *ngIf="product.status == 'Active'" >
                    <td class="text-center	mt-3" colspan="2"><b i18n>Your next invoice</b></td>
                </tr>

                <tr *ngIf="product.status == 'Active'">
                    <td *ngIf="product.stripeStatus === 'canceled'" class="text-right" i18n>Subscription end date:</td>
                    <td *ngIf="product.stripeStatus !== 'canceled'" class="text-right" i18n>Next Invoice Date:</td>
                    <td>{{product.nextduedate}}</td>
                </tr>
                <tr *ngIf="product.status == 'Active'">
                    <td *ngIf="product.stripeStatus === 'canceled'" class="text-right" i18n>Final Bill Amount:</td>
                    <td *ngIf="product.stripeStatus !== 'canceled'" class="text-right" i18n>Next Billing Amount:</td>
                    <td *ngIf="product.stripeStatus === 'canceled'">$0 CAD</td>
                    <td *ngIf="product.stripeStatus !== 'canceled'">
                        {{product.currency.prefix}}{{ (product?.deductionamount > 0 ? 
                        this.product.pricing - this.product.deductionamount : this.product.pricing) | number : '1.2-2'}} {{product.currency.suffix}}
                    </td>
                </tr>
                <tr *ngIf="product.status == 'Active' && product.stripeStatus === 'canceled'" >
                    <td class="text-center	mt-3" colspan="2" i18n>* Overage charges will apply if occurred during this final subscription period.</td>
                </tr>
                
                <tr *ngIf="product.stripeStatus !== 'canceled' && product.pricing > 0 && product.deductionamount > 0">
                    <td class="text-right" i18n>Original Billing Amount:</td>
                    <td>
                        {{product.currency.prefix}}{{product.pricing | number : '1.2-2'}} {{product.currency.suffix}}
                    </td>
                </tr>
                <tr *ngIf="product.stripeStatus !== 'canceled' && product.pricing > 0 && product.deductionamount > 0">
                    <td class="text-right" i18n>Deduction Amount:</td>
                    <td>
                        -{{product.currency.prefix}}{{product.deductionamount| number : '1.2-2'}} {{product.currency.suffix}}
                    </td>
                </tr>
            </table>
        </div>
        <div class="mt-2" *ngIf="product && product.status=='Suspended' && this.user.roleId != code.roles.TEAM_BILLING_ADMIN" i18n>
            Service suspended (To reactivate your service, the most recent invoice must be paid.)
        </div>
        <div class="mt-2" *ngIf="product && product.status=='Suspended' && this.user.roleId == code.roles.TEAM_BILLING_ADMIN" i18n>
            Service suspended (To reactivate your service, all the outstanding invoices must be paid. Click <a routerLink="/invoice/list">here</a> to pay your invoices.)
        </div>
    </div>
</nz-spin>
<div class="plan-buttons" *ngIf="!userInfo.accountdetails.IsThisATeamProduct">
    <button nz-button nzType="primary" class="mr-2" [routerLink]="'/change-plan'" i18n>Manage Plan</button>
    <button nz-button nzType="primary" nzDanger (click)="showCancelModal = true"
    [disabled]="product.status != 'Active' || product.stripeStatus != 'active'" i18n>Cancel Plan</button>
</div>
<div class="plan-buttons" *ngIf="userInfo.accountdetails.IsThisATeamProduct && this.user.roleId == code.roles.TEAM_BILLING_ADMIN && product">
    <button nz-button nzType="primary" class="mr-2" [routerLink]="'/change-plan'" i18n>Manage Plan</button>
    <button [disabled]="product.status != 'Active' || product.stripeStatus != 'active'" (click)="showCancelModal = true" nz-button nzType="primary" nzDanger i18n>Cancel Plan</button>
</div>

<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showCancelModal" nzTitle="Confirm" (nzOnOk)="cancelPlan()"
(nzOnCancel)="showCancelModal = false" [nzOkLoading]="isSpinning">
<ng-container class="p-2" *nzModalContent>
    <div i18n>You will be cancelling your subscription which will end on {{product.nextduedate}}.
            After this date, your account will be suspended.
        If you have team members their accounts will be closed as well.
            If you want to come back, you will need to purchase a new subscription to activate your account.
    </div><br>
    <div i18n>
        If you have any questions please <a href="https://www.signority.com/support-centre/" target="_blank">contact us</a>
         and we hope to welcome you back soon!
    </div>
</ng-container>
</nz-modal>

