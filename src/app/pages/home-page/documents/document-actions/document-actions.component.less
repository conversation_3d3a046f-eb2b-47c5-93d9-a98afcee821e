// WCAG 1.4.4 – replace fixed pixel max-width with rem so it scales with text size.
// Assumption: base browser font-size = 16 px (1 rem). 300 px → 18.75 rem.
.max-300 {
    max-width: 18.75rem;
}
::ng-deep .custom-panel {
    max-width: fit-content !important;
    }

// WCAG 2.1 Level AA – utility class for screen-reader-only text
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

/* WCAG 1.4.3 contrast fix: darken primary button colour so white text meets ≥4.5:1 ratio
   against the button background. Ant Design/NZ-Zorro default primary blue (#017BC6)
   provides only ~3.2:1 contrast, which fails WCAG AA for normal-sized text. We override
   it locally for this component using ::ng-deep to avoid global visual changes. */ // Assumption: component background is white/light grey.
::ng-deep .ant-btn-primary {
  background-color: #055485;
  border-color: #055485;
}

/* Maintain sufficient contrast on hover/focus/active states while preserving visual hierarchy */ // Assumption: darker shade ensures contrast ≥4.5:1.
::ng-deep .ant-btn-primary:hover,
::ng-deep .ant-btn-primary:focus,
::ng-deep .ant-btn-primary:active {
  background-color: #055485;
  border-color: #055485;
}

// Added high-contrast borders, focus outlines and underline overrides for
// interactive controls inside the component (WCAG 1.4.11 – Non-text Contrast).
// Assumption: component background is light (≈ #ffffff) so #797E8A (grey 600)
// provides ≥ 3 : 1 contrast; #2D323D focus ring ≥ 3 : 1 on both white and
// light-grey modal backgrounds.

/* ng-zorro inputs (input, textarea) */
::ng-deep input[nz-input],
::ng-deep textarea[nz-input] {
  border: 1px solid #797E8A; // Assumption: default #d9d9d9 fails <3:1.
}

/* Angular Material form-field underline (default 0.12 opacity grey) */
::ng-deep .mat-form-field-underline,
::ng-deep .mat-form-field-underline::after {
  background-color: #797E8A !important; // Increase contrast to ≥3:1.
}

/* Unchecked radio / checkbox stroke colours */
::ng-deep .mat-radio-outer-circle,
::ng-deep .mat-checkbox-frame {
  border-color: #797E8A !important; // Darker outline for ≥3:1 contrast.
}

/* Default (non-primary) ant buttons use low-contrast #d9d9d9 borders. */
::ng-deep .ant-btn:not(.ant-btn-primary) {
  border: 1px solid #017BC6; // ≥3:1 contrast vs white; maintains hierarchy.
}

/* High-contrast keyboard focus indicator for all interactive elements */
::ng-deep input[nz-input]:focus-visible,
::ng-deep textarea[nz-input]:focus-visible,
::ng-deep .mat-select-trigger:focus-visible,
::ng-deep mat-select:focus-visible,
::ng-deep mat-radio-button:focus-visible,
::ng-deep .ant-btn:focus-visible,
::ng-deep button:focus-visible {
  outline: 3px solid #2D323D; // Visible blue ring consistent with site.
  outline-offset: 2px;
}

/* WCAG 1.4.10 – ensure modal tables and lists wrap content and never introduce horizontal scroll at 320 px viewport */ // Assumption: document titles or option labels may contain long unbroken strings.
.modal-table {
  width: 100%;
  table-layout: fixed;
  word-break: break-word;
}

ul {
  word-break: break-word;
  max-width: 100%;
}

/* WCAG 1.4.3 contrast fix: darken utility class used for inline error text so that
   the text meets the minimum 4.5:1 contrast ratio against white backgrounds. */ // Assumption: component background is white/light grey.
::ng-deep .text-red-500 {
  color: #D40D00; // Tailwind's default #ef4444 provides ~4:1 contrast only.
}

/* Added WCAG 1.4.12 text-spacing override: allow Material select controls to grow vertically
   when users increase line / word / letter spacing. The global stylesheet forces a fixed
   30 px height via `.material-select-height`, which can clip content and hide the bottom
   half of the label text at larger spacing values. We reset the height to `auto` so the
   control can expand naturally while preserving the original visual baseline through a
   `min-height`. */ // Assumption: root font-size ≈ 16 px, so 1.875 rem ≈ 30 px.
.material-select-height {
  height: auto !important;  /* remove rigid vertical constraint */
  min-height: 1.875rem;     /* retain previous minimum size (≈30 px) */
  overflow: visible;        /* ensure expanded text is not clipped */
  white-space: normal;      /* permit line wrapping */
  line-height: normal;      /* honour user line-height settings */
}

// WCAG 3.3.2 – style for the visual required asterisk so users can easily
// identify mandatory fields without relying on colour alone. Matches style
// used in other components for consistency.
.required {
  color: #c0392b; // Dark red ≥4.5:1 contrast vs white.
  font-weight: 700;
}

/* WCAG 1.4.12 fix: boost line-height for warning messages */
.warningBox{
    width: 100%;
    height: auto;
    // padding: 0.9375rem; // 15px (commented)
    box-sizing: border-box;
    margin-top: 0.3125rem; // 5px → 0.3125rem
    /* WCAG 1.4.3 contrast fix: darkened orange text colour (#d48806 → #965a00) to achieve ≥4.5:1 ratio on white background */ // Assumption: component background is white
    color: #965a00;
    line-height: 1.5em;
}

/* Document Actions Modal Styles */
.document-actions-modal {
  width: 90vw;
  max-width: 33.8rem; /* ≈520px – adjusted for correct pixel equivalent */
}

.document-actions-modal .mat-form-field-appearance-legacy .mat-form-field-flex {
  border: 1px solid #797E8A;
  border-radius: 4px;
  padding: 0 0.5rem;
}

.document-actions-modal .mat-form-field-appearance-legacy .mat-form-field-underline {
  display: none;
}

.document-actions-modal .mat-form-field-required-marker {
  display: none;
}

/* Override Tailwind mt-10 for modal content */
.document-actions-modal .mt-10 {
  margin-top: 0 !important;
}

/* Force override for Tailwind mt-10 - highest specificity */
.ant-modal .document-actions-modal .mt-10,
.ant-modal-content .document-actions-modal .mt-10,
.ant-modal-body .document-actions-modal .mt-10 {
  margin-top: 0 !important;
}

/* Per-component modal table column widths for document-actions modals */
.document-actions-modal .modal-table td:first-child {
  width: 33.33%;
  white-space: nowrap;
  
  /* Allow text wrapping on small displays to prevent text cropping */
  @media (max-width: 480px) {
    white-space: normal;
    word-wrap: break-word;
    hyphens: auto;
  }
}
.document-actions-modal .modal-table td:last-child {
  width: 66.66%;
}

/* Per-component modal table column widths for document-actions-table */
.document-actions-table td:first-child {
  width: 33.33%;
  white-space: nowrap;
  
  /* Allow text wrapping on small displays to prevent text cropping */
  @media (max-width: 480px) {
    white-space: normal;
    word-wrap: break-word;
    hyphens: auto;
  }
}
.document-actions-table td:last-child {
  width: 66.66%;
}
