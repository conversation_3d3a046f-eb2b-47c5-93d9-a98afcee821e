// WCAG 2.1 Level AA – no logic changes; file retained for completeness and accessibility context
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { Validators, FormBuilder } from '@angular/forms';
import { DocumentService } from "@core/services";
import { Code, tableType } from '@core/models';
import { environment } from '@environment/environment';


@Component({
  selector: 'app-document-actions',
  templateUrl: './document-actions.component.html',
  styleUrls: ['./document-actions.component.less'],
  encapsulation: ViewEncapsulation.Emulated
})
export class DocumentActionsComponent implements OnInit {

  @Input() documents:any[] =[];
  @Input() tableType:tableType = 'document';
  @Input() tableStatus:string | undefined = '';

  @Input() set action(action: string ) {
    this.isSpinning = false;
    
    // Safety check to prevent errors when documents array is empty
    if (!this.documents || this.documents.length === 0) {
      console.warn('DocumentActionsComponent: Action triggered but no documents available');
      return;
    }
    
    switch(action) {
      case 'requestAdditionalSignature': this.showRequestAdditionalSignatureModal = true;
       this.requestAdditionalSignatureForm.controls['title'].setValue( this.documents[0].title); break;
      case 'viewDocument':
      case 'signDocument': window.location.href='/UI/documentDesigner2.html?iid='+this.documents[0].iid; break;
      case 'cancelSigning': this.showCancelSigningModal = true; break;
      case 'rename': this.showRenameModal = true; break;
      case 'edit': window.location.href='/UI/documentDesigner2.html?id='+this.documents[0].id; break;
      case 'download.legacy': this.showLegacyDownloadModal = true; break;
      case 'download': this.showDownloadModal = true; break;
      case 'history': window.open('/UI/invitationStatus.html?id=' + this.documents[0].id +'&tab=HistoryContainer'); break;
      case 'delete': this.showDeleteModal = true; break;
      case 'move': this.showMoveModal = true; this.getFolders(); break;
      case 'duplicateToDocumentFromShared':
      case 'toDocFromRTemplate':
      case 'toDocFromTLink':
      case 'duplicate': this.tableType == 'document' || this.tableType == 'manage'  ? this.showDuplicateModal = true : this.duplicateDocument(); break;
      case 'duplicateTemplates': this.duplicateTemplates(); break;
      case 'toTLinkFromRTemplate':
      case 'toTLinkFromDoc': this.duplicateToTemplateLink(); break;
      case 'toRTemplateFromDoc': this.duplicateToRegularTemplate(this.code.DOCUMENT_TYPE_DOCUMENT); break;
      case 'toRTemplateFromTLink':
      case 'toRTemplateFromLink': this.duplicateToRegularTemplate(this.code.DOCUMENT_TYPE_TEMPLATE_LINK); break;
      case 'shareToTeam': this.documents.length > 1 ? this.showMultiShareModal = true : this.showShareModal = true;  break;
      case 'pushToAnother': this.showPushModal = true;  break;
      case 'recover': this.recoverDocuments();  break;
      case 'deletePermanently': this.showDeletePermanentlyModal = true;  break;
      case 'templateLinkReport': window.open('/UI/documentExport.html?id=' + this.documents[0].id); break;
    }
  }
  @Output() refreshTable = new EventEmitter<boolean>();

  public code:any = Code;
  public userSetting:any;
  public isSpinning:boolean = false;
  public folders: any[] = [];
  // request additional signatures form
  public showRequestAdditionalSignatureModal:boolean = false;
  public requestAdditionalSignatureForm = this.formBuilder.group({
    includeRecipients: [false],
    title: ['', Validators.required]
  });
  public requestAdditionalSignatureFormSubmitted:boolean = false;
  // cancel form
  public showCancelSigningModal:boolean = false;
  public cancelForm = this.formBuilder.group({
    reason: ['', Validators.required],
    type: ['cancel', Validators.required]
  });
  public cancelFormSubmitted:boolean = false;
  // rename form
  public showRenameModal:boolean = false;
  public renameForm = this.formBuilder.group({
    name: ['', Validators.required],
  });
  public renameFormSubmitted:boolean = false;
  // download form
  public showDownloadModal:boolean = false;
  public downloadForm = this.formBuilder.group({
    type: ['esign', Validators.required],
  });
  // Legacy download form
  public showLegacyDownloadModal:boolean = false;
  public legacyDownloadForm = this.formBuilder.group({
    audit: [true, Validators.required],
    fileType: ['pdf', Validators.required],
  });
  // Move to folder form
  public showMoveModal:boolean = false;
  public moveFormSubmitted:boolean = false;
  public moveForm = this.formBuilder.group({
    folder: ['', Validators.required],
  });
  // duplicate form
  public showDuplicateModal:boolean = false;
  public duplicateFormSubmitted:boolean = false;
  public duplicateForm = this.formBuilder.group({
    type: ['duplicateAsNew', Validators.required],
  });
  // push to another account form
  public showPushModal:boolean = false;
  public pushFormSubmitted:boolean = false;
  public pushForm = this.formBuilder.group({
    email: ['', [Validators.required, Validators.email]],
  });
  
  public showDeleteModal:boolean = false;
  public showShareModal:boolean = false;
  public showMultiShareModal:boolean = false;
  public showDeletePermanentlyModal:boolean = false;


  constructor(
    private formBuilder: FormBuilder,
    private documentService: DocumentService
    ) { }

  ngOnInit(): void {
    let user = localStorage.getItem("user");
    this.userSetting = JSON.parse(user || '0').userSetting;
  }

  cancelSign(){
    this.cancelFormSubmitted = true;
    if (!this.cancelForm.invalid) {
      this.isSpinning = true;
      let reason = this.cancelForm.controls['reason'].value.replace(/^\s+|\s+$/g, '');
      this.documents[0].reason = $localize`Reason: ` + reason;
      this.documents[0].signingStatus = this.code.SIGNING_STATUS_SIGNING_CANCELED;
      this.documents[0].cancelType = this.cancelForm.controls['type'].value;
      this.documents[0].sendCancellationEmail = this.documents[0].cancelType == "cancel.nomail" ? false : true ;
  
      this.documentService.updateDocuments([this.documents[0]]).subscribe(val=>{
        this.isSpinning = false;
        //open new doc if cancel and resend
        if(val.newDoc && (this.cancelForm.controls['type'].value == 'cancel.copyToDraft' || this.cancelForm.controls['type'].value == 'cancel.copyToDraftWithContent')) {
          window.location.href = '/UI/documentDesigner2.html?id=' + val.newDoc;
        }
        else {
          this.refreshTable.emit(true);
          this.cancelFormSubmitted = false;
          this.showCancelSigningModal = false;
          this.cancelForm.reset({type: 'cancel'});
        }
      })
    }
  }

  renameDocument(){
    this.renameFormSubmitted = true;

    if (!this.renameForm.invalid) {
      this.isSpinning = true;
      this.documents[0].title = this.renameForm.controls['name'].value;
      this.documentService.updateDocuments([this.documents[0]]).subscribe({
        next:()=>{
        this.renameFormSubmitted = false;
        this.showRenameModal = false;
        this.renameForm.reset();
        this.refreshTable.emit(true);
      },
      complete:()=>{
        this.isSpinning = false;
      }
    })
    }
  }
  downloadDocuments() {
    let docs = this.documents;
    let downloadSeparate = "";
    let signDocWithAuditTrail	= "";
    let statusCompleted	= false;
    let hasDigitalSign = "";
    let downloadtype = this.downloadForm.controls['type'].value

    for (var i = 0, len = docs.length; i < len; i++) {
      downloadSeparate 		= docs[i].DownloadSeparateFilesEnabled;
      signDocWithAuditTrail 	= docs[i].ESignDocWithAuditTrailEnabled;
      statusCompleted 		= (docs[i].signingStatus === this.code.SIGNING_STATUS_COMPLETED);
      hasDigitalSign 			= docs[i].hsmDigitalSignEnabled && statusCompleted;

        if (downloadtype == "esign") {
          downloadtype = hasDigitalSign ? 'dsign' : (statusCompleted ? (signDocWithAuditTrail ? 'esignwithaudit' : 'esign') : 'esign');
        } else if (downloadtype == "archive") {
          if (statusCompleted && docs[i].DocumentFileNumbers > 1 && downloadSeparate) {
            downloadtype = hasDigitalSign ? 'archivedigital' : 'archive';
          } else {
            // if files.length <=1, reture combined PDF. be same as the above "esign"
            downloadtype = hasDigitalSign ? 'dsign' : (statusCompleted ? (signDocWithAuditTrail ? 'esignwithaudit' : 'esign') : 'esign');
          }
        }
        window.open(environment.API_URL + 'v1/documents/' + docs[i].id + '/download/?content=' + downloadtype);
    }
    this.showDownloadModal = false;

  }

  duplicateDocument() {
    this.isSpinning = true;
    let body;
    if (this.tableType == 'document' || this.tableType == 'manage') {
      let type = this.duplicateForm.controls['type'].value;
      let duplicateAsIs = false;
      let duplicateAsNew = false;
      let duplicateAsTemplate = false;

      if (type === "duplicateAsIs") {
        duplicateAsIs = true;
      } else if (type === "duplicateAsNew") {
        duplicateAsNew = true;
      } else if (type === "duplicateAsTemplate") {
        duplicateAsTemplate = true;
      }

      body = {
        documents: this.documents,
        returnSimpleObject: false,
        duplicateAsIs: duplicateAsIs,
        duplicateAsNew: duplicateAsNew,
        duplicateAsTemplate: duplicateAsTemplate,
        autoShare: this.userSetting.ShareAutomaticallyDocToTeam
      }
    }
    else if (this.tableType == 'template') {
      body = {
        documents: this.documents,
        returnSimpleObject: false,
        duplicateAsIs: false,
        duplicateAsNew: false,
        mergeDocuments: false,
        autoShare: this.userSetting.ShareAutomaticallyDocToTeam
      }
    }

    this.documentService.duplicateDocuments(body).subscribe((data) => {
      data.documents.forEach((document:any, index:number) => {

        if (document.documentType == this.code.DOCUMENT_TYPE_TEMPLATE_LINK) {
          document.invitations[0].actionType = this.code.INVITATION_TYPE_SIGNER;
          document.invitations[0].recipientEmail = "";

          for (var j = 0; j < (document.invitations).length; j++) {
            if (document.invitations[j].isPlaceHolder) {
              document.invitations[j].isPlaceHolder = false;
            }
          }
        }

        //fix bug 3028---Duplicate shared bulk sign as my document- bulk sign recipient list not deleted
        if (document.documentType == this.code.DOCUMENT_TYPE_BULK_SIGN_TEMPLATE) {
          for (var j = 0; j < (document.invitations).length; j++) {
            if (document.invitations[j].isBulkSigner) {
              document.invitations[j].isBulkSigner = false;
              document.invitations[j].recipientEmail = "";
              document.invitations[j].recipientName = "Role " + (j + 1);
            }
            else {
              document.invitations[j].recipientName = "Role " + (j + 1);
            }
          }
        }

        document.documentType = this.code.DOCUMENT_TYPE_DOCUMENT;

      })

      this.documentService.updateDocuments(data.documents).subscribe(()=>{ 
        this.refreshTable.emit(true);
        this.isSpinning = false;
      })


      this.duplicateForm.reset();
      this.showDuplicateModal = false;
    })
  }

  duplicateTemplates()
	{ 		
    let body = {
      documents: this.documents,
      returnSimpleObject: false,
      duplicateAsIs: false,
      duplicateAsNew: false,
      mergeDocuments: false,
      autoShare: this.userSetting.ShareAutomaticallyDocToTeam
    }
    
  this.documentService.duplicateDocuments(body).subscribe((data) => {
    this.refreshTable.emit(true);
  })
	
  }


  duplicateToTemplateLink() {
    let body = {
      documents: this.documents,
      returnSimpleObject: false,
      duplicateAsIs: false,
      duplicateAsNew: false,
      duplicateAsTemplate: true,
      autoShare: this.userSetting.ShareAutomaticallyTemplateToTeam
    }
    this.documentService.duplicateDocuments(body).subscribe((data) => {
      let documents = data.documents;
      for (var i = 0; i < documents.length; i++) {
        documents[i].documentType = this.code.DOCUMENT_TYPE_TEMPLATE_LINK;
        for (var j = 0; j < documents[i].invitations.length; j++) {
          documents[i].invitations[j].sequence++;

          //use for loop to replace signing host to signer
          if (documents[i].invitations[j].actionType == this.code.INVITATION_TYPE_SIGNING_HOST) {
            documents[i].invitations[j].actionType = this.code.INVITATION_TYPE_SIGNER;
          }
          if (documents[i].invitations[j].actionType == this.code.INVITATION_TYPE_EDITOR) {
            documents[i].invitations.splice(j,1);
          }
        }
        var linkSignerInvitation = {
          recipientName: $localize`Initial Link Signer`,
          recipientEmail: '<EMAIL>',
          actionType: this.code.INVITATION_TYPE_OPEN_SIGNER,
          sequence: 1,
        };
        
        if (documents[i].invitations.length == 0)
				{
					documents[i].invitations.unshift(linkSignerInvitation);//Add link signer
				}
				else
				{
					if (this.documents[0].documentType == this.code.DOCUMENT_TYPE_REGULAR_TEMPLATE)
					{
						documents[i].invitations[0].recipientName = $localize`Initial Link Signer`
						documents[i].invitations[0].recipientEmail = '<EMAIL>';
						documents[i].invitations[0].actionType = this.code.INVITATION_TYPE_OPEN_SIGNER;
						documents[i].invitations[0].isPlaceHolder = true;
						documents[i].invitations[0].sequence = 1;
					}
					else
					{
						documents[i].invitations.unshift(linkSignerInvitation);//Add link signer
					}
				}

      }
      this.documentService.updateDocuments(documents).subscribe(()=>{
        this.refreshTable.emit(true);
      })
    })

  }

  duplicateToRegularTemplate(fromType:string) {
    let body = {
      documents: this.documents,
      returnSimpleObject: false,
      duplicateAsIs: false,
      duplicateAsNew: false,
      duplicateAsTemplate: true,
      autoShare: this.userSetting.ShareAutomaticallyTemplateToTeam
    }
    this.documentService.duplicateDocuments(body).subscribe((data) => {
      let documents = data.documents;
      for (var i = 0; i < documents.length; i++) {
        //duplicate template link to regular template
        if (fromType == this.code.DOCUMENT_TYPE_TEMPLATE_LINK) {
            //change initial link signer to normal role
            documents[i].invitations[0].actionType = this.code.INVITATION_TYPE_SIGNER;
            documents[i].invitations[0].recipientEmail = "";
            documents[i].invitations[0].recipientName = 'Role 1'

            //if in a folder, add copied template to the same folder
            if(this.tableStatus && this.tableStatus.startsWith('folder_')) {
              documents[i].folderId = Number(this.tableStatus.split('_')[1])
            }

            for (var j = 0; j < (documents[i].invitations).length; j++) {
                if (documents[i].invitations[j].isPlaceHolder) {
                    documents[i].invitations[j].isPlaceHolder = false;
                }
            }
        }
        //duplicate document to regular template
        if (fromType == this.code.DOCUMENT_TYPE_DOCUMENT) {
            for (var j = 0; j < (documents[i].invitations).length; j++) {
                //use for loop to replace signing host to signer
                if (documents[i].invitations[j].actionType == this.code.INVITATION_TYPE_SIGNING_HOST) {
                    documents[i].invitations[j].actionType = this.code.INVITATION_TYPE_SIGNER;
                }
            }
        }

        documents[i].documentType = this.code.DOCUMENT_TYPE_REGULAR_TEMPLATE;
    }
      this.documentService.updateDocuments(documents).subscribe(()=>{
        this.refreshTable.emit(true);
      })
    })

  }

  legacyDownloadDocuments(){
    let docs = this.documents;
    let idstr = "";
    for (let i = 0, len = docs.length; i < len; i++) {
        idstr += ',' + docs[i].id;
    }

    idstr = idstr.substring(1);
    let format = this.legacyDownloadForm.controls['fileType'].value;
    let audit = this.legacyDownloadForm.controls['audit'].value;
    window.open(environment.API_URL + 'v1/documents/' + idstr + '/content/' + format + '?withCertificate=' + audit, '_blank');
    this.showLegacyDownloadModal = false;
  }

  deleteDocuments() {
    this.isSpinning = true;
    this.documentService.deleteDocuments(this.documents).subscribe(()=>{
      this.refreshTable.emit(true);
      this.isSpinning = false;

      this.showDeleteModal = false;
      
    })
  }

  moveDocuments() {
    this.moveFormSubmitted = true;
    this.isSpinning = true;
    if (!this.moveForm.invalid) {
      this.documents.forEach(doc=>{
        doc.folderId = this.moveForm.controls['folder'].value;
      })
      this.documentService.updateDocuments(this.documents).subscribe(val=>{
        this.documentService.changeFolder(this.moveForm.controls['folder'].value)
        this.renameFormSubmitted = false;
        this.showMoveModal = false;
        this.moveForm.reset();
        this.moveForm.get('folder')?.setValue(this.tableType == 'document' ? '999999' : this.folders[0].id);
        this.isSpinning = false;
      })
    }

  }

  requestAdditionalSignature() {
    this.requestAdditionalSignatureFormSubmitted = true;
    this.isSpinning = true;
    if (!this.requestAdditionalSignatureForm.invalid) {
      let body = {
        documents: this.documents,
        returnSimpleObject: false,
        duplicateAsIs: false,
        duplicateAsNew: false,
        autoShare: false,
        keepFinalizedTags: true
      }

      this.documentService.duplicateDocuments(body).subscribe((res) => {
        for (var i = 0; i < res.documents.length; i++) {
          let newInvitations: any = []
          //remove invitations
          if (!this.requestAdditionalSignatureForm.controls['includeRecipients'].value) {
            newInvitations = [];
          }
          else if (res.documents[i].files) {
            res.documents[i].invitations.forEach((invitation: any) => {
              //set invitations to 'viewer'
              invitation.actionType = 16;
              invitation.seal = -1;
              invitation.additionalSignViewer = true;
              //we don't want duplicate viewers
              if (!newInvitations.some((inv: any) => { return invitation.recipientEmail == inv.recipientEmail })) {
                newInvitations.push(invitation);
              }
            })
          }
          res.documents[i].title = this.requestAdditionalSignatureForm.controls['title'].value;
          res.documents[i].invitations = newInvitations;
          res.documents[i].sharedToTeam = this.documents[i].sharedToTeam;
          res.documents[i].tags = [];
          let newDoc = res.documents[i];
          //remove tags, then update document
          this.documentService.updateTags(newDoc.id, []).subscribe(() => {
            this.documentService.updateDocuments([newDoc]).subscribe(() => {
              this.showRequestAdditionalSignatureModal = false;
              this.requestAdditionalSignatureFormSubmitted = false;
              this.requestAdditionalSignatureForm.reset();
              this.isSpinning = false;
              window.location.href = '/UI/documentDesigner2.html?id=' + res.documents[0].id;
            })
          })
        }
      })
    }
  }

  getFolders() {
    this.documentService.getFolders(this.tableType == 'template').subscribe(data=>{
      this.folders = data.folders;
      this.moveForm.get('folder')?.setValue(this.tableType == 'document' ? '999999' : this.folders[0]?.id);
    })
  }


  shareUnshareSingleDoc() {
    if (!this.documents || this.documents.length === 0) {
      console.warn('DocumentActionsComponent: Cannot share/unshare - no documents available');
      return;
    }
    
    this.documents[0].sharedToTeam = !this.documents[0].sharedToTeam;
    this.documentService.updateDocuments(this.documents).subscribe(()=>{
      this.showShareModal = false;
      this.refreshTable.emit(true);
    })
  }
  shareUnshareMultiDoc(share:boolean) {
    this.documents.forEach(document=>{
      document.sharedToTeam = share;
    })
    this.documentService.updateDocuments(this.documents).subscribe(()=>{
      this.showMultiShareModal = false;
      this.refreshTable.emit(true);
    })
  }

  pushToAnotherUser() {
    this.pushFormSubmitted = true;
    if (!this.pushForm.invalid) {
      this.documentService.pushToAnotherUser(this.documents,  this.pushForm.controls['email'].value).subscribe(val=>{
        if(val.success) {
          this.showPushModal = false;
          this.pushFormSubmitted = false;
          this.pushForm.reset();
          this.refreshTable.emit(true);
        }
      })
    }
  }

  recoverDocuments() {
    this.documents.forEach(document => {
      document.deleted = false;
			
			// package support
			if (document.documents && document.documents.length > 0)
			{
				for (var j = 0; j < document.documents.length; j++)
				{
					document.documents[j].deleted = false;
				}
			}
    })
    this.documentService.restoreDocuments(this.documents).subscribe(()=>{
      this.refreshTable.emit(true);
    })
  }

  deletePermanently() {
    this.documentService.deleteDocumentsPermanently(this.documents).subscribe(()=>{ 
      this.showDeletePermanentlyModal = false;
      this.refreshTable.emit(true);
    })
  }

}
