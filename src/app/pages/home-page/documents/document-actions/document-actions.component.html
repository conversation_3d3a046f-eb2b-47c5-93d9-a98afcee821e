<!-- WCAG 2.1 Level AA – added explicit labels/ids, merged duplicate class attributes, grouped related controls and marked layout tables as presentational for improved semantics and programmatic relationships. -->
<!-- Orientation Fix (WCAG 2.1 – 1.3.4): set modal width to responsive 90vw so dialogs fit within the viewport in both portrait and landscape orientations. // Assumption: 90vw leaves comfortable margin on desktop while preventing horizontal scroll on mobile. -->
<!-- WCAG 1.4.13 – ensure each modal is dismissible via ESC key (keyboard) and click outside (mask) -->
<!-- WCAG 2.4.6 – replace generic “OK” button label with descriptive action so purpose is clear out of context -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Cancel Signing" [(nzVisible)]="showCancelSigningModal" 
nzTitle="Cancel Signing of this Document" (nzOnOk)="cancelSign()" (nzOnCancel)="showCancelSigningModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <!-- Updated wording to remove reliance on spatial cue "below" per WCAG 1.3.3 (Sensory Characteristics) -->
        <div class="mt-2" i18n>Enter the reason for cancelling the document in the "Reason for Cancelling" field. This will appear in the document
            history and audit trail.</div>
        <form [formGroup]="cancelForm">
            <!-- WCAG 4.1.1 – wrapped table rows in <tbody> so <tr> elements are not direct
                 children of <table>, ensuring valid, fully-parsable HTML structure. -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <!-- WCAG 3.3.2 – indicate mandatory field visually and programmatically so all users understand that
                             a value is required before form submission. -->
                        <label for="cancelReason" i18n>Reason for Cancelling <span class="required" aria-hidden="true">*</span></label>
                    </td>
                    <td>
                        <!-- WCAG 3.3.1 Error Identification: expose validation state via aria-invalid
                             and programmatically associate error message with the textarea via
                             aria-describedby so assistive technology announces the message when
                             it appears. // Assumption: `cancelFormSubmitted` flag indicates the
                             form has been attempted, matching existing validation workflow. -->
                            <textarea id="cancelReason" i18n-aria-label aria-label="Reason for Cancelling" required aria-required="true"
                            [style.borderColor]="cancelFormSubmitted && cancelForm.controls['reason'].errors ? 'red':''"
                            [attr.aria-invalid]="cancelFormSubmitted && cancelForm.controls['reason'].invalid"
                            [attr.aria-describedby]="'cancel-reason-error'"
                            formControlName="reason" nz-input></textarea>
                        <!-- Keep element in DOM at all times to avoid broken IDREF issues when
                             aria-describedby is evaluated. -->
                            <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                                 Provide actionable guidance with an example so the
                                 user understands *how* to correct the mistake. -->
                            <div id="cancel-reason-error" role="alert" class="text-red-500"
                                [hidden]="!(cancelFormSubmitted && cancelForm.controls['reason'].errors)" i18n>
                                Enter a brief reason such as <em>“Client requested cancellation”</em>
                            </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="cancelTypeSelect" i18n>Cancellation Type</label>
                    </td>
                    <td>
                        <mat-form-field class="material-select-height w-full"><!-- Assumption: merged previous duplicated class attrs -->
                            <mat-label i18n>Cancellation Type</mat-label>
                            <mat-select id="cancelTypeSelect" panelClass="custom-panel" name="Cancellation Type" formControlName="type" 
                            i18n-aria-label aria-label="Cancellation Type">
                                <mat-option value="cancel" i18n>Cancel the document and send an email notifying the signers</mat-option>
                                <mat-option value="cancel.nomail" i18n>Cancel the document and do not send an email to the signers</mat-option>
                                <mat-option value="cancel.copyToDraftWithContent" i18n>Cancel the document and create a copy to resend with added content</mat-option>
                                <mat-option value="cancel.copyToDraft" i18n>Cancel the document and create a copy to resend without added content</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>

<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Save Title" [(nzVisible)]="showRenameModal" 
nzTitle="Edit Title" (nzOnOk)="renameDocument()" (nzOnCancel)="showRenameModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <form [formGroup]="renameForm">
            <!-- WCAG 4.1.1 – add <tbody> wrapper to keep markup valid -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <label for="currentTitle" i18n>Current Title</label>
                    </td>
                    <td>
                        <input id="currentTitle" i18n-aria-label aria-label="Current Title" disabled="true" [value]="documents[0].title"  nz-input>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- WCAG 3.3.2 – required indicator added -->
                        <label for="newTitle" i18n>Enter a new title <span class="required" aria-hidden="true">*</span></label>
                    </td>
                    <td>
                        <!-- WCAG 3.3.1 – same pattern as above for rename title field -->
                        <input id="newTitle" [style.borderColor]="renameFormSubmitted && renameForm.controls['name'].errors ? 'red':''" required aria-required="true"
                            [attr.aria-invalid]="renameFormSubmitted && renameForm.controls['name'].invalid"
                            [attr.aria-describedby]="'rename-name-error'"
                            formControlName="name" nz-input i18n-aria-label aria-label="Enter a new title">
                        <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                             Add corrective hint with an illustrative example. -->
                        <div id="rename-name-error" role="alert" class="text-red-500"
                            [hidden]="!(renameFormSubmitted && renameForm.controls['name'].errors)" i18n>
                            Enter a descriptive title, e.g. <em>“Signed Contract v2”</em>
                        </div>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – replace default OK with specific label -->
<nz-modal nzClassName="document-actions-modal" i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Download" [(nzVisible)]="showDownloadModal" 
nzTitle="Download" (nzOnOk)="downloadDocuments()" (nzOnCancel)="showDownloadModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10" i18n>You have chosen to download</div>
        <ul class="list-disc ml-10">
            <li *ngFor="let document of documents">{{document.title}}</li>
        </ul>
        <form [formGroup]="downloadForm">
            <!-- WCAG 4.1.1 – ensure rows reside inside a <tbody> element -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <mat-form-field class="material-select-height w-full"><!-- Assumption: merged duplicate class attrs -->
                            <mat-label i18n class="sr-only">Download Type</mat-label>
                            <mat-select id="downloadTypeSelect" name="Download Type" formControlName="type" i18n-aria-label aria-label="Download Type">
                                <mat-option value="esign" i18n>Download (Single combined PDF)</mat-option>
                                <mat-option value="archive" i18n>Document (Separate PDFs)</mat-option>
                                <mat-option value="audit" i18n>Audit Trail</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – descriptive OK label for legacy download -->
<nz-modal nzClassName="document-actions-modal" i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Download" [(nzVisible)]="showLegacyDownloadModal" 
nzTitle="Download" (nzOnOk)="legacyDownloadDocuments()" (nzOnCancel)="showLegacyDownloadModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10" i18n>You have chosen to download</div>
        <ul class="list-disc ml-10">
            <li *ngFor="let document of documents">{{document.title}}</li>
        </ul>
        <form [formGroup]="legacyDownloadForm">
            <!-- WCAG 4.1.1 – added <tbody> for valid table structure -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <label for="auditTrailChk" class="align-middle">
                            <input id="auditTrailChk" i18n-aria-label aria-label="With audit Trail" class="ml-2 align-middle" type="checkbox"  formControlName="audit">
                            <span i18n>With audit Trail</span>
                        </label>
                    </td>
                </tr>
                <tr>
                    <td>
                        <fieldset role="group" aria-labelledby="fileTypeLegend"><!-- Grouping radios for programmatic relationship -->
                            <legend id="fileTypeLegend" class="sr-only" i18n>File Type</legend>
                            <mat-radio-group formControlName="fileType" i18n-aria-label aria-label="File Type">
                                <mat-radio-button class="mr-2" value="pdf">PDF</mat-radio-button>
                                <mat-radio-button value="zip">ZIP</mat-radio-button>
                            </mat-radio-group>
                        </fieldset>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>

<!-- WCAG 2.4.6 – make action explicit -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Delete" [(nzVisible)]="showDeleteModal" 
nzTitle="Confirm Delete" (nzOnOk)="deleteDocuments()" (nzOnCancel)="showDeleteModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10" i18n>Do you want to delete the selected document(s)? </div>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Create Copy" [(nzVisible)]="showRequestAdditionalSignatureModal" 
nzTitle="Copy for Additional Signatures" (nzOnOk)="requestAdditionalSignature()" (nzOnCancel)="showRequestAdditionalSignatureModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
            <div class="mt-10 " i18n>Need to add more signatures?
                Signority can create a copy of this completed document, with all signatures embedded, for you to get signed again.
                <br>
                Create a copy of this completed document for signing? </div>
            <form [formGroup]="requestAdditionalSignatureForm">
                <!-- WCAG 4.1.1 – wrap <tr> elements inside <tbody> -->
                <table class="modal-table document-actions-table" role="presentation">
                  <tbody>
                    <tr>
                        <td>
                            <label for="includeRecipientsChk"><input id="includeRecipientsChk" class="ml-2 align-middle" type="checkbox"  formControlName="includeRecipients" 
                                i18n-aria-label aria-label="Include recipients"><span class="ml-1" i18n>Include recipients:</span></label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <!-- WCAG 3.3.2 – indicate required -->
                            <label for="newDocTitle" i18n>New document title: <span class="required" aria-hidden="true">*</span></label>
                        </td>
                        <td>
                            <!-- WCAG 3.3.1 – expose validation error programmatically for new doc title -->
                                <input id="newDocTitle" [style.borderColor]="requestAdditionalSignatureFormSubmitted && requestAdditionalSignatureForm.controls['title'].errors ? 'red':''" required aria-required="true"
                                [attr.aria-invalid]="requestAdditionalSignatureFormSubmitted && requestAdditionalSignatureForm.controls['title'].invalid"
                                [attr.aria-describedby]="'request-title-error'"
                                formControlName="title" nz-input i18n-aria-label aria-label="New document title">
                            <div id="request-title-error" role="alert" class="text-red-500"
                                [hidden]="!(requestAdditionalSignatureFormSubmitted && requestAdditionalSignatureForm.controls['title'].errors)" i18n>
                                Please enter a title
                            </div>
                        </td>
                    </tr>
                  </tbody>
                </table>
            </form>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal nzClassName="document-actions-modal" i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Move" [(nzVisible)]="showMoveModal" 
nzTitle="Move Document" (nzOnOk)="moveDocuments()" (nzOnCancel)="showMoveModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10 " i18n>Please select a destination folder</div>
        <form [formGroup]="moveForm">
            <!-- WCAG 4.1.1 – added <tbody> to preserve syntactic correctness -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <mat-form-field class="material-select-height w-full"><!-- merged duplicate classes -->
                            <mat-label i18n class="sr-only">Destination Folder</mat-label>
                            <mat-select id="destinationFolderSelect" name="Please select a destination folder" formControlName="folder" 
                            i18n-aria-label aria-label="Please select a destination folder">
                                <mat-option *ngIf="tableType == 'document'" value="999999" i18n>Home Folder</mat-option>
                                <mat-option *ngFor="let folder of folders" [value]="folder.id" >{{folder.text}}</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal nzClassName="document-actions-modal" i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Copy" [(nzVisible)]="showDuplicateModal" 
nzTitle="Copy Document" (nzOnOk)="duplicateDocument()" (nzOnCancel)="showDuplicateModal = false" [nzOkLoading]="isSpinning" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10 " i18n>How would you like to copy</div>
        <ul class="list-disc ml-10">
            <li *ngFor="let document of documents">{{document.title}}</li>
        </ul>
        <form [formGroup]="duplicateForm">
            <!-- WCAG 4.1.1 – ensure parser-valid structure with <tbody> wrapper -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <mat-form-field class="material-select-height w-full"><!-- merged duplicate classes -->
                            <mat-label i18n class="sr-only">Copy type</mat-label>
                            <mat-select id="copyTypeSelect" name="Copy type" formControlName="type" i18n-aria-label aria-label="Copy type">
                                <mat-option value="duplicateAsNew" i18n>Copy only the document files</mat-option>
                                <mat-option value="duplicateAsTemplate" i18n>Copy document files, tags, and workflow</mat-option>
                                <mat-option value="duplicateAsIs" i18n>Copy document files, tags, workflow and recipients</mat-option>
                            </mat-select>
                          </mat-form-field>
                    </td>
                </tr>
              </tbody>
            </table>
        </form>
    </ng-container>
</nz-modal>
<!-- WCAG 2.4.6 – dynamic descriptive title depending on current share state. -->
<!-- WCAG 2.4.6 – dynamic OK label matches current action -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel"  [(nzVisible)]="showShareModal" 
[nzTitle]="documents[0]?.sharedToTeam ? 'Unshare Document' : 'Share Document'" 
[nzOkText]="documents[0]?.sharedToTeam ? 'Unshare' : 'Share'" (nzOnOk)="shareUnshareSingleDoc()" (nzOnCancel)="showShareModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent >
        <div class="mt-10" *ngIf="!documents[0]?.sharedToTeam" i18n>
            Do you want to share the selected Document with your team? 
        </div>
        <div class="mt-10" *ngIf="documents[0]?.sharedToTeam" i18n>
            Do you want to unshare the selected Document with your team? 
        </div>
    </ng-container>
</nz-modal>
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showMultiShareModal" 
nzTitle="Share / Unshare Documents" [nzFooter]="multiShareFooter" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent >
        <div class="mt-10"  i18n>
            Do you want to share/unshare the selected Documents or Templates with your team?
        </div>
    </ng-container>
    <ng-template #multiShareFooter>
        <!-- WCAG 2.4.6 – provide explicit accessible names for controls so their purpose is clear when encountered out of context -->
        <!-- Added explicit type="button" attributes to prevent implicit form submission and guarantee activation occurs on the _up_ event only (WCAG 2.5.2 Pointer Cancellation).  -->
        <button nz-button nzType="primary" type="button" (click)="shareUnshareMultiDoc(true)" i18n i18n-aria-label aria-label="Share selected documents">Share</button>
        <button nz-button nzType="primary" type="button" (click)="shareUnshareMultiDoc(false)" i18n i18n-aria-label aria-label="Unshare selected documents">Unshare</button>
        <button nz-button nzType="default" type="button" (click)="showMultiShareModal =false" i18n i18n-aria-label aria-label="Cancel and close dialog">Cancel</button>
      </ng-template>
</nz-modal>


<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Push" [(nzVisible)]="showPushModal" 
nzTitle="Push to Another User" (nzOnOk)="pushToAnotherUser()" (nzOnCancel)="showPushModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <form [formGroup]="pushForm">
            <!-- WCAG 4.1.1 – wrap rows within <tbody> to maintain valid markup -->
            <table class="modal-table document-actions-table" role="presentation">
              <tbody>
                <tr>
                    <td>
                        <label for="targetUserEmail" i18n>Target User's Email:</label>
                    </td>
                    <td>
                        <!-- Added semantic type and autocomplete for email per WCAG 2.1 SC 1.3.5 Identify Input Purpose // Assumption: Field collects a personal email address. -->
                        <!-- WCAG 3.3.1 – error association for target email field -->
                        <input id="targetUserEmail" type="email" autocomplete="email" [style.borderColor]="pushFormSubmitted && pushForm.controls['email'].errors ? 'red':''"
                            [attr.aria-invalid]="pushFormSubmitted && pushForm.controls['email'].invalid"
                            [attr.aria-describedby]="'push-email-error'"
                            formControlName="email" nz-input i18n-aria-label aria-label="Target User's Email">
                        <div id="push-email-error" role="alert" class="text-red-500"
                            [hidden]="!(pushFormSubmitted && pushForm.controls['email'].errors)" i18n>
                            Please enter a valid email address
                        </div>

                    </td>
                </tr>
              </tbody>
            </table>
        </form>
        <div i18n>
            These documents will not exist in your folder any more. Are you sure you want to push the selected document(s) to the target user?
        </div>
    </ng-container>
</nz-modal>

<!-- Removed stray text node that rendered outside of any element and could introduce unexpected layout width, potentially causing horizontal scroll on small viewports (WCAG 1.4.10 Reflow). -->
<!-- WCAG 2.4.6 – descriptive OK label -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle i18n-nzOkText nzOkText="Delete Permanently" [(nzVisible)]="showDeletePermanentlyModal" 
nzTitle="Confirm Permanent Delete" (nzOnOk)="deletePermanently()" (nzOnCancel)="showDeletePermanentlyModal = false" [nzWidth]="'90vw'" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- orientation width -->
    <ng-container *nzModalContent>
        <div class="mt-10" i18n>Are you sure you want to permanently delete the document(s)? Your documents cannot be recovered. </div>
    </ng-container>
</nz-modal>
