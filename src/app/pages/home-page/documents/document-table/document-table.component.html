
<!-- Accessibility: Added alt attribute to secure lock image and improved icon semantics (aria-hidden/aria-label) for WCAG 1.1.1 compliance -->
<!-- Added explicit type="button" on interactive buttons to ensure activation occurs on up-event only and prevent implicit form submission (WCAG 2.5.2 Pointer Cancellation) -->
<!-- WCAG 2.1 – added caption for data table and scope/labels for header cells to improve table semantics (1.3.1) -->
<!-- Updated modal widths to responsive 90vw for WCAG 2.1 – 1.3.4 (Orientation) compliance -->
<!-- WCAG 2.1 – 1.3.3 (Sensory Characteristics):
     Reworded aria-labels that previously relied on spatial orientation (e.g. “top”, “bottom”,
     “up”, “down”). Labels now describe the resulting list position without referencing
     visual location so users who cannot perceive layout still understand the action. -->
<!-- CHANGE (2025-08-06): WCAG 4.1.2 – supplied explicit role="button"/role="link" and keyboard
     handlers (Enter/Space) to custom interactive elements implemented with <a>, <span>
     and <td> so that assistive technology can correctly expose Name, Role and Value. -->
<div class="top-bar">
    <div aria-live="polite" class="flex items-center">
    
        <div class="top-sub-bar" [style.visibility]="setOfCheckedId.size >= 1 ? 'visible' : 'hidden'">
            <!-- WCAG 4.1.2 – expose correct role for custom anchor acting as a button -->
            <a tabindex="0" role="button" class="top-text" [style.display]="item.class" *ngFor="let item of documentMenu | slice:0:3; trackBy: trackByFn"
                (click)="setCurrentAction(item.actionName)"
                (keydown.enter)="setCurrentAction(item.actionName)"
                (keydown.space)="setCurrentAction(item.actionName); $event.preventDefault()">
                <!-- Assumption: list menu icon is decorative and already accompanied by visible text -->
                <span *ngIf="item.iconClass" class="mr-1" nz-icon [nzType]="item.iconClass" nzTheme="outline" aria-hidden="true"></span>
                <div>{{item.text}}</div>
            </a>
            <!-- WCAG 4.1.2 – provide semantic role for menu trigger implemented as <a> -->
            <a tabindex="0" role="button" class="top-text" *ngIf="documentMenu.length > 3" [matMenuTriggerFor]="moreActionsDropdown"
                i18n-aria-label aria-label="More document actions">
                ...&nbsp;More
            </a>
        </div>
    </div>
    <mat-menu  #moreActionsDropdown="matMenu">
          <!-- Added explicit type attribute for consistency with WCAG 2.5.2 preventative measure -->
          <button mat-menu-item type="button" *ngFor="let item of documentMenu| slice:3" (click)="setCurrentAction(item.actionName)" [style.display]="item.class" i18n>
            <i [class]="item.iconClass"></i>{{item.text}}
          </button>
    </mat-menu>


    <div *ngIf="(tableStatus == 'SharedTeamDocumentsSiderBar' || tableStatus == '' || tableStatus == 'template_link' ||
    tableStatus == 'bulk_sign_template' || tableStatus == 'shared') && tableType != 'inbox'"
    class="search-bar">
        <!-- Added autocomplete="off" to search input for WCAG 2.1 SC 1.3.5 Identify Input Purpose
             // Assumption: field is a generic search box and does not gather personal user data. -->
        <!-- WCAG 3.3.2 – provide an explicit label so the purpose of the input is
             available to all users even when placeholder text disappears. The
             visually-hidden label preserves the existing layout while exposing a
             persistent textual cue. // Assumption: tailwind's `sr-only` utility
             is available globally (already used elsewhere in code base). -->
        <label class="sr-only" for="documentsSearchInput" i18n>Search documents</label>
        <nz-input-group nzSearch (keyup.enter)="emitData()" [nzAddOnAfter]="suffixIconButton">
            <!-- WCAG 2.4.6 – clarify the purpose of the search control so users understand it filters documents -->
            <input id="documentsSearchInput" [(ngModel)]="searchValue" type="search" nz-input autocomplete="off" i18n-placeholder placeholder="Title, Recipient, Email "
            i18n-aria-label aria-label="Search documents" />
        </nz-input-group>
        <ng-template #suffixIconButton>
            <!-- WCAG 2.4.6 – descriptive label clarifies what will be searched -->
            <!-- Added explicit type attribute to prevent implicit form submission when keyboard users press Enter (WCAG 2.5.2 – pointer cancellation safety) -->
            <button class="flex items-center" type="button" i18n-aria-label aria-label="Search documents" nz-button nzType="primary" (click)="emitData()" nzSearch>
                <!-- Assumption: search icon is decorative; button's aria-label conveys purpose -->
                <span nz-icon nzType="search" aria-hidden="true"></span>
            </button>
        </ng-template>
    </div>
</div>
<!-- <nz-table  #basicTable [nzData]="tableData" [nzPageSize]="pageSize" [nzShowPagination]="false"> -->
<!-- WCAG 1.4.10 – wrapped data table in horizontally-scrollable region so the page does not require two-dimensional scrolling on small viewports. Caption now gets an ID used as the accessible name for the region. -->
<div class="table-responsive" role="region" aria-labelledby="documentsTableCaption">
    <nz-table [nzShowTotal]="totalTemplate" #basicTable nzFrontPagination="false" [nzTotal]="tableTotal" [nzData]="tableData" [nzPageSize]="pageSize"
    [nzPageIndex]="pageIndex" (nzPageIndexChange)="changePageIndex($event)" nzShowSizeChanger="true" [nzPageSizeOptions]="[10,20,30]"
    (nzPageSizeChange)="changePageSize($event)" tabindex="-1">
        <!-- Assumption: visually-hidden caption keeps layout unchanged while exposing table purpose -->
        <caption id="documentsTableCaption" class="sr-only" i18n>Documents table – list of documents, their recipients, status and last modified time</caption>
    <thead >
        <tr>
            <th *ngIf="tableType != 'inbox' &&  tableStatus != 'purgequeue'" scope="col" i18n-aria-label aria-label="Select all documents" [nzChecked]="checked" [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)" (keydown.enter)="onAllChecked(!checked)" (keydown.space)="onAllChecked(!checked); $event.preventDefault()" role="checkbox" [attr.aria-checked]="checked ? 'true' : 'false'" tabindex="-1">
            </th>
            <th nz-dropdown nzTrigger="click" scope="col">
                <!-- Added type attribute for safer activation behaviour (see reasoning above) -->
                <button class="flex items-center cursor-pointer" type="button" [matMenuTriggerFor]="docTitleDropdown">
                    <span i18n>Document title</span>
                    <!-- Assumption: caret icon is decorative; button has descriptive text -->
                    <span  class="text-xs ml-1" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span>
                </button>
                <!-- CHANGE (2025-08-06): Added explicit aria-label containing visible label text to satisfy
                     WCAG 2.5.3 "Label in Name" – ensures the pop-up menu’s accessible name starts with
                     the on-screen text "Document title". // Assumption: menu purpose is to choose sort
                     order for the "Document title" column. -->
                <mat-menu #docTitleDropdown="matMenu" i18n-aria-label aria-label="Document title – sort options">
                      <button *ngFor="let item of titleSortList" mat-menu-item type="button" (click)="orderBy(item.key)">
                        {{item.text}}
                      </button>
                </mat-menu>
            </th>
            <th *ngIf="tableStatus == 'purgequeue'" scope="col" i18n>
                Days left
            </th>
            <th *ngIf="tableStatus == 'SharedTeamDocumentsSiderBar' || tableStatus == 'shared' || this.tableType == 'manage'" scope="col" i18n>
                Owner
            </th>
            <th nz-dropdown nzTrigger="click" scope="col">
                <button class="flex items-center cursor-pointer" type="button" [matMenuTriggerFor]="recipientSortDropdown" *ngIf="tableType != 'template'">
                    <span i18n>Recipients</span>
                    <!-- Assumption: caret icon is decorative; button has descriptive text -->
                    <span class="text-xs ml-1" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span>
                </button>
                <div class="flex items-center" *ngIf="tableType == 'template'">
                    <span i18n>Recipients</span>
                </div>
                <!-- CHANGE (2025-08-06): See rationale above – align accessible name with visible text. -->
                <mat-menu  #recipientSortDropdown="matMenu" i18n-aria-label aria-label="Recipients – sort options" >
                    <div *ngIf="tableType != 'template'">
                        <button *ngFor="let item of recipientSortList" mat-menu-item type="button" (click)="orderBy(item.key)">
                            {{item.text}}
                          </button>
                    </div>
                </mat-menu>
            </th>
            <th scope="col"
            *ngIf="tableStatus != 'SharedTeamDocumentsSiderBar'&& tableStatus != 'purgequeue' &&
              !mediaService.isMobile() && tableType != 'template' && this.tableType != 'manage'">
                <button class="flex items-center cursor-pointer" type="button" [matMenuTriggerFor]="docStatusDropdown">
                    <span i18n>{{displayDocStatusHeader}}</span>
                    <!-- Assumption: caret icon is decorative; button has descriptive text -->
                    <span *ngIf="!tableStatus.startsWith('folder_') && tableStatus != 'homeFolder'" class="text-xs ml-1" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span>
                </button>
                <!-- CHANGE (2025-08-06): Ensure dialog/menu accessible name begins with visible text to meet
                     WCAG 2.5.3. The variable value `displayDocStatusHeader` is interpolated in the visible
                     label, but we include a static fallback "Document status" that still contains the
                     critical visible words. -->
                <mat-menu #docStatusDropdown="matMenu" i18n-aria-label aria-label="Document status – filter options" >
                    <div *ngIf="!tableStatus.startsWith('folder_') && tableStatus != 'homeFolder'">
                      <button *ngFor="let item of docStatusFilterList" mat-menu-item type="button" 
                      (click)="filterDocStatus(item)">
                      <span class="status_circle inline-block" [style.background-color]="item.color"></span>
                        {{item.text}}
                        </button>
                    </div>
                </mat-menu>
            </th>
            <th scope="col" *ngIf="(tableStatus == 'SharedTeamDocumentsSiderBar' || tableStatus == 'purgequeue' || this.tableType == 'manage') && !mediaService.isMobile()">
                <span class="flex items-center">
                    <span i18n>{{displayDocStatusHeader}}</span>
                </span>
            </th>
            <th scope="col" *ngIf="tableType == 'inbox' && !mediaService.isMobile()" nz-dropdown nzTrigger="click" >
                <button class="flex items-center cursor-pointer" type="button" [matMenuTriggerFor]="recipientStatusDropdown">
                    <span i18n>{{displayRecipientStatusHeader}}</span>
                    <!-- Assumption: caret icon is decorative; button has descriptive text -->
                    <span class="text-xs ml-1" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span>
                </button>
                <!-- CHANGE (2025-08-06): Added aria-label for consistency with 2.5.3 requirements. -->
                <mat-menu #recipientStatusDropdown="matMenu" i18n-aria-label aria-label="Recipient status – filter options">
                      <button *ngFor="let item of recipientStatusFilterList" mat-menu-item type="button" (click)="filterRecipientStatus(item)">
                        {{item.text}}
                      </button>
                  </mat-menu>
            </th>
            <th nz-dropdown nzTrigger="click" scope="col">
                <button class="flex items-center cursor-pointer" type="button" [matMenuTriggerFor]="timeOrderDropdown">
                    <span i18n>{{displayDateHeader}}</span>
                    <!-- Assumption: caret icon is decorative; button has descriptive text -->
                    <span class="text-xs ml-1" nz-icon nzType="caret-down" nzTheme="outline" aria-hidden="true"></span>
                </button>
                <!-- CHANGE (2025-08-06): Added aria-label beginning with visible header text (usually
                     "Last Modified Time") to satisfy WCAG 2.5.3. -->
                <mat-menu #timeOrderDropdown="matMenu" i18n-aria-label aria-label="Last Modified Time – sort options">
                      <button *ngFor="let item of dateSortList" mat-menu-item type="button" (click)="orderBy(item.key); displayDateHeader = item.text">
                        {{item.text}}
                      </button>
                  </mat-menu>
            </th>
        </tr>
    </thead>
    <tbody>
        <tr *ngFor="let data of basicTable.data">
            <td *ngIf="tableType != 'inbox' &&  tableStatus != 'purgequeue'" [nzChecked]="setOfCheckedId.has(data.id)" [nzDisabled]="data.disabled"
            (nzCheckedChange)="onItemChecked(data.id, $event)" (keydown.enter)="onItemChecked(data.id, !setOfCheckedId.has(data.id))" (keydown.space)="onItemChecked(data.id, !setOfCheckedId.has(data.id)); $event.preventDefault()" role="checkbox" [attr.aria-checked]="setOfCheckedId.has(data.id) ? 'true' : 'false'" [attr.aria-label]="'Select document: ' + (data.displayTitle || data.title)" tabindex="-1"></td>
            <td class="break-word-title">
                <span class="flex items-center">
                    <!-- WCAG 4.1.1 – switched to ngClass to eliminate duplicate class attributes
                         // Assumption: conditional pointer styling should not overwrite the permanent flex layout class. -->
                    <!-- WCAG 4.1.2 – add role and keyboard handlers so custom span behaves like a link -->
                    <span class="flex items-center"
                          [ngClass]="{'cursor-pointer': tableStatus != 'SharedTeamDocumentsSiderBar' && tableStatus != 'shared'}"  
                     role="link" (click)="goToDocument(data)"
                     (keydown.enter)="goToDocument(data)" (keydown.space)="goToDocument(data); $event.preventDefault()"
                     [tabIndex]="this.tableStatus != 'SharedTeamDocumentsSiderBar' && tableStatus != 'shared' && tableStatus != 'trash' && tableType != 'inbox' ? 0 : -1">
                        <!-- Assumption: template type icon purely visual indicator, descriptive text follows -->
                        <span *ngIf="tableStatus == 'shared' || tableStatus == 'trash'" class="mr-2" nz-icon [nzType]="getTemplateTypeIcon(data)" nzTheme="outline" aria-hidden="true"></span>
                        <span [id]="data.id" class="min-w-5" [innerHtml] = "data.displayTitle || data.title"></span>
                        <img class="ml-1" *ngIf="data.hsmDigitalSignEnabled && tableType != 'template'" src=".\assets\images\certificate_lock.png" 
                        i18n-alt alt="Secure document signed with Digital Signatures" 
                        i18n-nzTooltipTitle nzTooltipTitle="Secure document signed with Digital Signatures" 
                        nzTooltipPlacement="right" nz-tooltip i18n-aria-label aria-label="Secure document signed with Digital Signatures"/>
                    </span>
                    <!-- Assumption: share icon conveys document sharing status; added aria-label for screen readers -->
                    <span *ngIf="data.sharedToTeam" nz-icon nzType="share-alt" nzTheme="outline" class="share-icon med-icon ml-1"
                    [nzTooltipTitle]="getSharedString(data.user.name)" nzTooltipPlacement="right" nz-tooltip
                    i18n-aria-label aria-label="Shared with team"></span>
                </span>
                <!-- WCAG 1.4.3 contrast fix: replace low-contrast coloured text with
                     colour-coded status dot plus high-contrast default text. -->
                <div *ngIf="mediaService.isMobile()" class="flex items-center">
                    <span [style.color]="documentStatusCodes[data.signingStatus].color">{{documentStatusCodes[data.signingStatus].text}}</span>
                    <!-- Assumption: cloud icon indicates retention status; tooltip text used as accessible label -->
                    <span  *ngIf="getRetentionToolTipText(data) as toolTip" nz-icon nzType="cloud" nzTheme="outline" class="med-icon ml-1" 
                    [nzTooltipTitle]="toolTip" nzTooltipPlacement="right" nz-tooltip [style.color]="getIconColor(data)"
                    [attr.aria-label]="toolTip"></span>
                </div>
            </td>
            <td *ngIf="tableStatus == 'purgequeue'" 
                [attr.aria-label]="'Days left: ' + getPurgeDaysLeft(data)"
                i18n>
                {{getPurgeDaysLeft(data)}}
            </td>
            <td *ngIf="tableStatus == 'SharedTeamDocumentsSiderBar' || tableStatus == 'shared' || this.tableType == 'manage'" 
                [attr.aria-label]="'Owner: ' + (userProduct.IsThisATeamProduct ? data.user.groupname + ' (' + data.user.name + ')' : data.user.name)"
                i18n>
                <span *ngIf="userProduct.IsThisATeamProduct">{{data.user.groupname}} <span class="italic" ><br>({{data.user.name}})</span></span>
                <span *ngIf="!userProduct.IsThisATeamProduct">{{data.user.name}}</span>
            </td>
            <!-- WCAG 4.1.2 – expose role/link semantics for interactive table cell -->
            <td [class]="tableStatus == '' ? 'cursor-pointer' : '' "
                (click)="goToStatus(data, 'RecipientsListContainer')">
                <!-- WCAG 4.1.2 – focusable div now exposes correct role semantics and keyboard handlers -->
                <div *ngIf="tableType != 'template'" role="link" (keydown.enter)="goToStatus(data, 'RecipientsListContainer')" (keydown.space)="goToStatus(data, 'RecipientsListContainer'); $event.preventDefault()" [tabIndex]="tableStatus == '' && tableType != 'inbox' ? 0 : -1 ">
                    <div *ngIf="data.firstRecipientName" class="recipients" [innerHtml]="data.firstRecipientName"></div>
                    <div *ngIf="!data.firstRecipientName" i18n>No recipients</div>
                    <div *ngIf="data.signerCount > 1" class="text-xs" i18n>+{{data.signerCount-1}} recipient(s)</div>
                </div>
                <div *ngIf="tableType == 'template'" [class]="tableStatus != 'trash' && tableStatus != 'shared'  ? 'cursor-pointer' : '' "
                [tabIndex]="tableStatus != 'trash' && tableStatus != 'shared'  ? 0 : -1 ">
                    {{data.signerCount ? data.signerCount : 0}} Roles
                </div>
                
            </td>
            <td *ngIf="!mediaService.isMobile() && tableType != 'template'" 
            [class]="tableStatus == '' || tableType == 'manage' ? 'cursor-pointer' : '' " 
            (click)="goToStatus(data, 'HistoryContainer')"> 
                <span class="flex items-center" role="link" (keydown.enter)="goToStatus(data, 'HistoryContainer')" (keydown.space)="goToStatus(data, 'HistoryContainer'); $event.preventDefault()" [tabIndex]="(tableStatus == '' || tableType == 'manage') && tableType != 'inbox' ? 0 : -1 ">
                    <span class="status_circle" [style.background-color]="documentStatusCodes[data.signingStatus].color"></span>
                    <span>{{documentStatusCodes[data.signingStatus].text}}</span>
                    <!-- Assumption: cloud icon indicates retention status; tooltip text used as accessible label -->
                    <span  *ngIf="getRetentionToolTipText(data) as toolTip" nz-icon nzType="cloud" nzTheme="outline" class="med-icon ml-1" 
                    [nzTooltipTitle]="toolTip" nzTooltipPlacement="right" nz-tooltip [style.color]="getIconColor(data)"
                    [attr.aria-label]="toolTip"></span>
                </span>
            </td>
            <td *ngIf="tableType == 'inbox' && !mediaService.isMobile()">{{recipientStatusCodes[data.invitationStatus]}}</td>
            <td role="presentation">
                <span class="flex-space">
                    <span [attr.aria-label]="'Last modified time: ' + (data.lastModifiedTime | date:'HH:mm yyyy-MM-dd')">{{data.lastModifiedTime | date:"HH:mm yyyy-MM-dd"}}</span>
                    <span *ngIf="tableStatus == 'template_link'">
                        <button class="published" type="button" *ngIf="data.isPublished" aria-haspopup="dialog"
                        (click)="showPublishModal = true; currentDocuments = [data]; isCurrentPublished = data.isPublished" i18n>PUBLISHED</button>
                        <button class="unpublished" type="button" *ngIf="!data.isPublished" aria-haspopup="dialog"
                        (click)="showPublishModal = true; currentDocuments = [data]; isCurrentPublished = data.isPublished" i18n>UNPUBLISHED</button>
                    </span> 
                    <span>
                        <button aria-label="Use template" type="button" class="useTemplate" *ngIf="data.documentType == code.DOCUMENT_TYPE_REGULAR_TEMPLATE && tableStatus != 'trash'"
                        (click)="currentDocuments = [data];!showMultiTemplateSelectModal ? showUseTemplateModal = true : useTemplate(); " i18n>USE&nbsp;TEMPLATE</button>
                    </span>
                    <span class="flex-space">
                        <!-- WCAG 4.1.2 – add role and keyboard handlers for icon-only anchor acting as button -->
                        <a *ngIf="data.isPublished && tableStatus=='template_link'" nz-icon nzType="link" nzTheme="outline" 
                        class="cursor-pointer" role="button" (click)="showTemplateLinkUrlModal = true; currentDocuments = [data]" 
                        (keydown.enter)="showTemplateLinkUrlModal = true; currentDocuments = [data]"
                        (keydown.space)="showTemplateLinkUrlModal = true; currentDocuments = [data]; $event.preventDefault()" 
                        i18n-aria-label aria-label="Template link info" aria-haspopup="dialog" tabindex="0"></a>
                        <button *ngIf="tableType != 'inbox' &&  tableStatus != 'purgequeue'" type="button" class="table-action" i18n-aria-label aria-label="Document actions menu"
                        (click)="clearChecked();getActionsMenu(data)"   [matMenuTriggerFor]="actionDropdown" 
                        (menuOpened)="focusFirstMenuItem(menuTrigger)" #menuTrigger="matMenuTrigger">
                            <span  nz-icon nzType="more" nzTheme="outline"></span>
                        </button>
                    </span>
                    <mat-menu #actionDropdown="matMenu">
                          <button mat-menu-item *ngFor="let item of documentMenu;  trackBy: trackByFn" type="button" (click)="setCurrentAction(item.actionName)"
                           [class]="item.class" #menuItem  i18n>
                            {{item.text}}
                          </button>
                    </mat-menu>
                    <div *ngIf="tableType == 'inbox'" class="ml-1">
                        <a tabindex="0" role="button" *ngIf="data.invitationType == this.code.INVITATION_TYPE_SIGNER &&
                        (data.invitationStatus == this.code.RECIPIENT_STATUS_WAIT_TO_OPEN ||
                         data.invitationStatus == this.code.RECIPIENT_STATUS_WORK_IN_PROGRESS); else signDocument"  
                         (click)="setCurrentAction('signDocument'); currentDocuments = [data]"
                         (keydown.enter)="setCurrentAction('signDocument'); currentDocuments = [data]"
                         (keydown.space)="setCurrentAction('signDocument'); currentDocuments = [data]; $event.preventDefault()" i18n>
                            Sign Document
                        </a>
                        <ng-template #signDocument   i18n>
                            <a tabindex="0" role="button"  (click)="setCurrentAction('viewDocument'); currentDocuments = [data]"
                               (keydown.enter)="setCurrentAction('viewDocument'); currentDocuments = [data]"
                               (keydown.space)="setCurrentAction('viewDocument'); currentDocuments = [data]; $event.preventDefault()">View Document</a>
                        </ng-template>
                    </div>
                </span>
            </td>
        </tr>
    </tbody>
    </nz-table>
</div>
<!-- <mat-paginator [length]="tableTotal" [pageSize]="pageSize" [pageSizeOptions]="[10,20,30]" [pageIndex]="pageIndex-1"
(page)="changePageIndex($event)" [showFirstLastButtons]="true"></mat-paginator>  -->
<ng-template #totalTemplate let-total i18n>{{((pageIndex-1) * pageSize)+1}} - {{math.min((pageIndex-1) * pageSize + pageSize, total )}} of {{ total }}</ng-template>

<!-- Orientation fix (WCAG 2.1 – 1.3.4): use viewport-relative width so dialog fits within the
     viewport on small portrait screens. // Assumption: 90vw leaves comfortable margin on desktop
     while preventing overflow on mobile. -->
<!-- WCAG 1.4.13 – enable dismissal via ESC key and click outside -->
<nz-modal [nzFooter]="[]"   i18n-nzTitle [(nzVisible)]="showTemplateLinkUrlModal" 
nzTitle="Template Link Url"  [nzWidth]="'90vw'" (nzOnCancel)="showTemplateLinkUrlModal = false"
         [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
            <table class="modal-table">
                <tr>
                    <td i18n>Template Name</td>
                    <td>
                        {{currentDocuments[0].title}}
                    </td>
                </tr>
                <tr>
                    <td i18n>Publishing Link</td>
                    <td>
                        <!-- WCAG 3.3.2 – programmatic label added because <td> element
                             used as visible label is not reliably associated with
                             the textarea for assistive technologies. -->
                        <textarea disabled class="text-cursor" [nzAutosize]="{ minRows: 3}" nz-input [ngModel]="currentDocuments[0].templateLink" i18n-aria-label aria-label="Publishing link"></textarea>
                    </td>
                </tr>
            </table>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): responsive width so dialog does not overflow in portrait -->
<!-- WCAG 1.4.13 – enable dismissal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showPublishModal" 
nzTitle="Publish/Unpublish Status" [nzWidth]="'90vw'" (nzOnOk)="changePublishStatus()" (nzOnCancel)="showPublishModal = false"
          [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
            <table class="modal-table">
                <tr>
                    <td i18n>Template Name</td>
                    <td>
                        {{currentDocuments[0].title}}
                    </td>
                </tr>
                <tr>
                    <td i18n>Current Status</td>
                    <td>
                        <mat-radio-group [(ngModel)]="isCurrentPublished" i18n-aria-label aria-label="current status">
                            <mat-radio-button class="mr-2" [value]="true" i18n>Published</mat-radio-button>
                            <mat-radio-button [value]="false">Unpublished</mat-radio-button>
                          </mat-radio-group>
                    </td>
                </tr>
                <tr *ngIf="currentDocuments[0].isPublished">
                    <td i18n>Publishing Link</td>
                    <td>
                        <!-- WCAG 3.3.2 – programmatic label added for reasons above. -->
                        <textarea disabled class="text-cursor" [nzAutosize]="{ minRows: 3}" nz-input [ngModel]="currentDocuments[0].templateLink" i18n-aria-label aria-label="Publishing link"></textarea>
                    </td>
                </tr>
            </table>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): make template creation dialog responsive -->
<!-- WCAG 1.4.13 – enable dismissal via ESC key and click outside -->
<nz-modal  i18n-nzTitle [(nzVisible)]="showUseTemplateModal" nzTitle="Create New Document"  
[nzWidth]="'90vw'" (nzOnOk)="useTemplate()"
(nzOnCancel)="showUseTemplateModal = false; useMultipleTemplates = false;" [nzMaskClosable]="true" [nzKeyboard]="true">
    <ng-container *nzModalContent>
            <div>
                This template can be used to create a new document, with all tags ready to be assigned and sent out for signing.
                <br>
                Create a new document based on this template?
            </div>
            <div class="mt-2">
                <label>
                    <input class="mr-2" [(ngModel)]="useMultipleTemplates" type="checkbox" i18n-aria-label aria-label="Use multiple templates">
                    <span i18n>Use multiple templates</span>
                </label>
            </div>
    </ng-container>
</nz-modal>

<!-- WCAG 1.4.13 – mark as dialog, focusable and dismissible via ESC key -->
<div *ngIf="showMultiTemplateSelectModal" cdkDrag  class="select-templates-modal" role="dialog" tabindex="0"
     aria-label="Select templates" (keydown.escape)="showMultiTemplateSelectModal = false">
    <div  class="template-select-header">
        <span i18n>Create New Document</span>
        <!-- WCAG 1.4.13 – make close control operable via keyboard -->
        <span (click)="showMultiTemplateSelectModal = false" (keydown.enter)="showMultiTemplateSelectModal = false"
              (keydown.space)="showMultiTemplateSelectModal = false" class="close-template-tool" role="button" tabindex="0"
              i18n-aria-label aria-label="Close dialog">x</span></div>
    <div class="p-1">
        <span i18n>Templates to use:</span>
        <div class="flex">
            <div class="template-box">
                <div *ngFor="let template of useTemplates; let idx = index" class="template-row-item" tabindex="0">
                    <!-- WCAG 3.3.2 – associate checkbox with its title so users can
                         activate it by clicking the text and so assistive
                         technologies announce both control and purpose. -->
                    <input class="mr-2" [attr.id]="'templateCheckbox' + idx" [(ngModel)]="template.checked" type="checkbox" i18n-aria-label aria-label="Use template">
                    <label [attr.for]="'templateCheckbox' + idx">{{template.title}}</label>
                    <!-- Assumption: delete icon acts as a button so explicit aria-label added -->
                    <!-- WCAG 1.4.13 – make delete icon keyboard operable and accessible -->
                    <span  class="delete-template"  nz-icon nzType="delete" nzTheme="outline" (click)="removeTemplate(template)"
                          (keydown.enter)="removeTemplate(template)" (keydown.space)="removeTemplate(template)"
                          role="button" tabindex="0" i18n-aria-label aria-label="Remove template"></span>
                </div>
            </div>
            <div class="template-controls">
                <button nz-button nzType="primary" type="button"  (click)="moveCheckedTemplatesUpMax()">       
                    <span  class="template-control-icon" [nzRotate]="90"  nz-icon nzType="double-left" 
                    i18n-aria-label aria-label="Move selected templates to first position" nzTheme="outline" ></span>
                   </button>
                <button nz-button nzType="primary" class="mt-1" type="button" (click)="moveCheckedTemplatesUp()">       
                     <span class="template-control-icon"  nz-icon nzType="up"
                     i18n-aria-label aria-label="Move selected templates earlier" nzTheme="outline" ></span>
                </button>
                <button nz-button nzType="primary" class="mt-1" type="button" (click)="moveCheckedTemplatesDown()">       
                    <span class="template-control-icon"  nz-icon nzType="down"
                    i18n-aria-label aria-label="Move selected templates later" nzTheme="outline" ></span>
               </button>
               <button nz-button nzType="primary" class="mt-1" type="button" (click)="moveCheckedTemplatesDownMax()">       
                <span class="template-control-icon" [nzRotate]="90"  nz-icon nzType="double-right"
                i18n-aria-label aria-label="Move selected templates to last position" nzTheme="outline" ></span>
               </button>

            </div>
        </div>

        <div i18n>Add a template to the list by clicking the <span class="use-template-small">Use&nbsp;Template</span> button</div>
        <div class="mt-2">
            <!-- Accessibility (WCAG 1.3.2): Reordered buttons so DOM order matches
                 visual order now that flex-direction is no longer reversed. // Assumption: primary action visually rightmost is still acceptable -->
            <div class="template-buttons">
                <button nz-button nzType="default" class="mr-2" type="button" (click)="showMultiTemplateSelectModal = false"  i18n>Cancel</button>
                <button nz-button nzType="primary" type="button" (click)="useMultiTemplates()" i18n>Ok</button>
            </div>
        </div>
    </div>
</div>
<app-document-actions [documents]="currentDocuments" [action]="currentAction" [tableStatus]="tableStatus" [tableType]="tableType" (refreshTable)="emitData()"></app-document-actions>
