// Added high-contrast borders, focus indicators and input/checkbox overrides to comply with
// WCAG 2.1 SC 1.4.11 (Non-text Contrast). Earlier resize/orientation comments retained.

.share-icon {
    color:#017BC6; // WCAG 1.4.3: darkened blue (#0183d3 → #017BC6) to pass ≥4.5:1 contrast if rendered as text
}

// WCAG 1.4.4 – switched absolute px font-size to relative rem so icon size scales with user text zoom
.med-icon {
    font-size: 1rem; // 16px → 1rem  // Assumption: base font-size = 16px
}

::ng-deep .ant-tooltip-inner {
    white-space: pre-line;
}

.flex-space {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

.table-action {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left:0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
}

// WCAG 1.4.10 – ensure the data table can be scrolled horizontally within
// its own region at 320 px viewport without forcing the entire page to
// scroll sideways (Reflow). Also allows vertical scroll in the rest of the
// page independently.
.table-responsive {
  width: 100%;
  max-width: 100%; // Assumption: container should never exceed viewport
  overflow-x: auto; // single-axis scroll container
}

// Orientation fix (WCAG 2.1 – 1.3.4): use fluid width so search bar adapts in portrait view
// WCAG 1.4.4 – converted fixed pixel dimensions to rem so search bar remains usable at 200 % text size
.search-bar {
    width: 100%;
    max-width: 15.625rem; // 250px → 15.625rem  // Assumption: base font-size = 16px
    float: right;
    margin-bottom: 0.625rem; // 10px → 0.625rem
}

// WCAG 1.4.4 – use relative spacing so wrapped items don't overlap when text enlarges
.top-text {
    font-weight: bold;
    margin-right: 1.25rem; // 20px → 1.25rem
    display: flex;
    align-items: center;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 0.625rem; // 10px → 0.625rem (WCAG 1.4.4)
    margin-left: 0.625rem;  // 10px → 0.625rem (WCAG 1.4.4)
}

.top-sub-bar {
    display: flex;
    flex-wrap: wrap;
}

// WCAG 1.4.10 – relax width restriction so text can wrap instead of causing
// unnecessary clipping on very narrow screens.
.recipients {
    max-width: 50vw; // 20 → 50 vw allows up to half the viewport
    white-space: normal;
    word-break: break-word;
}

// WCAG 1.4.4 – convert padding/margin to rem units so buttons scale with text
.published {
    border: 1px solid #12836c; // WCAG 1.4.3: darkened green for contrast
    color: #12836c;
    font-weight: bold;
    padding: 0.5rem; // 8px → 0.5rem
    line-height: normal; // WCAG 1.4.12: allow user-defined text spacing
    border-radius: 0.3125rem; // 5px → 0.3125rem
    cursor: pointer;
    margin-left: 0.125rem; // 2px → 0.125rem
}
.published:hover {
    background-color: #12836c; // Maintain darker shade on hover for sufficient contrast
    color: white;
}
    
.unpublished,.useTemplate {
    border: 1px solid #0078c2;
    color: #0078c2;
    font-weight: bold;
    padding: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    line-height: normal; // WCAG 1.4.12: respect increased line spacing
    border-radius: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    cursor: pointer;
    margin-left: 0.125rem; // 2px → 0.125rem (WCAG 1.4.4)
}

.unpublished:hover,.useTemplate:hover {
    background-color: #0078c2;
    color: white;
}

.text-cursor {
    cursor: text !important;
}

// Orientation fix (WCAG 2.1 – 1.3.4): allow title cell to shrink on narrow screens instead of
// forcing a 300 px minimum that could push the table off-screen.
.break-word-title {
    max-width: 60vw; // Assumption: 60 vw prevents overflow while keeping reasonable width on desktop
    white-space: normal;
    word-break: break-word;
}

// Orientation fix (WCAG 2.1 – 1.3.4): make custom modal responsive using viewport units.
.select-templates-modal {
    width: 90vw;
    max-width: 20.625rem; // 330px → 20.625rem (WCAG 1.4.4) – Assumption: base font-size 16px
    height: auto;
    max-height: 80vh; // Prevent overflow on landscape mobile devices
    border: 1px solid #797E8A; // WCAG 1.4.11 – increase modal border contrast
    top:30%;
    left:50%;
    transform: translateX(-50%);
    background: #fff;
    position: fixed;
    z-index: 3000;
    overflow: auto; // Allow scrolling if content overflows vertically
    transition: box-shadow 200ms cubic-bezier(0, 0, 0.2, 1);
    box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
                0 2px 2px 0 rgba(0, 0, 0, 0.14),
                0 1px 5px 0 rgba(0, 0, 0, 0.12);
}

.template-select-header {
    cursor: move;
    background-color: #017BC6; // WCAG 1.4.3: darkened blue for better contrast
    min-height: 1.875rem; // 30px → 1.875rem (WCAG 1.4.4) – allows text growth
    width: 100%;
    color: white;
    padding: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    line-height: normal; // WCAG 1.4.12: ensure header grows with increased line spacing
}

// Orientation fix (WCAG 2.1 – 1.3.4): switch to max-height so box can shrink in landscape.
.template-box {
    border: 1px solid #797E8A; // WCAG 1.4.11 – darken container border for ≥3:1 contrast
    width: 100%;
    max-height: 60vh; // Assumption: prevents box from overflowing viewport height
    overflow-y: auto;
}

    
.use-template-small {
    border: 1px solid #036cb3; // WCAG 1.4.3: darkened blue to surpass 4.5:1 contrast on white
    color: #036cb3;
    font-weight: bold;
    padding: 0.1875rem; // 3px → 0.1875rem (WCAG 1.4.4)
    line-height: normal; // WCAG 1.4.12: accommodate user text spacing
    border-radius: 0.3125rem; // 5px → 0.3125rem
}

// WCAG 1.4.4 – relative spacing for close icon
.close-template-tool {
    cursor: pointer;
    font-weight: bold;
    margin-right: 0.3125rem; // 5px → 0.3125rem
}

.template-buttons {
    /* Accessibility (WCAG 1.3.2): Ensure DOM and visual order are identical
       so assistive technologies announce the controls in the same sequence
       they appear visually. Removed row-reverse and kept natural order. // Assumption: visual layout remains acceptable */
    display: flex;
    flex-direction: row;
}
// WCAG 1.4.10 – allow template titles to wrap within modal instead of being
// truncated, preventing loss of information when viewport is narrow.
.template-row-item {
    padding: 0.3125rem; // 5px → 0.3125rem
    border-bottom: 1px solid #797E8A; // WCAG 1.4.11 – darken separator line
    white-space: normal;
    word-break: break-word;
    display: flex;
    align-items: center;
}

.delete-template {
    position: absolute;
    right: 4.0625rem; // 65px → 4.0625rem (WCAG 1.4.4)
    color: red;
    background-color: white;
    display: none;
    cursor: pointer;
}

// WCAG 2.1 – visually-hidden utility for accessibility (reused across components)
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

.template-row-item:hover .delete-template {
    display: block;
}

// WCAG 1.4.13 – allow keyboard users to reveal the inline delete icon
// Additional content (delete icon) previously appeared only on pointer hover.
// Make it visible when any child of the row receives keyboard focus so it
// remains persistent, hoverable and dismissible per the guideline.
.template-row-item:focus-within .delete-template,
.template-row-item:focus-visible .delete-template {
    display: block;
}

.template-controls {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 0.25rem; // 4px → 0.25rem (WCAG 1.4.4)
}
.template-control-icon {
    display: flex;
    cursor: pointer;
}

.status_circle {
    width: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    height: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    border-radius: 50%;
    margin-right: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    margin-left: 0.3125rem; // 5px → 0.3125rem (WCAG 1.4.4)
    align-self: center;
}

// WCAG 1.4.1 – small, neutral text to accompany retention icon ensuring information is
// conveyed without relying on colour alone.
// WCAG 1.4.4 – relative font-size so text enlarges with user preference
.retention-days {
  font-size: 0.75rem; // 12px → 0.75rem
  color: inherit;
}

// Added high-contrast borders, focus indicators, and checkbox/input overrides (WCAG 1.4.11 – Non-text Contrast)

/* Ensure input component boundaries meet ≥ 3 : 1 contrast */
.search-bar input[nz-input],
.modal-table input[nz-input],
.modal-table textarea[nz-input] {
  border: 1px solid #797E8A; // Assumption: component background is white
}

/* Visible keyboard focus ring for all interactive elements inside the component */
button:focus-visible,
a:focus-visible,
input[nz-input]:focus-visible,
textarea[nz-input]:focus-visible,
[tabindex="0"]:focus-visible,
.template-control-icon:focus-visible,
.delete-template:focus-visible {
  outline: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 against light backgrounds
  outline-offset: 2px;
}

.mat-menu-item:focus-visible {
    border: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 against light backgrounds
    outline: none;
}

/* Darken default checkbox outline for sufficient contrast */
::ng-deep .ant-checkbox-inner {
  border: 1px solid #797E8A; // Override low-contrast #d9d9d9
}

/* WCAG 1.4.12 fix: allow Material menu / radio items to expand under custom
   text-spacing settings – remove hard-coded 48 px line-height & height so
   content is not clipped when users increase line, word or letter spacing.
   // Assumption: change is scoped to this component via ::ng-deep to avoid
   // affecting other areas that may rely on the default measurements. */
::ng-deep .mat-menu-item,
::ng-deep .mat-radio-button .mat-radio-label,
::ng-deep .mat-radio-button .mat-radio-label-content {
  height: auto !important;      // permit vertical growth
  line-height: normal !important; // honour user stylesheet line-height ≥1.5×
  white-space: normal;          // allow text to wrap if letter/word spacing increases
  overflow: visible;           // ensure expanded text is not clipped
}

// Enhance vertical padding for context menu items to restore comfortable spacing
// at typical zoom levels while keeping auto height/line-height for WCAG text-spacing compliance.
::ng-deep .mat-menu-item {
  padding-top: 0.5rem !important;  // 8px
  padding-bottom: 0.5rem !important; // 8px
}

/*
 * WCAG 1.4.10 – Reflow fix: On very narrow viewports (≤380 px) reduce
 * the default 16 px cell padding applied by NG-ZORRO so the far-right
 * context-menu column remains visible without triggering horizontal scroll.
 * Using rem keeps the padding proportional when users increase the base
 * font size for readability, and the @media query ensures normal spacing
 * remains untouched on wider screens.
 */
@media (max-width: 380px) {
  ::ng-deep .ant-table-thead > tr > th,
  ::ng-deep .ant-table-tbody > tr > td,
  ::ng-deep .ant-table tfoot > tr > th,
  ::ng-deep .ant-table tfoot > tr > td {
    padding-left: 0.25rem !important; // 4px
    padding-right: 0.25rem !important; // 4px
  }
}

/* Ensure checkbox focus is visible */
::ng-deep .ant-checkbox:focus-visible,
::ng-deep .ant-checkbox-wrapper:focus-visible,
::ng-deep .ant-checkbox-input:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}

/* Make checkbox inputs focusable */
::ng-deep .ant-checkbox-input {
  tabindex: 0;
}

/* Ensure pagination controls get the same focus styling as table elements - SCOPED TO THIS COMPONENT ONLY */
:host ::ng-deep .ant-pagination button:focus-visible,
:host ::ng-deep .ant-pagination a:focus-visible,
:host ::ng-deep .ant-pagination-item:focus-visible,
:host ::ng-deep .ant-pagination-item button:focus-visible,
:host ::ng-deep .ant-pagination-item a:focus-visible,
:host ::ng-deep .ant-pagination-prev:focus-visible,
:host ::ng-deep .ant-pagination-next:focus-visible,
:host ::ng-deep .ant-pagination-jump-next:focus-visible,
:host ::ng-deep .ant-pagination-jump-prev:focus-visible,
:host ::ng-deep .ant-pagination-jump-next button:focus-visible,
:host ::ng-deep .ant-pagination-jump-prev button:focus-visible,
:host ::ng-deep .ant-pagination-item-link:focus-visible,
:host ::ng-deep .ant-pagination-jump-next .ant-pagination-item-link:focus-visible,
:host ::ng-deep .ant-pagination-jump-prev .ant-pagination-item-link:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
}

/* More specific selectors for ellipsis buttons - SCOPED TO THIS COMPONENT ONLY */
:host ::ng-deep .ant-pagination .ant-pagination-jump-next a.ant-pagination-item-link:focus-visible,
:host ::ng-deep .ant-pagination .ant-pagination-jump-prev a.ant-pagination-item-link:focus-visible,
:host ::ng-deep .ant-pagination li.ant-pagination-jump-next a:focus-visible,
:host ::ng-deep .ant-pagination li.ant-pagination-jump-prev a:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
  border: none !important;
}

/* Direct CSS class for ellipsis buttons - restore working highlighting */
.ellipsis-button-focus:focus-visible {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
}

.ellipsis-button-focus:focus {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
}
