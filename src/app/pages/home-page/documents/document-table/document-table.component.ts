// Accessibility: Introduced helper utilities and status-colour mappings to support WCAG fixes
// (1.4.1 – non-colour cues & 1.4.3 – sufficient contrast) without altering business logic.
// WCAG 1.4.13 – import HostListener to handle ESC key dismissal of custom dialog
// WCAG 2.4.4 – import ElementRef & Renderer2 so we can append programmatic
// `aria-label`s to pagination controls generated by NG-ZORRO at runtime.
import { Component, EventEmitter, Input, OnInit, Output, HostListener, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';
import { DocumentStatus, RecipientStatus} from "@core/models/status";
import { Code, tableType } from '@core/models';
import { MediaService, DocumentService } from '@core/services';
import {getCookie} from "@core/services/utilities.service";
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'app-document-table',
  templateUrl: './document-table.component.html',
  styleUrls: ['./document-table.component.less'],
})
export class DocumentTableComponent implements OnInit, AfterViewInit {

  @Input() tableData:any[] = [];
  @Input() tableTotal:number = 0;
  @Input() tableType:tableType = 'document';
  @Input() set changeTableStatus(status: string ) {
    // reset table if status changes, 
    this.setupTable();
    this.tableStatus = status;
    this.clearChecked();
    this.pageSize =  Number(getCookie('tablePageSize')) || 10;
    this.pageIndex = 1;
    this.searchValue = '';
  }
  @Input() setDocStatus:string = '';

  @Output() tableParams = new EventEmitter<any>();

  public user:any;
  public checked = false;
  public indeterminate = false;
  public setOfCheckedId = new Set<number>();

  public pageSize:number = Number(getCookie('tablePageSize')) || 10;
  public pageIndex:number = 1;
  public docStatus:string = '';
  public orderKey:string = 'LastModifiedTime';
  public recipientStatus:string = '';

  public documentStatusCodes:any = DocumentStatus;
  public recipientStatusCodes:any = RecipientStatus;
  public code:any = Code;

  public searchValue:string = '';
  public currentDocuments:any[] = [];
  public currentAction:string = '';
  public showTemplateLinkUrlModal:boolean = false
  public showPublishModal:boolean = false;
  public showUseTemplateModal:boolean = false;
  public showMultiTemplateSelectModal:boolean = false;
  public useMultipleTemplates:boolean = false;
  public useTemplates: any[] = [];

  public isCurrentPublished: boolean = false;
  public displayDateHeader = $localize `Last Modified Time`;
  // Assumption: Expanded abbreviation to improve clarity for assistive-technology users – WCAG 2.4.6 (Headings and Labels)
  public displayDocStatusHeader = $localize `Document Status`;
  public displayRecipientStatusHeader = $localize `Recipient Status`;
  public tableStatus:string = '';
  public docStatusFilterList:any;
  public recipientStatusFilterList:any = [
    {key:'allRecipientStatus', text: $localize `All`},
    {key:'WaittoOpen', text: $localize `Wait to Open`},
    {key:'ReachedDeadline', text: $localize `Reached Deadline`},
    {key:'WorkingInProgress', text: $localize `Working In Progress`},
    {key:'Viewed', text: $localize `Viewed`},
    {key:'Rejected', text: $localize `Rejected`},
    {key:'Finalized', text: $localize `Finalized`},
    {key:'Cancelled', text: $localize `Cancelled`},
  ]
  public dateSortList:any = [
    {key:'CreationTime', text: $localize `Created Time`},
    {key:'LastModifiedTime', text: $localize `Last Modified Time`},
  ]
  public titleSortList:any = [
    {key:'TitleAsc', text: $localize `Ascending`},
    {key:'TitleDesc', text: $localize `Descending`},
  ]
  public recipientSortList:any = [
    {key:'RecipientsAsc', text: $localize `Ascending`},
    {key:'RecipientsDesc', text: $localize `Descending`},
  ]
  public math = Math;
  public docSharingDisabled = "";
  public documentMenu: any[] = [];
  public userProduct:any;
  private userSetting:any;

  // WCAG 1.4.13 – allow users to dismiss the multi-template selection dialog via ESC key
  @HostListener('document:keydown.escape')
  handleDialogEscape(): void {
    if (this.showMultiTemplateSelectModal) {
      this.showMultiTemplateSelectModal = false;
    }
  }

  constructor(
         public mediaService: MediaService,
         private documentService: DocumentService,
         // Assumption: injecting ElementRef/Renderer2 is safe and does not
         // impact existing logic, used solely for accessibility patching.
         private elementRef: ElementRef,
         private renderer: Renderer2
    ) { }

  ngOnInit(): void {

    this.setupTable();
    this.removeUnwantedFocusTargets();
    if(this.setDocStatus) {
      //if doc status passed by url param
      let findStatus = this.docStatusFilterList.find((val:any)=>{return val.key == this.setDocStatus})
      this.filterDocStatus(findStatus)
    }
  }

  /**
   * Lifecycle: After the view initialises we patch pagination controls so
   * their purpose is conveyed to screen-reader users (WCAG 2.4.4).
   */
  ngAfterViewInit(): void {

    this.patchPaginationAriaLabels();
  }

  /**
   * WCAG 2.4.3 (Focus Order)
   * Remove any problematic tabindex attributes from elements that should not 
   * receive focus before the main interactive content.
   */
  private removeUnwantedFocusTargets(): void {
    setTimeout(() => {
      // Remove tabindex from potential focus-trapping elements
      const problematicSelectors = [
        '.sr-only[tabindex]',
        'caption[tabindex]',
        '.table-responsive[tabindex]',
        '.ant-table-wrapper[tabindex]',
        '.ant-table-container[tabindex]',
        '.ant-table[tabindex]'
      ];

      problematicSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element: Element) => {
          if (element.hasAttribute('tabindex')) {
            (element as HTMLElement).removeAttribute('tabindex');
          }
        });
      });
    });
  }

  setupTable() {
    this.user = JSON.parse(localStorage.getItem("user") || '0');
    this.userSetting = this.user.userSetting;
    this.userProduct = this.user.userProduct;
    this.displayDateHeader = $localize `Last Modified Time`;
    // WCAG 2.4.6 – use full wording instead of abbreviation in column header
    this.displayDocStatusHeader = $localize `Document Status`;
    this.displayRecipientStatusHeader = $localize `Recipient Status`;
    //setup view
    if(this.tableType == 'inbox') {
      this.docStatus = 'allStatus_inbox';
      this.docStatusFilterList = [
        {key:'allStatus_inbox', text: $localize `All`, color:'#737373'},
        {key:'in_progress_inbox', text: $localize `In Progress`, color:'#ffae04'},
        {key:'completed_inbox', text: $localize `Completed`, color:'#149278'},
      ]
    }
    else if(this.tableType == 'document') {
      this.docStatus = 'allStatus';
      this.docStatusFilterList = [
        {key:'allStatus', text: $localize `All`, color:'#737373'},
        {key:'draft', text: $localize `Draft`, color:'#017bc6'},
        {key:'in_progress', text: $localize `In Progress`, color:'#ffaf04'},
        {key:'completed', text: $localize `Completed`, color:'#249651'},
        {key:'expired', text: $localize `Expired`, color:'#7a7e8a'},
        {key:'ExpiringWithinAWeek', text: $localize `Expiring Within a Week`, color:'#83478d'},
        {key:'canceled', text: $localize `Canceled`, color:'#d50d00'},
      ]
    }
    if (!this.userProduct.teamTemplateDocumentSharingEnabled)
    {
      this.docSharingDisabled = "none";
    }
  }

  // changePageIndex(event:any) {
  //   this.pageSize = event.pageSize;
  //   this.pageIndex = event.pageIndex+1;
  //   document.cookie = "tablePageSize" + "="+  this.pageSize + ";path=/";
  //   this.clearChecked();
  //   this.emitData();
  // }
changePageIndex(index:number) {
    this.pageIndex = index;
    this.emitData();
}

changePageSize(size:number) {
    this.pageSize = size;
    document.cookie = "tablePageSize" + "="+ size + ";path=/";
    this.pageIndex = 1;
    this.clearChecked();
    this.emitData();

}

  filterDocStatus(status:any) {
    this.docStatus = status.key;
    this.recipientStatus = '';
    this.displayRecipientStatusHeader = $localize `Recipient Status`;
    if(status.key == 'allStatus_inbox') {
      // WCAG 2.4.6 – fall back to descriptive label when "all" filter selected
      this.displayDocStatusHeader = $localize `Document Status`;
    }
    else {
      this.displayDocStatusHeader = status.text;
    }
    this.pageIndex = 1;
    this.emitData();
  }

  orderBy(value:string) {
    this.orderKey = value;
    this.pageIndex = 1;
    this.emitData();
  }
  
  filterRecipientStatus(status:any) {
    this.recipientStatus = status.key;
    if(status.key == 'allRecipientStatus') {
      this.displayRecipientStatusHeader = $localize `Recipient Status`;
    }
    else {
      this.displayRecipientStatusHeader = status.text;
    }
    this.pageIndex = 1;
    this.emitData();
  }

  emitData() {
    this.setOfCheckedId.clear();
    this.checked = false;
    this.indeterminate = false;
    this.tableParams.emit({ pageIndex: this.pageIndex, pageSize: this.pageSize, docStatus: this.docStatus, recipientStatus: this.recipientStatus, orderBy: this.orderKey, searchValue: this.searchValue});
    this.removeUnwantedFocusTargets();
    
    // Add delay before pagination patching to ensure table is fully rendered
    setTimeout(() => {
      this.patchPaginationAriaLabels();
      this.ensureProperTableNavigation();
    }, 50);
  }

  private ensureProperTableNavigation(): void {
    // Ensure table elements have consistent tabindex values after page changes
    setTimeout(() => {
      // Find all table elements that should be focusable
      const tableElements = this.elementRef.nativeElement.querySelectorAll('td[tabindex], th[tabindex], tr[tabindex]');
      
      tableElements.forEach((element: HTMLElement) => {
        // Ensure all table elements that were meant to be focusable stay focusable
        if (element.getAttribute('tabindex') === '-1') {
          // Skip elements that are intentionally not focusable
          return;
        }
        
        // Ensure consistent tabindex for proper navigation
        if (!element.getAttribute('tabindex') || element.getAttribute('tabindex') === '0') {
          element.setAttribute('tabindex', '0');
        }
      });
      
      // Make checkbox inputs directly focusable instead of their TD/TH containers
      const checkboxInputs = this.elementRef.nativeElement.querySelectorAll('.ant-checkbox-input');
      
      checkboxInputs.forEach((checkboxInput: HTMLElement) => {
        // Make checkbox inputs focusable in tab order
        checkboxInput.setAttribute('tabindex', '0');
      });
      
      // Ensure TD/TH containers are not focusable
      const checkboxContainers = this.elementRef.nativeElement.querySelectorAll('td[role="checkbox"], th[role="checkbox"]');
      
      checkboxContainers.forEach((container: HTMLElement) => {
        container.setAttribute('tabindex', '-1');
      });
    }, 10);
  }



  refreshCheckedStatus(): void {
    this.checked = this.tableData.every(({ id }:any) => this.setOfCheckedId.has(id));
    this.indeterminate = this.tableData.some(({ id }:any) => this.setOfCheckedId.has(id)) && !this.checked;
  }

  updateCheckedSet(id: number, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }
  clearChecked() {
    this.setOfCheckedId.clear()
    this.checked = false;
    this.indeterminate = false;
  }

  /**
   * WCAG 2.4.4 (Link Purpose – In Context) & WCAG 2.4.3 (Focus Order)
   * --------------------------------------------------
   * NG-ZORRO renders its `nz-pagination` controls as a list of numeric / icon
   * anchors that lack meaningful text.  Assistive-technology users navigating
   * via the links list therefore cannot determine each control's purpose – a
   * failure of SC 2.4.4 at Level AA.  We programmatically inject descriptive
   * `aria-label`s after Angular has painted the view.
   *
   * Additionally, we fix the tab order so numbered page buttons receive focus
   * before Previous/Next navigation arrows, providing a more logical keyboard
   * navigation experience. This is achieved by setting explicit tabindex values:
   * - Page numbers: tabindex 1, 2, 3, etc.
   * - Previous/Next: tabindex 100+
   * - Other controls: tabindex 200+
   *
   * Assumption: DOM structure follows Ant Design v12 where pagination list
   * items carry classes such as `.ant-pagination-prev`, `.ant-pagination-next`
   * and `.ant-pagination-item`.
   */
  private patchPaginationAriaLabels(): void {
    // TESTING: Add a very obvious console log to verify this method is called

    
    // Defer to ensure the pagination markup exists in the DOM.
    // Try multiple times to overcome NG-ZORRO potentially resetting our changes
    this.attemptPaginationPatch(0);
  }

  private attemptPaginationPatch(attempt: number): void {

    
    setTimeout(() => {
      // Ensure we don't interfere with table element focus during the patch
      const activeElement = document.activeElement as HTMLElement;
      const isTableElementFocused = activeElement && (
        activeElement.closest('.ant-table') || 
        activeElement.closest('tr') || 
        activeElement.closest('td') || 
        activeElement.closest('th')
      );
      
      // If a table element is currently focused, defer the patch to avoid focus traps
      if (isTableElementFocused) {
        setTimeout(() => this.attemptPaginationPatch(attempt), 100);
        return;
      }

      
      // Look for both old and new pagination patterns
      const paginationSelectors = [
        'ul.ant-pagination',
        'nz-pagination ul',
        '.ant-table-pagination ul',
        'ul[class*="pagination"]',
        '.ant-pagination'
      ];
      
      // Also look for page size selectors separately - including the specific input that's causing issues
      const pageSizeSelectors = [
        '.ant-pagination-options',
        '.ant-select',
        'nz-select',
        '[class*="page-size"]',
        '[class*="pagination-options"]',
        '.ant-select-selection-search-input',  // This is the specific input causing the tab issue
        'input.ant-select-selection-search-input'
      ];
      
      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });

      // Also check for ng-zorro table pagination directly
      const nzTables = this.elementRef.nativeElement.querySelectorAll('nz-table');
      nzTables.forEach((table: HTMLElement) => {
        const paginationInTable = table.querySelectorAll('ul.ant-pagination, .ant-pagination');
        paginations.push(...Array.from(paginationInTable) as HTMLElement[]);
      });

      // Debug: log found paginations
      // Also find page size selectors 
      let pageSizeControls: HTMLElement[] = [];
      pageSizeSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        pageSizeControls.push(...Array.from(found) as HTMLElement[]);
      });



      


      paginations.forEach(pagination => {
        const items = pagination.querySelectorAll('li');

        
        // Arrays to store different types of pagination controls for proper tab order
        const numberButtons: HTMLElement[] = [];
        const prevNextButtons: HTMLElement[] = [];
        const otherControls: HTMLElement[] = [];

        items.forEach(li => {
          // Ant Design changed markup from <a> to <button> in v13.
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) {
            return; // nothing to patch
          }

          // Get text content for processing
          const text = control.textContent?.trim() || '';

          // Debug: log key info about control elements


          // Skip if another process already supplied an accessible name.
          if (control.hasAttribute('aria-label')) {
            return;
          }

          // Skip disabled controls entirely - remove from tab order
          if (li.classList.contains('ant-pagination-disabled') || 
              control.hasAttribute('disabled') || 
              control.getAttribute('aria-disabled') === 'true') {

            this.renderer.setAttribute(control, 'tabindex', '-1');
            return;
          }

          // Add CSS class for focus styling
          this.renderer.addClass(control, 'pagination-control');

          // Provide semantic role when href is missing so <a> without href is exposed correctly.
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button'); // Assumption: matches built-in keyboard support
          }

          // Add keyboard event handlers for pagination controls only
          this.addPaginationKeyboardActivation(control);

          // Check for various pagination control patterns and categorize them for tab order
          if (li.classList.contains('ant-pagination-prev') || 
              li.querySelector('[class*="prev"]') ||
              text.includes('‹') ||
              text.includes('<')) {

            this.renderer.setAttribute(control, 'aria-label', 'Previous page');
            // Only add to tab order if not disabled
            if (!li.classList.contains('ant-pagination-disabled') && !control.hasAttribute('disabled')) {
              prevNextButtons.push(control);
            } else {

              // Remove from tab order completely
              this.renderer.setAttribute(control, 'tabindex', '-1');
            }
          } else if (li.classList.contains('ant-pagination-next') || 
                     li.querySelector('[class*="next"]') ||
                     text.includes('›') ||
                     text.includes('>')) {

            this.renderer.setAttribute(control, 'aria-label', 'Next page');
            // Only add to tab order if not disabled
            if (!li.classList.contains('ant-pagination-disabled') && !control.hasAttribute('disabled')) {
              prevNextButtons.push(control);
            } else {

              // Remove from tab order completely
              this.renderer.setAttribute(control, 'tabindex', '-1');
            }
          } else if ((li.classList.contains('ant-pagination-item') || 
                     li.classList.contains('ant-pagination-item-active')) &&
                     /^\d+$/.test(text)) {
            // Only treat as page number if it's ONLY digits (not "10 / page" or similar)

            this.renderer.setAttribute(control, 'aria-label', `Page ${text}`);
            numberButtons.push(control);
          } else if (li.classList.contains('ant-pagination-options') ||
                     li.classList.contains('ant-pagination-options-size-changer') ||
                     text.includes('/') ||
                     text.includes('page') ||
                     control.querySelector('select')) {
            // Page size selector or options

            otherControls.push(control);
          } else {

            // Add detailed debugging for ellipsis/jump buttons
            if (text.includes('•') || text.includes('...') || text.includes('…')) {

              
              // Add a specific CSS class for ellipsis button styling
              this.renderer.addClass(control, 'ellipsis-button-focus');
              
              // Force focus styling with inline styles as backup
              this.renderer.setStyle(control, 'outline', 'none');
              this.renderer.listen(control, 'focus', () => {

                this.renderer.setStyle(control, 'outline', '3px solid #2D323D');
                this.renderer.setStyle(control, 'outline-offset', '2px');
                this.renderer.setStyle(control, 'box-shadow', '0 0 0 3px #2D323D');
              });
              this.renderer.listen(control, 'blur', () => {

                this.renderer.setStyle(control, 'outline', 'none');
                this.renderer.setStyle(control, 'box-shadow', 'none');
              });
            }
            otherControls.push(control);
          }
        });

        // Set proper tab order: numbered buttons first (tabindex 1-N), then prev/next (tabindex 100+), then other controls (tabindex 200+)
        // This ensures numbered page buttons are reached before Previous/Next buttons when tabbing
        

        
        // Strategy: Use very high tabindex for page numbers to ensure they come first,
        // and use even higher numbers for prev/next to ensure they come after
        
        // Number buttons get high tabindex values to ensure they come first
        numberButtons.forEach((control, index) => {
          const tabindex = "0";
          this.renderer.setAttribute(control, 'tabindex', tabindex);
          // Force the tabindex by setting it directly on the element as well
          control.setAttribute('tabindex', tabindex);
          
          // Remove any visual test styling that might interfere with focus indicators
          this.renderer.removeStyle(control, 'border');
          this.renderer.removeStyle(control, 'background-color');
          

          // Verify it was set
          setTimeout(() => {

          }, 100);
        });

        // Prev/Next buttons get even higher tabindex to come after numbers
        prevNextButtons.forEach((control, index) => {
          const tabindex = "0";
          this.renderer.setAttribute(control, 'tabindex', tabindex);
          control.setAttribute('tabindex', tabindex);
          // Remove test class that adds yellow background
          this.renderer.removeClass(control, 'pagination-control-test');

        });

        // Other controls get highest tabindex to come last
        otherControls.forEach((control, index) => {
          const tabindex = "0";
          this.renderer.setAttribute(control, 'tabindex', tabindex);
          control.setAttribute('tabindex', tabindex);
          // Remove test class that adds yellow background
          this.renderer.removeClass(control, 'pagination-control-test');

        });
      });

      // Process page size controls - make them focusable and accessible, but avoid double-tabbing
      pageSizeControls.forEach((control, index) => {
        if (control.tagName === 'NZ-SELECT' || control.classList.contains('ant-select')) {
          // For nz-select, find ALL focusable elements and only enable the main one
          const allFocusable = control.querySelectorAll('input, button, [tabindex], .ant-select-selector');

          
          allFocusable.forEach((el, elIndex) => {
            const element = el as HTMLElement;
            if (elIndex === 0) {
              // Only the first focusable element gets tabindex 0
              this.renderer.setAttribute(element, 'tabindex', '0');
              element.setAttribute('tabindex', '0');
              this.renderer.removeAttribute(element, 'disabled');
              this.renderer.removeStyle(element, 'pointer-events');

            } else {
              // All other focusable elements get tabindex -1 to avoid double-tabbing
              this.renderer.setAttribute(element, 'tabindex', '-1');
              element.setAttribute('tabindex', '-1');

            }
          });
        } else if (control.tagName === 'SELECT' || control.tagName === 'INPUT' || control.tagName === 'BUTTON') {
          this.renderer.setAttribute(control, 'tabindex', '0');
          control.setAttribute('tabindex', '0');
          this.renderer.removeAttribute(control, 'disabled');
          this.renderer.removeStyle(control, 'pointer-events');

        }
      });

      // If no paginations found and we haven't tried too many times, retry
      if (paginations.length === 0 && attempt < 3) {

        this.attemptPaginationPatch(attempt + 1);
      } else if (paginations.length > 0) {
        // Set up a mutation observer to watch for changes to pagination
        this.setupPaginationWatcher(paginations);
      }
    }, 200 + (attempt * 200)); // Increasing delay for each attempt
  }

  private setupPaginationWatcher(paginations: HTMLElement[]): void {
    // Watch for any DOM changes that might reset our tabindex values
    const observer = new MutationObserver((mutations) => {
      let shouldReapply = false;
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'tabindex') {
          shouldReapply = true;
        }
      });
      
      if (shouldReapply) {
        // Don't reapply pagination fixes while user is navigating table elements
        const activeElement = document.activeElement as HTMLElement;
        const isTableElementFocused = activeElement && (
          activeElement.closest('.ant-table') || 
          activeElement.closest('tr') || 
          activeElement.closest('td') || 
          activeElement.closest('th')
        );
        
        if (!isTableElementFocused) {
          this.attemptPaginationPatch(0);
        }
      }
    });

    // Observe all pagination elements for attribute changes
    paginations.forEach(pagination => {
      const controls = pagination.querySelectorAll('button, a');
      controls.forEach(control => {
        observer.observe(control, {
          attributes: true,
          attributeFilter: ['tabindex']
        });
      });
    });
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to pagination controls to ensure they respond
   * to Enter and Space key activation, matching expected button behavior.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    // Remove any existing keyboard handlers to prevent duplicates
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) {
      control.removeEventListener('keydown', existingHandler);
    }

    // Create new keyboard handler
    const keyboardHandler = (event: KeyboardEvent) => {
      // Activate on Enter or Space (standard button behavior)
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // Trigger click event to activate pagination
        control.click();
      }
    };

    // Store reference for cleanup and add listener
    (control as any).__keyboardHandler = keyboardHandler;
    this.renderer.listen(control, 'keydown', keyboardHandler);
  }


  onItemChecked(id: number, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
    this.getActionsMenu(null);
  }

  focusCheckbox(event: FocusEvent): void {
    // Don't interfere with tab navigation - only focus checkbox on direct clicks/enters
    // Check if this was triggered by a keyboard navigation event
    const target = event.target as HTMLElement;
    
    // If the focus came from tab navigation, don't force focus to the inner checkbox
    // This prevents the focus trap during shift+tab navigation
    if (event.relatedTarget) {
      return;
    }
    
    const checkbox = target.querySelector('.ant-checkbox-input') as HTMLElement;
    
    if (checkbox) {
      checkbox.focus();
    }
  }

  onAllChecked(checked: boolean): void {
    this.tableData
      .forEach(({ id }:any) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
    this.getActionsMenu(null)
  }

  getSharedString(user:string) {
    return $localize `Shared by: `+ user;
  }

  getRetentionToolTipText(document:any) {
    let currDate = new Date();
    let tooltipText;
    if (this.userProduct.isRetentionEnabled &&  this.userSetting.isRetentionEnabled && document.isRetentionEnabled &&
       this.code.fssCodes.includes(document.signingStatus)) {
      var timestamp = document.RetentionStartTimestamp || document.FileLockTimestamp || 0;
      var expireDate = new Date(timestamp + document.RetentionPeriodInDays * 24 * 60 * 60 * 1000);
      let daysLeft = Math.ceil((expireDate.getTime() - currDate.getTime()) / (24 * 60 * 60 * 1000));
      let offload = this.userSetting.RetentionSaveBeforePurge;

      if (daysLeft > 0 && (document.signingStatus !== this.code.SIGNING_STATUS_COMPLETED || !(document.pkiEnabled && document.pkiSigningStatus === this.code.DOCUMENT_PKI_SIGNING_STATUS_IN_PROGRESS))) {
        tooltipText = $localize`Retention
        This document will be purged on: ${this.formatDate(expireDate)} in ${daysLeft} day(s)`
        if (offload) {
          tooltipText = $localize`Retention
          This document will be offloaded and purged on: ${this.formatDate(expireDate)} in ${daysLeft} day(s)`
        }
      }
    }
    return tooltipText;
  }

  getIconColor(document:any)
	{
    let currDate = new Date();
    var timestamp = document.RetentionStartTimestamp || document.FileLockTimestamp || 0;
    var expireDate = new Date(timestamp + document.RetentionPeriodInDays * 24 * 60 * 60 * 1000);
    let daysLeft = Math.floor((expireDate.getTime() - currDate.getTime()) / (24 * 60 * 60 * 1000));

    //red if 1 day or less left
    if(daysLeft <= 1) {
        return '#CA3F3F'
    }
    //yellow if less than 25% of retention period is left
    else if((daysLeft / document.RetentionPeriodInDays) <= 0.25) {
        return '#777709';
    }
    else return '#1add1a';
	};

  // WCAG 2.1 – 1.4.1 (Use of Colour): Provide a textual representation of retention status so
  // severity is not conveyed by colour alone. Helper returns remaining days or null when
  // retention countdown does not apply. // Assumption: negative days indicates expired – we
  // treat as 0.
  getRetentionDaysLeft(document: any): number | null {
    if (!(this.userProduct?.isRetentionEnabled && this.userSetting?.isRetentionEnabled && document?.isRetentionEnabled)) {
      return null;
    }

    if (!this.code.fssCodes.includes(document.signingStatus)) {
      return null;
    }

    const timestamp = document.RetentionStartTimestamp || document.FileLockTimestamp || 0;
    if (!timestamp) {
      return null;
    }

    const expireDate = new Date(timestamp + document.RetentionPeriodInDays * 24 * 60 * 60 * 1000);
    const currDate   = new Date();
    let daysLeft = Math.ceil((expireDate.getTime() - currDate.getTime()) / (24 * 60 * 60 * 1000));
    if (daysLeft < 0) {
      daysLeft = 0;
    }
    return daysLeft;
  }

  // WCAG 2.1 – 1.4.1: Provide icon variation (shape) in addition to colour so users who cannot
  // perceive colour can still identify severity levels.
  // green -> 'check-circle', yellow -> 'exclamation-circle', red -> 'close-circle'
  getRetentionIconType(document: any): string {
    const colour = this.getIconColor(document);
    switch (colour) {
      case '#CA3F3F': // critical / red
        return 'close-circle';
      case '#777709': // warning / yellow
        return 'exclamation-circle';
      default: // safe / green
        return 'check-circle';
    }
  }
  formatDate(date: any) {
    var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

    if (month.length < 2)
      month = '0' + month;
    if (day.length < 2)
      day = '0' + day;

    return [year, month, day].join('-');
  }

  goToDocument(document:any) {
    if(this.tableType == 'inbox') {
      window.location.href='/UI/documentDesigner2.html?iid='+document.iid;
    }
    else if(this.tableStatus != 'SharedTeamDocumentsSiderBar' &&
     this.tableStatus != 'shared' &&
     this.tableStatus != 'trash') {
      window.location.href='/UI/documentDesigner2.html?id='+document.id;
    }
  }

  getPurgeDaysLeft(data:any) {
		
		if (data.isRetentionEnabled === true && this.code.fssCodes.indexOf(data.signingStatus) !== -1 && data.RetentionStartTimestamp)
		{
			var timestamp = data.RetentionStartTimestamp || data.FileLockTimestamp || 0;
			var expireDate = new Date(timestamp + data.RetentionPeriodInDays * 24 * 60 * 60 * 1000);
			var currDate = new Date();
			return  14 - Math.ceil((currDate.getTime() - expireDate.getTime()) / (24 * 60 * 60 * 1000));
		} else  {
			return "--";
		}
	};

  isRetentionExpired(document:any) {
		var timestamp = document.RetentionStartTimestamp || document.FileLockTimestamp || 0;
		var expireDate = new Date(timestamp + document.RetentionPeriodInDays * 24 * 60 * 60 * 1000);
		var currDate = new Date();
		
		return currDate.getTime() > expireDate.getTime();
	};


  getActionsMenu(document:any) {
    this.currentDocuments = [];
    //if document is null, need to get document(s) from checked ids
    if(!document) {
      //if no checked ids, it means item was unchecked. clear list
      if(this.setOfCheckedId.size == 0) {
        this.documentMenu = [];
        return
      }
      else {
        this.setOfCheckedId.forEach(id=>{
          this.currentDocuments.push(this.getDocumentFromId(id));
       })
      }
    }
    //if document was passed, it is from the left hand side menu in the table
    else{
      this.currentDocuments = [document];
    }

    //check if any selected doc has an editor
    let hasEditor = false;
    this.currentDocuments.forEach(doc=>{
      if(doc.hasEditor) {hasEditor = true;}
    })

    if(this.setOfCheckedId.size <= 1) {
      //menu is different for template page
      if(this.tableType == 'template') {
        this.getTemplateActions();
        return;
      }

      this.currentAction='';
      this.documentMenu = [
        {text: $localize`Open`, actionName: "edit", iconClass:"folder-open"},
        {text: $localize`Rename Title`, actionName: "rename", iconClass:"edit"},
        {text: $localize`Download`, actionName: "download", iconClass:"download"},
        {text: $localize`Legacy Download`, actionName: "download.legacy",iconClass:"file-search"},
        {text: $localize`Status and History`, actionName: "history", iconClass:""},
        {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
        {text: $localize`Move to Folder`, actionName: "move", iconClass:""},
        {text: $localize`Copy as a new Document`, actionName: "duplicate", iconClass:""},
        {text: $localize`Copy as a new Template Link`, actionName: "toTLinkFromDoc", iconClass:""},
        {text: $localize`Copy as a new Regular Template`, actionName: "toRTemplateFromDoc", iconClass:""},
        {text: this.currentDocuments[0].sharedToTeam ? $localize`Unshare with my Team` : $localize`Share with my Team`, actionName: "shareToTeam", iconClass:"", class:this.docSharingDisabled},
        {text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
      ];
      if(hasEditor){
        this.documentMenu.splice(9, 1);
        this.documentMenu.splice(8, 1);
      }
      if (this.currentDocuments[0].isRetentionEnabled && this.code.fssCodes.indexOf(this.currentDocuments[0].signingStatus) !== -1
      && this.currentDocuments[0].RetentionType !== 75 && !this.isRetentionExpired(this.currentDocuments[0]))
      {
        // remove Delete from context menu
        this.documentMenu.splice(5, 1);
      }
      switch (this.docStatus)
      {
        case 'WaitingForSignatures':
          this.documentMenu = [{text: $localize`Sign Document`, actionName: "signDocument"}];
          return;
        
        case 'DocumentsForReview':
          this.documentMenu = [{text: $localize`View Document`, actionName: "viewDocument"}];
          return;
        
        case 'CompletedInbox':
          this.documentMenu = [{text: $localize`View Document`, actionName: "viewDocument"}];
          return;
      }
    }
    else {
      // multi documents selections
      this.documentMenu = [
        {text: $localize`Download`, actionName: "download", iconClass:"download"},
        {text: $localize`Legacy Download`, actionName: "download.legacy",iconClass:"file-search"},
        {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
        {text: $localize`Move to Folder`, actionName: "move", iconClass:"folder"},
        {text: $localize`Copy as a new Document`, actionName: "duplicate", iconClass:""},
        {text: $localize`Copy as a new Template Link`, actionName: "toTLinkFromDoc", iconClass:""},
        {text: $localize`Copy as a new Regular Template`, actionName: "duplicateTemplates", iconClass:""},
        {text: $localize`Share/Unshare with my Team`, actionName: "shareToTeam", iconClass:"", class:this.docSharingDisabled},
        {text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
      ];
      if(hasEditor){
        this.documentMenu.splice(5, 1);
        this.documentMenu.splice(4, 1);
      }
      if (this.currentDocuments.some(doc=>{return (doc.RetentionType == 76 || doc.RetentionType == 77) && doc.isRetentionEnabled &&
        this.code.fssCodes.indexOf(doc.signingStatus) !== -1 && 
        !this.isRetentionExpired(doc)
      }))
      {
        // remove Delete from context menu
        this.documentMenu.splice(2, 1);
      }
      if(this.tableType == 'template' && (this.tableStatus == '' || this.tableStatus.startsWith('folder_'))) {
        this.documentMenu = [
          {text: $localize`Download`, actionName: "download", iconClass:"download"},
          {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
          {text: $localize`Move to Folder`, actionName: "move", iconClass:"folder"},
          {text: $localize`Copy as a new Document`, actionName: "duplicate", iconClass:""},
          {text: $localize`Copy as a new Regular Template`, actionName: "toRTemplateFromTLink", iconClass:""},
          {text: $localize`Share/Unshare with my Team`, actionName: "shareToTeam", iconClass:"", class:this.docSharingDisabled},
          {text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
        ];
      } 
      if(this.tableType == 'template' && this.tableStatus == 'template_link' ) {
        this.documentMenu = [
          {text: $localize`Download`, actionName: "download", iconClass:"download"},
          {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
          {text: $localize`Copy as a new Document`, actionName: "duplicate", iconClass:""},
          {text: $localize`Copy as a new Template Link`, actionName: "duplicateTemplates", iconClass:""},
          {text: $localize`Copy as a new Regular Template`, actionName: "toRTemplateFromLink", iconClass:""},
          {text: $localize`Share/Unshare with my Team`, actionName: "shareToTeam", iconClass:"", class:this.docSharingDisabled},
          {text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
        ];
      } 
      else if (this.tableType == 'template' && this.tableStatus == 'bulk_sign_template') {
        this.documentMenu = [
          {text: $localize`Download`, actionName: "download", iconClass:"download"},
          {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
          {text: $localize`Copy as a new Bulksign`, actionName: "duplicateTemplates", iconClass:""},
          {text: $localize`Share/Unshare with my Team`, actionName: "shareToTeam", iconClass:"", class:this.docSharingDisabled},
          {text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
        ];
      }
      else if (this.tableType == 'template' && this.tableStatus == 'shared') {
        this.documentMenu = [
          {text: $localize`Copy as a new Document`, actionName: "duplicate", iconClass:""},
          {text: $localize`Copy as a new Regular Template`, actionName: "toRTemplateFromDoc", iconClass:""},
        ];
      }

    }

    if (this.tableType == 'inbox')
      {
        if (document.invitationType == this.code.INVITATION_TYPE_SIGNER &&
          (document.invitationStatus == this.code.RECIPIENT_STATUS_WAIT_TO_OPEN || document.invitationStatus == this.code.RECIPIENT_STATUS_WORK_IN_PROGRESS))
        {
          this.documentMenu = [{text:  $localize`Sign Document`, actionName: "signDocument"}];
          return;
        }
        else
        {
          this.documentMenu = [{text: $localize`View Document`, actionName: "viewDocument"}];
          return;
        }
      }
      if(this.tableStatus == 'SharedTeamDocumentsSiderBar') {
        this.documentMenu = [
          {text: $localize`Download`, actionName: "download", iconClass:"download"},
          {text: $localize`Legacy Download`, actionName: "download.legacy",iconClass:"file-search"},
          {text: $localize`Copy as new Document`, actionName: "duplicateToDocumentFromShared", iconClass:"copy"}
        ];
      }
      else if(this.tableType == 'manage') {
        this.documentMenu = [
          {text: $localize`Download`, actionName: "download", iconClass:"download"},
          {text: $localize`Copy as new Document`, actionName: "duplicateToDocumentFromShared", iconClass:"copy"}
        ]
      }
      if(this.tableStatus == 'trash') {
        this.documentMenu = [
          {text: $localize`Restore`, actionName: "recover", iconClass:"undo"},
          {text: $localize`Delete Permanently`, actionName: "deletePermanently", iconClass:"delete"}
        ];
        return;
      }
      if((this.tableStatus == '' ||  this.tableStatus == 'homeFolder' || this.tableStatus.startsWith('folder_')) && this.tableType != 'manage') {
        if (this.currentDocuments[0].signingStatus == this.code.SIGNING_STATUS_COMPLETED && this.setOfCheckedId.size <= 1)
        {
          this.documentMenu.unshift({text: $localize`Request Additional Signature`, actionName: "requestAdditionalSignature", iconClass:"edit"});
        }
        else if (this.currentDocuments[0].signingStatus == this.code.SIGNING_STATUS_IN_PROGRESS && this.setOfCheckedId.size <= 1)
        {
          this.documentMenu.unshift({text: $localize`Cancel Signing`, actionName: "cancelSigning", iconClass:"stop"});
        }
      }
	};

  focusFirstMenuItem(trigger: MatMenuTrigger) {
    setTimeout(() => {
      trigger.menu?.focusFirstItem()
    }, 0
    )
  }


  getTemplateActions() {
    
    this.documentMenu = [
      {text: $localize`Download`, actionName: "download", iconClass:"download"},
      {text: $localize`Delete`, actionName: "delete", iconClass:"delete"},
      {text: $localize`Rename Title`, actionName: "rename", iconClass:"edit"},
      {text: $localize`Status and History`, actionName: "history", iconClass:""},
      {text: $localize`Report/Export`, actionName: "templateLinkReport",iconClass:""},

    ];


    switch (this.tableStatus)
		{
			case '':
				this.documentMenu  = this.documentMenu.concat([
					{text: $localize`Copy as new Document`, actionName: "toDocFromRTemplate"},
					{text: $localize`Copy as new Regular Template`, actionName: "duplicateTemplates", iconClass:""},
					{text: $localize`Copy as new Template Link`, actionName: "toTLinkFromRTemplate"},
					{text: this.currentDocuments[0].sharedToTeam ? $localize`Unshare with my Team` : $localize`Share with my Team`, actionName: "shareToTeam", iconClass:"", class: this.docSharingDisabled},
					{text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""},
					{text: $localize`Move to Folder`, actionName: "move", iconClass:""},
				]);break;
			
			case 'template_link':
        this.documentMenu  = this.documentMenu.concat([
					{text: $localize`Copy as new Document`, actionName: "toDocFromTLink", iconClass:""},
					{text: $localize`Copy as new Regular Template`, actionName: "toRTemplateFromTLink", iconClass:""},
					{text: $localize`Copy as new Template Link`, actionName: "duplicateTemplates", iconClass:""},
					{text: this.currentDocuments[0].sharedToTeam ? $localize`Unshare with my Team` : $localize`Share with my Team`, actionName: "shareToTeam", iconClass:"", class: this.docSharingDisabled},
					{text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
				]); break;
			
			case 'bulk_sign_template':
        this.documentMenu  = this.documentMenu.concat([
					{text: $localize`Copy as new Bulksign`, actionName: "duplicateTemplates", iconClass:""},
					{text: this.currentDocuments[0].sharedToTeam ? $localize`Unshare with my Team` : $localize`Share with my Team`, actionName: "shareToTeam", iconClass:"", class: this.docSharingDisabled},
					{text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""}
				]); break;
			
			case 'shared':
        this.documentMenu  = [
					{text: $localize`Copy as new Document`, actionName: "duplicateToDocumentFromShared"},
          {text: $localize`Copy as new Template`, actionName: "duplicateTemplates"},

				]; break;
			
			case 'trash':
        this.documentMenu  = [
					{text: $localize`Restore`, actionName: "recover", iconClass:"undo"},
					{text: $localize`Delete Permanently`, actionName: "deletePermanently", iconClass:"delete"}
				]; break
			
			default:
        this.documentMenu  = this.documentMenu.concat([
					{text: $localize`Copy as new Document`, actionName: "toDocFromRTemplate"},
					{text: $localize`Copy as new Regular Template`, actionName: "toRTemplateFromLink", iconClass:""},
					{text: $localize`Copy as new Template Link`, actionName: "toTLinkFromRTemplate"},
					{text: this.currentDocuments[0].sharedToTeam ? $localize`Unshare with my Team` : $localize`Share with my Team`, actionName: "shareToTeam", iconClass:"", class: this.docSharingDisabled},
					{text: $localize`Push to an account`, actionName: "pushToAnother", iconClass:""},
					{text: $localize`Move to Folder`, actionName: "move", iconClass:"ti-share-alt"},
				]); break;
		}
  }

  //used to optmize ngFor reloading
  trackByFn(index: number, item: any): any {
    return item.actionName;
  }

  setCurrentAction(action:string) {
    this.currentAction = action;
    setTimeout(()=>{this.currentAction = ''},0)

  }

  goToStatus(document:any, filter: string) {
    //only when on the 'all documents' tab
    if(this.tableStatus == '' && this.tableType == 'document') {
      window.location.href='/UI/invitationStatus.html?id='+document.id+'&tab='+filter
    }
    else if(this.tableType == 'manage'){
        window.open('/UI/invitationStatus.html?id='+document.id+'&tab='+filter);
    }
    else if(this.tableType == 'template' && filter == 'RecipientsListContainer' &&
        (this.tableStatus != 'shared' && this.tableStatus != 'trash')){
      window.location.href='/UI/invitationStatus.html?id='+document.id+'&tab='+filter
    }
  }

  getDocumentFromId(id:number) {
    return this.tableData.find(document=>document.id == id);
  }

  changePublishStatus() {
    this.currentDocuments[0].isPublished = this.isCurrentPublished;
    this.documentService.updateDocuments(this.currentDocuments).subscribe(val=>{
      this.showPublishModal = false;
      this.emitData();
    })
  }

  getTemplateTypeIcon(document:any) {
    switch (document.documentType) {
      case this.code.DOCUMENT_TYPE_REGULAR_TEMPLATE:
          return "file-text";
      case this.code.DOCUMENT_TYPE_TEMPLATE_LINK:
          return "link";
      case this.code.DOCUMENT_TYPE_BULK_SIGN_TEMPLATE:
          return "appstore";
      default: return '';
  }
  }

  useTemplate() {
    //if multiple template tool is already open
    if(this.showMultiTemplateSelectModal == true) {
      //add template to list if it is not already in it
      if(!this.useTemplates.some(item=>{return item.id == this.currentDocuments[0].id})) {
        this.currentDocuments[0].checked = false;
        this.useTemplates.push(this.currentDocuments[0]);
      }
      return;
    }

    if(this.useMultipleTemplates) {
        this.showUseTemplateModal = false;
        this.useMultipleTemplates = false;
        this.showMultiTemplateSelectModal = true;
        this.useTemplates = [];
        this.currentDocuments[0].checked = false;
        this.useTemplates.push(this.currentDocuments[0]);
    }
    else {
      var tempDoc = this.currentDocuments[0];
      tempDoc.documentType = this.code.DOCUMENT_TYPE_DOCUMENT;
      let body ={
        documents: [tempDoc],
        returnSimpleObject: false,
        duplicateAsIs: false,
        duplicateAsNew: false,
        mergeDocuments: false,
        autoShare: this.userSetting.ShareAutomaticallyDocToTeam
      }
      this.documentService.duplicateDocuments(body).subscribe((data) => {
        this.showUseTemplateModal = false;
        this.useMultipleTemplates = false;
        location.assign("/UI/documentDesigner2.html?id=" + data.documents[0].id);
      })
    }
  }

  useMultiTemplates() {
    var tempDocs = this.useTemplates;
    tempDocs.forEach((doc:any)=>{
      doc.documentType = this.code.DOCUMENT_TYPE_DOCUMENT;

    })
    let body ={
      documents: tempDocs,
      returnSimpleObject: false,
      duplicateAsIs: false,
      duplicateAsNew: false,
      mergeDocuments: true,
      autoShare: this.userSetting.ShareAutomaticallyDocToTeam
    }
    this.documentService.duplicateDocuments(body).subscribe((data) => {
      this.showMultiTemplateSelectModal = false;
      location.assign("/UI/documentDesigner2.html?id=" + data.documents[0].id);
    })
  }

  removeTemplate(template:any) {
    this.useTemplates = this.useTemplates.filter(function(el) { return el.id != template.id }); 
  }
  moveCheckedTemplatesDown() {
    //iterate array backwards to prevent going over the same item after pushing it down
    let reversedArray = this.useTemplates.slice().reverse();
    reversedArray.forEach((el:any, index:number) => {
      if (el.checked) {
        let reversedIndex = reversedArray.length - 1 - index;
        this.arrayMove(  reversedIndex, reversedIndex + 1);
      }
    }) 
  }

  moveCheckedTemplatesUp() {
    this.useTemplates.forEach((el:any, index:number) => {
      if (el.checked) {
        this.arrayMove(  index, index - 1);
      }
    }) 
  }

  moveCheckedTemplatesUpMax() {
    let movedToTopCounter = 0;

    this.useTemplates.forEach((el:any, index:number) => {
      if (el.checked) {
        this.arrayMove( index, movedToTopCounter);
        movedToTopCounter++;
      }
    }) 
  }

  moveCheckedTemplatesDownMax() {
    let reversedArray = this.useTemplates.slice().reverse();
    let movedToBottomCounter = 1;
    reversedArray.forEach((el:any, index:number) => {
      if (el.checked) {
        let reversedIndex = reversedArray.length - 1 - index;
        this.arrayMove(  reversedIndex, reversedArray.length - movedToBottomCounter);
        movedToBottomCounter++;
      }
    }) 
  }

  arrayMove( fromIndex:number, toIndex:number) {
    if(fromIndex < 0 || fromIndex > this.useTemplates.length - 1 || toIndex < 0 || toIndex > this.useTemplates.length - 1) {
      return;
    }
    if(!this.useTemplates[toIndex].checked) {
      var element = this.useTemplates[fromIndex];
      this.useTemplates.splice(fromIndex, 1);
      this.useTemplates.splice(toIndex, 0, element);
    }
  }
}
