// File updated for WCAG 1.4.4 – converted remaining pixel-based padding values (20px, 55px) to rem units
// so text can be resized to 200 % without clipping. Other orientation and status message styles retained.
// Updated for WCAG 1.4.12 – increased line-height for titles to 1.5em, removed
// !important on `.mobile-title`, and neutralised negative top margin applied
// via Tailwind so text remains readable under custom text-spacing.
// Orientation fix (WCAG 2.1 – 1.3.4):
// Converted fixed pixel font-sizes and line-heights to responsive `clamp()` and
// relative units so the heading text adapts fluidly in both portrait and
// landscape orientations without causing horizontal scroll or truncation. // Assumption: design intent was ~50 px on small screens and ~70 px on desktops.

@media (max-width:640px) {
    .mobile-title {
        font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // Assumption: 28 px–50 px
        /* WCAG 1.4.12 – increase default line-height to ≥1.5 × font size and
           drop the !important flag so user style sheets can override with even
           larger values if desired.  This prevents clipping when users apply
           custom text-spacing. */ // Assumption: 1.5em preserves intended visual rhythm
        line-height: 1.5em;
        // WCAG 1.4.4 – replaced fixed padding with rem so it scales with text size
        padding-bottom: 1.25rem !important; // 20px → 1.25rem // Assumption: base font-size = 16px
    }
  }

.title {
    font-size: clamp(2.5rem, 6vw, 4.375rem); // 40 px–70 px
    font-weight: 275;
    color: #2D323D;
    // WCAG 1.4.4 – use relative padding so enlarged text does not overlap following content
    padding-bottom: 3.4375rem; // 55px → 3.4375rem // Assumption: 1rem = 16px
  }

// Added visually-hidden utility class for screen-reader only content (WCAG 2.1 – 4.1.3 Status Messages)
.sr-only { // Assumption: class reused across project for hidden live regions
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

/* WCAG 1.4.10 – ensure overlay-based UI (Angular Material mat-menu, Ant Design nz-modal, etc.)
   never causes horizontal scrolling at 320 px viewport.  Assumption: All overlay
   panels rendered from child components inside the inbox view can safely scale
   down to 90 % of the viewport width on very small screens without breaking
   usability. */

@media (max-width: 640px) {
  /* Limit modal dialog width so the page doesn’t require two-axis scrolling */
  :host ::ng-deep .ant-modal {
    max-width: 90vw; // widths are overridden inline by library; force them to fit
    width: 90vw !important; // Assumption: preserving comfortable side gutters
  }

  /* Limit Angular Material / CDK menu panels and ensure long labels wrap */
  :host ::ng-deep .cdk-overlay-pane .mat-menu-panel,
  :host ::ng-deep .mat-menu-panel {
    max-width: 90vw;
    width: auto !important;
    white-space: normal; // allow line-wrapping
    word-break: break-word; // break long, unbroken strings
  }
}

// End of WCAG 1.4.10 overlay fixes

// WCAG 1.4.10 – ensure data table can scroll horizontally within its own
// container at 320 px viewport without forcing the whole page to scroll. The
// region is created in the template with class="table-responsive".

.table-responsive {
  width: 100%;
  max-width: 100%; // prevents container from exceeding viewport width
  overflow-x: auto; // single-axis scroll; vertical scroll stays on page
}

/* WCAG 1.4.11 – Provide a visible keyboard focus indicator for the
   scrollable region so low-vision users can identify focus without relying
   on colour alone. Re-use the standard application blue adopted in other
   components for consistency. */
.table-responsive:focus,
.table-responsive:focus-visible {
  outline: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 contrast on white
  outline-offset: 2px;
}

/* WCAG 2.4.5 – Breadcrumb visuals
   Keep styling lightweight to avoid visual regressions. The list items are
   separated by a slash using CSS generated content so that markup remains
   clean and assistive technologies announce the links individually. */

.breadcrumb-nav {
  margin: 1rem 1.25rem; // space below global header; matches existing gutter
}

.breadcrumb-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem; // small gap between items and separators
}

.breadcrumb-item {
  font-size: 0.875rem; // 14px equivalent; keep unobtrusive
  color: #4B5563; // Tailwind gray-700 equivalent; meets 4.5:1 on white
}

.breadcrumb-item a {
  color: inherit;
  text-decoration: underline;
}

.breadcrumb-item+ .breadcrumb-item::before {
  content: '/';
  margin-right: 0.5rem;
  color: #9CA3AF; // Tailwind gray-400 for separator – decorative
}

/* WCAG 2.4.7 – Focus Visible
   Many browsers/third-party component libraries (e.g. Ant Design) suppress the
   native focus outline on anchor and button elements, replacing it with a
   *very* subtle box-shadow that fails colour-contrast requirements in forced-
   colours mode and is often removed entirely by global resets such as
   `*{outline:none}`.  To guarantee that every interactive control rendered by
   the Inbox view exposes a **visible** keyboard focus indicator we re-enable a
   high-contrast outline for the most common HTML controls *within the
   component’s host subtree only*.  This localised rule avoids side-effects on
   other pages while fully satisfying WCAG 2.4.7.

   The colour (#2D323D) is already used elsewhere in the application as a focus
   ring and meets the 3:1 contrast ratio against white backgrounds.
*/
:host ::ng-deep a:focus-visible,
:host ::ng-deep button:focus-visible,
:host ::ng-deep [role="button"]:focus-visible,
:host ::ng-deep input[type="checkbox"]:focus-visible,
:host ::ng-deep input[type="radio"]:focus-visible,
:host ::ng-deep select:focus-visible {
  outline: 3px solid #2D323D; // WCAG 2.4.7 – visible focus ring
  outline-offset: 2px;
}

/* Specific override for Ant Design pagination links rendered inside the
   DocumentTable so they inherit the same outline even if the library applies
   its own focus styles in future updates. */
:host ::ng-deep .ant-pagination a:focus-visible,
:host ::ng-deep .ant-pagination button:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
}


