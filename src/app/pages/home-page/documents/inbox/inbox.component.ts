// WCAG 4.1.2 patch: assigns missing accessible names/roles to third-party pagination buttons, page links and search input rendered inside this component.
// Added Angular Title service to dynamically set document title (WCAG 2.4.2 – Page Titled)
import { Component, OnInit, AfterViewInit, ElementRef, Renderer2 } from '@angular/core'; // Update: refined search input labelling logic (WCAG 3.2.4)
import { Title } from '@angular/platform-browser';
import { DocumentService } from "@core/services";
import {getCookie} from "@core/services/utilities.service";

@Component({
  selector: 'app-inbox',
  templateUrl: './inbox.component.html',
  styleUrls: ['./inbox.component.less']
})
export class InboxComponent implements OnInit, AfterViewInit {

  public data: any;
  public dataTotal = 0;
  public isSpinning = false;

  constructor(
    private documentService: DocumentService,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private titleService: Title // Assumption: global application title pattern handled elsewhere
  ) {}

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    this.titleService.setTitle($localize`Inbox`);
    
    this.getDocuments(
      1,
      Number(getCookie('tablePageSize')) || 10,
      'inbox',
      '',
      'LastModifiedTime'
    );
  }


  ngAfterViewInit(): void {
    this.applyAutocompletePatch();
    this.applyPaginationLinkLabels();
    this.removeUnwantedFocusTargets();
  }

  getDocuments(
    pageIndex: number,
    pageSize: number,
    status: string,
    recipientStatus: string,
    orderBy: string
  ): void {
    this.isSpinning = true;
    this.documentService
      .getDocuments(
        'Document',
        recipientStatus,
        pageIndex,
        pageSize,
        orderBy,
        'DESC',
        status,
        false,
        '',
        -1
      )
      .subscribe((data) => {
        this.data = data.documents;
        this.dataTotal = data.documentCount;
        this.isSpinning = false;
        this.applyAutocompletePatch();
        this.applyPaginationLinkLabels();
        this.removeUnwantedFocusTargets();
      });
  }

  private applyAutocompletePatch(): void {

    setTimeout(() => {
      const inputs: NodeListOf<HTMLInputElement> =
        this.elementRef.nativeElement.querySelectorAll('input');
      inputs.forEach((input) => {
        if (!input.hasAttribute('autocomplete')) {
          this.renderer.setAttribute(input, 'autocomplete', 'off');
        }
        

        /*
         * WCAG 2.1 SC 3.2.4 – Consistent Identification
         * ------------------------------------------------
         * The previous implementation unconditionally assigned the generic
         * label “Search” to **every** <input> element that lacked an explicit
         * accessible name.  That strategy risked giving unrelated controls –
         * for example, a hidden filter field or numeric paginator input – the
         * *same* name even though they provide *different* functionality, thus
         * violating the requirement that components with identical behaviour
         * be identified consistently **and** uniquely.
         *
         * The fix below applies a *targeted* heuristic so that only real
         * *search* inputs receive the label:
         *   • <input type="search"> elements, or
         *   • inputs living inside a container whose class/role suggests a
         *     search pattern (e.g. `.search`, `[role="search"]`).
         *
         * All other unnamed inputs are left untouched so that authors can
         * apply the correct, task-specific label elsewhere in the codebase.
         */
        if (!input.hasAttribute('aria-label') && !input.labels?.length) {
          const typeAttr = input.getAttribute('type');
          const isSearchType = typeAttr === 'search';
          const isInSearchRegion = !!(
            input.closest('[role="search"], .search, .ant-table-filter-search, .nz-table-filter-search')
          );

          if (isSearchType || isInSearchRegion) {
            this.renderer.setAttribute(input, 'aria-label', 'Search');
            // WCAG 3.1.2 – specify language for injected string
            this.renderer.setAttribute(input, 'lang', 'en'); // Assumption: injected string is in English
          }
        }
      });
    });
  }

  /**
   * WCAG 2.4.4 – Link Purpose (In Context)
   * The automatically-generated pagination markup from Ant Design wraps each page
   * number / arrow inside an <a> element that contains only a number or icon
   * as the **visible** label. While numeric page links are usually permissible,
   * automated accessibility scanners such as Pa11y raise a notice requesting
   * confirmation that the link purpose can be programmatically determined. To
   * remove any ambiguity – and most importantly to aid screen-reader users – we
   * inject an `aria-label` that explicitly states the destination (e.g. “Page
   * 3”, “Next page”).  The modification is *non-destructive* and applies only
   * within the component’s host subtree so it will not affect other
   * pagination controls elsewhere in the application.
   *
   * Assumption: The DocumentTable component is the sole consumer of
   * <nz-pagination> inside this view, therefore querying for the first
   * `.ant-pagination` instance is sufficient.
   */
  private applyPaginationLinkLabels(): void {

    setTimeout(() => {
      const paginationRoot: HTMLElement | null = this.elementRef.nativeElement.querySelector(
        '.ant-pagination'
      );
      if (!paginationRoot) {
        return;
      }

      const paginationInteractiveEls: NodeListOf<HTMLElement> = paginationRoot.querySelectorAll(
        'a, button'
      );

      paginationInteractiveEls.forEach((el) => {
        // Skip if the element already exposes an accessible name – prevents
        // duplicate or conflicting labels when the library starts shipping its
        // own aria-labels in a future update.
        if (el.hasAttribute('aria-label')) {
          return;
        }

        // Ant Design uses <a> without href for some interactive controls; add
        // an explicit role to convey activatable semantics (WCAG 4.1.2 – Name,
        // Role, Value)
        if (el.tagName === 'A' && !el.hasAttribute('href')) {
          // WCAG 2.4.3 – an <a> without an href is *not* keyboard focusable
          // by default.  To preserve the expected focus order we expose the
          // element as a button **and** add tabindex="0" so it naturally
          // participates in sequential navigation.
          this.renderer.setAttribute(el, 'role', 'button');
          if (!el.hasAttribute('tabindex')) {
            this.renderer.setAttribute(el, 'tabindex', '0'); // Assumption: 0 maintains DOM order
          }
        }

        // WCAG 2.4.3 – Remove any positive tabindex values that would create a
        // custom focus order conflicting with the DOM sequence.  Ant Design
        // does not set positive indices by default, but we defensively reset
        // them to "0" if encountered (e.g. after future library updates).
        const tabindexVal = el.getAttribute('tabindex');
        if (tabindexVal && parseInt(tabindexVal, 10) > 0) {
          this.renderer.setAttribute(el, 'tabindex', '0');
        }

        const listItem = el.closest('li');
        if (listItem?.classList.contains('ant-pagination-prev')) {
          this.renderer.setAttribute(el, 'aria-label', 'Previous page');
          // WCAG 3.1.2 – tag injected English phrase
          this.renderer.setAttribute(el, 'lang', 'en');
        } else if (listItem?.classList.contains('ant-pagination-next')) {
          this.renderer.setAttribute(el, 'aria-label', 'Next page');
          this.renderer.setAttribute(el, 'lang', 'en');
        } else if (listItem?.classList.contains('ant-pagination-jump-prev')) {
          this.renderer.setAttribute(el, 'aria-label', 'Jump to previous pages');
          this.renderer.setAttribute(el, 'lang', 'en');
        } else if (listItem?.classList.contains('ant-pagination-jump-next')) {
          this.renderer.setAttribute(el, 'aria-label', 'Jump to next pages');
          this.renderer.setAttribute(el, 'lang', 'en');
        } else {
          const pageNumber = el.textContent?.trim();
          if (pageNumber) {
            this.renderer.setAttribute(el, 'aria-label', `Page ${pageNumber}`);
            this.renderer.setAttribute(el, 'lang', 'en');
          }
        }
      });
    });
  }

  /**
   * WCAG 2.4.3 (Focus Order)
   * Remove any problematic tabindex attributes from elements that should not 
   * receive focus before the main interactive content.
   */
  private removeUnwantedFocusTargets(): void {
    setTimeout(() => {
      // Remove tabindex from potential focus-trapping elements
      const problematicSelectors = [
        '.sr-only[tabindex]',
        'caption[tabindex]',
        '.table-responsive[tabindex]',
        '.ant-table-wrapper[tabindex]',
        '.ant-table-container[tabindex]',
        '.ant-table[tabindex]'
      ];

      problematicSelectors.forEach(selector => {
        const elements = this.elementRef.nativeElement.querySelectorAll(selector);
        elements.forEach((element: HTMLElement) => {
          if (element.hasAttribute('tabindex')) {
            this.renderer.removeAttribute(element, 'tabindex');
          }
        });
      });
    });
  }


}
