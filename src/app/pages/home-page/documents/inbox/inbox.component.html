<!-- WCAG 4.1.2 patch: no structural changes needed; retained previous functional markup without extraneous comments. -->




<h1 id="inboxHeading" class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    Inbox
</h1>

<!-- WCAG 3.1.2 – explicitly denote that the following runtime status message
     is in English so it is announced correctly if the page language differs -->
<span *ngIf="isSpinning" class="sr-only" aria-live="polite" lang="en" i18n>Loading content</span>

<nz-spin [attr.role]="isSpinning ? 'status' : null"
         [attr.aria-label]="isSpinning ? 'Loading content' : null"
         [attr.lang]="isSpinning ? 'en' : null"
         [nzSpinning]="isSpinning">
    <!-- WCAG 1.4.10 – wrapped document table in single-axis scroll container so the
         page never requires horizontal scrolling at 320 px viewport. -->

    <main id="inboxMain" tabindex="-1"><!-- Assumption: local <main> landmark does not conflict with global layout -->
        <div class="table-responsive" role="region" aria-labelledby="inboxHeading">
            <app-document-table (tableParams)="getDocuments($event.pageIndex, $event.pageSize, $event.docStatus, $event.recipientStatus, $event.orderBy)" [tableData]="data"
            [tableTotal]="dataTotal" tableType="inbox" tabindex="-1"></app-document-table>
        </div>
    </main>
</nz-spin>
