// WCAG 1.4.4 – replaced fixed pixel heights/padding with relative rem units for scalable text; retains previous improvements
.doc-sider {
    background-color: #f7f9fa;
    position: fixed;
    /* WCAG 1.4.10 – include padding/border within declared width so sidebar
       never exceeds the calculated responsive width and triggers horizontal
       scrolling. */
    box-sizing: border-box;
    // Start immediately below the header; exact value supplied via inline style binding.
    top: 0;
    bottom: 0;
    // margin-top removed – height is now controlled by top/bottom anchors.
    /* WCAG 1.4.10 – ensure sidebar never exceeds viewport width and allow
       internal scrolling instead of causing horizontal scroll */
    max-width: 100vw; // Assumption: prevents overflow beyond viewport
    overflow-y: auto; // Allows vertical scroll inside sidebar if needed
    z-index: 2;
    .doc-bar-header {
      /* Restore original brand colour while keeping sufficient contrast */
      background-color: #0183d3;
      color: #ffffff;
      /* Ensures DOM order matches visual order to satisfy WCAG 1.3.2 Meaningful
         Sequence – removed row-reverse so icons appear for assistive tech in
         the same order as visual presentation. */ // Assumption: visual design
      display: flex;
      flex-direction: row;
      justify-content: flex-end; /* keep icons on right edge as before */
      align-items: center;
      /* WCAG 1.4.4 – switch to relative units so header grows with text resizing */
      min-height: 3.125rem; // 50px → 3.125rem – Assumption: base font-size 16px
      cursor: pointer;
      visibility: visible;

      /* WCAG 1.4.12 text-spacing: allow header text to wrap when users
         increase letter/word spacing – ensures no clipping */ // Assumption: header may grow vertically
      flex-wrap: wrap;
      white-space: normal;
    }
  }

  // WCAG 2.1 – menu-item can now be rendered as a <button>; reset default styles while keeping previous look
  // Added high-contrast focus & hover styles and darkened separator borders (WCAG 1.4.11 – Non-text Contrast)
  .menu-item {
    /* WCAG 1.4.4 – use min-height and relative padding for scalable touch target */
    min-height: 2.5rem; // 40px → 2.5rem
    padding: 0.25rem 0.625rem;  // reduce top/bottom padding to tighten list
    cursor: pointer;
    display: flex;
    align-items: center;
    /* WCAG 1.4.12 text-spacing: permit multi-line wrapping so content is not
       truncated when users apply custom spacing styles. */ // Assumption: button label may require wrapping
    flex-wrap: wrap;
    white-space: normal;
    border: none; // Assumption: button reset
    background: transparent; // Assumption: inherit coloured background via selected/unselected classes
    width: 100%;
    text-align: left;

    /* WCAG 1.4.12: keep overflow visible so additional line height does not
       get clipped inside the button */
    overflow: visible;

    // WCAG 1.4.11 – Provide a high-contrast hover indicator so state change is
    // perceptible to low-vision users.
    &:hover:not(.menu-selected) {
      border-left: 0.25rem solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 vs #f7f9fa
    }
  }
  .menu-item:hover {
    background-color: #ffffff !important;
  }
  
  .menu-item, .edit-folder {
    border-bottom: 1px solid #f2f2f2;
  }

  .edit-folder {
    margin-top: -5px;
  }

/* Orientation fix (WCAG 2.1 – 1.3.4):
   Use viewport-relative max-height on Ant Design modals opened from this
   component so dialogs remain fully visible in both portrait and landscape
   orientations across devices. Adding overflow:auto lets users scroll the
   content if it still exceeds the available space. */ // Assumption: 60vh
:host ::ng-deep .ant-modal-body {
  max-height: 60vh; // prevents vertical overflow on landscape phone
  overflow-y: auto;
}

  .menu-item:hover .edit-folder {
    visibility: visible;
  }

  // Reveal edit icon when list item (li) is hovered or focused
  li:hover .edit-folder,
  // WCAG 1.4.13 – ensure additional content revealed on pointer hover is
  // equally available on keyboard focus; keep visible while any child within
  // list item has focus so it remains persistent until focus moves away.
  .menu-item:focus-visible + .edit-folder,
  li:focus-within .edit-folder,
  .edit-folder:focus-visible {
    visibility: visible !important; // Ensures visibility when keyboard focused
  }

  .edit-folder {
    visibility: hidden;
    cursor: pointer;
    border: none;
    background: transparent;
    padding: 0 4px; // small hit area enhancement
  }

  .folder-name {
    line-break: anywhere;
    display: flex;
    justify-content: space-between;
  }

  .menu-selected {
    background-color: #ffffff;
    /* WCAG 1.3.3 – retain non-colour cue via bolder font weight */
    font-weight: 600;
    // WCAG 1.4.11 – darken separators so they reach ≥ 3 : 1 contrast against white
    border-top: 1px solid #000; // Assumption: #797E8A ≈ 4.5 : 1 vs white
    border-bottom: 1px solid #000; // Assumption: same as above

    /* Add non-colour visual indicator (left-hand pointer) so selected state
       is still distinguishable when colours are not perceived – addresses
       WCAG 1.4.1 Use of Colour */ // Assumption: pointer shape acceptable for design.
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: currentColor; // uses current text colour ensuring sufficient contrast
    }
  }
  .menu-unselected {
    background-color: #f7f9fa;
    font-weight: 400; // Assumption: default weight; explicitly set for consistency
  }

  // Utility to align folder row content
  .flex-space {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  // Remove default bullets for semantic lists rendered inside the sidebar
  nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  /* WCAG 2.4.1 – Style the skip link so it is hidden off-screen until it
     receives keyboard focus, at which point it becomes visible at the top of
     the sidebar. */
  .skip-link {
    position: absolute; // Assumption: positions relative to .doc-sider container
    left: -9999px;
    top: 0;
    background: #000;
    color: #fff;
    padding: 0.5rem 1rem;
    z-index: 100;
    text-decoration: none;
  }

  .skip-link:focus-visible {
    left: 0;
  }

/* Remove extra gap below header */
.doc-sider nav ul {
  margin-top: 0;
  padding-top: 0;
}

/* WCAG 1.4.3 contrast fix: override Tailwind's .text-red-500 (#ef4444, ~3.99:1) with darker red to exceed 4.5:1 on light backgrounds */ // Assumption: component background is white/light grey
.text-red-500 {
  color: #D40D00;
}

/* WCAG 1.4.11 – high-contrast keyboard focus for the header button */
.doc-bar-header:focus,
.doc-bar-header:focus-visible {
  outline: 2px solid #ffffff; // Assumption: white outline ≥ 3 : 1 against #017BC6 header background
  outline-offset: 2px;
}

/* WCAG 1.4.11 – increase input border contrast inside modal tables */
.modal-table input[nz-input] {
  border: 1px solid #797E8A; // Assumption: #797E8A ≥ 3 : 1 vs white
}

.modal-table input[nz-input]:focus,
.modal-table input[nz-input]:focus-visible {
  outline: 3px solid #2D323D; // Assumption: consistent with hover indicator colour
  outline-offset: 2px;
}

/* WCAG 1.4.10 – ensure modal tables used inside sidebar dialogs wrap content
   rather than introduce sideways scroll on small viewports */
.modal-table {
  width: 100%;
  table-layout: fixed;
  word-break: break-word;
}

/* Ensure folder icon appears before chevron visually */
::ng-deep .doc-bar-header span.anticon-folder {
  order: 1;
}
::ng-deep .doc-bar-header i {
  order: 2;
}

/* High-contrast keyboard focus indicator for all interactive elements except the New Folder button */
  ::ng-deep button:not(.new-folder-btn):focus-visible {
    outline: 3px solid #2D323D; // Visible blue ring consistent with site.
    outline-offset: -2px;
  }

// WCAG 2.4.7 – ensure the "New Folder" button also receives a highly-visible
// keyboard focus indicator. It was previously excluded from the generic
// button selector above, leaving the default outline potentially suppressed
// by global styles. The rule below restores a 3 px blue ring with adequate
// contrast so users navigating by keyboard can reliably locate focus.
.new-folder-btn:focus-visible {
  outline: 3px solid #2D323D; // Assumption: colour meets 3:1 contrast on light grey background
  outline-offset: 2px;
}

// Add margin-bottom to the new folder button for better spacing
.new-folder-btn {
  margin-bottom: .5rem; // 16px margin-bottom for better spacing
}

/* WCAG 2.4.6 – provide utility class to visually hide elements while keeping them available to assistive tech.
   Tailwind normally supplies .sr-only but we add a fallback to ensure the component remains standalone. */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.doc-bar-header:focus-visible {
  outline: #000 3px solid !important;
  outline-offset: -2px !important;
  z-index: 2000 !important;
}

#docSidebarNavigation ul li button.folder-name {
  padding-left: 1.4em !important;
}