// WCAG 1.4.4 – converted pixel-based left offsets to rem units so sidebar position scales with text size
// WCAG 2.4.2 – ensure each route/component updates the document title so
// assistive technology users immediately know which page is loaded.
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { MediaService } from '@core/services';
import { DocumentService } from "@core/services";
import {  Validators, FormBuilder  } from '@angular/forms';
import { Code } from '@core/models';
import { ResolveEnd, Router } from '@angular/router';
import { FocusService } from '@core/services/focus.service';


@Component({
  selector: 'app-doc-sidebar',
  templateUrl: './doc-sidebar.component.html',
  styleUrls: ['./doc-sidebar.component.less']
})
export class DocSidebarComponent implements OnInit {
  @Input() isParentCollapsed:boolean = false;

  @Input() set type(type: string) {
    this.pageType = type;
    this.getFolders();
    // Update the page title whenever the page type changes.
    this.updateDocumentTitle();
  }
  @Output() docBarCollapse = new EventEmitter<boolean>();
  @Output() menuSelect = new EventEmitter<string[]>();

  // Height of the fixed header (in pixels). Passed from parent so the sidebar starts
  // immediately below it and remains fully visible even when users zoom to 400 %.
  @Input() headerOffset: number = 64;

  public code:any = Code;
  public user:any;
  public showRetention:boolean = false;
  public pageType:string = ''
  public isCollapsed:boolean = this.mediaService.isMobile();
  public isMobile:boolean = false; // runtime flag for current viewport size – updated dynamically
  public selectedItem:string = '';
  public folders:any;
  public showAddFolderModal:boolean = false;
  public addFolderForm = this.formBuilder.group({
    name: ['', Validators.required]
  });
  public addFolderFormSubmitted:boolean = false;
  public showEditFolderModal:boolean = false;
  public editFolderForm = this.formBuilder.group({
    name: ['', Validators.required]
  });
  public editFolderFormSubmitted:boolean = false;
  public currentFolder:any;
  // Tracks which folder action dropdown is currently open so aria-expanded
  // can be bound on each trigger button. // Assumption: only one dropdown
  // can be open at a time.
  public activeFolderDropdownId: number | null = null;
  public showDeleteFolderModal:boolean = false;
  constructor(
    public mediaService: MediaService,
    private documentService: DocumentService,
    private formBuilder: FormBuilder,
    private router: Router,
    private titleService: Title,
    private focusService: FocusService
  ) { }

  ngOnInit(): void {
    // Determine current viewport size so width/offset calc are correct on first render
    this.isMobile = this.mediaService.isMobile(); // WCAG 1.4.10 – initialise mobile flag for initial template bindings
    this.user = JSON.parse(localStorage.getItem("user") || '0');
    this.showRetention = this.user.userProduct.isRetentionEnabled && this.user.userSetting.isRetentionEnabled;
    this.docBarCollapse.emit(this.isCollapsed);
    this.mediaService.breakpoint$.subscribe(() => {
      this.isMobile = this.mediaService.isMobile();
    });
    //reset selection on route change
    this.router.events.subscribe((val:any) => {
      if(val instanceof ResolveEnd) {
        this.outputSelection('');  
      } 
    })
    
    //message from action-list to change folder
    this.documentService.currentFolderId$.subscribe(id=>{
      //homefolder
      if(id == 999999) {
        this.outputSelection('homeFolder');
      }
      else{
       this.folders.forEach((folder:any) => {
          if(folder.id == id) {
            this.outputSelection('folder_'+ folder.id, folder.FolderText)
          }
        })
      }
    })

    // WCAG 2.4.2 – set an initial descriptive title. This will be updated
    // again from the `type` input setter when available.
    this.updateDocumentTitle();
  }

  /**
   * Sets a descriptive document <title> based on the current pageType so the
   * browser tab and assistive technologies convey meaningful information.
   */
  private updateDocumentTitle(): void {
    const baseTitle = this.pageType === 'template'
      ? $localize`Templates`
      : $localize`Documents`;
    // Set title to just the page name without the dashboard suffix
    this.titleService.setTitle(baseTitle);
  }

  getFolders() {
    this.documentService.getFolders(this.pageType=='template').subscribe(data=>{
      this.folders = data.folders;
    })
  }

  toggleCollapse(){
    this.isCollapsed = !this.isCollapsed;
    this.docBarCollapse.emit(this.isCollapsed);
  }

  /* WCAG 1.4.10 – overlay sidebar on mobile to prevent horizontal scroll
     Sidebar therefore always starts at the viewport edge (left:0). For larger
     viewports we retain the existing offset logic that keeps it adjacent to
     the parent navigation bar. */
  getLeftPosition() {
    if (this.isMobile) {
      return '0'; // Assumption: overlay pattern on mobile avoids two-dimensional scrolling
    }
    return this.isParentCollapsed ? '5rem' : '16rem'; // 80px/256px → 5rem/16rem – scales with text
  }

  /* WCAG 1.4.10 – compute responsive sidebar width so content reflows at
     320 px viewport without requiring horizontal scrolling. */
  getSidebarWidth() {
    if (this.isCollapsed) {
      return '5rem';
    }
    // On very small screens use 90 % of viewport width; on larger screens keep fixed rem width.
    return this.isMobile ? '90vw' : '11.875rem'; // 190 px → 11.875rem
  }

  outputSelection(selection:string, pageName?:string) {
    this.selectedItem = selection;
    let outPutPageName = '';
    switch(selection) {
      case 'homeFolder': outPutPageName = $localize`Home Folder`; break;
      case 'trash': outPutPageName = $localize`Trash`; break;
      case 'purgequeue': outPutPageName = $localize`Purge`; break;
      case 'SharedTeamDocumentsSiderBar': outPutPageName = $localize`Shared Documents`; break;
      case 'template_link': outPutPageName = $localize`Template Link`; break;
      case 'bulk_sign_template': outPutPageName = $localize`Bulk Sign`; break;
      case 'shared': outPutPageName = $localize`Shared Templates`; break;
      case '': outPutPageName = ''; break;
      default: outPutPageName = pageName || '';
    }
    this.menuSelect.emit([selection,outPutPageName]);

  }

  addFolder() {
    this.addFolderFormSubmitted = true;
    if (!this.addFolderForm.invalid) {
      let folderType = this.pageType=='document' ? 1 : 2;
      this.documentService.createFolder(this.addFolderForm.controls['name'].value, folderType).subscribe(val=>{
        this.addFolderFormSubmitted = false;
        this.showAddFolderModal = false;
        this.addFolderForm.reset();
        this.getFolders();
      })
    }
  }
  editFolder() {
    this.editFolderFormSubmitted = true;
    if (!this.editFolderForm.invalid) {
      this.documentService.renameFolder(this.editFolderForm.controls['name'].value, this.currentFolder.id).subscribe(val=>{
        this.editFolderFormSubmitted = false;
        this.showEditFolderModal = false;
        this.outputSelection('folder_'+this.currentFolder.id, this.editFolderForm.controls['name'].value)
        this.editFolderForm.reset();
        this.getFolders();

      })
    }
  }
  deleteFolder() {
      this.documentService.deleteFolder(this.currentFolder.id).subscribe(val=>{
        this.showDeleteFolderModal = false;
        if(val.success) {
          this.getFolders();
          this.outputSelection('');
        }
      })
    }
  addDocToFolder() {
    let newDocument = {
      title: "Unnamed Document " + (new Date()).getTime(),
      documentType: 5,
      folderId: this.currentFolder.id
      };
      this.documentService.create_new_doc(newDocument);
    }
  addTemplateToFolder() {
    let newDocument = {
      title: "Unnamed Document " + (new Date()).getTime(),
      invitations : [{
          recipientName: "Role 1",
          recipientEmail: '',
          actionType: this.code.INVITATION_TYPE_SIGNER,
          sequence: 1
      }],
      documentType: this.code.DOCUMENT_TYPE_REGULAR_TEMPLATE,
      folderId: this.currentFolder.id
    };
      this.documentService.create_new_doc(newDocument);
    }

  setCurrentFolder(folder: any) {
    this.currentFolder = folder;
  }

  /**
   * Updates `activeFolderDropdownId` when a folder's action menu visibility changes
   * so each trigger button exposes an accurate `aria-expanded` value. This
   * satisfies WCAG 2.1 – 4.1.2 Name, Role, Value for custom disclosure widgets.
   */
  onFolderDropdownVisibility(visible: boolean, folderId: number) {
    this.activeFolderDropdownId = visible ? folderId : (this.activeFolderDropdownId === folderId ? null : this.activeFolderDropdownId);
    if (visible) {
      setTimeout(() => this.focusFirstDropdownMenuItem(), 0);
    }
  }

  /**
   * Focus the first item in the folder action dropdown menu for accessibility
   */
  focusFirstDropdownMenuItem() {
    const menuItems = Array.from(document.querySelectorAll('ul[nz-menu][role="menu"] > li[role="menuitem"]')) as HTMLElement[];
    if (menuItems.length) {
      menuItems[0].focus();
    }
  }

  /**
   * Keyboard navigation for folder action dropdown menu
   * @param event KeyboardEvent
   * @param _index Unused, kept for template compatibility
   */
  onDropdownMenuKeydown(event: KeyboardEvent, _index: number) {
    // Only select menu items that are currently visible and focusable
    const menuItems = Array.from(document.querySelectorAll('ul[nz-menu][role="menu"] > li[role="menuitem"][tabindex="0"]:not([hidden])')) as HTMLElement[];
    if (!menuItems.length) return;
    // Find the index of the currently focused item
    const currentIndex = menuItems.findIndex(item => item === document.activeElement);
    let nextIndex = currentIndex;
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        nextIndex = (currentIndex + 1) % menuItems.length;
        menuItems[nextIndex].focus();
        break;
      case 'ArrowUp':
        event.preventDefault();
        nextIndex = (currentIndex - 1 + menuItems.length) % menuItems.length;
        menuItems[nextIndex].focus();
        break;
      case 'Home':
        event.preventDefault();
        menuItems[0].focus();
        break;
      case 'End':
        event.preventDefault();
        menuItems[menuItems.length - 1].focus();
        break;
      case 'Enter':
      case ' ': // Space
        event.preventDefault();
        if (currentIndex !== -1) {
          menuItems[currentIndex].click();
        }
        break;
      default:
        break;
    }
  }

  public openEditFolderModal(folderId: number) {
    this.focusService.saveFocus(() =>
      document.querySelector(`button.edit-folder[data-folder-id='${folderId}']`) as HTMLElement
    );
    this.showEditFolderModal = true;
  }

  public openDeleteFolderModal(folderId: number) {
    this.focusService.saveFocus(() =>
      document.querySelector(`button.edit-folder[data-folder-id='${folderId}']`) as HTMLElement
    );
    this.showDeleteFolderModal = true;
  }

  public closeEditFolderModal(folderId: number) {
    this.showEditFolderModal = false;
    setTimeout(() => {
      this.focusService.restoreFocusWithRetry(`button.edit-folder[data-folder-id='${folderId}']`);
    }, 0);
  }

  public closeDeleteFolderModal(folderId: number) {
    this.showDeleteFolderModal = false;
    setTimeout(() => {
      this.focusService.restoreFocusWithRetry(`button.edit-folder[data-folder-id='${folderId}']`);
    }, 0);
  }

  /**
   * Handle space key press for skip link
   * @param event Keyboard event
   */
  handleSpaceKeyPress(event: Event): void {
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    if (target) {
      target.click();
    }
  }
}
