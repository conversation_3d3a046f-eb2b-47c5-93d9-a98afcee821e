<!-- WCAG 1.4.4 – sidebar width converted to rem units to support 200 % text resizing; retains previous semantic and orientation fixes. -->
<!-- Validation patch: Replaced [class] bindings with [ngClass] to eliminate duplicate class attributes that violated WCAG 4.1.1 Parsing. -->
<!-- WCAG 3.1.2 Language of Parts – explicitly identify the language of this sidebar's content as English. This helps assistive technologies and automated translation tools accurately process the section, even when the surrounding page or injected user data may be in a different language. // Assumption: Sidebar static UI strings are authored in English. -->
<div *ngIf="(mediaService.isMobile() && isParentCollapsed) || !mediaService.isMobile()" class="doc-sider" lang="en"
[style.left]="getLeftPosition()" [style.visibility]="isCollapsed && mediaService.isMobile() ? 'hidden': ''"
[style.width]="getSidebarWidth()" [style.top.px]="headerOffset" [style.bottom.px]="0"> <!-- WCAG 1.4.10 – responsive width calculation to prevent horizontal scrolling on small screens -->

     <!-- Assumption: header acts as a button to collapse/expand sidebar -->
     <!-- Updated order matches visual presentation after CSS change; ensures screen-reader reading order aligns with visual (WCAG 1.3.2). // Assumption: arrow icon can be placed to the left without breaking design -->
     <!-- Added (keydown.space) handler so the custom button responds to Spacebar
          as well as Enter, meeting WCAG 2.1.1 Keyboard. -->
     <!-- WCAG 3.2.4 – Use a page-agnostic label ("Toggle sidebar") so the same
          control is identified consistently across Documents and Templates
          pages. Previously it included the word "documents", which caused the
          same component to expose different accessible names depending on the
          context. -->
     <div class="doc-bar-header" (click)="toggleCollapse()" (keydown.enter)="toggleCollapse()" (keydown.space)="toggleCollapse()" i18n-aria-label aria-label="Toggle sidebar"
[attr.aria-expanded]="!isCollapsed" tabindex="0" role="button" aria-controls="docSidebarNavigation"><!-- WCAG 2.4.6 – link toggle to controlled nav via aria-controls -->
          <i class="p-3"  [ngClass]="isCollapsed
         ? 'fa fa-chevron-right'
         : 'fa fa-chevron-left'" aria-hidden="true"></i>
          <span nz-icon nzType="folder" nzTheme="outline" class="text-3xl flex" aria-hidden="true"></span>
     </div>

     <!-- WCAG 2.1 Level AA – replaced non-semantic <div> wrappers with semantic list (<ul>/<li>) and single interactive <button> element. This eliminates nested interactive controls and ensures keyboard accessibility. -->
     <!-- WCAG 3.2.4 – Standardise nav label so it remains identical for the
          same sidebar component regardless of the page type where it is used
          (Documents vs Templates). -->
     <nav *ngIf="!isCollapsed" i18n-aria-label aria-label="Sidebar navigation" id="docSidebarNavigation"><!-- Added id so toggle button can reference via aria-controls -->
        <!-- WCAG 2.4.6 – provide a programmatic heading for the navigation region. The heading is visually hidden so it does not alter layout but allows assistive technologies to present a clear outline. -->
        <!-- WCAG 3.2.4 – Provide a consistent hidden heading for the sidebar so
             assistive-technology users encounter the same landmark name on
             every page. The functional purpose of the sidebar does not change
             between Documents and Templates views. -->
        <h2 class="sr-only" id="docSidebarHeading" i18n>Sidebar</h2>
        <!-- Document view links -->
        <ul *ngIf="pageType=='document'" class="p-0 m-0" role="list">
          <li>
            <!-- Fix duplicate class attribute (static + bound) which violated HTML parsing rules (WCAG 4.1.1). -->
            <button type="button" class="menu-item" (click)="outputSelection('')"
              [ngClass]="{ 'menu-selected': '' == selectedItem, 'menu-unselected': '' != selectedItem }"
              [attr.aria-current]="'' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="file" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>All Documents</span>
            </button>
          </li>
          <li *ngIf="user.roleId != code.roles.TEAM_DOCADMIN">
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('SharedTeamDocumentsSiderBar')"
              [ngClass]="{ 'menu-selected': 'SharedTeamDocumentsSiderBar' == selectedItem, 'menu-unselected': 'SharedTeamDocumentsSiderBar' != selectedItem }"
              [attr.aria-current]="'SharedTeamDocumentsSiderBar' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="solution" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Shared Documents</span>
            </button>
          </li>
          <li *ngIf="showRetention">
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('purgequeue')"
              [ngClass]="{ 'menu-selected': 'purgequeue' == selectedItem, 'menu-unselected': 'purgequeue' != selectedItem }"
              [attr.aria-current]="'purgequeue' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="wallet" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Purge</span>
            </button>
          </li>
          <!-- Moved Trash item to always render after Purge (when present) so its
               position remains consistent across the Documents and Templates
               views, meeting WCAG 2.1 – 3.2.3 Consistent Navigation. // Assumption: visual order change is acceptable to product design -->
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('trash')"
              [ngClass]="{ 'menu-selected': 'trash' == selectedItem, 'menu-unselected': 'trash' != selectedItem }"
              [attr.aria-current]="'trash' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="delete" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Trash</span>
            </button>
          </li>
        </ul>

        <!-- Template view links -->
        <ul *ngIf="pageType=='template'" class="p-0 m-0" role="list">
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('')"
              [ngClass]="{ 'menu-selected': '' == selectedItem, 'menu-unselected': '' != selectedItem }"
              [attr.aria-current]="'' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="file-text" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Templates</span>
            </button>
          </li>
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('template_link')"
              [ngClass]="{ 'menu-selected': 'template_link' == selectedItem, 'menu-unselected': 'template_link' != selectedItem }"
              [attr.aria-current]="'template_link' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="link" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Template Link</span>
            </button>
          </li>
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('bulk_sign_template')"
              [ngClass]="{ 'menu-selected': 'bulk_sign_template' == selectedItem, 'menu-unselected': 'bulk_sign_template' != selectedItem }"
              [attr.aria-current]="'bulk_sign_template' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="appstore" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Bulk Sign</span>
            </button>
          </li>
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('shared')"
              [ngClass]="{ 'menu-selected': 'shared' == selectedItem, 'menu-unselected': 'shared' != selectedItem }"
              [attr.aria-current]="'shared' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="solution" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Shared Templates</span>
            </button>
          </li>
          <li>
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('trash')"
              [ngClass]="{ 'menu-selected': 'trash' == selectedItem, 'menu-unselected': 'trash' != selectedItem }"
              [attr.aria-current]="'trash' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="delete" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Trash</span>
            </button>
          </li>
        </ul>

        <!-- Folder controls -->
        <div class="mt-3 mr-2 flex flex-row-reverse">
          <!-- Added aria-label so accessible name contains visible text “New Folder” without the decorative plus sign (WCAG 2.5.3). -->
          <button type="button" class="new-folder-btn" (click)="showAddFolderModal = true" aria-label="New Folder" i18n-attr.aria-label i18n>+ New Folder</button>
        </div>

        <ul role="list" class="p-0 ml-2">
          <!-- Home Folder (documents only) -->
          <li *ngIf="pageType=='document'">
            <!-- WCAG 4.1.1 duplicate attribute fix -->
            <button type="button" class="menu-item" (click)="outputSelection('homeFolder')"
              [ngClass]="{ 'menu-selected': 'homeFolder' == selectedItem, 'menu-unselected': 'homeFolder' != selectedItem }"
              [attr.aria-current]="'homeFolder' == selectedItem ? 'page' : null">
                 <span nz-icon nzType="home" nzTheme="outline" class="mx-2" aria-hidden="true"></span>
                 <span i18n>Home Folder</span>
            </button>
          </li>

          <!-- Dynamic folders -->
          <li *ngFor="let folder of folders" class="flex-space" role="listitem">
            <!-- Folder selection button -->
            <button type="button" class="menu-item folder-name flex-auto text-left"
              (click)="outputSelection('folder_'+folder.id, folder.FolderText); currentFolder = folder"
              [ngClass]="{ 'menu-selected': 'folder_'+folder.id == selectedItem, 'menu-unselected': 'folder_'+folder.id != selectedItem }"
              [attr.aria-current]="'folder_'+folder.id == selectedItem ? 'page' : null">
                {{folder.FolderText}}
                <!-- Dropdown trigger for folder actions -->
                <button type="button" class="edit-folder"
                [attr.data-folder-id]="folder.id"
                [attr.aria-label]="'Options for folder ' + folder.FolderText" i18n-attr.aria-label="@@editFolderOptions"
                aria-haspopup="menu"
                [attr.aria-expanded]="activeFolderDropdownId === folder.id"
                nz-dropdown [nzDropdownMenu]="editFolderDropdown" nzTrigger="click"
                (click)="setCurrentFolder(folder)"
                (keydown.enter)="setCurrentFolder(folder)"
                (nzVisibleChange)="onFolderDropdownVisibility($event, folder.id)">
                  <span nz-icon nzType="form" nzTheme="outline" aria-hidden="true"></span>
              </button>
            </button>
                        
            <!-- Dropdown menu for this folder -->
            <nz-dropdown-menu #editFolderDropdown="nzDropdownMenu">
              <ul nz-menu role="menu">
                <li *ngIf="pageType=='document'" nz-menu-item i18n (click)="addDocToFolder()"
                    role="menuitem" tabindex="0"
                    (keydown)="onDropdownMenuKeydown($event, 0)">
                   <span nz-icon nzType="plus" nzTheme="outline" class="mr-1" aria-hidden="true"></span>New Document
                </li>
                <li *ngIf="pageType=='template'" nz-menu-item i18n (click)="addTemplateToFolder()"
                    role="menuitem" tabindex="0"
                    (keydown)="onDropdownMenuKeydown($event, 1)">
                   <span nz-icon nzType="plus" nzTheme="outline" class="mr-1" aria-hidden="true"></span>Regular Template
                </li>
                <li nz-menu-item i18n (click)="openEditFolderModal(folder.id)"
                    role="menuitem" tabindex="0"
                    (keydown)="onDropdownMenuKeydown($event, 2)">
                   <span nz-icon nzType="edit" nzTheme="outline" class="mr-1" aria-hidden="true"></span>Rename Folder
                </li>
                <li nz-menu-item i18n (click)="openDeleteFolderModal(folder.id)"
                    role="menuitem" tabindex="0"
                    (keydown)="onDropdownMenuKeydown($event, 3)">
                   <span nz-icon nzType="delete" nzTheme="outline" class="mr-1" aria-hidden="true"></span>Delete Folder
                </li>
              </ul>
            </nz-dropdown-menu>
          </li>
        </ul>
     </nav>
</div>

<!-- Orientation fix (WCAG 2.1 – 1.3.4): use viewport-relative width so dialog fits in both portrait and landscape on small screens. -->
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showAddFolderModal" nzTitle="New Folder" [nzWidth]="'90vw'" (nzOnOk)="addFolder()"
    (nzOnCancel)="showAddFolderModal = false" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- Assumption: default true but made explicit -->
    <ng-container *nzModalContent>
     <form [formGroup]="addFolderForm">
        <!-- Layout table marked as presentational to avoid mis-identification as data table -->
        <table class="modal-table" role="presentation"> <!-- Added <tbody> for valid table structure (WCAG 4.1.1) -->
            <tbody>
            <tr>
                <td>
                    <!-- WCAG 3.3.2 – include required indication in the visible label so users know input is mandatory -->
                    <label for="addFolderName" i18n>Enter a new folder name <span class="sr-only">(required)</span><span aria-hidden="true">*</span></label>
                </td>
                <td aria-live="polite">
                    <!-- WCAG 3.3.1 – expose validation state programmatically. -->
                    <input id="addFolderName" i18n-aria-label aria-label="Enter a new folder name"
                        required aria-required="true"
                        [style.borderColor]="addFolderFormSubmitted && addFolderForm.controls['name'].errors ? 'red':''"
                        formControlName="name" nz-input
                        [attr.aria-invalid]="addFolderForm.controls['name'].invalid"
                        aria-describedby="addFolderNameError" />
                    <div *ngIf="addFolderFormSubmitted && addFolderForm.controls['name'].errors"
                        id="addFolderNameError" role="alert"
                        class="text-red-500" i18n>The folder name is required</div>
                </td>
            </tr>
            </tbody>
        </table>
     </form>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): make rename-folder dialog responsive across orientations. -->
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showEditFolderModal" nzTitle="New Folder Name" [nzWidth]="'90vw'" (nzOnOk)="editFolder()"
    (nzOnCancel)="closeEditFolderModal(currentFolder.id)" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- Assumption: ensure modal dismissible -->
    <ng-container *nzModalContent>
     <form [formGroup]="editFolderForm">
        <table class="modal-table" role="presentation"> <!-- Added <tbody> for valid table structure (WCAG 4.1.1) -->
            <tbody>
            <tr>
                <td>
                    <!-- WCAG 3.3.2 – include required indication in the visible label so users know input is mandatory -->
                    <label for="editFolderName" i18n>Enter a new folder name <span class="sr-only">(required)</span><span aria-hidden="true">*</span></label>
                </td>
                <td aria-live="polite">
                    <!-- WCAG 3.3.1 – expose validation state programmatically. -->
                    <input id="editFolderName" [placeholder]="currentFolder.FolderText" i18n-aria-label aria-label="Enter a new folder name"
                        required aria-required="true"
                        [style.borderColor]="editFolderFormSubmitted && editFolderForm.controls['name'].errors ? 'red':''"
                        formControlName="name" nz-input
                        [attr.aria-invalid]="editFolderForm.controls['name'].invalid"
                        aria-describedby="editFolderNameError" />
                    <div *ngIf="editFolderFormSubmitted && editFolderForm.controls['name'].errors"
                        id="editFolderNameError" role="alert"
                        class="text-red-500" i18n>The folder name is required</div>
                </td>
            </tr>
            </tbody>
        </table>
     </form>
    </ng-container>
</nz-modal>
<!-- Orientation fix (WCAG 2.1 – 1.3.4): ensure delete-confirmation dialog remains within viewport in both orientations. -->
<!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
<nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="showDeleteFolderModal" nzTitle="Confirm" [nzWidth]="'90vw'" (nzOnOk)="deleteFolder()"
    (nzOnCancel)="closeDeleteFolderModal(currentFolder.id)" [nzMaskClosable]="true" [nzKeyboard]="true"><!-- Assumption: modal should close on ESC/click outside -->
    <ng-container *nzModalContent>
        <table class="modal-table" role="presentation"> <!-- Added <tbody> for valid table structure (WCAG 4.1.1) -->
            <tbody>
            <tr>
                <td i18n>Do you want to delete the selected folder?
               </td>
            </tr>
            </tbody>
        </table>
    </ng-container>
</nz-modal>
