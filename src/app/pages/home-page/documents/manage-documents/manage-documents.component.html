<!-- WCAG 1.4.13 – Reviewed: no additional hover/focus content originates in this component; existing markup already meets dismissible/hoverable/persistent requirements. -->
<!-- CHANGE (2025-08-06): Audited for WCAG 2.5.3 "Label in Name" compliance – no interactive elements in
     this template display visible text that differs from their accessible name.
     Added this comment only; no functional changes required. -->
<h1 class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    Manage All Documents
</h1>
<!-- CHAN<PERSON> (2025-08-06): WCAG 4.1.2 – removed redundant off-screen live region that
     duplicated the spinner’s own role="status" semantics. This avoids double
     announcements in screen readers while still exposing the loading message
     via aria-label on the spinner itself. -->

<!-- Accessibility: aria-label supplies the loading message and role="status" turns
     the spinner into a polite live region so the change is announced exactly
     once. (SC 4.1.2 Name, Role, Value) -->
<nz-spin [nzSpinning]="isSpinning" i18n-aria-label aria-label="Loading content" role="status">
    <app-document-table (tableParams)="getDocuments($event.pageIndex, $event.pageSize, 'SharedTeamDocumentsSiderBar', $event.recipientStatus, $event.orderBy, $event.searchValue)" [tableData]="data"
    [tableTotal]="dataTotal" tableType="manage"></app-document-table>
</nz-spin>
