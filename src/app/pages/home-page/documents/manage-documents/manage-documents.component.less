/* File modified: WCAG 1.4.4 – converted remaining fixed pixel padding/max-width
   values to rem units so text can be resized up to 200 % without loss of
   content or functionality. Retains previous orientation (1.3.4) fixes. */
/* Updated: WCAG 1.4.10 – ensure Angular Material menu/popover overlays reflow
   within 320 px viewport without causing horizontal scroll. */

/* Added: WCAG 1.4.11 – increase non-text contrast by adding a dark border to
   overlay menus and a visible focus ring for menu items. */

/* WCAG 1.4.12 fix: allow custom text spacing – raised heading line-height to
   1.5em, removed `!important` overrides, and reset negative top margin that
   could cause overlap when text expands. */

@media (max-width:640px) {
  .mobile-title {
    font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // Assumption: 28-50 px
    /* WCAG 1.4.12 – allow user-defined text spacing: raise line-height to ≥1.5× and
       drop !important so the value can be overridden by a user style sheet. */ // Assumption: 1.5em meets minimum requirement while preserving design rhythm
    line-height: 1.5em;
    /* WCAG 1.4.4 – convert fixed padding so it scales with root font size */
    padding-bottom: 1.25rem; // 20px → 1.25rem
  }
}

.title {
  font-size: clamp(2.5rem, 6vw, 4.375rem); // Assumption: 40-70 px
  /* WCAG 1.4.12 – increase default line-height so heading retains ≥1.5× spacing
     and remove negative tailwind margin that could create overlap when the
     heading wraps or grows with custom text spacing. */ // Assumption: margin reset avoids content overlap without harming layout
  line-height: 1.5em;
  margin-top: 0 !important;
  font-weight: 275;
  color: #2D323D;
  /* WCAG 1.4.4 – replaced fixed px padding with rem for scalable spacing */
  padding-bottom: 3.4375rem; // 55px → 3.4375rem
}

/* Ensure any Ant Design modal opened from nested components remains within the
   viewport in both orientations by using viewport-relative width. */
:host ::ng-deep .ant-modal {
  width: 90vw !important; // Assumption: leaves comfortable margin on desktop
  /* WCAG 1.4.4 – convert fixed max-width to rem so modal scales with text */
  max-width: 37.5rem; // 600px → 37.5rem
}

// WCAG 2.1 – utility class to visually hide elements while keeping them accessible to screen readers (reused for status messages)
.sr-only {
  position: absolute; // Assumption: remove from visual flow
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

/* WCAG 1.3.3 (Sensory Characteristics):
   Added styling for .search-highlight so highlighted search terms use both
   colour and font-weight, ensuring emphasis is not conveyed by colour alone. */
.search-highlight {
  font-weight: 600; // Assumption: Semi-bold aligns with existing typography scale
  color: #D40D00; // Matches Tailwind's text-red-700 for visual consistency
  /* WCAG 1.4.1 – Use of Colour:
     Added dotted underline so emphasis remains perceivable when colour
     information is lost (e.g. in forced-colours or greyscale modes). */
  text-decoration: underline dotted currentColor; // Assumption: dotted style acceptable visually
}

/* WCAG 1.4.10 – responsive width for Angular Material menus (mat-menu)
   and other CDK overlay panels so that long option labels wrap instead of
   forcing the page to scroll sideways on small screens. */
:host ::ng-deep .cdk-overlay-pane .mat-menu-panel,
:host ::ng-deep .mat-menu-panel {
  max-width: 90vw; // Assumption: maintain comfortable side gutters on 320 px viewport
  width: auto !important; // Allow intrinsic width up to the max constraint
  white-space: normal; // Permit multi-line wrapping of menu items
  word-break: break-word; // Break long, unbroken strings if necessary
  // Added high-contrast border so the menu boundary is clearly
  // perceivable against the white page background (WCAG 1.4.11 – Non-text Contrast)
  border: 1px solid #797E8A; // Assumption: #797E8A ≈ 4.5 : 1 contrast on white
}

/* WCAG 1.4.11 – provide a visible keyboard focus indicator for each menu option
   within the overlay so that focus state meets the ≥ 3 : 1 contrast ratio */
:host ::ng-deep .mat-menu-item:focus,
:host ::ng-deep .mat-menu-item:focus-visible {
  outline: 3px solid #2D323D; // Assumption: #2D323D ≥ 3 : 1 on both white & grey backgrounds
  outline-offset: 2px;
}
