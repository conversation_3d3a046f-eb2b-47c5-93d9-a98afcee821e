import { Component, OnInit } from '@angular/core';
import { DocumentService } from "@core/services";
import { getCookie } from "@core/services/utilities.service";
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-manage-documents',
  templateUrl: './manage-documents.component.html',
  styleUrls: ['./manage-documents.component.less']
})
export class ManageDocumentsComponent implements OnInit {

  public data:any;
  public dataTotal:number = 0;
  public isSpinning:boolean = false;
  constructor(
    private documentService: DocumentService,
    private titleService: Title
  ) { }

  ngOnInit(): void {
    // Set document title for WCAG 2.4.2 compliance
    this.titleService.setTitle($localize`Manage Documents`);
    
    this.getDocuments(1, Number(getCookie('tablePageSize')) || 10,'SharedTeamDocumentsSiderBar', '', 'LastModifiedTime', '');
  }

  getDocuments(pageIndex:number, pageSize:number, status:string, recipientStatus:string, orderBy:string, searchValue:string) {
    this.isSpinning = true;
    this.documentService.getDocuments('Document',recipientStatus, pageIndex, pageSize, orderBy, 'DESC',status, false, searchValue, -1).subscribe(data=>{
      this.data = data.documents;
      var patt = new RegExp("(" + searchValue.replace(/(\W)/g, "\\$1") + ")", "ig");

      if(searchValue.length >=1) {
        this.data.forEach((document:any)=>{
          // WCAG 1.3.3 (Sensory Characteristics):
          // Replace colour-only highlight with semantic <mark> element and additional
          // font-weight styling (.search-highlight). Meaning is now conveyed by
          // more than colour alone. // Assumption: existing consumers safely render HTML.
          if (document.firstRecipientName && patt.test(document?.firstRecipientName)) {
            document.firstRecipientName = document.firstRecipientName.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
          if (document.title && patt.test(document.title)) {
            document.displayTitle = document.title.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
        })
      }
      this.dataTotal = data.documentCount;
      this.isSpinning = false;
    })
  }

}
