// Added ElementRef, Renderer2 and AfterViewInit to allow post-render DOM updates – WCAG 2.4.4
// Added Angular Title service so the component can set a descriptive
// <title> element that reflects the visible page heading – WCAG 2.4.2 (Page
// Titled).
// Assumption: There is no global title-management strategy in the
// application; therefore this component takes responsibility for its own
// view.
import { Component, Input, OnInit, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { DocumentService, TabIndexService } from "@core/services";
import { Router } from '@angular/router';
import {getCookie} from "@core/services/utilities.service";

@Component({
  selector: 'app-template-list',
  templateUrl: './template-list.component.html',
  styleUrls: ['./template-list.component.less']
})
export class TemplateListComponent implements OnInit, AfterViewInit {
  @Input() set status(status: string[]) {
    //filter documents based on sidebar menu
    this.displayStatus = '';
    if(status[0] =='') {
      this.type = 'regular_template';
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, this.displayStatus, '', 'LastModifiedTime', false, '');
    }
    else if(status[0] =='trash') {
      this.type = 'AllType';
      this.displayStatus = 'templateTrash'
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, this.displayStatus, '', 'LastModifiedTime', true, '');
    }
    else if(status[0].startsWith('folder_')) {
      this.type = 'AllType';
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, '', '', 'LastModifiedTime', false, '', Number(status[0].split('_')[1]));
    }
    else if(status[0] =='shared') {
      this.type = 'AllType';
      this.displayStatus = 'SharedTeamTemplates'
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, this.displayStatus, '', 'LastModifiedTime', false, '');
    }
    else{
      this.type = status[0];
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, '', '', 'LastModifiedTime', false, '');
    }
    if(status[1] == '') {
      this.pageName = $localize`Templates`;
    }
    else{
      this.pageName = status[1];
    }
    this.tableStatus = status[0];

    // Ensure the browser tab title remains in sync with the visible heading.
    this.updateDocumentTitle(); // WCAG 2.4.2
}

  public data:any;
  public dataTotal:number = 0;
  public isSpinning:boolean = false;
  public tableStatus:string = '';
  public displayStatus:string = '';
  public type: string = 'regular_template';
  public pageName = $localize `Templates`;
  public folderId:number = -1;
  // WCAG 4.1.1 – Ensure each instance of the component produces a unique
  // heading/landmark ID so that duplicate IDs are never rendered. Duplicate
  // IDs break the HTML parsing rules and can disrupt assistive technologies.
  // The static counter approach keeps the implementation lightweight and
  // free of additional dependencies. // Assumption: Component instances are
  // only created client-side so an in-memory counter is sufficient.
  private static nextHeadingId = 0;
  public headingId!: string;
  // Unique region ID used as the target for the local “Skip to template list”
  // link that lets keyboard users bypass preceding navigation blocks –
  // WCAG 2.4.1 (Bypass Blocks). // Assumption: Deriving the value from the
  // already-unique headingId guarantees uniqueness without extra state.
  public regionId!: string;
  constructor(
    private documentService: DocumentService,
    private router: Router,
    // Assumption: Safe to access host element for read-only DOM queries.
    private elementRef: ElementRef,
    private renderer: Renderer2,
    // Used to update the document <title> – WCAG 2.4.2
    private titleService: Title,
    private tabIndexService: TabIndexService
  ) { }

  ngOnInit(): void {
    // Generate a unique ID early so that it is available during template
    // rendering. Prefixing with the component selector helps avoid clashes
    // with IDs generated elsewhere in the application.
    this.headingId = `template-list-heading-${TemplateListComponent.nextHeadingId++}`;

    // Generate the corresponding region ID **after** headingId so the heading
    // ID is available.  Keeping the IDs closely related helps maintain
    // readability when debugging the DOM.
    this.regionId = `${this.headingId}-region`;

    this.router.onSameUrlNavigation = 'reload';
    this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, this.displayStatus, '', 'LastModifiedTime', false,'');

    // Set a descriptive <title> on initial load – WCAG 2.4.2
    this.updateDocumentTitle();
  }

  // WCAG 2.4.4 – Patch pagination controls after the initial render so that
  // automated audits (e.g. Pa11y) detect descriptive link text.
  ngAfterViewInit(): void {
    this.patchPaginationAriaLabels();
    this.addCheckboxKeyboardActivation();
  }

  getDocuments(pageIndex:number, pageSize:number, status:string, recipientStatus:string, orderBy:string, deleted:boolean, searchValue:string, folderId:number = -1) {
    this.folderId = folderId;
    this.isSpinning = true;
    this.data = [];
    this.documentService.getDocuments(this.type,recipientStatus, pageIndex, pageSize, orderBy, 'DESC',status,deleted, searchValue, folderId).subscribe(data=>{
      this.data = data.documents;
      var patt = new RegExp("(" + searchValue.replace(/(\W)/g, "\\$1") + ")", "ig");
      // WCAG 1.3.3 (Sensory Characteristics):
      // Replace found keywords with <mark> element that carries additional
      // non-colour styling (defined in component stylesheet) so the emphasis
      // is not conveyed by colour alone.
      // Assumption: Using the semantic <mark> element along with increased
      // font-weight satisfies the requirement without impacting existing
      // functionality.
      if (searchValue.length >= 1) {
        this.data.forEach((document: any) => {
          if (document.firstRecipientName && patt.test(document?.firstRecipientName)) {
            document.firstRecipientName = document.firstRecipientName.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
          if (document.title && patt.test(document.title)) {
            document.displayTitle = document.title.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
        });
      }
      this.dataTotal = data.documentCount;
      this.isSpinning = false;

      // Re-apply aria-labels because pagination markup may have updated.
      this.patchPaginationAriaLabels();
      // Re-apply checkbox keyboard handlers because table data has updated
      this.addCheckboxKeyboardActivation();
    })
  }

  refreshTable($event:any) {

    this.getDocuments($event.pageIndex, $event.pageSize, this.displayStatus , $event.recipientStatus, $event.orderBy, this.tableStatus == 'trash', $event.searchValue, this.folderId)
  }

  /**
   * Adds meaningful `aria-label` attributes to every pagination control inside
   * this component so that screen-reader users can identify each link’s
   * function when navigating the links list.  Implementation mirrors the
   * approach taken in other components across the code-base for consistency.
   */
  private patchPaginationAriaLabels(): void {
    // Wait until NG-ZORRO finishes updating the DOM.
    setTimeout(() => {
      const paginations: NodeListOf<HTMLElement> = this.elementRef.nativeElement.querySelectorAll('ul.ant-pagination');
      paginations.forEach(pagination => {
        // WCAG 3.2.3 (Consistent Navigation): Ensure every pagination
        // component exposes itself as a <navigation> landmark with a
        // consistent accessible name so assistive-technology users encounter
        // the same landmark structure on every page where the component
        // appears. Adding the attribute here (instead of editing the
        // third-party NG-ZORRO template) keeps the change self-contained and
        // guarantees it is applied after the library re-renders the
        // pagination DOM. // Assumption: There is no existing role or
        // aria-label on the <ul> that would clash with this enhancement.
        if (!pagination.hasAttribute('role')) {
          this.renderer.setAttribute(pagination, 'role', 'navigation');
        }
        if (!pagination.hasAttribute('aria-label')) {
          this.renderer.setAttribute(pagination, 'aria-label', $localize`Pagination`);
        }

        const items = pagination.querySelectorAll('li');
        items.forEach(li => {
          const control: HTMLElement | null = li.querySelector('a, button');
          if (!control) { return; }

          // Ensure <a> elements rendered without href behave like true buttons
          // for assistive technologies and keyboard users. Anchors lacking an
          // href are removed from the tab order by default and are announced
          // as generic text. Providing an explicit role *and* tabindex
          // exposes the intended semantics, satisfying WCAG 4.1.2.
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button'); // Assumption: NG-ZORRO click handlers already present
            if (!control.hasAttribute('tabindex')) {
              this.renderer.setAttribute(control, 'tabindex', '0');
            }
          }

          // Skip accessible-name processing if another mechanism already
          // supplied one (prevents duplicate work and potential conflicts).
          if (control.hasAttribute('aria-label')) { return; }

          if (li.classList.contains('ant-pagination-prev')) {
            this.renderer.setAttribute(control, 'aria-label', $localize`Previous page`);
          } else if (li.classList.contains('ant-pagination-next')) {
            this.renderer.setAttribute(control, 'aria-label', $localize`Next page`);
          } else if (li.classList.contains('ant-pagination-jump-prev')) {
            this.renderer.setAttribute(control, 'aria-label', $localize`Jump to previous pages`);
          } else if (li.classList.contains('ant-pagination-jump-next')) {
            this.renderer.setAttribute(control, 'aria-label', $localize`Jump to next pages`);
          } else if (li.classList.contains('ant-pagination-item')) {
            const text = control.textContent?.trim();
            if (text && /^\d+$/.test(text)) {
              this.renderer.setAttribute(control, 'aria-label', $localize`Page ${text}`);
            }
            if (li.classList.contains('ant-pagination-item-active')) {
              this.renderer.setAttribute(control, 'aria-current', 'page');
            }
          }
        });
      });
    });
  }

  /**
   * Handle space key press for skip link
   * @param event Event
   */
  handleSpaceKeyPress(event: Event): void {
    event.preventDefault();
    const target = event.currentTarget as HTMLElement;
    if (target) {
      // Save current position before navigation
      this.tabIndexService.saveTabPosition();
      target.click();
    }
  }

  /**
   * Keeps the document <title> in sync with the component's visible heading
   * so that the topic of the page is conveyed to all users, including those
   * relying on assistive technologies or browser UI such as tabs and
   * bookmarks – fulfilment of WCAG 2.4.2 (Page Titled).
   *
   * Pattern used: "<Page name>" to keep titles concise and clear.
   */
  private updateDocumentTitle(): void {
    if (!this.pageName) { return; }

    // Set title to just the page name without the app suffix
    this.titleService.setTitle(this.pageName);
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to checkboxes to ensure they respond
   * to Enter and Space key activation for toggling checked state.
   */
  private addCheckboxKeyboardActivation(): void {
    setTimeout(() => {
      // Find all actual checkbox inputs and ensure they're the only focusable elements
      const allCheckboxInputs = this.elementRef.nativeElement.querySelectorAll('.ant-checkbox-input, input[type="checkbox"]');
      
      allCheckboxInputs.forEach((checkboxInput: HTMLElement) => {
        // Ensure the checkbox input itself is focusable
        if (!checkboxInput.hasAttribute('tabindex')) {
          this.renderer.setAttribute(checkboxInput, 'tabindex', '0');
        }

        // Remove focusability from parent cell to avoid duplicate tab stops
        const parentCell = checkboxInput.closest('td, th');
        if (parentCell) {
          this.renderer.removeAttribute(parentCell, 'tabindex');
        }

        // Add keyboard handler directly to the checkbox input
        if (!(checkboxInput as any).__checkboxKeyboardHandler) {
          this.addKeyboardHandlerToCheckbox(checkboxInput);
        }
      });

      // Handle header checkbox specially (it might not have a direct input)
      const headerCheckbox = this.elementRef.nativeElement.querySelector('th[tabindex="0"]');
      if (headerCheckbox) {
        // Look for checkbox input inside header
        const headerInput = headerCheckbox.querySelector('.ant-checkbox-input, input[type="checkbox"]');
        if (headerInput) {
          // Transfer focus to the input
          this.renderer.setAttribute(headerInput, 'tabindex', '0');
          this.renderer.removeAttribute(headerCheckbox, 'tabindex');
          if (!(headerInput as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerInput as HTMLElement);
          }
        } else {
          // Fallback: keep header cell focusable if no input found
          if (!(headerCheckbox as any).__checkboxKeyboardHandler) {
            this.addKeyboardHandlerToCheckbox(headerCheckbox);
          }
        }
      }
    }, 100);
  }

  private addKeyboardHandlerToCheckbox(element: HTMLElement): void {
    const keyboardHandler = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // If this IS a checkbox input, click it directly
        if (element.classList.contains('ant-checkbox-input') || element.tagName === 'INPUT') {
          element.click();
        } else {
          // Otherwise, look for checkbox input inside the element
          const checkboxInput = element.querySelector('.ant-checkbox-input, input[type="checkbox"]');
          if (checkboxInput) {
            (checkboxInput as HTMLElement).click();
          } else {
            // Fallback: trigger click on the element itself (for header checkbox cells)
            element.click();
          }
        }
      }
    };

    // Store reference and add listener
    (element as any).__checkboxKeyboardHandler = keyboardHandler;
    this.renderer.listen(element, 'keydown', keyboardHandler);
  }
}
