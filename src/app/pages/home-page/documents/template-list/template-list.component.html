<!-- Added explicit heading ID and landmark relationship – WCAG 2.4.6
     This allows assistive-technology users to understand that the following
     table constitutes a distinct region on the page whose purpose is
     described by the heading text. -->
<!-- WCAG 4.1.1 – Replaced hard-coded ID with component-generated unique ID
     to prevent duplicate IDs when multiple <app-template-list> instances are
     present on the same page. Duplicate IDs violate the parsing rule and can
     confuse assistive technologies. -->
<!-- WCAG 2.4.1 – Add a component-scoped skip link so keyboard users can
     bypass repeated navigation elements and move focus directly to the main
     template list region. Hidden off-screen until focused, see stylesheet. -->

<h1 [attr.id]="headingId" class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    {{ pageName }}
</h1>

<!-- Wrap the template table in a section that references the heading so it
     forms a named landmark for easier navigation – WCAG 2.4.6. -->
<!-- The main region that the skip link targets. `tabindex="-1"` allows the
     link activation to move focus programmatically to this landmark so that
     screen readers announce it. -->
<section [attr.aria-labelledby]="headingId" [attr.id]="regionId" tabindex="-1">
  <!-- WCAG 1.1.1: spinner accessibility via live region & hidden text -->
  <div role="status"
       aria-live="polite"
       [attr.aria-busy]="isSpinning">
    <span class="visually-hidden" *ngIf="isSpinning">Loading content</span>
    <!-- WCAG 4.1.2 – removed unconditional aria-hidden that concealed the
         table from assistive technologies once the spinner finished. The live
         region and aria-busy state already communicate the loading status
         without needing to hide descendant content. -->
    <nz-spin [nzSpinning]="isSpinning">
      <app-document-table (tableParams)="refreshTable($event)" [tableData]="data"
        [tableTotal]="dataTotal" tableType="template" [changeTableStatus]="tableStatus"></app-document-table>
    </nz-spin>
  </div>
</section>
