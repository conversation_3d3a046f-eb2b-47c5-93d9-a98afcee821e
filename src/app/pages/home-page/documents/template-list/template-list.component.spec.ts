// Injected Title stub so the component can set the document title during the
// test without depending on the real browser API – WCAG 2.4.2.
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';

import { TemplateListComponent } from './template-list.component';
import { DocumentService } from '@core/services';

describe('TemplateListComponent', () => {
  let component: TemplateListComponent;
  let fixture: ComponentFixture<TemplateListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TemplateListComponent ],
      // Provide minimal stubs so the component can instantiate without
      // hitting the real Angular router or DocumentService – keeps the test
      // lightweight and focused. // Assumption: Only the methods accessed in
      // ngOnInit need to exist on the stub.
      providers: [
        { provide: DocumentService, useValue: { getDocuments: () => ({ subscribe: (_cb: any) => {} }) } },
        { provide: Title, useValue: { setTitle: (_t: string) => {} } }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TemplateListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
