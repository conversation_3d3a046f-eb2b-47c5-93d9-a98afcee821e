// Orientation fix (WCAG 2.1 – 1.3.4):
// Converted fixed pixel font-sizes to responsive `clamp()` values so headings
// resize fluidly in both portrait and landscape orientations without causing
// horizontal scroll or clipping. // Assumption: Existing design intent was to
// keep approximate 70 px on desktop and ~50 px on small screens.
// WCAG 1.4.12 fix: raised line-height to 1.5em for `.title` and `.mobile-title`
// to ensure no loss of content/functionality under custom text-spacing.

@media (max-width:640px) {
    .mobile-title {
        font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // 28px‒50px
        /* WCAG 1.4.12 – switch to a relative ≥1.5× line-height so text does not
           get clipped when users apply custom text-spacing styles. Using 1.5em
           satisfies the minimum requirement while preserving visual rhythm. */ // Assumption: 1.5× is visually acceptable across designs
        line-height: 1.5em; // allows user override without !important
        /* WCAG 1.4.4 – replaced fixed pixel spacing with rem so it scales with text */ // Assumption: base font-size = 16px
        padding-bottom: 1.25rem !important; // 20px → 1.25rem
        margin-top: -0.625rem; // -10px → -0.625rem
    }
}

.title {
    font-size: clamp(2.5rem, 6vw, 4.375rem); // 40px‒70px
    /* WCAG 1.4.12 – increase to ≥1.5× font size to honour user text-spacing
       preferences and avoid clipping when line-height is overridden. */ // Assumption: 1.5em retains intended design spacing
    line-height: 1.5em;
    font-weight: 275;
    color: #2D323D;
    /* WCAG 1.4.4 – converted fixed pixel padding to rem for scalable spacing */ // Assumption: base font-size = 16px
    padding-bottom: 3.4375rem; // 55px → 3.4375rem
}

/* Added to satisfy WCAG 1.3.3 (Sensory Characteristics):
   Highlighted search terms now have increased font-weight in addition
   to colour so that emphasis is not conveyed by colour alone. */
.search-highlight {
  font-weight: 600; // semi-bold for additional non-colour emphasis
  color: #D40D00; // matches Tailwind's text-red-700 for visual consistency
  /* WCAG 1.4.1 – Use of Colour:
     Added underline so the highlight remains perceivable without colour. */ // Assumption: underline acceptable visually.
  text-decoration: underline;
}

// Visually hidden utility for accessibility (WCAG 1.1.1)
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* WCAG 2.4.1 – Off-screen until focused skip-link styling so it remains
   discoverable via keyboard but does not visually clutter the interface. */
.skip-link {
  position: absolute;
  left: -9999px; // hide off-screen while still keyboard-focusable
  top: auto;
  width: auto;
  height: auto;
  overflow: hidden;
  white-space: nowrap;
}

.skip-link:focus-visible {
  left: 0; // slide into viewport when focused
  top: 0;
  z-index: 1000; // ensure link is above overlays
  background: #ffffff;
  color: #000000;
  padding: 0.5rem 1rem;
  border: 2px solid #000000; // visible focus indication with ≥3:1 contrast
}

/* WCAG 2.4.7 – Visible Focus (Level AA)
   -------------------------------------------------------------
   Global style resets elsewhere in the code-base remove the native browser
   outline from many interactive controls, leaving keyboard users unable to
   determine which element currently holds focus.  The following rules
   reinstate a high-contrast focus indicator for every focusable element
   rendered by this component, without affecting the rest of the
   application.  Because the stylesheet is compiled with Angular's default
   `ViewEncapsulation.Emulated`, the selectors are automatically scoped to
   this component so a simple element selector is sufficient and avoids the
   need for the discouraged `::ng-deep` combinator.

   The indicator borrows the element's current text colour so it always
   achieves at least the same colour contrast that regular text already
   satisfies, while the 3 px thickness and 2 px offset comply with the
   visual-contrast recommendations in WCAG's focus appearance guidance. */

a:focus-visible,
button:focus-visible,
[role='button']:focus-visible,
[tabindex]:not([tabindex='-1']):focus-visible {
  outline: 3px solid currentColor;
  outline-offset: 2px;
}

/* Fallback for browsers that do not yet support the `:focus-visible` pseudo
   class (e.g. Safari ≤15).  The `@supports` feature-query ensures the older
   `:focus` selector is only applied when `:focus-visible` is unavailable so
   mouse users in modern browsers do not see an unnecessary focus ring on
   click – WCAG 2.4.7 (Focus Visible). */

@supports not selector(:focus-visible) {
  a:focus,
  button:focus,
  [role='button']:focus,
  [tabindex]:not([tabindex='-1']):focus {
    outline: 3px solid currentColor;
    outline-offset: 2px;
  }
}

/* WCAG 2.4.7 – enhanced focus indicators for checkboxes */
:host ::ng-deep .ant-checkbox-input:focus + .ant-checkbox-inner,
:host ::ng-deep .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
  z-index: 1; // ensure focus ring appears above other elements
}

/* Ensure checkbox container doesn't clip focus outline */
:host ::ng-deep .ant-checkbox-wrapper {
  overflow: visible !important;
}

/* Increase checkbox stroke contrast (Ant Design) */
:host ::ng-deep .ant-checkbox-inner {
  border: 1px solid #797E8A !important; // replace faint default border
}

/* Ensure selected checkbox maintains focus outline when focused */
:host ::ng-deep .ant-checkbox-checked .ant-checkbox-input:focus + .ant-checkbox-inner,
:host ::ng-deep .ant-checkbox-checked .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
  outline: 3px solid #2D323D !important;
  outline-offset: 2px !important;
}
