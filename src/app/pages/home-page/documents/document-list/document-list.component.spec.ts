// Added NzModalModule to TestBed so the injected NzModalService can be resolved
// after introducing descriptive error suggestions (WCAG 2.1 – SC 3.3.3).
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NzModalModule } from 'ng-zorro-antd/modal';

import { DocumentListComponent } from './document-list.component';

describe('DocumentListComponent', () => {
  let component: DocumentListComponent;
  let fixture: ComponentFixture<DocumentListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NzModalModule],
      declarations: [ DocumentListComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DocumentListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
