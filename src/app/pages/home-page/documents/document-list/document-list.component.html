<!-- WCAG 2.1 – added live status message and role for spinner to satisfy Success Criterion 4.1.3 (Status Messages). -->
<h1 class="title -mt-6 ml-5 font-thin mobile-title" i18n>
    {{pageName}}
</h1>
<div class="m-3" *ngIf="tableStatus == 'purgequeue'" i18n>
    Note: All documents in the Purge Queue will be automatically deleted permanently after 14 days.
</div>

<!-- Visually hidden live region that notifies screen-reader users when content is loading -->
<span *ngIf="isSpinning" class="sr-only" aria-live="polite" i18n>Loading content</span>

<!-- Spinner now explicitly presents as a status element for assistive technology -->
<nz-spin [nzSpinning]="isSpinning" i18n-aria-label aria-label="Loading content" role="status">
    <app-document-table (tableParams)="refreshTable($event)" [tableData]="data"
    [tableTotal]="dataTotal" tableType="document" [changeTableStatus]="tableStatus" [setDocStatus]="docStatus"></app-document-table>
</nz-spin>