/* Updated for WCAG 1.4.12: Raised default line-height to 1.5em and removed negative margins/!important overrides that could block user-defined text spacing. */
/* WCAG 1.3.3 fix: Added .search-highlight style for non-colour emphasis on highlighted search terms. */
/* Additional WCAG 1.4.4 fix: Replaced pixel-based spacing (20px, -10px, 55px) with rem units so text can be resized to 200 % without causing layout issues. */

// Orientation fix (WCAG 2.1 – 1.3.4):
// Converted fixed pixel font-size/line-height on mobile titles to responsive
// `clamp()` values so headings scale fluidly in both portrait and landscape
// orientations without causing horizontal scroll or truncation.
// Assumption: Design intends ~50 px on large mobile/desktop and ~28 px on the
// smallest screens – clamp range mirrors previous fixes in other components.
@media (max-width:640px) {
  .mobile-title {
    font-size: clamp(1.75rem, 9vw, 3.125rem) !important; // 28px‒50px

    // WCAG 1.4.4 – converted fixed pixel spacing to rem so heading scales with user text zoom
    padding-bottom: 1.25rem; // 20px → 1.25rem – Assumption: base font-size = 16px
    /* WCAG 1.4.12 fix: remove negative top margin that could cause content
       overlap when users increase line/word/letter spacing. */
  }
}

  
// Orientation fix (WCAG 2.1 – 1.3.4): switched desktop heading to responsive
// clamp() so it adapts to viewport width changes across orientations. Keeps
// original 70 px maximum while allowing shrink to ~40 px on narrow screens.
.title {
  font-size: clamp(2.5rem, 6vw, 4.375rem); // 40px‒70px
  /* WCAG 1.4.12 fix: increase line-height so the heading retains ≥1.5× spacing
     by default, and enable user override by avoiding !important. */
  /* WCAG 1.4.12 (Text Spacing): override the negative top-margin introduced by
     the utility class `-mt-6` that is applied in the template.  Negative
     margins can cause content to overlap when users adjust paragraph spacing
     or line-height.  Resetting to `0` ensures adequate separation while
     preserving the overall layout. */
  margin-top: 0;
  font-weight: 275;
  color: #2D323D;
  // WCAG 1.4.4 – use rem for bottom padding so large heading does not clip adjacent content when text is enlarged
  padding-bottom: 3.4375rem; // 55px → 3.4375rem

}

// WCAG 2.1 – visually hidden utility class for screen-reader only content (used for live status)
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}

// Assumption: 600 weight fits visual design while maintaining brand guidelines.
.search-highlight {
  font-weight: 600; /* non-colour emphasis */
  color: #D40D00;
}
