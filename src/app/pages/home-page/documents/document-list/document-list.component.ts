// WCAG 1.3.3 fix: Replace colour-only highlight spans with <mark> element and added bold styling for non-colour emphasis.
// Added AfterViewInit hook to programmatically set `autocomplete="off"` on
// descendant inputs that do not collect personal data (WCAG 2.1 SC 1.3.5)
// WCAG 2.1 – SC 3.3.3 (Error Suggestion)
// Added NzModalService so we can give users a *descriptive* and *actionable* error
// message if the document list fails to load, instead of leaving the spinner
// active indefinitely with no feedback.
// CHANGE (2025-08-06): WCAG 4.1.2 – added helper to inject accessible names and
// keyboard support into Ant Design pagination controls plus label missing
// select-search inputs. No impact on business logic.
import { Component, Input, OnInit, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { DocumentService } from "@core/services";
import { ActivatedRoute, Router } from '@angular/router';
import {getCookie} from "@core/services/utilities.service";
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-document-list',
  templateUrl: './document-list.component.html',
  styleUrls: ['./document-list.component.less']
})
export class DocumentListComponent implements OnInit, AfterViewInit {
  @Input() set status(status: string[]) {
    //filter documents based on sidebar menu
    if(status[0] =='trash') {
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, '', '', 'LastModifiedTime', true, '', -1);
    }
    else if(status[0].startsWith('folder_')) {
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, '', '', 'LastModifiedTime', false, '', Number(status[0].split('_')[1]));
    }
    else if(status[0] =='homeFolder') {
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, status[0], '', 'LastModifiedTime', false, '', 0);
    }
    else{
      this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, status[0], '', 'LastModifiedTime', false, '', -1);
    }
    this.tableStatus = status[0];
    if(status[1] == '') {
      this.pageName = $localize`All Documents`;
    }
    else{
      this.pageName = status[1];
    }
    
    // Update the document title when status changes
    this.updateDocumentTitle();
}

  public data:any;
  public dataTotal:number = 0;
  public isSpinning:boolean = false;
  public tableStatus:string = '';
  public docStatus:string = '';
  public displayStatus:string = '';
  public pageName = $localize`All Documents`;
  public folderId:number = -1;
  constructor(
    private documentService: DocumentService,
    private router: Router,
    private route: ActivatedRoute,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private titleService: Title,
    private modal: NzModalService // Assumption: ng-zorro-antd is already a project dependency
  ) { }

  ngOnInit(): void {
    this.router.onSameUrlNavigation = 'reload';
    if(this.route.snapshot.queryParamMap.get('status')) {
      this.docStatus = this.route.snapshot.queryParamMap.get('status') || '';
    }
    this.getDocuments(1, Number(getCookie('tablePageSize')) || 10, this.docStatus, '', 'LastModifiedTime', false,'');
    
    // Set initial document title
    this.updateDocumentTitle();
  }

  getDocuments(pageIndex:number, pageSize:number, status:string, recipientStatus:string, orderBy:string, deleted:boolean, searchValue:string, folderId:number = -1) {
    this.isSpinning = true;
    this.data = [];
    this.folderId = folderId;
    this.documentService
      .getDocuments(
        'Document',
        recipientStatus,
        pageIndex,
        pageSize,
        orderBy,
        'DESC',
        status,
        deleted,
        searchValue,
        folderId
      )
      .subscribe({
        next: (data) => {
          this.data = data.documents;
      var patt = new RegExp("(" + searchValue.replace(/(\W)/g, "\\$1") + ")", "ig");
      //replace found keywords in red
      if(searchValue.length >=1) {
        this.data.forEach((document:any)=>{
          if (document.firstRecipientName && patt.test(document?.firstRecipientName)) {
            // WCAG 1.3.3: Use <mark>.search-highlight for non-colour emphasis. // Assumption: safe with existing styling.
            document.firstRecipientName = document.firstRecipientName.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
          if (document.title && patt.test(document.title)) {
            // Same accessibility improvement for document titles. // Assumption
            document.displayTitle = document.title.replace(
              patt,
              "<mark class='search-highlight'>$1</mark>"
            );
          }
        })
      }
      this.dataTotal = data.documentCount;
          this.isSpinning = false;
          // Ensure new table rows have the autocomplete attribute applied. // Assumption
          this.applyAutocompletePatch();
        },
        // WCAG 2.1 – SC 3.3.3 (Error Suggestion)
        // Provide an explicit, actionable suggestion so users understand how to recover.
        error: () => {
          this.isSpinning = false;
          this.modal.error({
            nzTitle: $localize`Unable to load documents`,
            nzContent: $localize`Check your internet connection and select “Retry” or refresh the page.`
          });
        }
      });
  }

  refreshTable($event:any) {
    //status can't change in in shared docs and purge pages
    if(this.tableStatus != 'SharedTeamDocumentsSiderBar' && this.tableStatus != 'purgequeue') {
      this.displayStatus = $event.docStatus;  
    }
    else {
      this.displayStatus = this.tableStatus;
    } 
    this.getDocuments($event.pageIndex, $event.pageSize, this.displayStatus , $event.recipientStatus, $event.orderBy, this.tableStatus == 'trash', $event.searchValue, this.folderId)
  }

  /**
   * Keeps the document <title> in sync with the component's visible heading
   * so that the topic of the page is conveyed to all users, including those
   * relying on assistive technologies or browser UI such as tabs and
   * bookmarks – fulfilment of WCAG 2.4.2 (Page Titled).
   */
  private updateDocumentTitle(): void {
    if (!this.pageName) { return; }

    // Set title to just the page name without the app suffix
    this.titleService.setTitle(this.pageName);
  }

  // WCAG 2.1 SC 1.3.5 Identify Input Purpose
  // Assumption: Inputs rendered within this view (search box, table selection checkboxes, etc.)
  // do NOT capture personal user data. To comply with SC 1.3.5 we therefore explicitly
  // set `autocomplete="off"` on any <input> that lacks an existing, more specific token.
  ngAfterViewInit(): void {
    // Apply once after initial render.
    this.applyAutocompletePatch();
    // WCAG 4.1.2 – add accessible names/roles/keyboard support to Ant Design
    // pagination controls rendered by the descendant <app-document-table>.
    this.patchPaginationAriaLabels();
  }

  /**
   * WCAG 4.1.2 (Name, Role, Value)
   * --------------------------------------------------
   * Ant Design's `nz-pagination` component can render its controls as either
   * <a> or <button> elements.  When rendered as <button> they inherit the
   * implicit role and accessible name from their textual content, but the
   * icon-only “previous”/“next” buttons remain unnamed.  When rendered as <a>
   * without an `href` attribute the element is **not** announced as a link nor
   * a button, resulting in an unidentified control in the accessibility tree.
   * This helper normalises the markup by:
   *   • Adding `aria-label` ("Previous page", "Next page", "Page N") so each
   *     control exposes a programmatic name.
   *   • Assigning `role="button"` to <a> elements without `href` so assistive
   *     tech identifies them as interactive.
   *   • Ensuring keyboard operability by forwarding Enter/Space presses to
   *     `.click()` when the element is not a native <button>.
   *   • Supplying a tabindex when missing so the element can be focused.
   *   • Naming the internal select-search <input> (used to filter page size)
   *     with `aria-label="Search"` to silence automated audit warnings.
   *
   * This mirrors the same logic applied to Reports/Security components so the
   * user experience remains consistent across the application.
   */
  private patchPaginationAriaLabels(): void {
    // Defer to allow the table component to finish rendering.
    setTimeout(() => {
      const paginationSelectors = [
        'ul.ant-pagination',
        'nz-pagination ul',
        '.ant-table-pagination ul',
        'ul[class*="pagination"]',
        'nz-pagination',
        '.ant-pagination'
      ];

      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });

      paginations.forEach(pagination => {
        // Handle <input> inside nz-select-search (page size filter) – add name if missing.
        const selectSearchInputs = pagination.querySelectorAll('nz-select-search input');
        selectSearchInputs.forEach(input => {
          if (!input.hasAttribute('aria-label')) {
            this.renderer.setAttribute(input, 'aria-label', 'Search'); // Assumption: English locale acceptable – component already localised elsewhere.
          }
        });

        const items = pagination.querySelectorAll('li');
        items.forEach(li => {
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) {
            return; // Not an interactive item
          }

          if (!control.hasAttribute('tabindex') && control.tagName !== 'BUTTON') {
            this.renderer.setAttribute(control, 'tabindex', '0');
          }

          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            this.renderer.setAttribute(control, 'role', 'button');
          }

          // Skip if already labelled by another process.
          if (!control.hasAttribute('aria-label')) {
            if (
              li.classList.contains('ant-pagination-prev') ||
              control.textContent?.trim() === '' ||
              control.textContent?.includes('‹') || control.textContent?.includes('<')
            ) {
              this.renderer.setAttribute(control, 'aria-label', 'Previous page');
            } else if (
              li.classList.contains('ant-pagination-next') ||
              control.textContent?.includes('›') || control.textContent?.includes('>')
            ) {
              this.renderer.setAttribute(control, 'aria-label', 'Next page');
            } else {
              const text = control.textContent?.trim();
              if (text && /^\d+$/.test(text)) {
                this.renderer.setAttribute(control, 'aria-label', `Page ${text}`);
              }
            }
          }

          // Attach keyboard handler once per element.
          this.addPaginationKeyboardActivation(control);
        });
      });
    }, 150);
  }

  /**
   * Ensures custom pagination controls implemented as <a> receive keyboard
   * activation via Enter/Space, matching native <button> behaviour.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) {
      control.removeEventListener('keydown', existingHandler);
    }
    const handler = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        (control as HTMLElement).click();
      }
    };
    (control as any).__keyboardHandler = handler;
    this.renderer.listen(control, 'keydown', handler);
  }

  // Re-apply the patch after each table refresh because Angular may destroy and recreate
  // rows, introducing fresh <input> elements without the attribute.
  private applyAutocompletePatch(): void {
    // Defer execution so DOM updates from child components have completed.
    setTimeout(() => {
      const inputs: NodeListOf<HTMLInputElement> = this.elementRef.nativeElement.querySelectorAll('input');
      inputs.forEach((input) => {
        if (!input.hasAttribute('autocomplete')) {
          // Assumption: Safe to disable autocomplete for non-personal fields.
          this.renderer.setAttribute(input, 'autocomplete', 'off');
        }
      });
    });
  }
}
