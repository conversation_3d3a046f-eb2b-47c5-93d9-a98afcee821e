import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {HomePageComponent} from "./home-page.component";

import {DashboardComponent} from "./dashboard/dashboard.component";
import {AdminGuard} from "@core/guards/admin.guard";
import { UsersAndTeamsComponent } from './users-and-teams/users-and-teams.component';
import { CancelComponent } from './reports/cancel/cancel.component';
import { SecurityComponent } from './reports/security/security.component';
import { UsageComponent } from './reports/usage/usage.component';
import { ContactsComponent } from './contacts/contacts.component';
import { BulkExportComponent } from './bulk-export/bulk-export.component';
import { InboxComponent } from './documents/inbox/inbox.component';
import { DocumentListComponent } from './documents/document-list/document-list.component';
import { TemplateListComponent } from './documents/template-list/template-list.component';
import { ManagePlanComponent } from './plan/manage-plan/manage-plan.component';
import { SealsComponent } from './seals/seals.component';
import { ApplicationsIframeComponent } from './applications-iframe/applications-iframe.component';
import { PaymentMethodComponent } from './plan/payment-method/payment-method.component';
import { ManageDocumentsComponent } from './documents/manage-documents/manage-documents.component';
import { InvoiceComponent } from './plan/invoice/invoice.component';
import { ChangePlanComponent } from './plan/change-plan/change-plan.component';


const childRoutes = [
   {path:'dashboard', component:DashboardComponent},
   {path:'teams', component:UsersAndTeamsComponent},
   {path:'seals', component:SealsComponent},
   {path:'reports/cancel', component:CancelComponent},
   {path:'reports/security', component:SecurityComponent},
   {path:'reports/usage', component:UsageComponent},
   {path:'contacts', component:ContactsComponent},
   {path:'bulk-export/:id', component:BulkExportComponent},
   {path:'bulk-export', component:BulkExportComponent},
   {path:'applications', component:ApplicationsIframeComponent},
   {path:'inbox', component:InboxComponent},
   {path:'documents', component:DocumentListComponent},
   {path:'templates', component:TemplateListComponent},
   {path:'plan', component:ManagePlanComponent},
   {path:'change-plan', component:ChangePlanComponent},
   {path:'payment', component:PaymentMethodComponent},
   {path:'invoice/list', component:InvoiceComponent},
   {path:'invoice/view/:id', component:InvoiceComponent},
   {path:'manage-documents', component:ManageDocumentsComponent},
   {path: '',  redirectTo:'dashboard', pathMatch:'full'}
]

const routes: Routes = [
    {
        path:'admin-console',
        component:HomePageComponent,
        children:childRoutes,
        canActivate:[AdminGuard] 
    },
    {
        path:'',
        component:HomePageComponent,
        children:childRoutes,
    },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomePageRoutingModule { }
