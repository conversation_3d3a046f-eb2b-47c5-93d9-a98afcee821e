
<!-- CHANGE (2025-08-01): WCAG 2.4.1 (Bypass Blocks)
     ------------------------------------------------
     Added an in-component skip link and wrapped the primary content in a
     <main> landmark so keyboard and assistive-technology users can bypass the
     repeating navigation/action bar when they land on the route.  The anchor
     is visually hidden until focused (styles added in the accompanying .less
     file) and targets the unique ID `bulkexport-main`.  A tabindex="-1" on the
     <main> element allows programmatic focus after route change as required by
     the guideline. -->



<!-- PRIMARY LANDMARK: all bulk-export page content is now contained within the
     <main> element so the skip link target is meaningful. -->
<!-- Changed <main> to <div role="main"> to resolve Angular template parse error
     (closing </main> was reported as unexpected). The role="main" continues to
     satisfy WCAG 2.4.1 (Bypass Blocks) by exposing a main landmark without
     relying on the <main> element, which conflicts with certain parent/child
     constraints in Angular’s HTML parser. -->
<div id="main" role="main" tabindex="-1">
<!-- Updated labels and visible text to adhere to WCAG 2.4.6 (Headings & Labels) guideline -->
<!-- Added actionable error-suggestion messages to meet WCAG 2.1 SC 3.3.3 (Error Suggestion) -->
<!-- WCAG 2.1 – SC 3.2.3 (Consistent Navigation):
     The action bar is a repeated navigation region that appears on multiple
     list/detail/create views throughout the application.  Exposing it as a
     semantic <nav> landmark (instead of a generic <div>) ensures assistive
     technologies recognise it as navigation and can present it consistently
     with other, similar toolbars.  The visual layout and existing CSS classes
     remain unchanged so functionality and styling are preserved. -->
<nav class="ml-5 m-3 mb-4 action-bar" aria-label="Bulk export actions"><!-- Assumption: aria-label provides unique landmark name -->
    <h1 *ngIf="!toggleAddExport && !toggleDetails" class="text-2xl" i18n>
        Export History
    </h1>
    <!-- WCAG 2.1 SC 3.2.4 – Use the same phrase everywhere the user is creating
         a bulk export so the functionality is identified consistently across
         the UI (button text, page heading and document title). -->
    <h1 *ngIf="toggleAddExport" class="text-2xl" i18n>
        Create Bulk Export
    </h1>
    <h1 *ngIf="toggleDetails" class="text-2xl" i18n>
        Export Details
    </h1>
    <div>
        <!-- WCAG 2.1 SC 3.2.4 – Align button label with page heading so the
             action is named consistently wherever it appears. -->
        <button *ngIf="!toggleAddExport && !toggleDetails" nz-button nzType="primary" routerLink="/bulk-export/create" i18n>Create Bulk Export</button>
        <button *ngIf="toggleAddExport || toggleDetails" nz-button nzType="primary" routerLink="/bulk-export" i18n>View Exports</button>
    </div>
</nav>
<!-- WCAG validation – wrapped previous plain text comment to avoid it rendering in the UI -->
<!-- Accessibility: added aria-label to spinner for WCAG 1.1.1 compliance -->
<nz-spin [nzSpinning]="isSpinning" i18n-aria-label aria-label="Loading content">

    <div *ngIf="toggleDetails" class="export-details">
        <div>
            <div><span class="font-bold" i18n>Exported by: </span>{{exportInfo.exportUser}}</div>
            <div><span class="font-bold" i18n>Export date: </span>{{exportInfo.exportDate}}</div>
            <div><span class="font-bold" i18n>Export period: </span>{{exportInfo.exportPeriod}}</div>
            <div><span class="font-bold" i18n>Document status: </span>{{getDocStatuses(exportInfo.documentStatus)}}</div>
            <div><span class="font-bold" i18n>Documents were exported to: </span>{{exportInfo.exportDestination}}</div>
            <div><span class="font-bold" i18n>Exported documents deleted: </span>
                <span *ngIf="exportInfo.exportedDocumentsDeleted == 0" i18n>Yes</span>
                <span *ngIf="exportInfo.exportedDocumentsDeleted == -1" i18n>No</span>
                <span *ngIf="exportInfo.exportedDocumentsDeleted > 0" i18n>{{exportInfo.exportedDocumentsDeleted}} failed</span>
            </div>
        </div>
        <!-- WCAG 2.4.4 – clarified button purpose so its label is meaningful in isolation -->
        <!-- WCAG 2.4.6 – visible button text now describes what will be downloaded -->
        <!-- CHANGE (2025-08-01): WCAG 2.5.3 – The aria-label must contain the exact visible text.
             We therefore start the attribute with “Download Documents”. Additional context is
             kept after an em-dash so it still conveys purpose without breaking the rule. -->
        <button  nz-button nzType="primary" (click)="downloadExport()" i18n-aria-label aria-label="Download Documents — exported files" i18n>Download Documents</button>
    </div>
    <div *ngIf="!toggleAddExport && !toggleDetails" class="mx-4 mb-2" i18n>
        Exports may take a few minutes to process. You will receive an email when the transfer is complete.
        <br>
        NOTE: If the files selected for the export are greater than 1GB, the documents will be automatically seperated into seperate files.
    </div>
    
    <!-- <nz-table *ngIf="!toggleAddExport && !toggleDetails" class="mx-3" #basicTable [nzData]="displayData"  [nzShowPagination]="false"> -->
    <!-- WCAG 1.4.10 – wrapped table in single-axis scroll region so page never requires horizontal scrolling on small viewports -->
    <div *ngIf="!toggleAddExport && !toggleDetails" class="table-responsive" role="region" aria-labelledby="bulkExportTableCaption" tabindex="-1">
    <nz-table  #basicTable nzFrontPagination="false"
    [nzTotal]="tableTotal" [nzData]="displayData" (nzPageIndexChange)="getData($event)">    
        <caption id="bulkExportTableCaption" class="sr-only" i18n>Bulk exports – list of export jobs, status and destination</caption>
        <thead>
            <tr>
                <th i18n>Document Period</th>
                <th i18n>Document Status</th>
                <th i18n>Export Date</th>
                <th i18n>Expire Date</th>
                <th i18n>Exported documents deleted</th>
                <th i18n>Export Status</th>
                <th i18n>Exported To</th>
            </tr>
        </thead>
        <tbody>
            <!-- WCAG 4.1.2 – Make row keyboard-focusable; removed invalid role override
                 because <tr> has strong native semantics (row) that cannot be
                 changed per ARIA-in-HTML. Screen readers will still announce it
                 as a row, but the presence of an accessible name and keyboard
                 handlers makes the interaction perceivable and operable. -->
            <tr tabindex="0" *ngFor="let data of basicTable.data"
                (click)="navigateToExport(data.id, data.exportStatus)" class="cursor-pointer"
                i18n-aria-label
                [attr.aria-label]="'Open export for period:' + data.documentStartDate + ' - ' + data.documentEndDate"
                (keydown.enter)="navigateToExport(data.id, data.exportStatus)"
                (keydown.space)="navigateToExport(data.id, data.exportStatus)">
                <td>{{data.documentStartDate}} - {{data.documentEndDate}}</td>
                <td>{{ getDocStatuses(data.documentStatus) }}</td>
                <td>{{data.exportDate}}</td>
                <td>{{data.exportExpiredDate}}</td>
                <td *ngIf="data.exportedDocumentsDeleted == 0" i18n>Yes</td>
                <td *ngIf="data.exportedDocumentsDeleted == -1" i18n>No</td>
                <td *ngIf="data.exportedDocumentsDeleted > 0" i18n>{{data.exportedDocumentsDeleted}} failed</td>
                <td [style.color]="exportStatuses[data.exportStatus].color">{{exportStatuses[data.exportStatus].text}}</td>
                <td >
                    <div class="flex-space">
                        <span>{{exportDestinations[data.exportToType].text}}</span>
                        <!-- Accessibility: added descriptive aria-label for delete icon (WCAG 1.1.1) -->
                        <!-- WCAG 2.1 – make icon keyboard-accessible by turning it into a button -->
                        <span *ngIf="data.exportStatus == 1 || data.exportStatus == 2" class="delete-icon" nz-icon nzType="delete" nzTheme="outline"
                              role="button" tabindex="0"
                              i18n-aria-label aria-label="Cancel export task"
                              (click)="$event.stopPropagation();deleteConfirmationModal = true; selectedExport = data.id"
                              (keydown.enter)="$event.stopPropagation();deleteConfirmationModal = true; selectedExport = data.id"
                              (keydown.space)="$event.stopPropagation();deleteConfirmationModal = true; selectedExport = data.id"></span>
                    </div>
                </td>
            </tr>
        </tbody>
    </nz-table>
    </div>
    <!-- <mat-paginator *ngIf="!toggleAddExport && !toggleDetails" [showFirstLastButtons]="true" [length]="tableTotal" class="mx-3"
    [pageSize]="10" [hidePageSize]="true" (page)="getData($event.pageIndex+1)" ></mat-paginator>     -->

    <!-- <nz-table *ngIf="toggleDetails" class="mx-3" #basicDetailsTable  [nzData]="displayDetailsData" [nzShowPagination]="false"> -->
    <!-- WCAG 1.4.10 – wrapped table in single-axis scroll region to prevent page-level horizontal scroll at 320 px viewport -->
    <div *ngIf="toggleDetails" class="table-responsive" role="region" aria-labelledby="exportDetailsTableCaption" tabindex="0">
    <nz-table  #basicDetailsTable nzFrontPagination="false"
        [nzTotal]="detailsTableTotal" [nzData]="displayDetailsData" (nzPageIndexChange)="getExportDetails(exportInfo.id, $event)">
        <caption id="exportDetailsTableCaption" class="sr-only" i18n>Export details – list of exported documents and their status</caption>
        <thead>
            <tr>
                <th nzWidth="3.75rem"></th> <!-- WCAG 1.4.4 – width converted from 60px to rem -->
                <th i18n>Document Name</th>
                <th i18n>Owner</th>
                <th i18n>Last Modified</th>
                <th i18n>Status</th>
                <th i18n>Document Size</th>
            </tr>
        </thead>
        <tbody>
            <ng-container *ngFor="let data of basicDetailsTable.data">
                <tr>
                    <!-- WCAG 4.1.2 – Provide role, keyboard operability and accessible name for the built-in
                         Ant Design expandable table control so its purpose is exposed to AT users. -->
                    <td [nzExpand]="expandSet.has(data.id)"
                        role="button"
                        tabindex="0"
                        (nzExpandChange)="onExpandChange(data.id, $event)"
                        (keydown.enter)="onExpandChange(data.id, !expandSet.has(data.id))"
                        (keydown.space)="onExpandChange(data.id, !expandSet.has(data.id))"
                        [attr.aria-expanded]="expandSet.has(data.id)"
                        i18n-aria-label
                        [attr.aria-label]="(expandSet.has(data.id) ? 'Collapse details for ' : 'Expand details for ') + data.downloadFileName">
                    </td>
                    <td colspan="5" *ngIf="currentTime >= getDateTime(data.expireTime) || exportInfo.exportToType != 0">{{data.downloadFileName}}</td>
                    <td colspan="5" *ngIf="currentTime < getDateTime(data.expireTime) && exportInfo.exportToType == 0">
                        <!-- WCAG 1.4.1 – ensure link remains recognisable without relying on colour alone -->
                        <a class="download-link" [href]="data.downloadUrl"  target="_blank" >{{data.downloadFileName}}</a> (Expires on {{data.expireTime}})
                    </td>
                </tr>
                <ng-container *ngIf="expandSet.has(data.id)" >
                    <tr *ngFor="let doc of data.documentList.slice(0, expandSize[data.id]);">
                        <td>
                            <!-- Accessibility: provide text alternatives for status icons (WCAG 1.1.1) -->
                            <span *ngIf="doc.exportStatus == 2" nz-icon class="text-red-500" nzType="close-circle" nzTheme="outline"
                                  i18n-aria-label aria-label="Export failed"></span>
                            <span *ngIf="doc.exportStatus == 1" nz-icon class="text-green-500" nzType="check-circle" nzTheme="outline"
                                  i18n-aria-label aria-label="Export succeeded"></span>
                        </td>
                        <td>{{doc.documentName}}</td>
                        <td *ngIf="user.userProduct.IsThisATeamProduct">{{doc.teamName}} <span class="italic" ><br>({{doc.userName}})</span></td>
                        <td *ngIf="!user.userProduct.IsThisATeamProduct">{{doc.userName}}</td>
                        <td>{{doc.lastModify}}</td>
                        <td>{{getDocStatus(doc.documentStatus)}}</td>
                        <td>{{doc.exportStatus == 0 ? '' : doc.documentSize}}</td>
                    </tr>
                    <!-- WCAG 2.1 – ensure keyboard access for expandable 'More…' row -->
                    <!-- CHANGE (2025-08-01): WCAG 2.5.3 – prepend the visible label text “More…” to the
                         accessible name so voice-control users can activate the row by saying
                         exactly what they see on screen. -->
                    <tr *ngIf="data.documentList.length > expandSize[data.id]" class="more" role="button" tabindex="0"
                        (click)="expandSize[data.id] =  expandSize[data.id] + 10"
                        (keydown.enter)="expandSize[data.id] =  expandSize[data.id] + 10"
                        (keydown.space)="expandSize[data.id] =  expandSize[data.id] + 10"
                        i18n-aria-label aria-label="More... – load more documents">
                        <td colspan="6">
                            <i class="fa-regular fa-chevron-down mr-1" aria-hidden="true"></i>
                            <span i18n>More...</span> ({{data.documentList.length - expandSize[data.id]}})
                        </td>
                    </tr>
                    <!-- Validation fix: removed superfluous closing </tr> that caused template parse error -->
                </ng-container>
            </ng-container>
        </tbody>
    </nz-table>
    </div>
    <!-- <mat-paginator *ngIf="toggleDetails" [showFirstLastButtons]="true" [length]="detailsTableTotal" class="mx-3"
    [pageSize]="10" [hidePageSize]="true" (page)="getData($event.pageIndex+1)" ></mat-paginator>     -->

</nz-spin>
    <div class="form-container">
        <form class="exportForm" *ngIf="toggleAddExport && !toggleDetails" [formGroup]="addExportForm">
            <span class="formItemTitle" i18n>Document Status</span>
            <mat-form-field class="material-select-height select">
                <!-- CHANGE (2025-08-01): WCAG 2.5.3 – Ensure accessible name contains full
                     visible text including capitalisation. Moved comment outside of the
                     opening tag to avoid breaking Angular template parsing. -->
                <mat-select name="doc status"
                           formControlName="docStatus"
                           (valueChange)="statusSelectHelper($event)"
                           multiple
                           i18n-aria-label aria-label="Document Status"
                           [attr.aria-invalid]="addExportFormSubmitted && addExportForm.controls['docStatus'].invalid"
                           [attr.aria-describedby]="'docstatus-error'">
                    <mat-option *ngFor="let status of documentStatuses" [value]="status.value">{{status.text}}</mat-option>
                </mat-select>
            </mat-form-field>
            <!-- WCAG 3.3.1 (Error Identification): provide persistent element referenced by aria-describedby -->
            <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion)
                 Provide actionable guidance so the user understands how to fix the
                 error instead of a terse “required” message. -->
            <div id="docstatus-error" role="alert" class="text-red-500" aria-live="polite"
                 [hidden]="!(addExportFormSubmitted && addExportForm.controls['docStatus'].errors)" i18n>
                 Select at least one document status before exporting.
            </div>
            <span class="formItemTitle" i18n>Export documents from</span>
            <div class="flex items-center flex-wrap">
                <span class="mr-2">
                    <span class="mr-2" i18n>Start Date:</span>
                    <mat-form-field appearance="standard" >
                        <!-- WCAG 2.4.6 – replace generic label with a descriptive one so users immediately understand which date they are choosing -->
                        <mat-label i18n>Select start date</mat-label>
                        <!-- Added autocomplete="off" because this date field does not capture personal data (WCAG 2.1 SC 1.3.5) -->
                        <!-- CHANGE (2025-08-01): WCAG 2.5.3 – Accessible name must include the visible
                             label text “Select start date”. Updated accordingly. -->
                        <input matInput [matDatepicker]="picker" formControlName="fromDate" i18n-aria-label aria-label="Select start date" autocomplete="off"
                               [attr.aria-invalid]="addExportFormSubmitted && addExportForm.controls['fromDate'].invalid || !validDates"
                               [attr.aria-describedby]="'daterange-error date-required-error'">
                        <mat-hint i18n>YYYY-MM-DD</mat-hint>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker ></mat-datepicker>
                    </mat-form-field>
                </span>
               <span>
                <span class="mr-2" i18n>End Date:</span>
                <mat-form-field appearance="standard">
                    <!-- WCAG 2.4.6 – descriptive label clarifies that this field captures the end date of the export period -->
                    <mat-label i18n>Select end date</mat-label>
                    <!-- Added autocomplete="off" because this date field does not capture personal data (WCAG 2.1 SC 1.3.5) -->
                    <!-- CHANGE (2025-08-01): WCAG 2.5.3 – Align accessible name with visible label
                         “Select end date”. -->
                    <input matInput [matDatepicker]="picker2" formControlName="toDate" i18n-aria-label aria-label="Select end date" autocomplete="off"
                           [attr.aria-invalid]="addExportFormSubmitted && addExportForm.controls['toDate'].invalid || !validDates"
                           [attr.aria-describedby]="'daterange-error date-required-error'">
                    <mat-hint i18n>YYYY-MM-DD</mat-hint>
                    <mat-datepicker-toggle matSuffix [for]="picker2"></mat-datepicker-toggle>
                    <mat-datepicker #picker2 ></mat-datepicker>
                  </mat-form-field>
               </span>
            </div>
            <!-- WCAG 3.3.1: persistent error containers referenced by both date inputs -->
            <div id="daterange-error" role="alert" class="text-red-500" aria-live="polite"
                 [hidden]="validDates">
                 <!-- Assumption: message only relevant when validDates === false -->
                 <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion) -->
                 <span i18n>Select a start date that is earlier than the end date.</span>
            </div>
            <div id="date-required-error" role="alert" class="text-red-500" aria-live="polite"
                 [hidden]="!(addExportFormSubmitted && (addExportForm.controls['fromDate'].errors || addExportForm.controls['toDate'].errors))">
                 <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion) -->
                 <span i18n>Select both a start date and an end date.</span>
            </div>
            <span class="formItemTitle" i18n>Export documents to</span>
            <mat-radio-group formControlName="exportTo" class="flex-down" i18n-aria-label aria-label="Export documents to">
                <mat-radio-button value="0" >
                    <span class="flex items-center">
                        <span i18n>Direct Download</span> 
                        <!-- WCAG 1.4.13 – make tooltip content keyboard-operable, dismissible with ESC and persistent on hover -->
                        <span nz-icon nzType="question-circle" nzTheme="twotone" nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                              [nzTooltipTitle]="directDownloadTooltip"
                              nzTooltipTrigger="click"
                              [nzTooltipVisible]="directDownloadTooltipVisible"
                              (mouseenter)="directDownloadTooltipVisible = true; integrationTooltipVisible = false"
                              (mouseleave)="directDownloadTooltipVisible = false"
                              (click)="$event.stopPropagation(); directDownloadTooltipVisible = !directDownloadTooltipVisible; integrationTooltipVisible = false"
                              (keydown.enter)="directDownloadTooltipVisible = !directDownloadTooltipVisible; integrationTooltipVisible = false"
                              (keydown.space)="directDownloadTooltipVisible = !directDownloadTooltipVisible; integrationTooltipVisible = false"
                              (keydown.escape)="directDownloadTooltipVisible = false"
                              style="font-size: 1.125rem;" class="ml-2" role="button" tabindex="0"
                              aria-haspopup="dialog"
                              [attr.aria-expanded]="directDownloadTooltipVisible"
                              aria-label="More information about Direct Download"></span> <!-- WCAG 1.4.4 – font-size 18px→rem -->
                    </span>
                </mat-radio-button>
                <mat-radio-button value="1" class="flex" [disabled]="userInfo?.accountdetails?.branding?.SyncType == 0 || !user.userProduct.IsThisATeamProduct">
                    <span class="flex items-center">
                        <span i18n>Integration</span> 
                        <span nz-icon nzType="question-circle" nzTheme="twotone" *ngIf="user.userProduct.IsThisATeamProduct"
                              nzTwotoneColor="#017BC6" nz-tooltip nzTooltipPlacement="rightTop"
                              [nzTooltipTitle]="integrationTooltip"
                              nzTooltipTrigger="click"
                              [nzTooltipVisible]="integrationTooltipVisible"
                              (mouseenter)="integrationTooltipVisible = true; directDownloadTooltipVisible = false"
                              (mouseleave)="integrationTooltipVisible = false"
                              (click)="$event.stopPropagation(); integrationTooltipVisible = !integrationTooltipVisible; directDownloadTooltipVisible = false"
                              (keydown.enter)="integrationTooltipVisible = !integrationTooltipVisible; directDownloadTooltipVisible = false"
                              (keydown.space)="integrationTooltipVisible = !integrationTooltipVisible; directDownloadTooltipVisible = false"
                              (keydown.escape)="integrationTooltipVisible = false"
                              style="font-size: 1.125rem;" class="ml-2" role="button" tabindex="0"
                              aria-haspopup="dialog"
                              [attr.aria-expanded]="integrationTooltipVisible"
                              aria-label="More information about Integration export"></span> <!-- WCAG 1.4.4 – font-size 18px→rem -->
                        <a *ngIf="userInfo?.accountdetails?.branding?.SyncType == 0 && user.userProduct.IsThisATeamProduct" 
                           class="download-link ml-2 flex items-center" 
                           style="margin-top: 0px;"
                           href="/UI/userSettings.html" 
                           tabindex="0" 
                           i18n-aria-label 
                           aria-label="Configure storage integration settings">Configure storage integration</a>
                    </span>
                </mat-radio-button>
            </mat-radio-group>
            <ng-template #directDownloadTooltip>
                <div i18n>
                   A zipped file of your documents will be generated and an email will be sent with download link.
                </div>
              </ng-template>
            <ng-template #integrationTooltip>
                <div *ngIf="userInfo?.accountdetails?.branding?.SyncType == 0" i18n >
                    Your bulk download will be pushed to your storage integration. You currently do not have one set up.
                </div>
                <div *ngIf="userInfo?.accountdetails?.branding?.SyncType != 0" i18n>
                    Your bulk download will be pushed to your {{getIntegrationText(userInfo?.accountdetails?.branding?.SyncType)}} when complete.
                </div>
              </ng-template>
            <span class="formItemTitle" i18n>File Type</span>
            <mat-radio-group formControlName="fileType" class="flex-down" i18n-aria-label aria-label="File Type">
                <mat-radio-button value="1">ZIP</mat-radio-button>
                <mat-radio-button value="2">TGZ</mat-radio-button>
            </mat-radio-group>

            <span class="flex mt-2 mb-2">
                <mat-slide-toggle i18n-aria-label aria-label="Automatically Delete Exported Documents"
                 appToggleEnterKey color="primary" formControlName="autoDelete" class="mr-2"></mat-slide-toggle>
                <span i18n>Automatically Delete Exported Documents</span>
            </span>
           

        </form>
    <div>
    <button *ngIf="toggleAddExport && !toggleDetails" nz-button nzType="primary"  (click)="export(true)" i18n>Export</button>
    <!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
    <!-- Orientation fix (WCAG 2.1 – 1.3.4): use viewport-relative width so the dialog fits within
         both portrait and landscape viewports on small screens. -->
    <nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="deleteConfirmationModal" nzTitle="Confirm"
        [nzWidth]="'90vw'" (nzOnOk)="deleteExport(selectedExport)" (nzOnCancel)="deleteConfirmationModal = false; selectedExport = -1"
        [nzMaskClosable]="true" [nzKeyboard]="true">
        <ng-container *nzModalContent>
            <table class="modal-table">
                <tr>
                    <td i18n>Are you sure you want to cancel this export task?</td>
                </tr>
            </table>
        </ng-container>
    </nz-modal>
    <!-- WCAG 1.4.13 – enable dismissal of modal via ESC key and click outside -->
    <!-- Orientation fix (WCAG 2.1 – 1.3.4): ensure confirmation dialog adapts to viewport width -->
    <nz-modal i18n-nzCancelText nzCancelText="Cancel" i18n-nzTitle [(nzVisible)]="bulkDeleteConfirmationModal"
        nzTitle="Automatically Delete Exported Documents" [nzWidth]="'90vw'" (nzOnOk)="validateDelete()"
        (nzOnCancel)="bulkDeleteConfirmationModal = false;" [nzMaskClosable]="true" [nzKeyboard]="true">
        <ng-container *nzModalContent>
            <form  [formGroup]="deleteConfirmationForm">
                <table class="modal-table">
                    <tr>
                        <td i18n>Once your bulk export is complete, your documents will be removed from Signority. This action is permanent and cannot be undone.</td>
                    </tr>
                    <tr>
                        <!-- WCAG 2.4.6 – use explicit <label> element linked to the input for a clearer programmatic name -->
                        <td><label for="confirmDeleteInput" i18n>Type 'DELETE' to confirm:</label></td>
                    </tr>
                    <tr>
                        <td>
                            <!-- Added autocomplete="off" to non-personal confirmation input (WCAG 2.1 SC 1.3.5) -->
                            <input id="confirmDeleteInput" formControlName="confirmDelete" nz-input i18n-aria-label aria-label="Type 'DELETE' to confirm" autocomplete="off"
                                   [attr.aria-invalid]="deleteConfirmationFormSubmitted && deleteConfirmationForm.controls['confirmDelete'].invalid"
                                   [attr.aria-describedby]="'confirmdelete-error'" /><br>
                            <!-- WCAG 3.3.1 – persistent error element referenced by aria-describedby -->
                            <div id="confirmdelete-error" role="alert" class="text-red-500" aria-live="polite"
                                 [hidden]="!(deleteConfirmationFormSubmitted && deleteConfirmationForm.controls['confirmDelete'].errors)" i18n>
                                 <!-- WCAG 2.1 – SC 3.3.3 (Error Suggestion) -->
                                 Type the word “DELETE” in capital letters to confirm.
                            </div>
                        </td>
                    </tr>
                </table>
            </form>
        </ng-container>
    </nz-modal>

</div>
