@media (max-width:640px) {
    .mobile-title {
    font-size: 3.125rem !important; // WCAG 1.4.4 – convert 50px to rem for scalable text
        /* WCAG 1.4.12 fix: switch to relative ≥1.5× line-height and drop !important so
           users can override with even larger spacing via user style sheets. */
        line-height: 1.5em; // Assumption: 1em ≈ font-size, so 1.5em ≥ 1.5×
        padding-bottom: 1.25rem !important; // WCAG 1.4.4 – convert 20px to rem
    }
  }

  .title {
    font-size: 4.375rem; // WCAG 1.4.4 – convert 70px to rem so heading scales with text zoom
    /* WCAG 1.4.12 fix: increase default line-height to ≥1.5× font size so heading
       text is not clipped when users apply custom text-spacing. Using a relative
       unit allows further user overrides. */
    line-height: 1.5em; // Assumption: 1.5em meets guideline while preserving layout
    font-weight: 275;
    color: #2D323D;
    padding-bottom: 3.4375rem; // WCAG 1.4.4 – convert 55px to rem
  }

  .action-bar {
    display: flex; 
    justify-content: space-between;
    flex-wrap: wrap; // WCAG 1.4.10 – allow toolbar items to wrap on small screens to avoid horizontal overflow
    border-bottom:1px solid grey;
    padding-bottom: 0.9375rem; // WCAG 1.4.4 – convert 15px to rem
  }

  .exportForm {
    margin-left: 1.25rem; // WCAG 1.4.4 – convert 20px to rem
    display: flex;
    flex-direction: column;
    .formItemTitle {
      margin-top:0.9375rem; // WCAG 1.4.4 – convert 15px to rem
      margin-bottom:0.3125rem; // WCAG 1.4.4 – convert 5px to rem
      font-weight: bold;
    }
  }

  .flex-down {
    display: flex;
    flex-direction: column;
  }
  .flex-space {
    display: flex;
    justify-content: space-between;
  }
  .delete-icon {
    vertical-align: middle;
     font-size: 1.125rem; // WCAG 1.4.4 – convert 18px to rem
     color: #e21d27;
    cursor: pointer ;
    // Assumption: provide visible focus indicator for keyboard users – WCAG 2.4.7
    &:focus-visible {
      outline: 2px solid #2D323D;
      outline-offset: 2px;
    }
  }

// ---------------------------------------------------------------------------
// WCAG 2.1 – Success Criterion 1.4.11 (Non-text Contrast)
// ---------------------------------------------------------------------------
// Ant Design inputs (`nz-input`) and default (non-primary) buttons rely on a
// very light grey border (#d9d9d9 ≈ 1.2 : 1 contrast on white) which does not
// meet the required ≥ 3 : 1 ratio for graphical component boundaries.
// The confirmation modal inside Bulk Export contains an <input nz-input> field
// that therefore fails SC 1.4.11.  The following scoped overrides darken the
// border and add a visible keyboard focus indicator so low-vision users can
// perceive component states.
// Assumption: The component background is white, so #757575 (≈ 4.5 : 1) and
// #1a73e8 (> 3 : 1) provide sufficient contrast while aligning with colours
// adopted in previous automated fixes.

/* Increase input boundary contrast inside confirmation modals */
.modal-table input[nz-input] {
  border: 1px solid #757575; // WCAG 1.4.11 – darken low-contrast default border
}

/* Visible keyboard focus indicator for all interactive controls in this component */
button:focus-visible,
a:focus-visible,
input[nz-input]:focus-visible,
[tabindex="0"]:focus-visible {
  outline: 3px solid #2D323D; // High-contrast blue ring consistent across app
  outline-offset: 2px;
}

/* Darken secondary (non-primary) Ant Design button borders so their boundary
   remains perceivable against the white background. */
button.ant-btn:not(.ant-btn-primary) {
  border: 2px solid #4c4c4c; // ≈ 8 : 1 contrast on white – aligns with other modules
}

/* Provide focus visibility for clickable table rows (export list) */
tr[tabindex="0"]:focus-visible {
  outline: 3px solid #2D323D; // Ensures row focus meets ≥ 3 : 1 contrast
  outline-offset: -4px;
}



  /* WCAG 2.1 – 1.4.3 (Contrast): The Tailwind utility classes `text-red-500` and
     `text-green-500` map to colours that fall below the 4.5:1 ratio when used on
     a white background. Locally override those classes within this component so
     that error and success messages/icons meet contrast requirements without
     affecting other components. */
  .text-red-500 {
    color: #D40D00 !important; // High-contrast red – 5.44:1
  }

  .text-green-500 {
    color: #1D7741 !important; // High-contrast green – 5.57:1
  }

  .form-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .export-details {
    margin: 1.25rem; // WCAG 1.4.4 – convert 20px to rem
    justify-content: space-between;
    align-items: flex-end;
    display: flex;
    // WCAG 1.4.10 – stack detail items vertically on narrow screens so long text does not force horizontal scroll
    @media (max-width: 640px) {
      flex-direction: column; // Assumption: Column layout acceptable for mobile
      align-items: flex-start;
    }
  }

  .more {
    cursor: pointer;
    text-align: center;
    // Assumption: ensure focus visibility when row is keyboard-focused – WCAG 2.4.7
    &:focus-visible {
      outline: 2px solid #2D323D;
      outline-offset: 2px;
    }
  }



/* ---------------------------------------------------------------------------
   WCAG 2.1 – Success Criterion 1.4.12 (Text Spacing)
   ---------------------------------------------------------------------------
   Global stylesheet constrains Angular Material selects via a fixed 30 px height
   utility class (`.material-select-height`).  When users increase line-, word- or
   letter-spacing the rigid height can clip the label/value text, causing loss of
   content – a direct failure of SC 1.4.12.  Override the rule locally so the
   control may expand while keeping its original minimum footprint.
*/
.material-select-height { // Assumption: component-scoped override acceptable
  height: auto !important;  /* allow vertical growth under custom spacing */
  min-height: 1.875rem;     /* ≈30 px baseline retains visual rhythm */
  overflow: visible;        /* prevent clipping of expanded text */
  white-space: normal;      /* permit wrapping if letter/word spacing increases */
  line-height: normal;      /* defer to user/UA stylesheet (≥1.5× enforced externally) */
}

  // WCAG 1.4.1 – Use of Colour:
  // Ensure that hyperlinks remain identifiable for users who cannot perceive colour by
  // explicitly underlining them. This local rule targets links we added the
  // `.download-link` class to so that global styles remain unaffected. // Assumption: underline acceptable within design system
  .download-link {
    text-decoration: underline;
    // Provide visible focus state in addition to default browser outline.
    &:focus-visible {
      outline: 2px solid #2D323D; // high-contrast blue outline, meets 3:1 ratio
      outline-offset: 2px;
    }
    
    // Special styling for "Configure storage integration" link to make it more prominent
    &[href="/UI/userSettings.html"] {
      font-weight: bold;
      color: #e21d27; // Red color to highlight importance
      text-decoration: underline;
      
      &:hover {
        color: #b91c1c; // Darker red on hover
      }
      
      &:focus {
        outline: 2px solid #e21d27; // Red outline to match the link color
        outline-offset: 2px;
      }
    }
  }

// ---------------------------------------------------------------------------
// WCAG 1.4.10 (Reflow)
// ---------------------------------------------------------------------------
// Ensure data tables can be scrolled horizontally within their own container at
// 320 px viewport without forcing the entire page to scroll sideways.
.table-responsive {
  width: 100%;
  max-width: 100%; // Assumption: container should never exceed viewport width
  overflow-x: auto; // single-axis scroll; vertical scroll remains on page
  margin: 0; // Remove any default margins
  padding: 0 0.9375rem; // Add padding instead of margin for better control
}

/* Ensure the table itself doesn't exceed container width */
.table-responsive nz-table {
  width: 100%;
  max-width: 100%;
}

// ---------------------------------------------------------------------------
// WCAG 2.1 – Success Criterion 1.3.4 (Orientation)
// ---------------------------------------------------------------------------
// Ant Design modals default to a fixed 520 px width which can overflow the 320 px
// portrait viewport, effectively forcing users to rotate their device or
// pan horizontally – a violation of SC 1.3.4.  Constrain the modal to
// `90vw` and cap the maximum so the dialog re-flows in both orientations while
// retaining the original design on larger screens.

/* CHANGE (2025-08-01 VALIDATION): Removed unnecessary ::ng-deep selector now that
   ViewEncapsulation.None exposes styles globally. This avoids reliance on the
   deprecated combinator while retaining the original modal sizing override. */
.ant-modal {
  width: 90vw !important; // Assumption: leaves comfortable margin on desktop
  max-width: 37.5rem;     // 600 px – matches other global fixes
}

/* ---------------------------------------------------------------------------
   WCAG 2.4.7 (Focus Visible) – Material Radio Button Focus Indicators
   ---------------------------------------------------------------------------
   Ensure that Material Design radio button outer circles have visible focus
   indicators that meet the minimum 3:1 contrast ratio for keyboard users.
   The focus-visible pseudo-class ensures the indicator only appears when
   navigating via keyboard, not when clicking with a mouse.
*/

/* Custom radio button styles for bulk export component only */
:host ::ng-deep {
  .mat-radio-button {
    /* Make the entire radio button interactive */
    .mat-radio-label {
      cursor: pointer;
      tabindex: -1; /* Remove from tab order */
    }
    
    .mat-radio-container {
      cursor: pointer;
    }
    
    .mat-radio-label-content {
      cursor: pointer;
    }

    /* Hide Material's built-in focus/ripple effects */
    .mat-radio-ripple {
      display: none !important;
    }

    .mat-focus-indicator {
      display: none !important;
    }

    /* Prevent focus on checked radio buttons */
    &.mat-radio-checked {
      pointer-events: auto !important; /* Keep the button clickable */
      .mat-radio-input {
        pointer-events: none !important;
        visibility: hidden !important;
      }
      .mat-radio-label {
        pointer-events: auto !important;
      }
    }

    /* Show focus outline only on unchecked buttons */
    &:not(.mat-radio-checked) {
      .mat-radio-input:focus + .mat-radio-label {
        outline: 3px solid #2D323D !important;
        outline-offset: 2px !important;
        border-radius: 4px !important;
      }
    }
  }
}

/* Ensure disabled radio buttons are still accessible */
.mat-radio-button.mat-radio-disabled {
  .mat-radio-outer-circle {
    cursor: not-allowed;
  }
  
  .mat-radio-label-content {
    cursor: not-allowed;
  }
  
  .mat-radio-container {
    cursor: not-allowed;
  }
  
  .mat-radio-input {
    cursor: not-allowed;
  }
}

/* Make the configure link more prominent and accessible */
.download-link[href="/UI/userSettings.html"] {
  font-weight: bold;
  color: #e21d27 !important; // Red color to highlight importance
  text-decoration: underline;
  display: inline-block; // Ensure it's properly clickable
  margin-top: 8px; // Add some spacing
  
  &:hover {
    color: #b91c1c !important; // Darker red on hover
  }
  
  &:focus-visible {
    outline: 2px solid #e21d27 !important; // Red outline to match the link color
    outline-offset: 2px;
  }
}

/* Pagination keyboard accessibility - similar to contacts component */
.ant-pagination a:focus,
.ant-pagination button:focus,
.ant-pagination a:focus-visible,
.ant-pagination button:focus-visible,
.ant-pagination-item:focus,
.ant-pagination-item:focus-visible,
.ant-pagination-item a:focus,
.ant-pagination-item a:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Ensure pagination controls are keyboard accessible */
.ant-pagination li {
  position: relative;
}

.ant-pagination a,
.ant-pagination button,
.ant-pagination-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.ant-pagination a:hover,
.ant-pagination button:hover,
.ant-pagination-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* Pagination control focus styling */
.pagination-control:focus-visible {
  outline: 3px solid #2D323D;
  outline-offset: 2px;
  border-radius: 2px;
}
  
  @media (min-width:640px) {
    .form-container {
      margin-right: 30%;
    }
  }
