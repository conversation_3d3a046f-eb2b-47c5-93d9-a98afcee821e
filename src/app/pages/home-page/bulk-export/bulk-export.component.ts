// CHANGE (2025-08-01): WCAG 2.4.2 (Page Titled) – import Angular Title service so
// the component can programmatically set a descriptive <title> element when the
// route/view is activated.
import { Component, OnInit, HostListener, AfterViewInit, ElementRef, ViewEncapsulation } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ReportService } from '@core/services';
import { FormControl, Validators, FormBuilder } from '@angular/forms';
import { DocumentStatus } from "@core/models/status";
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-bulk-export',
  templateUrl: './bulk-export.component.html',
  styleUrls: ['./bulk-export.component.less'],
  encapsulation: ViewEncapsulation.None
})
export class BulkExportComponent implements OnInit, AfterViewInit {
  public displayData: any;
  public displayDetailsData: any;
  public tableTotal: number = 0;
  public detailsTableTotal: number = 0;
  public toggleAddExport = false;
  public toggleDetails = false;
  public addExportFormSubmitted = false;
  public deleteConfirmationFormSubmitted = false;
  public deleteConfirmationModal: boolean = false;
  public bulkDeleteConfirmationModal: boolean = false;
  public selectedExport = -1;
  public exportInfo:any;
  public expandSet = new Set<number>();
  public expandSize:any = {}
  public isSpinning: boolean = false;
  public validDates = true;
  public currentTime:any = new Date().getTime();
  public user: any;
  public documentStatus:any = DocumentStatus;
  public previousStatuses:any[] = [0];

  public userInfo: any;
  public documentStatuses = 
  [{value:0, text:$localize `All`},
   {value:53, text:$localize `Completed`},
   {value:52, text: $localize `In Progress`},
   {value:55, text: $localize `Expired`}, 
   {value:56, text: $localize `Cancelled`}, 
   {value:51, text: $localize `Draft`}];

   // WCAG 2.1 – 1.4.3 (Contrast):
   // Updated status colours to meet the minimum 4.5:1 contrast ratio against a white
   // background. Only colours from the approved design-token list are used.
   // Assumption: visual distinction between different statuses can be preserved
   // with the new, higher-contrast palette.
   public exportStatuses:any = {
    1:{ text:$localize `Queued`,        color: '#055485'}, // High-contrast blue (6.15:1)
    2:{ text:$localize `In Progress`,  color: '#2D323D'}, // Dark gray (≈8:1) – replaces low-contrast orange
    3:{ text:$localize `Ready`,        color: '#1D7741'}, // Dark green (5.57:1)
    4:{ text: $localize `Expired`,     color: '#e21d27'}  // Existing red already passes (4.73:1)
   };

   public exportDestinations:any = {
    0:{ text:$localize `Direct Download`, color: '#055485'},
    1:{ text:$localize `Integration`,     color: '#055485'},
    2:{ text:$localize `Integration`,     color: '#2D323D'},
    3:{ text:$localize `Integration`,     color: '#1D7741'},
    4:{ text:$localize `Integration`,     color: '#e21d27'},
    5:{ text:$localize `Integration`,     color: '#e21d27'}
   };
  public addExportForm = this.formBuilder.group({
    docStatus: [[0], Validators.required],
    fromDate: ['', Validators.required],
    toDate: ['', Validators.required],
    exportTo: ['0', Validators.required],
    fileType: ['1', Validators.required],
    autoDelete:[false]
  });
  public deleteConfirmationForm = this.formBuilder.group({
    confirmDelete: ['', [Validators.required, Validators.pattern('^'+$localize `DELETE`+'$')]],
  });

  constructor(
    private reportService: ReportService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private elementRef: ElementRef, // Assumption: injecting ElementRef is acceptable in this component context
    private titleService: Title // Assumption: Title service provided at root by BrowserModule
    ) { }

  // WCAG 2.4.4 (Link Purpose – In Context)
  // --------------------------------------------------
  // The Ant Design `nz-table` component renders its pagination controls as a list of
  // anchor (`<a>`) elements that contain only an icon (prev/next) or a page
  // number.  When a screen-reader user opens the links list (e.g. NVDA Ins + F7)
  // those anchors are announced simply as “1, 2, 3, …” or nothing at all for the
  // icon buttons, which does not convey their purpose in isolation and therefore
  // fails WCAG 2.4.4 at Level AA.  Because the markup is generated internally by
  // the component library we inject descriptive `aria-label` attributes after each
  // view render.
  public directDownloadTooltipVisible = false; // Assumption: only one tooltip can be open at a time
  public integrationTooltipVisible = false;   // therefore we will close the other when one opens

  // Assumption: ESC should close whichever tooltip is currently open
  onEscKey(): void {
    if (this.directDownloadTooltipVisible || this.integrationTooltipVisible) {
      this.directDownloadTooltipVisible = false;
      this.integrationTooltipVisible = false;
    }
  }

  // WCAG 1.4.13 – listen for Escape key at document level so open tooltip can
  // be dismissed without moving pointer or focus.
  @HostListener('document:keydown.escape', ['$event'])
  handleEscape(event: KeyboardEvent): void {
    this.onEscKey();
  }

  ngOnInit(): void {
    this.user = JSON.parse(localStorage.getItem("user")|| '0' );
    this.userInfo = JSON.parse(localStorage.getItem("userInfo")|| '0' );
    let urlId = Number(this.route.snapshot.paramMap.get('id'));
    this.isSpinning = true;
    this.getData(1);
    if(this.route.snapshot.paramMap.get('id') == 'create') {
      this.toggleAddExport = true;
    }
    else if(urlId) {
      this.getExportDetails(urlId);
    }

    // WCAG 2.4.2 – ensure the document receives a descriptive and unique title.
    this.updateDocumentTitle();
  }

  /**
   * Sets a descriptive document <title> for the Bulk Export page so that the
   * browser tab and assistive technology announce meaningful context.
   */
  private updateDocumentTitle(): void {
    let sectionTitle = $localize`Bulk Export`;
    if (this.toggleAddExport) {
      sectionTitle = $localize`Create Bulk Export`;
    } else if (this.toggleDetails) {
      sectionTitle = $localize`Export Details`;
    } else {
      sectionTitle = $localize`Bulk Export`;
    }
    // Set title to just the page name without the dashboard suffix
    this.titleService.setTitle(sectionTitle);
  }

  // After initial view render patch pagination controls.
  ngAfterViewInit(): void {
    this.patchPaginationAriaLabels();
    this.enhanceRadioButtonAccessibility();
  }

  /**
   * Enhances radio button accessibility by ensuring proper focus indicators
   * and keyboard navigation for Material Design radio buttons.
   */
  private enhanceRadioButtonAccessibility(): void {
    setTimeout(() => {
      const radioButtons = this.elementRef.nativeElement.querySelectorAll('.mat-radio-button');
      radioButtons.forEach((radio: HTMLElement) => {
        const input = radio.querySelector('.mat-radio-input') as HTMLInputElement;
        const label = radio.querySelector('.mat-radio-label') as HTMLElement;
        
        if (input) {
          // Ensure the input is focusable
          input.tabIndex = 0;
          
          // Make the label clickable and focusable
          if (label) {
            label.tabIndex = 0;
            label.style.cursor = 'pointer';
          }
          
          // Remove tabindex from Material's focus indicator and ripple elements
          const focusIndicator = radio.querySelector('.mat-focus-indicator') as HTMLElement;
          const ripple = radio.querySelector('.mat-ripple') as HTMLElement;
          
          if (focusIndicator) {
            focusIndicator.tabIndex = -1;
            focusIndicator.setAttribute('aria-hidden', 'true');
          }
          
          if (ripple) {
            ripple.tabIndex = -1;
            ripple.setAttribute('aria-hidden', 'true');
          }
          
          // Add keyboard event listeners for better accessibility
          input.addEventListener('keydown', (event: KeyboardEvent) => {
            if (event.key === 'Enter' || event.key === ' ') {
              event.preventDefault();
              input.checked = true;
              input.dispatchEvent(new Event('change', { bubbles: true }));
            }
          });
          
          // Also handle clicks on the label
          if (label) {
            label.addEventListener('click', () => {
              input.checked = true;
              input.dispatchEvent(new Event('change', { bubbles: true }));
            });
            
            label.addEventListener('keydown', (event: KeyboardEvent) => {
              if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                input.checked = true;
                input.dispatchEvent(new Event('change', { bubbles: true }));
              }
            });
          }
                  }
        });
      });
    }

  /**
   * WCAG 4.1.2 (Name, Role, Value) – ensure Ant Design pagination controls receive
   * descriptive accessible names and are keyboard accessible. This method patches
   * pagination links generated by Ant Design's `nz-pagination` so that
   * screen-reader users can understand the purpose of each control.
   */
  private patchPaginationAriaLabels(): void {
    // Defer execution so DOM is fully updated.
    setTimeout(() => {
      // Look for both old and new pagination patterns
      const paginationSelectors = [
        'ul.ant-pagination', 'nz-pagination ul', '.ant-table-pagination ul',
        'ul[class*="pagination"]', '.ant-pagination'
      ];
      let paginations: HTMLElement[] = [];
      paginationSelectors.forEach(selector => {
        const found = this.elementRef.nativeElement.querySelectorAll(selector);
        paginations.push(...Array.from(found) as HTMLElement[]);
      });

      paginations.forEach(pagination => {
        const items = pagination.querySelectorAll('li');
        items.forEach(li => {
          // Ant Design changed markup from <a> to <button> in v13.
          const control: HTMLElement | null = li.querySelector('button, a');
          if (!control) {
            return; // nothing to patch
          }

          // Skip if another process already supplied an accessible name.
          if (control.hasAttribute('aria-label')) {
            return;
          }

          // Ensure control is focusable for keyboard navigation
          if (!control.hasAttribute('tabindex') && control.tagName !== 'BUTTON') {
            control.setAttribute('tabindex', '0');
          }

          // Add CSS class for focus styling
          control.classList.add('pagination-control');

          // Provide semantic role when href is missing so <a> without href is exposed correctly.
          if (control.tagName === 'A' && !control.hasAttribute('href')) {
            control.setAttribute('role', 'button');
          }

          // Add keyboard event handlers for pagination controls only
          this.addPaginationKeyboardActivation(control);

          // Check for various pagination control patterns
          if (li.classList.contains('ant-pagination-prev') || 
              li.querySelector('[class*="prev"]') ||
              control.textContent?.includes('‹') ||
              control.textContent?.includes('<')) {
            control.setAttribute('aria-label', 'Previous page');
          } else if (li.classList.contains('ant-pagination-next') || 
                     li.querySelector('[class*="next"]') ||
                     control.textContent?.includes('›') ||
                     control.textContent?.includes('>')) {
            control.setAttribute('aria-label', 'Next page');
          } else if (li.classList.contains('ant-pagination-item') || 
                     li.classList.contains('ant-pagination-item-active')) {
            const text = control.textContent?.trim();
            if (text && /^\d+$/.test(text)) {
              control.setAttribute('aria-label', `Page ${text}`);
            }
          }
        });
      });
    }, 150); // Longer delay to ensure pagination is fully rendered
  }

  /**
   * WCAG 2.1.1 (Keyboard)
   * Add keyboard event handlers to pagination controls to ensure they respond
   * to Enter and Space key activation, matching expected button behavior.
   */
  private addPaginationKeyboardActivation(control: HTMLElement): void {
    // Remove any existing keyboard handlers to prevent duplicates
    const existingHandler = (control as any).__keyboardHandler;
    if (existingHandler) {
      control.removeEventListener('keydown', existingHandler);
    }

    // Create new keyboard handler
    const keyboardHandler = (event: KeyboardEvent) => {
      // Activate on Enter or Space (standard button behavior)
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        event.stopPropagation();
        
        // Trigger click event to activate pagination
        control.click();
      }
    };

    // Store reference for cleanup and add listener
    (control as any).__keyboardHandler = keyboardHandler;
    control.addEventListener('keydown', keyboardHandler);
  }

  getData(pageNumber: number) {
    this.reportService.listBulkExports(pageNumber).subscribe(data=>{
      this.displayData = data.records;
      this.tableTotal = data.total;
      this.isSpinning = false;
      this.patchPaginationAriaLabels(); // WCAG 2.4.4 – ensure new pagination controls are labelled
    });
  }

  export(confirmDelete:boolean) {
    this.addExportFormSubmitted= true;
    if(this.addExportForm.controls['fromDate'].value > this.addExportForm.controls['toDate'].value) {
      this.validDates = false;
    }
    else {
      this.validDates = true;
    }
    if(this.addExportForm.controls['autoDelete'].value && confirmDelete) {
      this.bulkDeleteConfirmationModal = true;
      return;
    } 
    if (!this.addExportForm.invalid && this.validDates) {
      //format dates
      let dateFromFormatted =   new Date(this.addExportForm.controls['fromDate'].value);
      let dateToFormatted =  new Date(this.addExportForm.controls['toDate'].value);
      this.isSpinning = true;
      this.toggleAddExport = false;
      this.reportService.createExport(this.addExportForm.controls['docStatus'].value,
      dateFromFormatted.getFullYear() + '-' + (dateFromFormatted.getMonth()+1) + '-' + dateFromFormatted.getDate(),
      dateToFormatted.getFullYear() + '-' + (dateToFormatted.getMonth()+1) + '-' + dateToFormatted.getDate(),
      this.addExportForm.controls['exportTo'].value,
      this.addExportForm.controls['fileType'].value,
      this.addExportForm.controls['autoDelete'].value
      ).subscribe(v => {
        this.addExportFormSubmitted= false;
        this.router.navigate(['/bulk-export']);
        this.addExportForm.reset();
        this.addExportForm.get('docStatus')?.setValue([0]);
        this.addExportForm.get('exportTo')?.setValue('0');
        this.addExportForm.get('fileType')?.setValue('1');
        this.addExportForm.get('autoDelete')?.setValue(false);

        // WCAG 2.4.2 – title should revert to Export History after export is created.
        this.updateDocumentTitle();
      });
    }

  }

  validateDelete() {
    this.deleteConfirmationFormSubmitted = true;
    if (!this.deleteConfirmationForm.invalid) {
      this.export(false);
      this.deleteConfirmationFormSubmitted = false;
    }
  }

  deleteExport(id:number) {
    this.isSpinning = true;
    this.reportService.deleteExport(id).subscribe(v=>{
      this.deleteConfirmationModal = false;
      this.getData(1);
    })
  }

  getExportDetails(id:number, pageNumber:number = 1) {
    this.reportService.getExport(id, pageNumber).subscribe(v=>{
      this.exportInfo = {};      
      this.exportInfo['exportDate'] = v.data.exportDate;      
      this.exportInfo['exportPeriod'] = v.data.documentStartDate + " - " +  v.data.documentEndDate;      
      this.exportInfo['exportUser'] = v.data.userName;
      this.exportInfo['id'] = v.data.id;
      this.exportInfo['documentStatus'] = v.data.documentStatus;
      this.exportInfo['exportDestination'] = this.exportDestinations[v.data.exportToType].text;
      this.exportInfo['exportToType'] = v.data.exportToType;
      this.exportInfo['exportedDocumentsDeleted'] = v.data.exportedDocumentsDeleted;
      this.displayDetailsData = v.data.detailsResponses.map((el:any)=>{el.showDocs = 10; return el});
      this.detailsTableTotal = v.data.total;
      this.toggleDetails = true;
      this.isSpinning = false;

      // WCAG 2.4.2 – update the <title> to reflect "Export Details" view.
      this.updateDocumentTitle();

      // Pagination in details table may have changed – patch labels again
      this.patchPaginationAriaLabels(); // WCAG 2.4.4

    })
  }
  downloadExport() {
    this.reportService.downloadExport(this.exportInfo.id).subscribe(v=>{
    })
  }

  onExpandChange(id: number, checked: any): void {
    if (checked) {
      this.expandSet.add(id);
      this.expandSize[id] = 10;
    } else {
      this.expandSet.delete(id);
    }
  }

  getDocStatus(id:number) {
   let doc = this.documentStatuses.find(val=>{
      return val.value == id
    })
    return doc?.text;
  }
  getDocStatuses(ids:number[]) {
    let statusString = '';
    ids.forEach(id=>{
      let doc = this.documentStatuses.find(val=>{
        return val.value == id
      })
      statusString += doc?.text +', '
    })
    return statusString.slice(0, -2);
  }

  statusSelectHelper(arr:any) {
  // if  'All' was added to array, clear all other statuses
   if(arr.includes(0) && (!this.previousStatuses.includes(0) || arr.length == 1)) {
      arr = [0];
      this.previousStatuses = [0];
      this.addExportForm.get('docStatus')?.setValue(arr);
   }
   else if (arr.includes(0)) {
    arr = arr.filter((val:number)=>{return val != 0});
    this.previousStatuses = this.previousStatuses.filter((val:number)=>{return val != 0});
    this.addExportForm.get('docStatus')?.setValue(arr);
   }

  }

  getDateTime(dateString:string) {
      let dateArr = dateString.split(' ');
      let date = dateArr[0].split('/');
      let time = dateArr[1];
      let temp = date[0];
      date[0] = date[1];
      date[1] = temp;
      return new Date(date.join('/')+' '+time).getTime();
  }

  getIntegrationText(id:number) {
    switch(id) {
      case 1: return 'Google Drive';
      case 2: return 'Dropbox';
      case 3: return 'AWS S3 Bucket';
      case 4: return 'Microsoft OneDrive';
      case 5: return 'SFTP';
    }
    return '';
  }

  navigateToExport(id:number, exportStatus:number) {
    this.router.navigate([exportStatus != 1 ?'/bulk-export/'+id : []])
  }

}
