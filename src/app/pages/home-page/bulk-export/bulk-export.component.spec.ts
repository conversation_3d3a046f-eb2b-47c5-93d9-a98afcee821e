import { ComponentFixture, TestBed } from '@angular/core/testing';
// TEST SETUP UPDATE (2025-08-01): Added missing Angular modules, NO_ERRORS_SCHEMA and
// a simple ReportService stub so the unit test still compiles after new
// dependencies were introduced for WCAG compliance. – See bulk-export.component.ts
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';

// Assumption: ReportService is located at @core/services (matching component import).
// Using the same path prevents TypeScript path-resolution errors in the test file.
import { ReportService } from '@core/services';

import { BulkExportComponent } from './bulk-export.component';

describe('BulkExportComponent', () => {
  let component: BulkExportComponent;
  let fixture: ComponentFixture<BulkExportComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BulkExportComponent ],
      imports: [
        // Assumption: RouterTestingModule is sufficient to satisfy Router
        // dependency without configuring actual routes.  ReactiveFormsModule
        // is required because the component injects FormBuilder and declares
        // multiple reactive form controls. Adding it avoids a runtime DI
        // error during test initialisation while keeping the spec isolated
        // from other application modules.
        RouterTestingModule,
        ReactiveFormsModule
      ],
      // CHANGE (2025-08-01): Added Title provider to satisfy new dependency in
      // BulkExportComponent introduced for WCAG 2.4.2 compliance. ActivatedRoute
      // is supplied via the testing module’s DI container using a minimal stub
      // because the component only reads static snapshot parameters during
      // initialisation.
      providers: [
        Title,
        { provide: ActivatedRoute, useValue: { snapshot: { paramMap: { get: () => null } } } },
        {
          // Stub ReportService because the component invokes several of its
          // methods during initialisation. Only the signatures needed for the
          // test are provided.  Returning empty Observables is sufficient for
          // the shallow “should create” assertion and avoids HTTP calls or
          // RxJS errors.
          provide: ReportService,
          useValue: {
            listBulkExports: () => ({ subscribe: () => {} }),
            getExport: () => ({ subscribe: () => {} }),
            createExport: () => ({ subscribe: () => {} }),
            deleteExport: () => ({ subscribe: () => {} }),
            downloadExport: () => ({ subscribe: () => {} })
          }
        }
      ],
      // Allow unknown elements/attributes (Ant Design, Angular Material) to be
      // ignored so the unit test remains focused on component logic.
      schemas: [NO_ERRORS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BulkExportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
