import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HomePageComponent } from './home-page/home-page.component';
import { LoginPageComponent } from './login-page/login-page.component';
import { SharedModule } from "@shared/shared.module";
import {NzFormModule} from "ng-zorro-antd/form";
import {NzCheckboxModule} from "ng-zorro-antd/checkbox";
import {NzButtonModule} from "ng-zorro-antd/button";
import {NzAlertModule} from "ng-zorro-antd/alert";

import {NzMessageService} from "ng-zorro-antd/message";
import {NzPopoverModule} from "ng-zorro-antd/popover";
import {NzDropDownModule} from "ng-zorro-antd/dropdown";
import { NzIconModule } from 'ng-zorro-antd/icon';
import {NzBadgeModule} from "ng-zorro-antd/badge";
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import {SafeHtmlPipe} from './inner-html/innerhtmlpipe'
import { UsersAndTeamsModule } from "./home-page/users-and-teams/users-and-teams.module";
import { DocSidebarComponent } from './home-page/documents/doc-sidebar/doc-sidebar.component';




@NgModule({
    declarations: [
        SafeHtmlPipe,
        HomePageComponent,
        LoginPageComponent,
        DocSidebarComponent
    ],
    providers: [
        NzMessageService
    ],
    imports: [
        CommonModule,
        SharedModule,
        NzFormModule,
        NzCheckboxModule,
        NzButtonModule,
        NzAlertModule,
        NzPopoverModule,
        NzDropDownModule,
        NzIconModule,
        NzBadgeModule,
        NzModalModule,
        NzDividerModule,
        UsersAndTeamsModule    ],
    exports: [HomePageComponent]
})
export class PagesModule { }
