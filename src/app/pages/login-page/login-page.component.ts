import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {ApiService, UserService} from "@core/services";
import { Router} from "@angular/router";
import {CookieService} from "ngx-cookie-service";

import {UrlService} from "@core/services/url.service";

import { NzMessageService } from 'ng-zorro-antd/message';



type alert = "success" | "warning"

@Component({
    selector: 'app-login-page',
    templateUrl: './login-page.component.html',
    styleUrls: ['./login-page.component.less']
})
export class LoginPageComponent implements OnInit {
    show = false
    validateForm!: FormGroup;
    // alert_message = ""
    // alert_type:alert = "warning"
    previousUrl = ''

    constructor(
        private fb: FormBuilder,
        private userService: UserService,
        private router:Router,
        private cookieService: CookieService,
        private urlService:UrlService,
        private message:NzMessageService,
        private apiService:ApiService
    ) {

        this.urlService.previousUrl$.subscribe((previousUrl: string) => {
           this.previousUrl = previousUrl
        });


    }
    ngOnInit(): void {
        this.validateForm = this.fb.group({
            email: ['<EMAIL>', [Validators.required]],
            password: ['Test5044', [Validators.required]]
        });

        if(this.previousUrl != ''){
            this.message.info('Not authorized. You need to login first.', {nzDuration:5000})
        }

        console.log(this.previousUrl)

    }

    submitForm(): void {
        if (this.validateForm.valid) {
            this.userService.login(this.validateForm.value)
                .subscribe(res => {
                    if(res.success){
                        // this.alert_type = "success"
                        // this.alert_message = "You have logged in"
                        this.message.success('Successfully logged in!', {nzDuration:5000})

                        // if(res.team2fa != undefined){
                        //     this.apiService.put('user/informsetuplater', {dontaskagain:false}).subscribe()
                        // }

                        setTimeout( () => {
                            if(this.previousUrl != '') {
                                this.router.navigate([this.previousUrl])
                                    .then(() => this.urlService.setPreviousUrl(''))
                            }
                            else {
                                this.router.navigate(['/']).then()
                            }
                        }, 1500)



                    } else {
                        // this.alert_type = "warning"
                        // this.alert_message = res.message
                        this.message.error(res.message, {nzDuration:5000})
                    }
                })
        } else {
            Object.values(this.validateForm.controls).forEach(control => {
                if (control.invalid) {
                    control.markAsDirty();
                    control.updateValueAndValidity({ onlySelf: true });
                }
            });
        }
    }

    logout(){
        this.userService.logout("")
    }
}
