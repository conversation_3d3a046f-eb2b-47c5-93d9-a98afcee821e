<ng-template #userIcon><i class="fa-light fa-user fa-fw mr-2"></i></ng-template>
<ng-template #pwdIcon><i class="fa-light fa-lock-keyhole fa-fw mr-2"></i></ng-template>

<div class="h-full bg-cover bg-center bg-[url('assets/images/background/1.jpg')]">
    <div class="h-full flex flex-col items-center justify-center ">
    <form nz-form [formGroup]="validateForm" class="login-form bg-white p-10" (ngSubmit)="submitForm()">
        <div class="flex justify-center">
            <img src="assets/images/BasicSiteCompanyLogoRegular.png?version=3">
        </div>
        <nz-form-item>
            <nz-form-control nzErrorTip="Please input your username!">
                <nz-input-group [nzPrefix]="userIcon">
                    <input type="email" nz-input formControlName="email" placeholder="Email" />
                </nz-input-group>
            </nz-form-control>
        </nz-form-item>

        <nz-form-item>
            <nz-form-control nzErrorTip="Please input your Password!">
                <nz-input-group [nzPrefix]="pwdIcon">
                    <input type="password" nz-input formControlName="password" placeholder="Password" />
                </nz-input-group>
            </nz-form-control>
        </nz-form-item>
          <!-- <div nz-row class="login-form-margin"> -->
          <!--   <div nz-col [nzSpan]="12"> -->
          <!--     <label nz-checkbox formControlName="remember"> -->
          <!--       <span>Remember me</span> -->
          <!--     </label> -->
          <!--   </div> -->
          <!--   <div nz-col [nzSpan]="12"> -->
          <!--     <a class="login-form-forgot">Forgot password</a> -->
          <!--   </div> -->
          <!-- </div> -->
          <button nz-button class="login-form-button login-form-margin" [nzType]="'primary'">Log in</button>
        <br>
        <button  type="button" nz-button class="login-form-button login-form-margin rounded-2xl text-start hover:text-black focus:text-black" >
            <i class="fa-brands fa-facebook fa-fw mr-2"></i>Continue with Facebook
        </button>


        <button  type="button" nz-button class="login-form-button login-form-margin rounded-2xl text-start hover:text-black focus:text-black" >
            <i class="fa-brands fa-google fa-fw mr-2"></i>Continue with Google
        </button>


        <button  type="button" nz-button class="login-form-button login-form-margin rounded-2xl text-start hover:text-black focus:text-black" >
            <i class="fa-brands fa-apple fa-fw mr-2"></i>Continue with Apple
        </button>


        <button  type="button" nz-button class="login-form-button login-form-margin rounded-2xl text-start hover:text-black focus:text-black" (click)="logout()" >
            <i class="fa-brands fa-apple fa-fw mr-2"></i>logout
        </button>



          <!-- Or -->
          <!-- <a>register now!</a> -->
          <!--   <nz-alert *ngIf="show" -->
          <!--       [nzType]="alert_type" -->
          <!--       nzCloseable -->
          <!--       [nzMessage]="alert_message" -->
          <!--       (nzOnClose)="show = !show;" -->
          <!--   ></nz-alert> -->
        </form>


    </div>
</div>



