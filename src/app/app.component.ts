import { Component, NgZone } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from "rxjs/operators";
import { GTMService } from '@core/services/gtm.service';

@Component({
    selector: 'app-root',
    template: "<router-outlet></router-outlet>"
})
export class AppComponent{
    constructor(
        private _router: Router,
        private _gtm: GTMService,
        private ngZone: NgZone,
    ) {
        const navEndEvents = this._router.events.pipe(
            filter(event => event instanceof NavigationEnd),
        );

        navEndEvents.subscribe(event => {
            this._gtm.pageView();
        });
    }
    // This implements site-wide fix for accessibility compliance of the datepicker
    // Overrides the default Angular Material datepicker behavior to highlight properly when focused, and
    // maintain the proper height of the datepicker when the calendar is open at 400% zoom.
    ngAfterViewInit() {
        this.ngZone.runOutsideAngular(() => {
            let patchTimeout: any = null;
            let tabPressed = false;

            const patchDatepicker = () => {
                document.querySelectorAll('.mat-datepicker-content').forEach((el: any) => {
                    const computedHeight = el.offsetHeight;
                    const closeBtn = el.querySelector('.mat-datepicker-close-button') as HTMLElement;
                    const monthYearBtn = el.querySelector('.mat-calendar-period-button') as HTMLElement;
                    
                    // Reset tab state for new datepicker
                    tabPressed = false;
                    
                    // Always ensure close button is properly configured
                    if (closeBtn) {
                        // Initially keep close button visually hidden (like default Angular Material behavior)
                        closeBtn.style.setProperty('height', '1px', 'important');
                        closeBtn.style.setProperty('width', '1px', 'important');
                        closeBtn.style.setProperty('clip', 'rect(0 0 0 0)', 'important');
                        closeBtn.style.setProperty('overflow', 'hidden', 'important');
                        closeBtn.style.setProperty('margin', '-1px', 'important');
                        closeBtn.style.setProperty('padding', '0', 'important');
                        closeBtn.style.setProperty('position', 'absolute', 'important');
                        closeBtn.style.setProperty('border', '0', 'important');
                        
                        // Add focus outline for accessibility (when visible)
                        closeBtn.addEventListener('focus', () => {
                            closeBtn.style.setProperty('outline', '3px solid #2D323D', 'important');
                            closeBtn.style.setProperty('outline-offset', '2px', 'important');
                        });
                        closeBtn.addEventListener('blur', () => {
                            closeBtn.style.removeProperty('outline');
                            closeBtn.style.removeProperty('outline-offset');
                        });
                    }

                    // Listen for Tab key on the entire datepicker content to intercept early
                    if (!tabPressed) {
                        const handleTabKey = (event: KeyboardEvent) => {
                            if (event.key === 'Tab' && !event.shiftKey && closeBtn && !tabPressed) {
                                event.preventDefault();
                                event.stopPropagation();
                                tabPressed = true;
                                
                                // Make close button visible and focusable
                                closeBtn.style.setProperty('height', 'auto', 'important');
                                closeBtn.style.setProperty('width', 'auto', 'important');
                                closeBtn.style.setProperty('clip', 'auto', 'important');
                                closeBtn.style.setProperty('overflow', 'visible', 'important');
                                closeBtn.style.setProperty('margin', '0', 'important');
                                closeBtn.style.setProperty('padding', '0', 'important');
                                closeBtn.style.setProperty('position', 'static', 'important');
                                closeBtn.style.setProperty('border', '0', 'important');
                                
                                // Focus the close button immediately
                                setTimeout(() => {
                                    closeBtn.focus();
                                }, 10);
                                
                                // Remove the event listener since we only want this to happen once
                                el.removeEventListener('keydown', handleTabKey);
                            }
                        };
                        
                        el.addEventListener('keydown', handleTabKey, true); // Use capture phase to intercept early
                    }

                    if (computedHeight < 350) {
                        el.style.setProperty('height', 'auto', 'important');
                        el.style.setProperty('min-height', '380px', 'important');
                        el.style.setProperty('background', 'white', 'important');
                        if (closeBtn && tabPressed) {
                            closeBtn.style.setProperty('position', 'relative', 'important');
                            closeBtn.style.setProperty('top', '20px', 'important');
                        }
                    } else {
                        el.style.removeProperty('height');
                        el.style.removeProperty('min-height');
                        if (closeBtn && tabPressed) {
                            closeBtn.style.removeProperty('position');
                            closeBtn.style.removeProperty('top');
                        }
                    }

                    // Fix current day focus indication
                    const currentDayBtn = el.querySelector('.mat-calendar-body-active') as HTMLElement;
                    if (currentDayBtn) {
                        currentDayBtn.addEventListener('focus', () => {
                            currentDayBtn.style.setProperty('outline', '3px solid #2D323D', 'important');
                            currentDayBtn.style.setProperty('outline-offset', '2px', 'important');
                        });
                        currentDayBtn.addEventListener('blur', () => {
                            currentDayBtn.style.removeProperty('outline');
                            currentDayBtn.style.removeProperty('outline-offset');
                        });
                    }
                });
            };

            const observer = new MutationObserver(() => {
                const hasDatepicker = !!document.querySelector('.mat-datepicker-content');
                if (hasDatepicker && !patchTimeout) {
                    patchTimeout = setTimeout(() => {
                        patchDatepicker();
                        patchTimeout = null;
                    }, 350); // Wait for datepicker to fully open/animate
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
            });
        });
    }
}