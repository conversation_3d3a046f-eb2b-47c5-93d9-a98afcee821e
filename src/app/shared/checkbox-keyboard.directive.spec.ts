import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component } from '@angular/core';
import { CheckboxKeyboardDirective } from './checkbox-keyboard.directive';

@Component({
  template: `
    <div appCheckboxKeyboard
         [isChecked]="isChecked"
         [isDisabled]="isDisabled"
         (appCheckboxKeyboard)="onCheckboxChange($event)"
         tabindex="0"
         role="checkbox">
      Test Checkbox
    </div>
  `
})
class TestComponent {
  isChecked = false;
  isDisabled = false;
  lastEmittedValue: boolean | null = null;

  onCheckboxChange(value: boolean) {
    this.lastEmittedValue = value;
  }
}

describe('CheckboxKeyboardDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let element: HTMLElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestComponent, CheckboxKeyboardDirective]
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    element = fixture.nativeElement.querySelector('[appCheckboxKeyboard]');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit true when Enter key is pressed on unchecked checkbox', () => {
    component.isChecked = false;
    fixture.detectChanges();

    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    element.dispatchEvent(enterEvent);

    expect(component.lastEmittedValue).toBe(true);
  });

  it('should emit false when Enter key is pressed on checked checkbox', () => {
    component.isChecked = true;
    fixture.detectChanges();

    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    element.dispatchEvent(enterEvent);

    expect(component.lastEmittedValue).toBe(false);
  });

  it('should emit true when Space key is pressed on unchecked checkbox', () => {
    component.isChecked = false;
    fixture.detectChanges();

    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    element.dispatchEvent(spaceEvent);

    expect(component.lastEmittedValue).toBe(true);
  });

  it('should emit false when Space key is pressed on checked checkbox', () => {
    component.isChecked = true;
    fixture.detectChanges();

    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    element.dispatchEvent(spaceEvent);

    expect(component.lastEmittedValue).toBe(false);
  });

  it('should not emit when checkbox is disabled', () => {
    component.isChecked = false;
    component.isDisabled = true;
    fixture.detectChanges();

    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    element.dispatchEvent(enterEvent);

    expect(component.lastEmittedValue).toBeNull();
  });

  it('should prevent default and stop propagation on Enter key', () => {
    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    const preventDefaultSpy = spyOn(enterEvent, 'preventDefault');
    const stopPropagationSpy = spyOn(enterEvent, 'stopPropagation');

    element.dispatchEvent(enterEvent);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(stopPropagationSpy).toHaveBeenCalled();
  });

  it('should prevent default and stop propagation on Space key', () => {
    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    const preventDefaultSpy = spyOn(spaceEvent, 'preventDefault');
    const stopPropagationSpy = spyOn(spaceEvent, 'stopPropagation');

    element.dispatchEvent(spaceEvent);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(stopPropagationSpy).toHaveBeenCalled();
  });
});
