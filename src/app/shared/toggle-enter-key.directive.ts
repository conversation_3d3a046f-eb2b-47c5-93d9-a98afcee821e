import { Directive, HostListener } from '@angular/core';
import { MatSlideToggle } from '@angular/material/slide-toggle';

@Directive({
  selector: '[appToggleEnterKey]'
})
export class ToggleEnterKeyDirective {
  constructor( private toggle: MatSlideToggle) {}

  @HostListener('keydown.enter', ['$event'])
  onEnter(event: KeyboardEvent) {
    event.preventDefault(); // Prevents form submission if inside a form
    this.toggle.toggle();
  }
}
