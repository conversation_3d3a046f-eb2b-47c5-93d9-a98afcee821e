import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { TabIndexService } from '@core/services';

@Directive({
  selector: '[appFocusTracker]'
})
export class FocusTrackerDirective {
  @Input() elementId: string = '';

  constructor(
    private el: ElementRef,
    private tabIndexService: TabIndexService
  ) {}

  @HostListener('focus')
  onFocus(): void {
    const id = this.elementId || this.el.nativeElement.id;
    if (id) {
      this.tabIndexService.updateTabIndex(0, 0, id);
      this.tabIndexService.saveTabPosition();
    }
  }
} 