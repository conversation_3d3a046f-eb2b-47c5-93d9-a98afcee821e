// custom-date-adapter.ts
import { Injectable } from '@angular/core';
import { NativeDateAdapter } from '@angular/material/core';

@Injectable()
export class CustomDateAdapter extends NativeDateAdapter {
  override format(date: Date, displayFormat: any): string {
    const year = date.getFullYear();
    const month = this._to2Digit(date.getMonth() + 1);
    const day = this._to2Digit(date.getDate());
    return `${year}-${month}-${day}`;
  }

  override parse(value: any): Date | null {
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
      const [year, month, day] = value.split('-').map(Number);
      return new Date(year, month - 1, day);
    }
    return super.parse(value);
  }

  private _to2Digit(n: number): string {
    return ('00' + n).slice(-2);
  }
}
