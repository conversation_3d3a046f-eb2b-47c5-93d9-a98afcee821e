import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import {NgApexchartsModule} from "ng-apexcharts";

import {ZorroModule} from "@shared/zorro";
import {FormsModule} from "@angular/forms";
import {RouterModule} from "@angular/router";
import {ReactiveFormsModule} from "@angular/forms";

import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { PaginatorTranslation } from 'src/locale/mat-translations';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatRadioModule } from '@angular/material/radio';
import { ToggleEnterKeyDirective } from './toggle-enter-key.directive';
import { FocusTrackerDirective } from './focus-tracker.directive';
import { CheckboxKeyboardDirective } from './checkbox-keyboard.directive';
import { MAT_RADIO_DEFAULT_OPTIONS } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { CustomDateAdapter } from './custom-date-adapter';


const MY_DATE_FORMAT = {
    parse: {
      dateInput: 'yyyy-MM-DD', // this is how your date will be parsed from Input
    },
    display: {
      dateInput: 'yyyy-MM-DD', // this is how your date will get displayed on the Input
      monthYearLabel: 'MMMM YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'MMMM YYYY'
    }
  };

@NgModule({
    declarations: [
    ToggleEnterKeyDirective,
    FocusTrackerDirective,
    CheckboxKeyboardDirective
  ],
    imports: [
        CommonModule
    ],
    exports:[
        ZorroModule,
        NgApexchartsModule,
        FormsModule,
        RouterModule,
        ReactiveFormsModule,
        MatMenuModule,
        MatSelectModule,
        MatPaginatorModule,
        MatSlideToggleModule,
        MatRadioModule,
        ToggleEnterKeyDirective,
        FocusTrackerDirective,
        CheckboxKeyboardDirective,
        MatDatepickerModule,
        MatNativeDateModule,
        MatInputModule
    ],
        providers: [{
            provide: MatPaginatorIntl, useClass: PaginatorTranslation
        },
        MatNativeDateModule,
        { provide: DateAdapter, useClass: CustomDateAdapter, deps: [MAT_DATE_LOCALE] },
        { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT }
     ]   
})
export class SharedModule { }
