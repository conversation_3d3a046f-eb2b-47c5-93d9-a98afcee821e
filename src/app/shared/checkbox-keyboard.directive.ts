import { Directive, ElementRef, Input, HostListener, Renderer2, Output, EventEmitter } from '@angular/core';

@Directive({
  selector: '[appCheckboxKeyboard]'
})
export class CheckboxKeyboardDirective {
  @Input() isChecked: boolean = false;
  @Input() isDisabled: boolean = false;
  @Output() appCheckboxKeyboard = new EventEmitter<boolean>();

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Handle Enter and Space keys for checkbox activation
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
      
      if (!this.isDisabled) {
        // Toggle the checkbox state
        const newCheckedState = !this.isChecked;
        this.appCheckboxKeyboard.emit(newCheckedState);
      }
    }
  }

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent): void {
    // Prevent double-triggering when using keyboard
    if (event.detail === 0) {
      return; // This was triggered programmatically
    }
    
    if (!this.isDisabled) {
      const newCheckedState = !this.isChecked;
      this.appCheckboxKeyboard.emit(newCheckedState);
    }
  }
}
