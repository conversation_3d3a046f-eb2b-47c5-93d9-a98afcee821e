import {NgModule} from '@angular/core';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzGridModule } from 'ng-zorro-antd/grid';
import {NzTimePickerModule} from "ng-zorro-antd/time-picker";
import {NzToolTipModule} from "ng-zorro-antd/tooltip";
import { NzDividerModule } from 'ng-zorro-antd/divider';
import {NzInputModule} from "ng-zorro-antd/input";
import {NzCheckboxModule} from "ng-zorro-antd/checkbox";

@NgModule({
    exports: [
        NzLayoutModule,
        NzMenuModule,
        NzCardModule,
        NzGridModule,
        NzTimePickerModule,
        NzToolTipModule,
        NzDividerModule,
        NzInputModule,
        NzCheckboxModule
    ]
})
export class ZorroModule {}
