{"name": "new-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --output-path ../ui/v3 --configuration development", "test": "ng test", "lint": "ng lint", "test-server": "ng build --optimization=false --configuration=test-server --base-href=./"}, "private": true, "dependencies": {"@angular/animations": "~13.3.0", "@angular/cdk": "~13.3.9", "@angular/common": "~13.3.0", "@angular/compiler": "~13.3.0", "@angular/core": "~13.3.0", "@angular/forms": "~13.3.0", "@angular/material": "^13.3.1", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/router": "~13.3.0", "@ant-design/icons": "^4.7.0", "apexcharts": "^3.35.4", "card-validator": "^10.0.2", "ng-apexcharts": "^1.7.1", "ng-click-outside": "^9.0.1", "ng-http-caching": "^13.0.10", "ng-zorro-antd": "^13.4.0", "ngx-cookie-service": "^13.2.1", "ngx-papaparse": "^8.0.0", "object-to-formdata": "^4.4.2", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.3.9", "@angular-eslint/builder": "13.5.0", "@angular-eslint/eslint-plugin": "13.5.0", "@angular-eslint/eslint-plugin-template": "13.5.0", "@angular-eslint/schematics": "13.5.0", "@angular-eslint/template-parser": "13.5.0", "@angular/cli": "~13.3.9", "@angular/compiler-cli": "~13.3.0", "@angular/localize": "^13.3.11", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.27.1", "@typescript-eslint/parser": "5.27.1", "autoprefixer": "^10.4.8", "eslint": "^8.17.0", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "ng-extract-i18n-merge": "^2.4.0", "postcss": "^8.4.16", "tailwindcss": "^3.1.8", "typescript": "~4.6.2"}}