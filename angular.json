{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"new-angular": {"i18n": {"sourceLocale": {"code": "en", "baseHref": ""}, "locales": {"zh": {"baseHref": "", "translation": "src/locale/messages.zh.xlf"}, "fr": {"baseHref": "", "translation": "src/locale/messages.fr.xlf"}}}, "projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"localize": ["fr", "en"], "outputPath": "dist/new-angular", "outputHashing": "all", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js", "src/googleTagManager.js"]}, "configurations": {"production": {"optimization": {"styles": {"inlineCritical": false}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "5kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"localize": ["en"], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "sandbox": {"optimization": {"styles": {"inlineCritical": false}}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.sandbox.ts"}], "outputHashing": "all"}, "test-server": {"optimization": {"styles": {"inlineCritical": false}}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test-server.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "test-server"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "your-application-name:build", "proxyConfig": "src/proxy.conf.mjs"}, "configurations": {"production": {"browserTarget": "new-angular:build:production"}, "development": {"browserTarget": "new-angular:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "ng-extract-i18n-merge:ng-extract-i18n-merge", "options": {"browserTarget": "new-angular:build", "format": "xlf", "outputPath": "src/locale", "targetFiles": ["messages.zh.xlf", "messages.fr.xlf"]}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "less", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "defaultProject": "new-angular", "cli": {"analytics": "00afe5f7-6257-49ba-89fe-0694d6205d8a", "defaultCollection": "@angular-eslint/schematics"}}